/**
 * Attachments Handler for WMS Application
 * Handles all attachment-related operations
 */

class AttachmentsHandler {
    constructor() {
        this.currentFile = null;
        this.debug = true;
    }

    /**
     * Initialize the handler
     */
    init() {
        this.log('Initializing Attachments Handler');
        this.setupActionHandlers();
    }

    /**
     * Set up handlers for attachment actions
     */
    setupActionHandlers() {
        // Delegate event handling for file actions
        document.addEventListener('click', (event) => {
            // Handle view action
            if (event.target.closest('.file-action-view')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    const filename = fileItem.getAttribute('data-filename');
                    const fileType = fileItem.getAttribute('data-filetype') || this.getFileTypeFromName(filename);
                    this.viewFile(filename, fileType);
                }
            }
            
            // Handle download action
            if (event.target.closest('.file-action-download')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    const filename = fileItem.getAttribute('data-filename');
                    this.downloadFile(filename);
                }
            }
            
            // Handle share action
            if (event.target.closest('.file-action-share')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    const filename = fileItem.getAttribute('data-filename');
                    this.shareFile(filename);
                }
            }
            
            // Handle delete action
            if (event.target.closest('.file-action-delete')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    const filename = fileItem.getAttribute('data-filename');
                    this.deleteFile(filename);
                }
            }
        });
        
        // Add data attributes to all file items
        this.addDataAttributesToFileItems();
    }

    /**
     * Add necessary data attributes to file items for action handling
     */
    addDataAttributesToFileItems() {
        // Process all file list items in the attachments modal
        const attachmentsModal = document.getElementById('attachmentsModal');
        if (attachmentsModal) {
            const fileItems = attachmentsModal.querySelectorAll('.list-group-item');
            fileItems.forEach(item => {
                // Extract filename from the item
                const filenameElement = item.querySelector('h6');
                if (filenameElement) {
                    const filename = filenameElement.textContent.trim();
                    item.setAttribute('data-filename', filename);
                    
                    // Determine file type from icon or filename
                    const iconElement = item.querySelector('.bi');
                    if (iconElement) {
                        if (iconElement.classList.contains('bi-file-earmark-pdf')) {
                            item.setAttribute('data-filetype', 'pdf');
                        } else if (iconElement.classList.contains('bi-file-earmark-spreadsheet')) {
                            item.setAttribute('data-filetype', 'spreadsheet');
                        } else if (iconElement.classList.contains('bi-file-earmark-image')) {
                            item.setAttribute('data-filetype', 'image');
                        } else if (iconElement.classList.contains('bi-file-earmark-text')) {
                            item.setAttribute('data-filetype', 'document');
                        }
                    }
                }
            });
            
            // Update file actions to have proper classes
            const actionButtons = attachmentsModal.querySelectorAll('.list-group-item .btn');
            actionButtons.forEach(button => {
                const icon = button.querySelector('.bi');
                if (icon) {
                    if (icon.classList.contains('bi-eye')) {
                        button.classList.add('file-action-view');
                    } else if (icon.classList.contains('bi-download')) {
                        button.classList.add('file-action-download');
                    } else if (icon.classList.contains('bi-share')) {
                        button.classList.add('file-action-share');
                    } else if (icon.classList.contains('bi-trash')) {
                        button.classList.add('file-action-delete');
                    }
                }
            });
        }
    }

    /**
     * View a file
     * @param {string} filename - Name of the file to view
     * @param {string} fileType - Type of file (pdf, spreadsheet, etc.)
     */
    viewFile(filename, fileType) {
        this.log(`Viewing file: ${filename} (${fileType})`);
        
        // Set current file for other operations
        this.currentFile = {
            name: filename,
            type: fileType
        };
        
        // Hide the attachments modal
        const attachmentsModal = document.getElementById('attachmentsModal');
        if (attachmentsModal) {
            const modalInstance = bootstrap.Modal.getInstance(attachmentsModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
        
        // Create or get file viewer modal
        let fileViewerModal = document.getElementById('fileViewerModal');
        
        if (!fileViewerModal) {
            // Create file viewer modal if it doesn't exist
            fileViewerModal = document.createElement('div');
            fileViewerModal.className = 'modal fade';
            fileViewerModal.id = 'fileViewerModal';
            fileViewerModal.tabIndex = -1;
            fileViewerModal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
            fileViewerModal.setAttribute('aria-hidden', 'true');
            
            fileViewerModal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="fileViewerTitle"></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="fileViewerContent"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="attachmentsHandler.downloadCurrentFile()">
                                <i class="bi bi-download"></i> Download
                            </button>
                            <button type="button" class="btn btn-info text-white" onclick="attachmentsHandler.shareCurrentFile()">
                                <i class="bi bi-share"></i> Share
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(fileViewerModal);
        }
        
        // Update modal content
        const title = document.getElementById('fileViewerTitle');
        const content = document.getElementById('fileViewerContent');
        
        if (title && content) {
            const iconClass = this.getFileIconClass(fileType);
            title.innerHTML = `<i class="bi ${iconClass}"></i> ${filename}`;
            content.innerHTML = this.getPreviewContent(filename, fileType);
        }
        
        // Show the modal
        const modal = new bootstrap.Modal(fileViewerModal);
        modal.show();
    }

    /**
     * Download a file
     * @param {string} filename - Name of the file to download
     */
    downloadFile(filename) {
        this.log(`Downloading file: ${filename}`);
        alert(`Downloading ${filename}...`);
        
        // In a real app, this would trigger an actual download
        // For demo purposes, we just show an alert
    }

    /**
     * Download current file
     */
    downloadCurrentFile() {
        if (this.currentFile) {
            this.downloadFile(this.currentFile.name);
        }
    }

    /**
     * Share a file
     * @param {string} filename - Name of the file to share
     */
    shareFile(filename) {
        this.log(`Sharing file: ${filename}`);
        const email = prompt(`Enter email address to share "${filename}" with:`);
        if (email) {
            alert(`${filename} has been shared with ${email}`);
        }
    }

    /**
     * Share current file
     */
    shareCurrentFile() {
        if (this.currentFile) {
            this.shareFile(this.currentFile.name);
        }
    }

    /**
     * Delete a file
     * @param {string} filename - Name of the file to delete
     */
    deleteFile(filename) {
        this.log(`Deleting file: ${filename}`);
        if (confirm(`Are you sure you want to delete "${filename}"?`)) {
            alert(`${filename} has been deleted`);
            
            // In a real app, this would remove the file from the server
            // For demo purposes, we'll remove the element from the DOM
            const fileItems = document.querySelectorAll(`[data-filename="${filename}"]`);
            fileItems.forEach(item => {
                item.remove();
            });
            
            // Close the file viewer if this is the current file
            if (this.currentFile && this.currentFile.name === filename) {
                const fileViewerModal = document.getElementById('fileViewerModal');
                if (fileViewerModal) {
                    const modalInstance = bootstrap.Modal.getInstance(fileViewerModal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
            }
        }
    }

    /**
     * Get file type from filename
     * @param {string} filename - Name of file
     * @returns {string} File type
     */
    getFileTypeFromName(filename) {
        if (!filename) return 'unknown';
        
        const extension = filename.split('.').pop().toLowerCase();
        
        switch(extension) {
            case 'pdf':
                return 'pdf';
            case 'doc':
            case 'docx':
            case 'txt':
            case 'rtf':
                return 'document';
            case 'xls':
            case 'xlsx':
            case 'csv':
                return 'spreadsheet';
            case 'ppt':
            case 'pptx':
                return 'presentation';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'svg':
                return 'image';
            case 'zip':
            case 'rar':
            case '7z':
            case 'tar':
            case 'gz':
                return 'archive';
            default:
                return 'unknown';
        }
    }

    /**
     * Get icon class based on file type
     * @param {string} fileType - Type of file
     * @returns {string} Icon class
     */
    getFileIconClass(fileType) {
        switch(fileType) {
            case 'pdf':
                return 'bi-file-earmark-pdf text-danger';
            case 'document':
                return 'bi-file-earmark-text text-primary';
            case 'spreadsheet':
                return 'bi-file-earmark-spreadsheet text-success';
            case 'presentation':
                return 'bi-file-earmark-slides text-warning';
            case 'image':
                return 'bi-file-earmark-image text-info';
            case 'archive':
                return 'bi-file-earmark-zip text-secondary';
            default:
                return 'bi-file-earmark text-secondary';
        }
    }

    /**
     * Get preview content for a file
     * @param {string} filename - Name of file
     * @param {string} fileType - Type of file
     * @returns {string} HTML content for preview
     */
    getPreviewContent(filename, fileType) {
        switch(fileType) {
            case 'pdf':
                return `<div class="text-center p-5">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 64px;"></i>
                    <h4 class="mt-3">${filename}</h4>
                    <div class="border p-3 mt-3 text-start bg-light">
                        <h5>PDF Preview</h5>
                        <p>PDF preview would be displayed here in a production environment.</p>
                        <p>For this demo, we're just showing this placeholder.</p>
                    </div>
                </div>`;
                
            case 'document':
                return `<div class="border p-4 bg-light h-100">
                    <h4 class="mb-4">${filename}</h4>
                    <hr>
                    <p>Document content would be displayed here in a production environment.</p>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed vitae nisi eget nunc ultricies 
                    aliquet. Sed vitae nisi eget nunc ultricies aliquet.</p>
                    <p>Proin ac magna eu quam faucibus tempus id at purus. Sed vitae nisi eget nunc ultricies 
                    aliquet. Sed vitae nisi eget nunc ultricies aliquet.</p>
                </div>`;
                
            case 'spreadsheet':
                return `<div class="border p-3 bg-light h-100">
                    <h4 class="mb-3">${filename}</h4>
                    <hr>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Item ID</th>
                                    <th>Product Name</th>
                                    <th>Quantity</th>
                                    <th>Location</th>
                                    <th>Last Updated</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>WH-1001</td>
                                    <td>Widget A</td>
                                    <td>250</td>
                                    <td>A-12-03</td>
                                    <td>2024-05-10</td>
                                </tr>
                                <tr>
                                    <td>WH-1002</td>
                                    <td>Widget B</td>
                                    <td>175</td>
                                    <td>B-05-11</td>
                                    <td>2024-05-09</td>
                                </tr>
                                <tr>
                                    <td>WH-1003</td>
                                    <td>Widget C</td>
                                    <td>340</td>
                                    <td>A-14-07</td>
                                    <td>2024-05-08</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>`;
                
            case 'image':
                return `<div class="text-center p-3">
                    <h4 class="mb-3">${filename}</h4>
                    <img src="https://via.placeholder.com/800x600.png?text=Warehouse+Image" 
                        class="img-fluid border" alt="${filename}">
                </div>`;
                
            default:
                return `<div class="alert alert-info p-5">
                    <h4 class="alert-heading">${filename}</h4>
                    <p>Preview not available for this file type.</p>
                    <hr>
                    <p class="mb-0">Please download the file to view its contents.</p>
                </div>`;
        }
    }

    /**
     * Log a message
     * @param {string} message - Message to log
     */
    log(message) {
        if (this.debug) {
            console.log(`AttachmentsHandler: ${message}`);
        }
    }
}

// Create and initialize the handler
const attachmentsHandler = new AttachmentsHandler();
document.addEventListener('DOMContentLoaded', () => {
    attachmentsHandler.init();
});

// Global functions for file operations
function viewAttachment(type, fileName) {
    console.log(`Viewing ${type} file: ${fileName}`);

    // Hide attachments modal if it's open
    const attachmentsModal = bootstrap.Modal.getInstance(document.getElementById('attachmentsModal'));
    if (attachmentsModal) {
        attachmentsModal.hide();
    }

    // Create a file viewer modal if it doesn't exist
    const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

    // Set the file information
    const fileTitle = document.getElementById('fileViewerTitle');
    const fileContent = document.getElementById('fileViewerContent');

    if (fileTitle && fileContent) {
        // Set the title based on the file type and name
        const iconClass = attachmentsHandler.getFileIcon(type);
        fileTitle.innerHTML = `<i class="bi ${iconClass}"></i> ${fileName}`;

        // Set the content based on the file type
        fileContent.innerHTML = getPreviewContent(type, fileName);

        // Store current file info for download/share/delete operations
        window.currentFile = {
            name: fileName,
            type: type
        };

        // Show the modal
        const modal = new bootstrap.Modal(fileViewerModal);
        modal.show();

        console.log(`Viewing ${fileName} directly`);
    } else {
        console.error('File viewer components not found');
        alert('File viewer is not available. Please try again later.');
    }
}

// Helper function to create a file viewer modal if it doesn't exist
function createFileViewerModal() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'fileViewerModal';
    modal.tabIndex = '-1';
    modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
    modal.setAttribute('aria-hidden', 'true');
    modal.style.zIndex = '1200'; // Ensure it appears above the sidebar

    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="fileViewerTitle">File Viewer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="fileViewerContent" style="min-height: 400px; overflow: auto;">
                        <!-- File content will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="btn-group me-auto">
                        <button type="button" class="btn btn-success" onclick="downloadCurrentFile()"><i class="bi bi-download me-1"></i> Download</button>
                        <button type="button" class="btn btn-info" onclick="shareCurrentFile()"><i class="bi bi-share me-1"></i> Share</button>
                        <button type="button" class="btn btn-danger" onclick="deleteCurrentFile()"><i class="bi bi-trash me-1"></i> Delete</button>
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    return modal;
}

// Function to get preview content based on file type
function getPreviewContent(type, fileName) {
    switch(type) {
        case 'pdf':
            return `<div class="text-center">
                <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                <h4 class="mt-3">${fileName}</h4>
                <div class="border p-3 mt-3 text-start bg-light">
                    <h5>PDF Preview</h5>
                    <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                    <p>The document contains warehouse layout information and storage locations.</p>
                </div>
            </div>`;

        case 'document':
            return `<div class="border p-3 bg-light">
                <h4>${fileName}</h4>
                <hr>
                <p><strong>Warehouse Shipping Procedures</strong></p>
                <p>Document content would be displayed here in a real application.</p>
                <p>This document outlines the standard operating procedures for shipping operations in the warehouse.</p>
                <ol>
                    <li>Receive shipping order from system</li>
                    <li>Verify inventory availability</li>
                    <li>Pick items from designated locations</li>
                    <li>Pack items according to shipping guidelines</li>
                    <li>Generate shipping label and documentation</li>
                    <li>Load onto appropriate carrier</li>
                    <li>Update inventory system</li>
                </ol>
            </div>`;

        case 'spreadsheet':
            return `<div class="text-center">
                <i class="bi bi-file-earmark-excel text-success" style="font-size: 48px;"></i>
                <h4 class="mt-3">${fileName}</h4>
                <div class="table-responsive mt-3">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Item ID</th>
                                <th>Description</th>
                                <th>Location</th>
                                <th>Quantity</th>
                                <th>Last Updated</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>WH-1001</td>
                                <td>Widget A</td>
                                <td>A-01-02</td>
                                <td>500</td>
                                <td>2025-05-10</td>
                            </tr>
                            <tr>
                                <td>WH-1002</td>
                                <td>Widget B</td>
                                <td>B-03-01</td>
                                <td>250</td>
                                <td>2025-05-09</td>
                            </tr>
                            <tr>
                                <td>WH-1003</td>
                                <td>Widget C</td>
                                <td>C-02-03</td>
                                <td>1000</td>
                                <td>2025-05-08</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>`;

        case 'image':
            return `<div class="text-center">
                <h4 class="mb-3">${fileName}</h4>
                <img src="https://via.placeholder.com/800x400?text=Warehouse+Photo" class="img-fluid border" alt="Warehouse Photo">
                <p class="mt-3 text-muted">Image would be displayed here in a real application.</p>
            </div>`;

        case 'presentation':
            return `<div class="text-center">
                <i class="bi bi-file-earmark-slides text-warning" style="font-size: 48px;"></i>
                <h4 class="mt-3">${fileName}</h4>
                <div class="border p-3 mt-3 text-start bg-light">
                    <h5>Presentation Preview</h5>
                    <p>This is a preview of the presentation content. The actual slides would be displayed here in a real application.</p>
                    <div class="border p-3 mb-3 bg-white">
                        <h6 class="text-center">Slide 1: Warehouse Staff Training</h6>
                        <p class="text-center text-muted">Introduction and Overview</p>
                    </div>
                    <div class="border p-3 mb-3 bg-white">
                        <h6 class="text-center">Slide 2: Safety Procedures</h6>
                        <p class="text-center text-muted">Key safety guidelines for warehouse operations</p>
                    </div>
                </div>
            </div>`;

        default:
            return `<div class="text-center">
                <i class="bi bi-file-earmark text-secondary" style="font-size: 48px;"></i>
                <h4 class="mt-3">${fileName}</h4>
                <div class="alert alert-info mt-3">
                    <p>Preview not available for this file type.</p>
                </div>
            </div>`;
    }
}

// Function to download the current file
function downloadCurrentFile() {
    if (window.currentFile) {
        console.log(`Downloading file: ${window.currentFile.name}`);
        alert(`Download started for ${window.currentFile.name}`);
    } else {
        alert('No file selected for download');
    }
}

// Function to share the current file
function shareCurrentFile() {
    if (window.currentFile) {
        console.log(`Sharing file: ${window.currentFile.name}`);
        const shareEmail = prompt('Enter email address to share with:');
        if (shareEmail) {
            alert(`${window.currentFile.name} has been shared with ${shareEmail}`);
        }
    } else {
        alert('No file selected for sharing');
    }
}

// Function to delete the current file
function deleteCurrentFile() {
    if (window.currentFile) {
        console.log(`Deleting file: ${window.currentFile.name}`);
        if (confirm(`Are you sure you want to delete ${window.currentFile.name}?`)) {
            alert(`${window.currentFile.name} has been deleted.`);

            // Close the file viewer modal
            const fileViewerModal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
            if (fileViewerModal) {
                fileViewerModal.hide();
            }

            // Refresh the attachments list
            attachmentsHandler.loadFiles();
        }
    } else {
        alert('No file selected for deletion');
    }
}

// Function to download a file by name
function downloadFile(fileName) {
    console.log(`Downloading file: ${fileName}`);
    alert(`Download started for ${fileName}`);
}

// Function to share a file by name
function shareFile(fileName) {
    console.log(`Sharing file: ${fileName}`);
    const shareEmail = prompt('Enter email address to share with:');
    if (shareEmail) {
        alert(`${fileName} has been shared with ${shareEmail}`);
    }
}

// Function to delete a file by name
function deleteAttachment(fileName) {
    console.log(`Deleting file: ${fileName}`);
    if (confirm(`Are you sure you want to delete ${fileName}?`)) {
        alert(`${fileName} has been deleted.`);

        // Refresh the attachments list
        attachmentsHandler.loadFiles();
    }
}