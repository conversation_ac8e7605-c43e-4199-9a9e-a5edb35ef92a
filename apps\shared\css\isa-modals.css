/**
 * ISA Modal Styles
 * Consistent modal styling across ISASUITE applications
 */

/* Modal mount point */
#isa-modal-mount {
    position: relative;
    z-index: 1050;
}

/* Custom modal styling */
.isa-modal .modal-header {
    background-color: var(--app-primary-color, #e67e22);
    color: white;
    padding: 0.75rem 1rem;
}

.isa-modal .modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
}

/* Custom styling for specific modals */
#mapsModal .modal-body,
#attachmentsModal .modal-body {
    padding: 0; /* Remove padding for tabbed content */
}

#mapsModal .nav-tabs,
#attachmentsModal .nav-tabs {
    padding-top: 0.5rem;
    padding-left: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

#mapsModal .tab-content,
#attachmentsModal .tab-content {
    padding: 1rem;
}

/* File action buttons */
.file-action-btn {
    color: #6c757d;
    background: none;
    border: none;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: color 0.15s ease-in-out;
}

.file-action-btn:hover {
    color: #495057;
}

.file-action-btn.btn-view:hover {
    color: #0d6efd;
}

.file-action-btn.btn-download:hover {
    color: #198754;
}

.file-action-btn.btn-share:hover {
    color: #0dcaf0;
}

.file-action-btn.btn-delete:hover {
    color: #dc3545;
}

/* Gmail label color dots */
.color-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* Custom modal backdrop */
.modal-backdrop.custom {
    opacity: 0.5;
}
