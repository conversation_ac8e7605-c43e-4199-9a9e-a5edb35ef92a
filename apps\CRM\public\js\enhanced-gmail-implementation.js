/**
 * Enhanced Gmail Implementation for CRM Application
 */

// Define the CRM application colors
const CRM_COLORS = {
    primary: '#fd7e14',    // Orange
    secondary: '#6c757d',  // Gray
    success: '#28a745',    // Green
    danger: '#dc3545',     // Red
    warning: '#ffc107',    // Yellow
    info: '#17a2b8',       // Teal
    light: '#f8f9fa',      // Light gray
    dark: '#343a40'        // Dark gray
};

// Initialize the enhanced Gmail functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Enhanced Gmail for CRM');
    
    // Load the shared Gmail scripts
    loadScript('/shared/enhanced-gmail.js', function() {
        loadScript('/shared/enhanced-gmail-functions.js', function() {
            loadScript('/shared/enhanced-gmail-utils.js', function() {
                // Initialize the enhanced Gmail with CRM colors
                initEnhancedGmail('CRM', CRM_COLORS);
                
                // Make the openEmail function globally available
                window.openEmail = openEmail;
            });
        });
    });
});

/**
 * Load a script dynamically
 */
function loadScript(url, callback) {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.onload = callback;
    document.head.appendChild(script);
}
