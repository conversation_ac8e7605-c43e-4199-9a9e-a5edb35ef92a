# Getting Started with ISA APPS

This guide will help you set up and run the Ice Systems Australasia Applications Suite.

## Prerequisites

- Windows operating system
- No additional software required (Node.js is included in the project)

## Setup Steps

1. **Install Dependencies**

   Run the `install-dependencies.bat` file or use PowerShell:

   ```
   powershell -ExecutionPolicy Bypass -File "C:\ISASUITE\setup-and-run.ps1"
   ```
   > **Note:** Always run `setup-and-run.ps1` with PowerShell. Do not open it with Notepad.
   > The workspace uses `pnpm` for dependency management.

2. **Start the Applications**

   You have two options to start the applications:

   **Option 1: Using the Launcher**

   Run the `ice-systems-launcher.bat` file:

   ```
   Double-click on ice-systems-launcher.bat
   ```

   This will open a menu where you can select which application to run.

   **Option 2: Starting All Applications at Once**

   Run the `start-all-local.bat` or `start-and-open.bat` file:

   ```
   Double-click on start-and-open.bat
   ```

   This will start all applications in separate command windows.

3. **Access the Applications**

   Once the applications are running, you can access them at the following URLs:

   - **Integration Hub**: http://localhost:8000
   - **BMS** (Business Management System): http://localhost:3001
   - **MRP** (Materials Requirements Planning): http://localhost:3002
   - **CRM** (Customer Relationship Management): http://localhost:3003
   - **WMS** (Warehouse Management System): http://localhost:3004
   - **APS** (Advanced Planning and Scheduling): http://localhost:3005
   - **APM** (Asset Performance Management): http://localhost:3006
   - **PMS**: http://localhost:3007
   - **SCM**: http://localhost:3008
   - **TM**: http://localhost:3009

## For More Information

- See `USER_GUIDE.md` for detailed information on using the applications
- See `docs/TROUBLESHOOTING_GUIDE.md` for help with common issues

## For Developers

If you want to develop or modify the applications:

1. Install Node.js and npm on your system
2. Use the standard development workflow as described in `docs/DEVELOPER_GUIDE.md`
3. Use the standard scripts in package.json for development tasks
