/**
 * Modal Loader for WMS
 * Ensures required modals are loaded into the DOM
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Loading WMS modals');

    // List of modals to load
    const modalsToLoad = [
        { id: 'warehouseMapsModal', path: '/modals/warehouse-maps.html' },
        { id: 'attachmentsModal', path: '/modals/attachments.html' },
        { id: 'mapsModal', path: '/modals/maps.html' }
    ];

    // Load each modal if it doesn't exist
    modalsToLoad.forEach(modal => {
        if (!document.getElementById(modal.id)) {
            fetch(modal.path)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Failed to load ${modal.id}`);
                    }
                    return response.text();
                })
                .then(html => {
                    const modalContainer = document.createElement('div');
                    modalContainer.innerHTML = html;
                    document.body.appendChild(modalContainer.firstElementChild);
                    console.log(`${modal.id} loaded successfully`);

                    // Initialize the modal with Bootstrap
                    if (typeof bootstrap !== 'undefined') {
                        try {
                            new bootstrap.Modal(document.getElementById(modal.id));
                            console.log(`${modal.id} initialized with Bootstrap`);
                        } catch (err) {
                            console.error(`Error initializing ${modal.id}:`, err);
                        }
                    }
                })
                .catch(error => {
                    console.error(`Error loading ${modal.id}:`, error);
                });
        }
    });
});
