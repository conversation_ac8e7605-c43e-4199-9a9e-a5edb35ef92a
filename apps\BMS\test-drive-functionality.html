<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Drive Functionality Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Google Drive Search, Sort, and Filter Test</h1>
        <p>This page tests the Google Drive functionality implemented in BMS.</p>
        
        <div class="test-section">
            <h3>Test 1: Open Google Drive Modal</h3>
            <button class="btn btn-primary" onclick="testOpenModal()">Open Drive Modal</button>
            <div id="test1-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 2: Test Search Functionality</h3>
            <button class="btn btn-primary" onclick="testSearchFunctionality()">Test Search</button>
            <div id="test2-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 3: Test Sort Functionality</h3>
            <button class="btn btn-primary" onclick="testSortFunctionality()">Test Sort</button>
            <div id="test3-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 4: Test Filter Functionality</h3>
            <button class="btn btn-primary" onclick="testFilterFunctionality()">Test Filter</button>
            <div id="test4-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 5: Test Tab Switching</h3>
            <button class="btn btn-primary" onclick="testTabSwitching()">Test Tab Switching</button>
            <div id="test5-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Manual Test Instructions</h3>
            <ol>
                <li>Click "Open Drive Modal" to open the Google Drive modal</li>
                <li>Try typing in the search box - it should filter files in real-time</li>
                <li>Change the sort dropdown - files should reorder</li>
                <li>Change the filter dropdown - only matching file types should show</li>
                <li>Switch between "Files" and "Shared with me" tabs</li>
                <li>Verify that each tab has independent search, sort, and filter controls</li>
            </ol>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function showResult(testId, message, type) {
            const resultDiv = document.getElementById(testId + '-result');
            resultDiv.className = 'test-result test-' + type;
            resultDiv.innerHTML = message;
        }
        
        function testOpenModal() {
            try {
                // Create a simple modal for testing
                const modalHtml = `
                    <div class="modal fade" id="testDriveModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Google Drive Test</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <p>This is a test modal. In the actual BMS application, this would be the Google Drive modal with search, sort, and filter functionality.</p>
                                    <p><strong>To test the actual functionality:</strong></p>
                                    <ol>
                                        <li>Go to <a href="http://localhost:3001" target="_blank">http://localhost:3001</a></li>
                                        <li>Click on "Google Drive" in the sidebar</li>
                                        <li>Test the search, sort, and filter controls</li>
                                    </ol>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <a href="http://localhost:3001" target="_blank" class="btn btn-primary">Go to BMS App</a>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                const modal = new bootstrap.Modal(document.getElementById('testDriveModal'));
                modal.show();
                
                showResult('test1', 'Test modal opened successfully. Click "Go to BMS App" to test actual functionality.', 'pass');
            } catch (error) {
                showResult('test1', 'Error opening modal: ' + error.message, 'fail');
            }
        }
        
        function testSearchFunctionality() {
            showResult('test2', 'Search functionality test: Please go to the BMS app (http://localhost:3001) and test the search box in the Google Drive modal. It should filter files as you type.', 'info');
        }
        
        function testSortFunctionality() {
            showResult('test3', 'Sort functionality test: Please go to the BMS app and test the sort dropdown in the Google Drive modal. Files should reorder when you change the sort option.', 'info');
        }
        
        function testFilterFunctionality() {
            showResult('test4', 'Filter functionality test: Please go to the BMS app and test the filter dropdown in the Google Drive modal. Only matching file types should be visible.', 'info');
        }
        
        function testTabSwitching() {
            showResult('test5', 'Tab switching test: Please go to the BMS app and switch between "Files" and "Shared with me" tabs. Each should have independent search, sort, and filter controls.', 'info');
        }
    </script>
</body>
</html>
