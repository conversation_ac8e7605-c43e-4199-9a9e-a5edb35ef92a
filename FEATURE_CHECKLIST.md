# ISASUITE App Feature Checklist

| Feature / Integration                        | APM | APS | BMS | CRM | MRP | PMS | SCM | TM | WMS | hub | Notes/Status |
|----------------------------------------------|-----|-----|-----|-----|-----|-----|-----|----|-----|-----|--------------|
| Google Sheets Integration                    |     |     |     |     |     |     |     |    |     |     |              |
| Google Calendar Integration                  |     |     |     |     |     |     |     |    |     |     |              |
| Payment Gateway Integration                  |     |     |     |     |     |     |     |    |     |     |              |
| Outlook Email Integration                    |     |     |     |     |     |     |     |    |     |     |              |
| Invoice Import/Export (multi-format/source)  |     |     |     |     |     |     |     |    |     |     |              |
| Enhanced AI Features (insights, suggestions) |     |     |     |     |     |     |     |    |     |     |              |
| Voice Commands & Voice Assistant             |     |     |     |     |     |     |     |    |     |     |              |
| Bill of Materials API                        |     |     |     |     |     |     |     |    |     |     |              |
| API Mapping                                  |     |     |     |     |     |     |     |    |     |     |              |
| Refresh Buttons                              |     |     |     |     |     |     |     |    |     |     |              |
| Gamification (Sandbox Mode)                  |     |     |     |     |     |     |     |    |     |     |              |
| Multiple Modes (Normal/Sandbox/Demo)         |     |     |     |     |     |     |     |    |     |     |              |
| Currency Converter                           |     |     |     |     |     |     |     |    |     |     |              |
| Animations, Charts, Graphs, Dashboards       |     |     |     |     |     |     |     |    |     |     |              |
| Widgets (including Timer/Tracking)           |     |     |     |     |     |     |     |    |     |     |              |
| Cross-Platform Sync & Real-Time Updates      |     |     |     |     |     |     |     |    |     |     |              |
| Workflow Automation & Optimization           |     |     |     |     |     |     |     |    |     |     |              |
| Mobile Responsiveness                        |     |     |     |     |     |     |     |    |     |     |              |
| Performance Monitoring                       |     |     |     |     |     |     |     |    |     |     |              |
| System Health Monitoring                     |     |     |     |     |     |     |     |    |     |     |              |
| Error Handling                               |     |     |     |     |     |     |     |    |     |     |              |
| Settings (Tabs, Preferences)                 |     |     |     |     |     |     |     |    |     |     |              |
| Communication Features                       |     |     |     |     |     |     |     |    |     |     |              |
| Accessibility Features                       |     |     |     |     |     |     |     |    |     |     |              |
| Camera & Document Scanning                   |     |     |     |     |     |     |     |    |     |     |              |
| Accounting Software Integration              |     |     |     |     |     |     |     |    |     |     |              |
| Feature Flag System (Toggle Features)        |     |     |     |     |     |     |     |    |     |     |              |
| User Onboarding                              |     |     |     |     |     |     |     |    |     |     |              |
| Attachments Functionality                    |     |     |     |     |     |     |     |    |     |     |              |
| APWAI Hints/Tips/Prompts/Tutorials           |     |     |     |     |     |     |     |    |     |     |              |

---

**How to use:**
- For each app, mark ✓ if the feature is present, ✗ if missing, or add notes.
- Use the “Notes/Status” column for comments or links to issues.
