<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Relationship Management</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">

    <!-- Shared modal styles removed - using inline styles instead -->
    <style>
        :root {
            --app-primary-color: #e67e22; /* Orange for CRM */
            --app-primary-dark: #d35400;
            --app-primary-light: rgba(230, 126, 34, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            padding-top: 56px; /* Height of navbar */
        }

        /* Sidebar styles */
        .sidebar {
            position: fixed;
            top: 56px; /* Height of navbar */
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: var(--app-primary-color);
            width: 250px;
            transition: all 0.3s;
            overflow-y: auto; /* Add scrollbar when content overflows */
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--app-primary-dark);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar .nav-link {
            color: #f8f9fa;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-heading {
            padding: 0.75rem 1.25rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.1rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Main content area */
        .main-content {
            margin-left: 0;
            transition: margin-left 0.3s;
            padding: 20px;
        }

        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px; /* Width of sidebar */
            }
        }

        /* Navbar */
        .navbar {
            background-color: var(--app-primary-color) !important;
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
        }

        /* Toggle sidebar button */
        #sidebarToggle {
            cursor: pointer;
            background: transparent;
            border: none;
            color: white;
        }

        /* For mobile view */
        @media (max-width: 767.98px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .navbar {
                padding: 0.5rem;
            }
        }
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .card-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }
        .table-container {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .table-container th {
            background-color: #3498db;
            color: white;
        }
        .kpi-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            background-color: white;
        }
        .kpi-value {
            font-size: 36px;
            font-weight: bold;
            margin: 10px 0;
        }
        .kpi-label {
            color: #6c757d;
            font-size: 14px;
        }
        .kpi-trend {
            font-size: 14px;
            margin-top: 5px;
        }
        .kpi-trend.up {
            color: #28a745;
        }
        .kpi-trend.down {
            color: #dc3545;
        }
        .activity-item {
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
        }

        /* Modal z-index fixes to ensure they appear on top */
        .modal {
            z-index: 1050 !important;
        }
        .modal-backdrop {
            z-index: 1040 !important;
        }
        #fileViewerModal, #sheetsModal, #docsModal, #driveModal, #calendarModal, #gmailModal, #mapsModal,
        #slackModal, [id^="segmentStrategyModal-"], [id^="touchpointModal-"], [id^="topicFeedbackModal-"], [id^="actionPlanModal-"] {
            z-index: 1200 !important;
        }
        /*
         * FIXED: Special z-index for modals to ensure visibility and proper stacking
         * This fix ensures that Attachments and Maps links in the sidebar correctly open their modals
         * instead of embedding content directly in the sidebar, matching the reference applications.
         */
        #attachmentsModal, #mapsModal {
            z-index: 1300 !important;
            visibility: visible !important;
        }
        #attachmentsModal.show, #mapsModal.show {
            display: block !important;
        }
        /* File viewer modal should appear above all other modals */
        #fileViewerModal {
            z-index: 1400 !important;
            visibility: visible !important;
        }
        #fileViewerModal.show {
            display: block !important;
        }

        /* Additional modal styling to ensure consistency */
        .modal-content {
            border: none;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }

        /* File viewer modal specific styling */
        #fileViewerContent {
            min-height: 400px;
            overflow: auto;
        }

        /* Ensure modals don't get cut off on mobile */
        @media (max-width: 767.98px) {
            .modal-dialog {
                margin: 0.5rem;
            }
        }
        .activity-item.call {
            border-left-color: #28a745;
        }
        .activity-item.meeting {
            border-left-color: #dc3545;
        }
        .activity-item.email {
            border-left-color: #ffc107;
        }
        .activity-item.demo {
            border-left-color: #17a2b8;
        }
        .activity-item h5 {
            margin-bottom: 5px;
        }
        .activity-item p {
            margin-bottom: 5px;
            color: #6c757d;
        }
        .activity-item .badge {
            margin-right: 5px;
        }
        .deal-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .deal-card:hover {
            transform: translateY(-5px);
        }
        .deal-header {
            padding: 15px;
            background-color: #3498db;
            color: white;
        }
        .deal-body {
            padding: 20px;
        }
        .progress {
            height: 10px;
            margin-top: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .row {
            --bs-gutter-x: 0;
        }
    </style>
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-dark fixed-top" style="background-color: #e67e22;">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <button id="sidebarToggle" class="d-md-none me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-list fs-4"></i>
                </button>
                <a class="navbar-brand" href="/">CRM System</a>
            </div>
            <div class="d-flex">
                <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3" style="background: transparent; border: 1px solid white; color: white; padding: 5px 10px;">
                    <i class="bi bi-box-arrow-left me-1"></i> Back to Hub
                </a>
                <button class="btn position-relative me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-bell fs-5"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        3
                    </span>
                </button>
                <button class="btn" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-person-circle fs-5"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="pt-2">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="/">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/customers">
                        <i class="bi bi-people"></i>
                        Customers
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/deals">
                        <i class="bi bi-currency-dollar"></i>
                        Deals
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/activities">
                        <i class="bi bi-calendar-check"></i>
                        Activities
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/marketing">
                        <i class="bi bi-megaphone"></i>
                        Marketing
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="bi bi-file-earmark-text"></i>
                        Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/settings">
                        <i class="bi bi-gear-fill"></i>
                        Settings
                    </a>
                </li>
        </ul>

            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                <span>Integrations</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="/integrations/salesforce">
                        <i class="bi bi-cloud"></i>
                        Salesforce
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal">
                        <i class="bi bi-calendar3"></i>
                        Google Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal">
                        <i class="bi bi-folder"></i>
                        Google Drive
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal">
                        <i class="bi bi-file-earmark-text"></i>
                        Google Docs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                        <i class="bi bi-file-earmark-spreadsheet"></i>
                        Google Sheets
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" id="gmail-link">
                        <i class="bi bi-envelope"></i>
                        Gmail
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-contacts" data-bs-toggle="modal" data-bs-target="#contactsModal">
                        <i class="bi bi-person-rolodex"></i>
                        Google Contacts
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#slack" data-bs-toggle="modal" data-bs-target="#slackModal">
                        <i class="bi bi-slack"></i>
                        Slack
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" data-isa-action="open-maps">
                        <i class="bi bi-geo-alt"></i>
                        Maps
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" data-isa-action="open-attachments">
                        <i class="bi bi-paperclip"></i>
                        Attachments
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();">
                        <i class="bi bi-box-arrow-left"></i>
                        Back to Hub
                    </a>
                </li>
            </ul>


        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">Customer Relationship Dashboard</h1>
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                </div>
                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle">
                    <i class="bi bi-calendar"></i>
                    This month
                </button>
            </div>
        </div>

                <!-- KPI Section -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-people card-icon"></i>
                                <h5 class="card-title">Total Customers</h5>
                                <h2 class="card-text">256</h2>
                                <p class="card-text">+12% from last month</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-currency-dollar card-icon"></i>
                                <h5 class="card-title">Active Deals</h5>
                                <h2 class="card-text">28</h2>
                                <p class="card-text">+5% from last month</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-warning text-dark">
                            <div class="card-body text-center">
                                <i class="bi bi-graph-up-arrow card-icon"></i>
                                <h5 class="card-title">Conversion Rate</h5>
                                <h2 class="card-text">24%</h2>
                                <p class="card-text">+3% from last month</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-emoji-smile card-icon"></i>
                                <h5 class="card-title">Customer Satisfaction</h5>
                                <h2 class="card-text">92%</h2>
                                <p class="card-text">+2% from last month</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Google Integrations -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4 class="mb-3">Google Integrations</h4>
                    </div>

                    <!-- Google Gmail -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-gmail-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-envelope"></i> Gmail</h5>
                                <div class="component-actions">
                                    <button id="refresh-gmail" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="compose-email" class="btn btn-sm btn-outline-success" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-pencil"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">JD</div>
                                                <div>
                                                    <div class="fw-bold">John Davis</div>
                                                    <div class="small text-truncate" style="max-width: 200px;">Meeting follow-up: Product demo feedback</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">10m</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="openEmail('john-davis', 'Meeting follow-up: Product demo feedback')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="replyEmail('john-davis', 'Meeting follow-up: Product demo feedback')"><i class="bi bi-reply"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">SM</div>
                                                <div>
                                                    <div class="fw-bold">Sarah Miller</div>
                                                    <div class="small text-truncate" style="max-width: 200px;">Contract renewal discussion</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">1h</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="openEmail('sarah-miller', 'Contract renewal discussion')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="replyEmail('sarah-miller', 'Contract renewal discussion')"><i class="bi bi-reply"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">AC</div>
                                                <div>
                                                    <div class="fw-bold">Alex Chen</div>
                                                    <div class="small text-truncate" style="max-width: 200px;">New customer inquiry about enterprise plan</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">3h</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="openEmail('alex-chen', 'New customer inquiry about enterprise plan')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="replyEmail('alex-chen', 'New customer inquiry about enterprise plan')"><i class="bi bi-reply"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" onclick="document.getElementById('gmail-link').click();">
                                        <i class="bi bi-envelope me-2"></i>Open Gmail
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Drive -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-drive-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-folder"></i> Google Drive</h5>
                                <div class="component-actions">
                                    <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="upload-file" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-upload"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-folder-fill text-primary me-2"></i>
                                            <span>Customer Contracts</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-secondary rounded-pill me-2">15 files</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'customer-contracts')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Customer Contracts'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-folder-fill text-primary me-2"></i>
                                            <span>Sales Presentations</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-secondary rounded-pill me-2">8 files</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'sales-presentations')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Sales Presentations'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                            <span>Q2_2025_Sales_Report.pdf</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">Yesterday</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Q2_2025_Sales_Report.pdf')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Q2_2025_Sales_Report.pdf'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Q2_2025_Sales_Report.pdf'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
                                        <i class="bi bi-folder me-2"></i>View All Files
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Docs -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-docs-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                                <div class="component-actions">
                                    <button id="refresh-docs" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="create-new-doc" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#docsModal"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <span>Customer Onboarding Guide</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">Today</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Customer Onboarding Guide.docx')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Customer Onboarding Guide.docx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Customer Onboarding Guide.docx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <span>Sales Proposal Template</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">Yesterday</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Sales Proposal Template.docx')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Sales Proposal Template.docx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Sales Proposal Template.docx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <span>Meeting Notes - Enterprise Client</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">3 days ago</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Meeting Notes - Enterprise Client.docx')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Meeting Notes - Enterprise Client.docx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Meeting Notes - Enterprise Client.docx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#docsModal">
                                        <i class="bi bi-file-earmark-text me-2"></i>View All Documents
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Sheets -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-sheets-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                                <div class="component-actions">
                                    <button id="refresh-sheets" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="create-new-sheet" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#sheetsModal"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <span>Sales_Pipeline_Q2_2025.xlsx</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">2 days ago</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Sales_Pipeline_Q2_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Sales_Pipeline_Q2_2025.xlsx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Sales_Pipeline_Q2_2025.xlsx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <span>Customer_Contacts_Database.xlsx</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">1 week ago</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Customer_Contacts_Database.xlsx')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Customer_Contacts_Database.xlsx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Customer_Contacts_Database.xlsx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <span>Lead_Tracking_2025.xlsx</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">2 weeks ago</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Lead_Tracking_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Lead_Tracking_2025.xlsx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Lead_Tracking_2025.xlsx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                                        <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Calendar -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-calendar-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                                <div class="component-actions">
                                    <button id="refresh-calendar" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="create-new-event" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#calendarModal"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Client Demo: ABC Corporation</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-clock me-1"></i>Today, 2:00 PM - 3:30 PM
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge bg-warning rounded-pill me-2">Today</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewCalendarEvent('client-demo-abc')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareCalendarEvent('client-demo-abc')"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Sales Team Meeting</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-clock me-1"></i>Tomorrow, 9:00 AM - 10:00 AM
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge bg-info rounded-pill me-2">Tomorrow</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewCalendarEvent('sales-team-meeting')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareCalendarEvent('sales-team-meeting')"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Contract Negotiation: XYZ Inc</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-clock me-1"></i>May 15, 11:00 AM - 12:30 PM
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge bg-secondary rounded-pill me-2">Next Week</span>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewCalendarEvent('contract-negotiation-xyz')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="shareCalendarEvent('contract-negotiation-xyz')"><i class="bi bi-share"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#calendarModal">
                                        <i class="bi bi-calendar3 me-2"></i>View Full Calendar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <!-- Charts Section -->
                <div class="row mt-4">
                    <div class="col-md-8">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Sales Trends</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="salesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Lead Sources</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="leadSourcesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Deals -->
                <h2 class="mt-4">Upcoming Deals</h2>
                <div class="row" id="dealCards">
                    <!-- Deal cards will be populated by JavaScript -->
                </div>

                <!-- Recent Customers -->
                <h2 class="mt-4">Recent Customers</h2>
                <div class="table-container">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Status</th>
                                <th>Last Contact</th>
                            </tr>
                        </thead>
                        <tbody id="customerTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- Recent Activities -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Recent Activities</h5>
                            </div>
                            <div class="card-body">
                                <div id="activitiesList">
                                    <!-- Activity items will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Insights Section -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card dashboard-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title">AI-Powered Customer Insights</h5>
                                <div>
                                    <button id="refresh-insights-btn" class="btn btn-sm btn-primary me-2">
                                        <i class="bi bi-arrow-clockwise"></i> Refresh
                                    </button>
                                    <button id="expand-all-insights" class="btn btn-sm btn-outline-primary me-2">
                                        <i class="bi bi-arrows-expand"></i> Expand All
                                    </button>
                                    <button id="collapse-all-insights" class="btn btn-sm btn-outline-secondary">
                                        <i class="bi bi-arrows-collapse"></i> Collapse All
                                    </button>
                                    <button id="export-insights" class="btn btn-sm btn-outline-success ms-2">
                                        <i class="bi bi-download"></i> Export
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="ai-insights-container">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Integration Modals -->
    <!-- File Viewer Modal -->
    <div class="modal fade" id="fileViewerModal" tabindex="-1" aria-labelledby="fileViewerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="fileViewerTitle"></h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="downloadCurrentFileEnhanced()"><i class="bi bi-download"></i> Download</button>
                    <button type="button" class="btn btn-success" onclick="shareCurrentFileEnhanced()"><i class="bi bi-share"></i> Share</button>
                    <button type="button" class="btn btn-danger" onclick="deleteCurrentFile()"><i class="bi bi-trash"></i> Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Maps Modal -->
    <div class="modal fade" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="mapsModalLabel"><i class="bi bi-geo-alt"></i> Google Maps</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="mapsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="map-view-tab" data-bs-toggle="tab" data-bs-target="#map-view-content" type="button" role="tab" aria-controls="map-view-content" aria-selected="true">Map View</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="customer-locations-tab" data-bs-toggle="tab" data-bs-target="#customer-locations-content" type="button" role="tab" aria-controls="customer-locations-content" aria-selected="false">Customer Locations</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="route-planning-tab" data-bs-toggle="tab" data-bs-target="#route-planning-content" type="button" role="tab" aria-controls="route-planning-content" aria-selected="false">Route Planning</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="mapsTabContent">
                        <!-- Map View Tab -->
                        <div class="tab-pane fade show active" id="map-view-content" role="tabpanel" aria-labelledby="map-view-tab">
                            <div class="row">
                                <div class="col-md-9">
                                    <div class="ratio ratio-16x9">
                                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card h-100">
                                        <div class="card-header">
                                            <h6 class="mb-0">Search Location</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="location-search" class="form-label">Address or Place</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="location-search" placeholder="Enter address or place">
                                                    <button class="btn btn-outline-primary" type="button"><i class="bi bi-search"></i></button>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Recent Searches</label>
                                                <div class="list-group">
                                                    <a href="#" class="list-group-item list-group-item-action">ABC Corporation HQ</a>
                                                    <a href="#" class="list-group-item list-group-item-action">XYZ Inc. Office</a>
                                                    <a href="#" class="list-group-item list-group-item-action">Tech Innovations Campus</a>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Options</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="show-traffic" checked>
                                                    <label class="form-check-label" for="show-traffic">Show Traffic</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="show-transit">
                                                    <label class="form-check-label" for="show-transit">Show Transit</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="show-bicycling">
                                                    <label class="form-check-label" for="show-bicycling">Show Bicycling</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Locations Tab -->
                        <div class="tab-pane fade" id="customer-locations-content" role="tabpanel" aria-labelledby="customer-locations-tab">
                            <div class="row">
                                <div class="col-md-9">
                                    <div class="ratio ratio-16x9">
                                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card h-100">
                                        <div class="card-header">
                                            <h6 class="mb-0">Customer Locations</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="customer-filter" class="form-label">Filter Customers</label>
                                                <select class="form-select" id="customer-filter">
                                                    <option value="all">All Customers</option>
                                                    <option value="active">Active Customers</option>
                                                    <option value="prospects">Prospects</option>
                                                    <option value="high-value">High-Value Customers</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Customer List</label>
                                                <div class="list-group" style="max-height: 300px; overflow-y: auto;">
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        ABC Corporation
                                                        <span class="badge bg-primary rounded-pill">Active</span>
                                                    </a>
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        XYZ Inc.
                                                        <span class="badge bg-primary rounded-pill">Active</span>
                                                    </a>
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        Tech Innovations
                                                        <span class="badge bg-warning rounded-pill">Prospect</span>
                                                    </a>
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        Global Solutions
                                                        <span class="badge bg-primary rounded-pill">Active</span>
                                                    </a>
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        Acme Corp
                                                        <span class="badge bg-danger rounded-pill">Inactive</span>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="d-grid">
                                                <button class="btn btn-primary" type="button">
                                                    <i class="bi bi-download me-2"></i>Export Customer Map
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Route Planning Tab -->
                        <div class="tab-pane fade" id="route-planning-content" role="tabpanel" aria-labelledby="route-planning-tab">
                            <div class="row">
                                <div class="col-md-9">
                                    <div class="ratio ratio-16x9">
                                        <iframe src="https://www.google.com/maps/embed?pb=!1m28!1m12!1m3!1d193595.15830869428!2d-74.11976397304605!3d40.69766374874431!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!4m13!3e0!4m5!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!3m2!1d40.7127753!2d-74.0059728!4m5!1s0x89c25090129c363d%3A0x40c6a5770d25022b!2sQueens%2C%20NY%2C%20USA!3m2!1d40.7282239!2d-73.7948516!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card h-100">
                                        <div class="card-header">
                                            <h6 class="mb-0">Plan Your Route</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="route-start" class="form-label">Starting Point</label>
                                                <input type="text" class="form-control" id="route-start" placeholder="Enter starting location" value="Office HQ">
                                            </div>
                                            <div class="mb-3">
                                                <label for="route-destination" class="form-label">Destination</label>
                                                <input type="text" class="form-control" id="route-destination" placeholder="Enter destination">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Waypoints</label>
                                                <div class="input-group mb-2">
                                                    <input type="text" class="form-control" placeholder="Add waypoint">
                                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-plus"></i></button>
                                                </div>
                                                <div class="list-group">
                                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                                        ABC Corporation
                                                        <button class="btn btn-sm btn-outline-danger"><i class="bi bi-x"></i></button>
                                                    </div>
                                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                                        XYZ Inc.
                                                        <button class="btn btn-sm btn-outline-danger"><i class="bi bi-x"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Transportation Mode</label>
                                                <div class="btn-group w-100" role="group">
                                                    <input type="radio" class="btn-check" name="transportation" id="transportation-driving" checked>
                                                    <label class="btn btn-outline-primary" for="transportation-driving"><i class="bi bi-car-front"></i></label>

                                                    <input type="radio" class="btn-check" name="transportation" id="transportation-transit">
                                                    <label class="btn btn-outline-primary" for="transportation-transit"><i class="bi bi-bus-front"></i></label>

                                                    <input type="radio" class="btn-check" name="transportation" id="transportation-walking">
                                                    <label class="btn btn-outline-primary" for="transportation-walking"><i class="bi bi-person-walking"></i></label>

                                                    <input type="radio" class="btn-check" name="transportation" id="transportation-bicycling">
                                                    <label class="btn btn-outline-primary" for="transportation-bicycling"><i class="bi bi-bicycle"></i></label>
                                                </div>
                                            </div>
                                            <div class="d-grid">
                                                <button class="btn btn-primary" type="button">
                                                    <i class="bi bi-map me-2"></i>Get Directions
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://maps.google.com" target="_blank" class="btn btn-primary">Open in Google Maps</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Gmail Modal -->
    <div class="modal fade" id="gmailModal" tabindex="-1" aria-labelledby="gmailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="gmailModalLabel"><i class="bi bi-envelope"></i> Gmail</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-3 border-end">
                            <div class="d-grid gap-2 mb-3">
                                <button class="btn btn-primary" id="compose-new-email" onclick="document.getElementById('compose-tab').click()">
                                    <i class="bi bi-pencil-square me-2"></i>Compose
                                </button>
                            </div>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action active d-flex justify-content-between align-items-center" id="inbox-link" onclick="document.getElementById('inbox-tab').click()">
                                    <div><i class="bi bi-inbox me-2"></i>Inbox</div>
                                    <span class="badge bg-primary rounded-pill">3</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="starred-link">
                                    <div><i class="bi bi-star me-2"></i>Starred</div>
                                    <span class="badge bg-secondary rounded-pill">2</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="sent-link" onclick="document.getElementById('sent-tab').click()">
                                    <div><i class="bi bi-send me-2"></i>Sent</div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="drafts-link">
                                    <div><i class="bi bi-file-earmark me-2"></i>Drafts</div>
                                    <span class="badge bg-secondary rounded-pill">1</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="important-link">
                                    <div><i class="bi bi-bookmark me-2"></i>Important</div>
                                </a>
                            </div>

                            <div class="mt-4">
                                <h6 class="text-muted mb-2">LABELS</h6>
                                <div class="list-group">
                                    <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="color-dot bg-success me-2"></span>Clients
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="color-dot bg-danger me-2"></span>Urgent
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="color-dot bg-warning me-2"></span>Follow-up
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="color-dot bg-info me-2"></span>Team
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" class="form-control" placeholder="Search emails..." id="crm-email-search">
                                    <button class="btn btn-outline-secondary" type="button" id="crm-search-btn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                                <div>
                                    <div class="dropdown d-inline-block me-2">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="crm-sort-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-sort-down"></i> Sort
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="crm-sort-dropdown">
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="date-desc">Newest first</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="date-asc">Oldest first</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="sender-asc">Sender A-Z</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="sender-desc">Sender Z-A</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="subject-asc">Subject A-Z</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="subject-desc">Subject Z-A</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown d-inline-block me-2">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="crm-filter-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-funnel"></i> Filter
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="crm-filter-dropdown">
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="all">All emails</a></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="unread">Unread</a></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="read">Read</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="client">Client</a></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="urgent">Urgent</a></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="follow-up">Follow-up</a></li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-outline-secondary" id="crm-refresh-gmail">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Gmail Tabs -->
                            <ul class="nav nav-tabs mb-3" id="gmailTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox-content" type="button" role="tab" aria-controls="inbox-content" aria-selected="true">Inbox</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent-content" type="button" role="tab" aria-controls="sent-content" aria-selected="false">Sent</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="read-tab" data-bs-toggle="tab" data-bs-target="#read-content" type="button" role="tab" aria-controls="read-content" aria-selected="false">Read</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose-content" type="button" role="tab" aria-controls="compose-content" aria-selected="false">Compose</button>
                                </li>
                            </ul>

                            <div class="tab-content" id="gmailTabContent">
                                <!-- Inbox Tab Content -->
                                <div class="tab-pane fade show active" id="inbox-content" role="tabpanel" aria-labelledby="inbox-tab">
                                    <!-- Email List Section -->
                                    <div id="email-list-section">
                                        <div class="list-group">
                                            <a href="#" class="list-group-item list-group-item-action unread" data-sender="john-davis" data-subject="Meeting follow-up: Product demo feedback">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">JD</div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1 fw-bold">John Davis</h6>
                                                            <small class="text-muted">10:30 AM</small>
                                                        </div>
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <p class="mb-1 fw-bold">Meeting follow-up: Product demo feedback</p>
                                                            <span class="badge bg-success rounded-pill">Client</span>
                                                        </div>
                                                        <small class="text-muted">Thank you for the product demonstration yesterday. Our team was impressed with the features...</small>
                                                    </div>
                                                </div>
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action unread" data-sender="sarah-miller" data-subject="Contract renewal discussion">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #ea4335; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">SM</div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1 fw-bold">Sarah Miller</h6>
                                                            <small class="text-muted">Yesterday</small>
                                                        </div>
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <p class="mb-1 fw-bold">Contract renewal discussion</p>
                                                            <span class="badge bg-danger rounded-pill">Urgent</span>
                                                        </div>
                                                        <small class="text-muted">I'd like to schedule a call to discuss the upcoming contract renewal...</small>
                                                    </div>
                                                </div>
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action unread" data-sender="alex-chen" data-subject="New customer inquiry about enterprise plan">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #34a853; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">AC</div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1 fw-bold">Alex Chen</h6>
                                                            <small class="text-muted">May 10</small>
                                                        </div>
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <p class="mb-1 fw-bold">New customer inquiry about enterprise plan</p>
                                                            <span class="badge bg-warning rounded-pill">Follow-up</span>
                                                        </div>
                                                        <small class="text-muted">We're interested in learning more about your enterprise plan options...</small>
                                                    </div>
                                                </div>
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action" data-sender="team-manager" data-subject="Weekly team update - Q2 goals" onclick="openEmail('team-manager', 'Weekly team update - Q2 goals')">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #fbbc05; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">TM</div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1">Team Manager</h6>
                                                            <small class="text-muted">May 9</small>
                                                        </div>
                                                        <p class="mb-1">Weekly team update - Q2 goals</p>
                                                        <small class="text-muted">Here's a summary of our progress toward Q2 goals and priorities for next week...</small>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Sent Tab Content -->
                                <div class="tab-pane fade" id="sent-content" role="tabpanel" aria-labelledby="sent-tab">
                                    <div class="list-group">
                                        <a href="#" class="list-group-item list-group-item-action">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0 me-3">
                                                    <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">ME</div>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <div class="d-flex w-100 justify-content-between">
                                                        <h6 class="mb-1">To: John Davis</h6>
                                                        <small class="text-muted">11:45 AM</small>
                                                    </div>
                                                    <p class="mb-1">Re: Meeting follow-up: Product demo feedback</p>
                                                    <small class="text-muted">Thank you for your feedback. I'd be happy to schedule a follow-up call next week...</small>
                                                </div>
                                            </div>
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0 me-3">
                                                    <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">ME</div>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <div class="d-flex w-100 justify-content-between">
                                                        <h6 class="mb-1">To: Marketing Team</h6>
                                                        <small class="text-muted">Yesterday</small>
                                                    </div>
                                                    <p class="mb-1">Q2 Marketing Campaign Plan</p>
                                                    <small class="text-muted">Please find attached the Q2 marketing campaign plan for review...</small>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>

                                <!-- Read Tab Content -->
                                <div class="tab-pane fade" id="read-content" role="tabpanel" aria-labelledby="read-tab">
                                    <!-- This content will be populated dynamically by the openEmail function -->
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i> Select an email from the inbox to view its content.
                                    </div>
                                </div>

                                <!-- Compose Tab Content -->
                                <div class="tab-pane fade" id="compose-content" role="tabpanel" aria-labelledby="compose-tab">
                                    <div class="card">
                                        <div class="card-body">
                                            <form>
                                                <div class="mb-3">
                                                    <label for="email-to" class="form-label">To</label>
                                                    <input type="email" class="form-control" id="email-to" placeholder="<EMAIL>" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="email-cc" class="form-label">Cc/Bcc</label>
                                                    <input type="text" class="form-control" id="email-cc" placeholder="<EMAIL>">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="email-subject" class="form-label">Subject</label>
                                                    <input type="text" class="form-control" id="email-subject" placeholder="Email subject">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="email-body" class="form-label">Message</label>
                                                    <textarea class="form-control" id="email-body" rows="10" placeholder="Compose your message here..."></textarea>
                                                </div>
                                                <div class="mb-3">
                                                    <div id="crm-attachments-container" class="d-none mb-3">
                                                        <h6>Attachments</h6>
                                                        <div id="crm-attachments-list" class="border p-2 rounded mb-2" style="max-height: 150px; overflow-y: auto;">
                                                            <!-- Attachments will be added here dynamically -->
                                                        </div>
                                                    </div>
                                                    <button type="button" class="btn btn-outline-secondary" id="crm-attach-btn">
                                                        <i class="bi bi-paperclip"></i> Attach
                                                    </button>
                                                    <input type="file" class="d-none" id="crm-file-input" multiple>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <button type="button" class="btn btn-outline-secondary">Discard</button>
                                                    <div>
                                                        <button type="button" class="btn btn-outline-primary me-2">Save Draft</button>
                                                        <button type="button" class="btn btn-primary">Send</button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://mail.google.com" target="_blank" class="btn btn-primary">Open in Gmail</a>
                </div>
            </div>
        </div>
    </div>

        <!-- Google Drive Modal -->
    <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="driveModalLabel"><i class="bi bi-folder"></i> Google Drive</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="driveTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content" type="button" role="tab" aria-controls="files-content" aria-selected="true">Files</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button" role="tab" aria-controls="upload-content" aria-selected="false">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-tab" data-bs-toggle="tab" data-bs-target="#shared-content" type="button" role="tab" aria-controls="shared-content" aria-selected="false">Shared with me</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="driveTabContent">
                        <!-- Files Tab -->
                        <div class="tab-pane fade show active" id="files-content" role="tabpanel" aria-labelledby="files-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="file-search-drive" placeholder="Search files...">
                                        <button class="btn btn-outline-secondary" type="button" id="search-files-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="file-sort" class="form-label visually-hidden">Sort files</label>
                                    <select class="form-select" id="file-sort" title="Sort files" aria-label="Sort files">
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="date-desc">Newest First</option>
                                        <option value="date-asc">Oldest First</option>
                                        <option value="size-desc">Largest First</option>
                                        <option value="size-asc">Smallest First</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="file-filter" class="form-label visually-hidden">Filter files</label>
                                    <select class="form-select" id="file-filter" title="Filter files" aria-label="Filter files">
                                        <option value="all">All Files</option>
                                        <option value="folders">Folders</option>
                                        <option value="documents">Documents</option>
                                        <option value="spreadsheets">Spreadsheets</option>
                                        <option value="presentations">Presentations</option>
                                        <option value="images">Images</option>
                                        <option value="pdfs">PDFs</option>
                                    </select>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="drive-files-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                                    <span>Project Documentation</span>
                                                </div>
                                            </td>
                                            <td>Yesterday</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                                    <span>Reports</span>
                                                </div>
                                            </td>
                                            <td>3 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                                    <span>Project_Report_Q2_2025.pdf</span>
                                                </div>
                                            </td>
                                            <td>Yesterday</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Project_Timeline_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                    <span>Project_Requirements.docx</span>
                                                </div>
                                            </td>
                                            <td>1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Upload Tab -->
                        <div class="tab-pane fade" id="upload-content" role="tabpanel" aria-labelledby="upload-tab">
                            <div class="row">
                                <div class="col-md-7">
                                    <div class="upload-area p-5 mb-3 text-center" id="dropzone" style="border: 2px dashed #ccc; border-radius: 5px; background-color: #f8f9fa;">
                                        <i class="bi bi-cloud-upload fs-1 text-muted mb-3"></i>
                                        <h5>Drag & Drop Files Here</h5>
                                        <p class="text-muted">or</p>
                                        <label for="file-upload" class="btn btn-primary">
                                            Browse Files
                                        </label>
                                        <input id="file-upload" type="file" multiple style="display: none;">
                                        <p class="text-muted mt-3 small">Maximum file size: 50MB</p>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="card">
                                        <div class="card-header">
                                            Upload Options
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="upload-folder" class="form-label">Destination folder</label>
                                                <select class="form-select" id="upload-folder">
                                                    <option selected>My Drive</option>
                                                    <option>Project Documentation</option>
                                                    <option>Reports</option>
                                                    <option>Team Shared Folder</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="convert-to-google-format">
                                                    <label class="form-check-label" for="convert-to-google-format">
                                                        Convert to Google format
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="share-with-team">
                                                    <label class="form-check-label" for="share-with-team">
                                                        Share with team
                                                    </label>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-primary" id="upload-file-btn">
                                                <i class="bi bi-upload me-2"></i>Upload Files
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shared with me Tab -->
                        <div class="tab-pane fade" id="shared-content" role="tabpanel" aria-labelledby="shared-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="shared-search" placeholder="Search shared files...">
                                        <button class="btn btn-outline-secondary" type="button" id="search-shared-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="shared-sort" class="form-label visually-hidden">Sort shared files</label>
                                    <select class="form-select" id="shared-sort" title="Sort shared files" aria-label="Sort shared files">
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="date-desc">Newest First</option>
                                        <option value="date-asc">Oldest First</option>
                                        <option value="shared-desc">Recently Shared</option>
                                        <option value="shared-asc">Oldest Shared</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="shared-filter" class="form-label visually-hidden">Filter shared files</label>
                                    <select class="form-select" id="shared-filter" title="Filter shared files" aria-label="Filter shared files">
                                        <option value="all">All Files</option>
                                        <option value="documents">Documents</option>
                                        <option value="spreadsheets">Spreadsheets</option>
                                        <option value="presentations">Presentations</option>
                                        <option value="images">Images</option>
                                        <option value="pdfs">PDFs</option>
                                    </select>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Shared By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="shared-files-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                                    <span>Project_Proposal.pdf</span>
                                                </div>
                                            </td>
                                            <td>John Davis - 3 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Resource_Allocation.xlsx</span>
                                                </div>
                                            </td>
                                            <td>Sarah Wilson - 1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                    <span>Meeting_Notes.docx</span>
                                                </div>
                                            </td>
                                            <td>David Chen - 2 weeks ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://drive.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Google Drive</a>
                </div>
            </div>
        </div>
    </div>

<!-- Google Sheets Modal -->
    <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="sheetsModalLabel"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="sheetsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="recent-tab" data-bs-toggle="tab" data-bs-target="#recent-sheets" type="button" role="tab" aria-controls="recent-sheets" aria-selected="true">Recent Sheets</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create-sheet" type="button" role="tab" aria-controls="create-sheet" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import-sheet" type="button" role="tab" aria-controls="import-sheet" aria-selected="false">Import Data</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="sheetsTabContent">
                        <div class="tab-pane fade show active" id="recent-sheets" role="tabpanel" aria-labelledby="recent-tab">
                            <!-- Search, Sort, and Filter Controls -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="sheets-search" placeholder="Search sheets...">
                                        <button class="btn btn-outline-secondary" type="button" id="search-sheets-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="sheets-sort">
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="date-desc">Newest First</option>
                                        <option value="date-asc">Oldest First</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="sheets-filter">
                                        <option value="all">All Files</option>
                                        <option value="spreadsheets">Spreadsheets</option>
                                        <option value="shared">Shared</option>
                                        <option value="recent">Recent</option>
                                    </select>
                                </div>
                            </div>
                            <div class="list-group" id="sheets-list">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <strong>Sales_Pipeline_Q2_2025.xlsx</strong>
                                        <p class="mb-1 small text-muted">Last edited: 2 days ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary rounded-pill me-2">Shared</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'sales-pipeline-q2-2025');">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <strong>Customer_Contacts_Database.xlsx</strong>
                                        <p class="mb-1 small text-muted">Last edited: 1 week ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-secondary rounded-pill me-2">Private</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'customer-contacts-database');">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <strong>Lead_Tracking_2025.xlsx</strong>
                                        <p class="mb-1 small text-muted">Last edited: 2 weeks ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary rounded-pill me-2">Shared</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'lead-tracking-2025');">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="create-sheet" role="tabpanel" aria-labelledby="create-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="sheet-title" class="form-label">Spreadsheet Title</label>
                                    <input type="text" class="form-control" id="sheet-title" placeholder="Enter a title for your spreadsheet">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Template</label>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-file-earmark text-primary fs-3 mb-2"></i>
                                                    <h6>Blank</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="template" id="blank-template" value="blank" checked>
                                                        <label class="form-check-label" for="blank-template">
                                                            Start from scratch
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-calendar-week text-success fs-3 mb-2"></i>
                                                    <h6>Sales Tracker</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="template" id="sales-template" value="sales">
                                                        <label class="form-check-label" for="sales-template">
                                                            Sales tracker template
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-people text-danger fs-3 mb-2"></i>
                                                    <h6>Contact Database</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="template" id="contact-template" value="contact">
                                                        <label class="form-check-label" for="contact-template">
                                                            Contact database
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Sharing Options</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="sharing" id="private-sharing" value="private" checked>
                                        <label class="form-check-label" for="private-sharing">
                                            Private - Only you can access
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="sharing" id="team-sharing" value="team">
                                        <label class="form-check-label" for="team-sharing">
                                            Team - Your team members can access
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="sharing" id="public-sharing" value="public">
                                        <label class="form-check-label" for="public-sharing">
                                            Public - Anyone with the link can access
                                        </label>
                                    </div>
                                </div>
                                <button type="button" id="create-sheet-btn" class="btn btn-primary" onclick="createNewSpreadsheet()">Create Spreadsheet</button>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="import-sheet" role="tabpanel" aria-labelledby="import-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="import-file" class="form-label">Upload File</label>
                                    <input class="form-control" type="file" id="import-file">
                                    <div class="form-text">Supported formats: .xlsx, .xls, .csv, .tsv</div>
                                </div>
                                <div class="mb-3">
                                    <label for="import-sheet-name" class="form-label">Sheet Name</label>
                                    <input type="text" class="form-control" id="import-sheet-name" placeholder="Enter a name for your spreadsheet">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="first-row-headers" checked>
                                        <label class="form-check-label" for="first-row-headers">
                                            First row contains headers
                                        </label>
                                    </div>
                                </div>
                                <button type="button" id="import-sheet-btn" class="btn btn-primary" onclick="importSpreadsheetData()">Import Data</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://sheets.google.com" target="_blank" class="btn btn-primary">Open in Google Sheets</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Docs Modal -->
    <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="docsModalLabel"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="docsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="recent-docs-tab" data-bs-toggle="tab" data-bs-target="#recent-docs" type="button" role="tab" aria-controls="recent-docs" aria-selected="true">Recent Documents</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-doc-tab" data-bs-toggle="tab" data-bs-target="#create-doc" type="button" role="tab" aria-controls="create-doc" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-docs-tab" data-bs-toggle="tab" data-bs-target="#shared-docs" type="button" role="tab" aria-controls="shared-docs" aria-selected="false">Shared with me</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="docsTabContent">
                        <div class="tab-pane fade show active" id="recent-docs" role="tabpanel" aria-labelledby="recent-docs-tab">
                            <!-- Search, Sort, and Filter Controls -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="docs-search" placeholder="Search documents...">
                                        <button class="btn btn-outline-secondary" type="button" id="search-docs-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="docs-sort">
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="date-desc">Newest First</option>
                                        <option value="date-asc">Oldest First</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="docs-filter">
                                        <option value="all">All Files</option>
                                        <option value="documents">Documents</option>
                                        <option value="shared">Shared</option>
                                        <option value="recent">Recent</option>
                                    </select>
                                </div>
                            </div>
                            <div class="list-group" id="docs-list">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <strong>Project_Proposal_2025.docx</strong>
                                        <p class="mb-1 small text-muted">Last edited: 1 day ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary rounded-pill me-2">Shared</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'project-proposal-2025');">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <strong>Meeting_Notes_Q2.docx</strong>
                                        <p class="mb-1 small text-muted">Last edited: 3 days ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-secondary rounded-pill me-2">Private</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'meeting-notes-q2');">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <strong>Client_Contract_Template.docx</strong>
                                        <p class="mb-1 small text-muted">Last edited: 1 week ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary rounded-pill me-2">Shared</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'client-contract-template');">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="create-doc" role="tabpanel" aria-labelledby="create-doc-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="doc-title" class="form-label">Document Title</label>
                                    <input type="text" class="form-control" id="doc-title" placeholder="Enter a title for your document">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Template</label>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-file-earmark text-primary fs-3 mb-2"></i>
                                                    <h6>Blank</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="doc-template" id="blank-doc-template" value="blank" checked>
                                                        <label class="form-check-label" for="blank-doc-template">
                                                            Start from scratch
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-file-earmark-text text-success fs-3 mb-2"></i>
                                                    <h6>Proposal</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="doc-template" id="proposal-template" value="proposal">
                                                        <label class="form-check-label" for="proposal-template">
                                                            Business proposal template
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-file-earmark-ruled text-danger fs-3 mb-2"></i>
                                                    <h6>Contract</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="doc-template" id="contract-template" value="contract">
                                                        <label class="form-check-label" for="contract-template">
                                                            Contract template
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" id="create-doc-btn" class="btn btn-primary" onclick="createNewDocument()">Create Document</button>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="shared-docs" role="tabpanel" aria-labelledby="shared-docs-tab">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Shared By</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="shared-docs-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Shared_Project_Plan.docx</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2" style="width: 32px; height: 32px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">JD</div>
                                                    <span>John Davis</span>
                                                </div>
                                            </td>
                                            <td>Yesterday, 2:15 PM</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'shared-project-plan')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Team_Guidelines.docx</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2" style="width: 32px; height: 32px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">SM</div>
                                                    <span>Sarah Miller</span>
                                                </div>
                                            </td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'team-guidelines')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://docs.google.com" target="_blank" class="btn btn-primary">Open in Google Docs</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Calendar Modal -->
    <div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="calendarModalLabel"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-3 border-end">
                            <div class="d-grid gap-2 mb-3">
                                <button class="btn btn-primary" id="create-new-event">
                                    <i class="bi bi-plus-circle me-2"></i>Create Event
                                </button>
                            </div>
                            <div class="list-group mb-4">
                                <a href="#" class="list-group-item list-group-item-action active d-flex justify-content-between align-items-center" id="month-view-link">
                                    <div><i class="bi bi-calendar-month me-2"></i>Month</div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="week-view-link">
                                    <div><i class="bi bi-calendar-week me-2"></i>Week</div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="day-view-link">
                                    <div><i class="bi bi-calendar-day me-2"></i>Day</div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="agenda-view-link">
                                    <div><i class="bi bi-list-ul me-2"></i>Agenda</div>
                                </a>
                            </div>

                            <h6 class="text-muted mb-2">MY CALENDARS</h6>
                            <div class="list-group mb-3">
                                <div class="list-group-item d-flex align-items-center">
                                    <input class="form-check-input me-2" type="checkbox" id="primary-calendar" checked>
                                    <label class="form-check-label flex-grow-1" for="primary-calendar">
                                        <span class="color-dot bg-primary me-2"></span>Primary
                                    </label>
                                </div>
                                <div class="list-group-item d-flex align-items-center">
                                    <input class="form-check-input me-2" type="checkbox" id="work-calendar" checked>
                                    <label class="form-check-label flex-grow-1" for="work-calendar">
                                        <span class="color-dot bg-success me-2"></span>Work
                                    </label>
                                </div>
                                <div class="list-group-item d-flex align-items-center">
                                    <input class="form-check-input me-2" type="checkbox" id="personal-calendar">
                                    <label class="form-check-label flex-grow-1" for="personal-calendar">
                                        <span class="color-dot bg-danger me-2"></span>Personal
                                    </label>
                                </div>
                            </div>

                            <h6 class="text-muted mb-2">OTHER CALENDARS</h6>
                            <div class="list-group">
                                <div class="list-group-item d-flex align-items-center">
                                    <input class="form-check-input me-2" type="checkbox" id="team-calendar" checked>
                                    <label class="form-check-label flex-grow-1" for="team-calendar">
                                        <span class="color-dot bg-info me-2"></span>Team
                                    </label>
                                </div>
                                <div class="list-group-item d-flex align-items-center">
                                    <input class="form-check-input me-2" type="checkbox" id="holidays-calendar" checked>
                                    <label class="form-check-label flex-grow-1" for="holidays-calendar">
                                        <span class="color-dot bg-warning me-2"></span>Holidays
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="btn-group">
                                    <button class="btn btn-outline-secondary" id="prev-btn">
                                        <i class="bi bi-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" id="today-btn">Today</button>
                                    <button class="btn btn-outline-secondary" id="next-btn">
                                        <i class="bi bi-chevron-right"></i>
                                    </button>
                                </div>
                                <h4 class="mb-0" id="current-date">May 2025</h4>
                            </div>

                            <div id="calendar-main-view">
                                <!-- Month View (Default) -->
                                <div class="calendar-container">
                                    <table class="table table-bordered calendar-table">
                                        <thead>
                                            <tr>
                                                <th>Sun</th>
                                                <th>Mon</th>
                                                <th>Tue</th>
                                                <th>Wed</th>
                                                <th>Thu</th>
                                                <th>Fri</th>
                                                <th>Sat</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="text-muted">30</td>
                                                <td>1</td>
                                                <td>2</td>
                                                <td>3</td>
                                                <td>4</td>
                                                <td>5</td>
                                                <td>6</td>
                                            </tr>
                                            <tr>
                                                <td>7</td>
                                                <td>8</td>
                                                <td>9</td>
                                                <td>10</td>
                                                <td class="bg-light position-relative">
                                                    11
                                                    <div class="calendar-event bg-warning text-dark" onclick="viewEvent('client-demo')">Client Demo</div>
                                                </td>
                                                <td>12</td>
                                                <td>13</td>
                                            </tr>
                                            <tr>
                                                <td>14</td>
                                                <td class="bg-light position-relative">
                                                    15
                                                    <div class="calendar-event bg-info text-white" onclick="viewEvent('contract-negotiation')">Contract Negotiation</div>
                                                </td>
                                                <td>16</td>
                                                <td>17</td>
                                                <td>18</td>
                                                <td>19</td>
                                                <td>20</td>
                                            </tr>
                                            <tr>
                                                <td>21</td>
                                                <td>22
                                                    <div class="calendar-event bg-success text-white" onclick="viewEvent('sales-meeting')">Sales Meeting</div>
                                                </td>
                                                <td>23</td>
                                                <td>24</td>
                                                <td>25</td>
                                                <td>26</td>
                                                <td>27</td>
                                            </tr>
                                            <tr>
                                                <td>28</td>
                                                <td>29</td>
                                                <td>30</td>
                                                <td>31</td>
                                                <td class="text-muted">1</td>
                                                <td class="text-muted">2</td>
                                                <td class="text-muted">3</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Event Details Section (initially hidden) -->
                            <div id="event-details-section" style="display: none;">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <button class="btn btn-sm btn-outline-secondary" onclick="backToCalendar()">
                                                <i class="bi bi-arrow-left"></i> Back to Calendar
                                            </button>
                                            <div>
                                                <button class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <h4 id="event-title" class="card-title">Client Demo: ABC Corporation</h4>
                                        <div class="mb-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-clock me-2 text-primary"></i>
                                                <span id="event-time">May 11, 2025, 2:00 PM - 3:30 PM</span>
                                            </div>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-geo-alt me-2 text-danger"></i>
                                                <span id="event-location">Conference Room A / Zoom Meeting</span>
                                            </div>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-calendar-check me-2 text-success"></i>
                                                <span id="event-calendar">Work Calendar</span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <h6>Description</h6>
                                            <p id="event-description">Product demonstration for ABC Corporation's executive team. We'll be showcasing the new features of our enterprise solution and discussing potential implementation strategies.</p>
                                        </div>
                                        <div class="mb-3">
                                            <h6>Attendees</h6>
                                            <div id="event-attendees" class="d-flex flex-wrap">
                                                <div class="me-2 mb-2 d-flex align-items-center">
                                                    <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">JD</div>
                                                    <span class="ms-2">John Davis</span>
                                                </div>
                                                <div class="me-2 mb-2 d-flex align-items-center">
                                                    <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #ea4335; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">SM</div>
                                                    <span class="ms-2">Sarah Miller</span>
                                                </div>
                                                <div class="me-2 mb-2 d-flex align-items-center">
                                                    <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #34a853; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">AC</div>
                                                    <span class="ms-2">Alex Chen</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-end">
                                            <button class="btn btn-outline-secondary me-2">
                                                <i class="bi bi-download"></i> Download
                                            </button>
                                            <button class="btn btn-outline-primary">
                                                <i class="bi bi-share"></i> Share
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Create Event Section (initially hidden) -->
                            <div id="create-event-section" style="display: none;">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">Create New Event</h5>
                                            <button class="btn-close" onclick="closeCreateEvent()"></button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <form>
                                            <div class="mb-3">
                                                <label for="new-event-title" class="form-label">Event Title</label>
                                                <input type="text" class="form-control" id="new-event-title" placeholder="Add title" required>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="new-event-start" class="form-label">Start</label>
                                                    <div class="input-group">
                                                        <input type="date" class="form-control" id="new-event-start-date">
                                                        <input type="time" class="form-control" id="new-event-start-time" value="09:00">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="new-event-end" class="form-label">End</label>
                                                    <div class="input-group">
                                                        <input type="date" class="form-control" id="new-event-end-date">
                                                        <input type="time" class="form-control" id="new-event-end-time" value="10:00">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="new-event-location" class="form-label">Location</label>
                                                <input type="text" class="form-control" id="new-event-location" placeholder="Add location">
                                            </div>
                                            <div class="mb-3">
                                                <label for="new-event-calendar" class="form-label">Calendar</label>
                                                <select class="form-select" id="new-event-calendar">
                                                    <option value="primary">Primary</option>
                                                    <option value="work" selected>Work</option>
                                                    <option value="personal">Personal</option>
                                                    <option value="team">Team</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="new-event-description" class="form-label">Description</label>
                                                <textarea class="form-control" id="new-event-description" rows="3" placeholder="Add description"></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label for="new-event-attendees" class="form-label">Attendees</label>
                                                <div class="input-group">
                                                    <input type="email" class="form-control" id="new-event-attendees" placeholder="Add guests">
                                                    <button class="btn btn-outline-secondary" type="button">
                                                        <i class="bi bi-person-plus"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Notification</label>
                                                <select class="form-select" id="new-event-notification">
                                                    <option value="30">30 minutes before</option>
                                                    <option value="60">1 hour before</option>
                                                    <option value="1440">1 day before</option>
                                                    <option value="none">None</option>
                                                </select>
                                            </div>
                                            <div class="d-flex justify-content-end">
                                                <button type="button" class="btn btn-outline-secondary me-2" onclick="closeCreateEvent()">Cancel</button>
                                                <button type="submit" class="btn btn-primary">Save</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://calendar.google.com" target="_blank" class="btn btn-primary">Open in Google Calendar</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Maps Modal -->
    <div class="modal fade" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="mapsModalLabel"><i class="bi bi-geo-alt"></i> Google Maps</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="mapsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations-content" type="button" role="tab" aria-controls="locations-content" aria-selected="true">Locations</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="directions-tab" data-bs-toggle="tab" data-bs-target="#directions-content" type="button" role="tab" aria-controls="directions-content" aria-selected="false">Directions</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="saved-tab" data-bs-toggle="tab" data-bs-target="#saved-content" type="button" role="tab" aria-controls="saved-content" aria-selected="false">Saved Places</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="mapsTabContent">
                        <!-- Locations Tab -->
                        <div class="tab-pane fade show active" id="locations-content" role="tabpanel" aria-labelledby="locations-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="location-search" placeholder="Search locations" aria-label="Search locations">
                                    <button class="btn btn-primary" type="button" id="search-location-btn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                                <div class="btn-group ms-2">
                                    <button type="button" class="btn btn-outline-secondary" id="current-location-btn">
                                        <i class="bi bi-geo"></i> Current
                                    </button>
                                </div>
                            </div>
                            <div class="ratio ratio-16x9 mb-3">
                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                            </div>
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-building me-2"></i>
                                            <span>ABC Corporation HQ</span>
                                            <div class="small text-muted">123 Business Ave, New York, NY 10001</div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewLocation('abc-corporation')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="getDirections('abc-corporation')"><i class="bi bi-signpost-2"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareLocation('abc-corporation')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="saveLocation('abc-corporation')"><i class="bi bi-bookmark"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-building me-2"></i>
                                            <span>XYZ Inc Office</span>
                                            <div class="small text-muted">456 Corporate Blvd, New York, NY 10002</div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewLocation('xyz-inc')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="getDirections('xyz-inc')"><i class="bi bi-signpost-2"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareLocation('xyz-inc')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="saveLocation('xyz-inc')"><i class="bi bi-bookmark"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-building me-2"></i>
                                            <span>Tech Conference Center</span>
                                            <div class="small text-muted">789 Innovation Way, New York, NY 10003</div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewLocation('tech-conference')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="getDirections('tech-conference')"><i class="bi bi-signpost-2"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareLocation('tech-conference')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="saveLocation('tech-conference')"><i class="bi bi-bookmark"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Directions Tab -->
                        <div class="tab-pane fade" id="directions-content" role="tabpanel" aria-labelledby="directions-tab">
                            <div class="mb-3">
                                <div class="input-group mb-3">
                                    <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>
                                    <input type="text" class="form-control" id="directions-start" placeholder="Starting point">
                                    <button class="btn btn-outline-secondary" type="button" id="use-current-location">
                                        <i class="bi bi-geo"></i>
                                    </button>
                                </div>
                                <div class="input-group mb-3">
                                    <span class="input-group-text"><i class="bi bi-geo-alt-fill"></i></span>
                                    <input type="text" class="form-control" id="directions-end" placeholder="Destination">
                                </div>
                                <div class="d-flex justify-content-between mb-3">
                                    <div class="btn-group" role="group" aria-label="Travel mode">
                                        <input type="radio" class="btn-check" name="travel-mode" id="travel-driving" autocomplete="off" checked>
                                        <label class="btn btn-outline-primary" for="travel-driving"><i class="bi bi-car-front"></i></label>

                                        <input type="radio" class="btn-check" name="travel-mode" id="travel-transit" autocomplete="off">
                                        <label class="btn btn-outline-primary" for="travel-transit"><i class="bi bi-bus-front"></i></label>

                                        <input type="radio" class="btn-check" name="travel-mode" id="travel-walking" autocomplete="off">
                                        <label class="btn btn-outline-primary" for="travel-walking"><i class="bi bi-person-walking"></i></label>

                                        <input type="radio" class="btn-check" name="travel-mode" id="travel-cycling" autocomplete="off">
                                        <label class="btn btn-outline-primary" for="travel-cycling"><i class="bi bi-bicycle"></i></label>
                                    </div>
                                    <button class="btn btn-primary" id="get-directions-btn">
                                        <i class="bi bi-signpost-2 me-2"></i>Get Directions
                                    </button>
                                </div>
                            </div>
                            <div class="ratio ratio-16x9 mb-3">
                                <iframe src="https://www.google.com/maps/embed?pb=!1m28!1m12!1m3!1d48369.59254503551!2d-74.0056!3d40.7128!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!4m13!3e0!4m5!1s0x89c259a9b3117469%3A0xd134e199a405a163!2sEmpire%20State%20Building%2C%20New%20York%2C%20NY!3m2!1d40.748817!2d-73.985428!4m5!1s0x89c25903e47b9657%3A0xd8c988bf42e909d4!2sOne%20World%20Trade%20Center%2C%20New%20York%2C%20NY!3m2!1d40.7127431!2d-74.0133795!5e0!3m2!1sen!2sus!4v1682531529270!5m2!1sen!2sus" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                            </div>
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Directions</h6>
                                    <div>
                                        <span class="badge bg-primary">12.3 miles</span>
                                        <span class="badge bg-secondary">25 min</span>
                                    </div>
                                </div>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="bi bi-geo-alt text-primary me-2"></i> Start at Empire State Building
                                    </li>
                                    <li class="list-group-item">
                                        <i class="bi bi-arrow-right text-secondary me-2"></i> Head south on 5th Ave
                                    </li>
                                    <li class="list-group-item">
                                        <i class="bi bi-arrow-right text-secondary me-2"></i> Turn right onto W 34th St
                                    </li>
                                    <li class="list-group-item">
                                        <i class="bi bi-arrow-right text-secondary me-2"></i> Continue onto Broadway
                                    </li>
                                    <li class="list-group-item">
                                        <i class="bi bi-geo-alt-fill text-danger me-2"></i> Arrive at One World Trade Center
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Saved Places Tab -->
                        <div class="tab-pane fade" id="saved-content" role="tabpanel" aria-labelledby="saved-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" class="form-control" placeholder="Search saved places" id="saved-search">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Places</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="clients">Clients</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="prospects">Prospects</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="events">Events</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-building me-2 text-primary"></i>
                                                <div>
                                                    <h6 class="mb-0">ABC Corporation HQ</h6>
                                                    <div class="small text-muted">123 Business Ave, New York, NY 10001</div>
                                                    <span class="badge bg-primary">Client</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewLocation('abc-corporation')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="getDirections('abc-corporation')"><i class="bi bi-signpost-2"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareLocation('abc-corporation')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeLocation('abc-corporation')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-building me-2 text-primary"></i>
                                                <div>
                                                    <h6 class="mb-0">XYZ Inc Office</h6>
                                                    <div class="small text-muted">456 Corporate Blvd, New York, NY 10002</div>
                                                    <span class="badge bg-primary">Client</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewLocation('xyz-inc')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="getDirections('xyz-inc')"><i class="bi bi-signpost-2"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareLocation('xyz-inc')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeLocation('xyz-inc')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-building me-2 text-secondary"></i>
                                                <div>
                                                    <h6 class="mb-0">Tech Conference Center</h6>
                                                    <div class="small text-muted">789 Innovation Way, New York, NY 10003</div>
                                                    <span class="badge bg-secondary">Event</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewLocation('tech-conference')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="getDirections('tech-conference')"><i class="bi bi-signpost-2"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareLocation('tech-conference')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeLocation('tech-conference')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://maps.google.com" target="_blank" class="btn btn-primary">Open in Google Maps</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Contacts Modal -->
    <div class="modal fade" id="contactsModal" tabindex="-1" aria-labelledby="contactsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contactsModalLabel">Google Contacts</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-3">
                            <button class="btn btn-primary" id="add-new-contact">
                                <i class="bi bi-person-plus me-2"></i>Add Contact
                            </button>
                            <div class="input-group" style="max-width: 300px;">
                                <input type="text" class="form-control" placeholder="Search contacts" aria-label="Search contacts">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex align-items-center">
                                    <div class="me-3" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">JD</div>
                                    <div>
                                        <h6 class="mb-0">John Davis</h6>
                                        <small class="text-muted">ABC Corporation - CEO</small>
                                        <div class="small"><EMAIL> | (555) 123-4567</div>
                                    </div>
                                </div>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex align-items-center">
                                    <div class="me-3" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">SM</div>
                                    <div>
                                        <h6 class="mb-0">Sarah Miller</h6>
                                        <small class="text-muted">XYZ Inc - CTO</small>
                                        <div class="small"><EMAIL> | (555) 987-6543</div>
                                    </div>
                                </div>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex align-items-center">
                                    <div class="me-3" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">AC</div>
                                    <div>
                                        <h6 class="mb-0">Alex Chen</h6>
                                        <small class="text-muted">Tech Innovations - Director</small>
                                        <div class="small"><EMAIL> | (555) 456-7890</div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://contacts.google.com" target="_blank" class="btn btn-primary">Open in Contacts</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Slack Modal -->
    <div class="modal fade" id="slackModal" tabindex="-1" aria-labelledby="slackModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="slackModalLabel">Slack</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-3">
                            <button class="btn btn-primary" id="new-slack-message">
                                <i class="bi bi-chat-text me-2"></i>New Message
                            </button>
                            <button class="btn btn-outline-secondary" id="refresh-slack">
                                <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                            </button>
                        </div>
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">#sales-team</h6>
                                    <small>5 minutes ago</small>
                                </div>
                                <p class="mb-1">John Davis: Just closed the deal with ABC Corporation! $250K annual contract.</p>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">#customer-support</h6>
                                    <small>30 minutes ago</small>
                                </div>
                                <p class="mb-1">Sarah Miller: XYZ Inc needs help with their integration. Can someone assist?</p>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">#marketing</h6>
                                    <small>2 hours ago</small>
                                </div>
                                <p class="mb-1">Alex Chen: New campaign materials are ready for review in the shared folder.</p>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://slack.com" target="_blank" class="btn btn-primary">Open in Slack</a>
                </div>
            </div>
        </div>
    </div>



    <!-- Attachments Modal -->
    <div class="modal fade" id="attachmentsModal" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="attachmentsModalLabel"><i class="bi bi-paperclip"></i> Attachments</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <ul class="nav nav-tabs border-0" id="attachmentsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content" type="button">Files</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-tab" data-bs-toggle="tab" data-bs-target="#shared-content" type="button">Shared with me</button>
                        </li>
                    </ul>

                    <div class="tab-content p-3" id="attachmentsTabContent">
                        <!-- Files Tab -->
                        <div class="tab-pane fade show active" id="files-content" role="tabpanel" aria-labelledby="files-tab">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" class="form-control form-control-sm" placeholder="Search files..." id="attachment-search">
                                    <button class="btn btn-outline-secondary btn-sm" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-sort-down"></i> Sort by
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                                        <li><a class="dropdown-item" href="#">Name</a></li>
                                        <li><a class="dropdown-item" href="#">Type</a></li>
                                        <li><a class="dropdown-item" href="#">Size</a></li>
                                        <li><a class="dropdown-item" href="#">Modified</a></li>
                                    </ul>
                                </div>
                            </div>

                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Size</th>
                                        <th>Modified</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="attachments-list">
                                    <tr data-filename="Project_Proposal_2025.pdf">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-file-earmark-pdf text-danger me-2 fs-5"></i>
                                                <span>Project_Proposal_2025.pdf</span>
                                            </div>
                                        </td>
                                        <td>PDF Document</td>
                                        <td>3.2 MB</td>
                                        <td>2025-04-15</td>
                                        <td>
                                            <div class="d-flex">
                                                <button type="button" class="btn btn-sm btn-link" onclick="viewAttachment('pdf', 'Project_Proposal_2025.pdf')"><i class="bi bi-eye"></i></button>
                                                <button type="button" class="btn btn-sm btn-link" onclick="downloadFile('Project_Proposal_2025.pdf'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                <button type="button" class="btn btn-sm btn-link" onclick="shareFile('Project_Proposal_2025.pdf'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                                <button type="button" class="btn btn-sm btn-link text-danger" onclick="deleteAttachment('Project_Proposal_2025.pdf'); event.stopPropagation();"><i class="bi bi-trash"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr data-filename="Project_Timeline_2025.xlsx">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-file-earmark-excel text-success me-2 fs-5"></i>
                                                <span>Project_Timeline_2025.xlsx</span>
                                            </div>
                                        </td>
                                            <td>1.8 MB</td>
                                            <td>2 weeks ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Customer_Feedback_Analysis.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Customer_Feedback_Analysis.xlsx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Customer_Feedback_Analysis.xlsx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Customer_Feedback_Analysis.xlsx'); event.stopPropagation();"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr data-filename="Product_Demo_Screenshots.jpg">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-image text-primary me-2 fs-5"></i>
                                                    <span>Product_Demo_Screenshots.jpg</span>
                                                </div>
                                            </td>
                                            <td>3.2 MB</td>
                                            <td>1 month ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('image', 'Product_Demo_Screenshots.jpg')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Product_Demo_Screenshots.jpg'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Product_Demo_Screenshots.jpg'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Product_Demo_Screenshots.jpg'); event.stopPropagation();"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr data-filename="Sales_Proposal_Template.docx">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Sales_Proposal_Template.docx</span>
                                                </div>
                                            </td>
                                            <td>1.5 MB</td>
                                            <td>2 months ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Sales_Proposal_Template.docx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Sales_Proposal_Template.docx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Sales_Proposal_Template.docx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Sales_Proposal_Template.docx'); event.stopPropagation();"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Upload Attachment Tab -->
                        <div class="tab-pane fade" id="upload-attachment-content" role="tabpanel" aria-labelledby="upload-attachment-tab">
                            <form id="upload-attachment-form">
                                <div class="mb-3">
                                    <label for="attachment-file" class="form-label">Select file to upload</label>
                                    <input class="form-control" type="file" id="attachment-file">
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-name" class="form-label">File Name (optional)</label>
                                    <input type="text" class="form-control" id="attachment-name" placeholder="Leave blank to use original filename">
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-description" class="form-label">Description (optional)</label>
                                    <textarea class="form-control" id="attachment-description" rows="3" placeholder="Add a description for this file"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-category" class="form-label">Category</label>
                                    <select class="form-select" id="attachment-category">
                                        <option value="contract">Contract</option>
                                        <option value="proposal">Proposal</option>
                                        <option value="invoice">Invoice</option>
                                        <option value="report">Report</option>
                                        <option value="presentation">Presentation</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-tags" class="form-label">Tags (comma separated)</label>
                                    <input type="text" class="form-control" id="attachment-tags" placeholder="e.g. client, important, draft">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="attachment-share-team">
                                        <label class="form-check-label" for="attachment-share-team">
                                            Share with team
                                        </label>
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="upload-attachment-btn">
                                        <i class="bi bi-cloud-upload me-2"></i>Upload File
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Recent Tab -->
                        <div class="tab-pane fade" id="recent-content" role="tabpanel" aria-labelledby="recent-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <h6 class="mb-0">Recently Uploaded</h6>
                                <button class="btn btn-sm btn-outline-secondary" id="refresh-recent">
                                    <i class="bi bi-arrow-clockwise"></i> Refresh
                                </button>
                            </div>
                            <div class="list-group">
                                <div class="list-group-item" data-filename="ABC_Corporation_Contract.pdf">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">ABC_Corporation_Contract.pdf</h6>
                                                <small>2.4 MB - Uploaded: Yesterday</small>
                                                <div>
                                                    <span class="badge bg-primary">Contract</span>
                                                    <span class="badge bg-secondary">Client</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'ABC_Corporation_Contract.pdf')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('ABC_Corporation_Contract.pdf'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('ABC_Corporation_Contract.pdf'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('ABC_Corporation_Contract.pdf'); event.stopPropagation();"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item" data-filename="Customer_Feedback_Analysis.xlsx">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Customer_Feedback_Analysis.xlsx</h6>
                                                <small>1.8 MB - Uploaded: 2 weeks ago</small>
                                                <div>
                                                    <span class="badge bg-primary">Report</span>
                                                    <span class="badge bg-secondary">Feedback</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Customer_Feedback_Analysis.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Customer_Feedback_Analysis.xlsx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Customer_Feedback_Analysis.xlsx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Customer_Feedback_Analysis.xlsx'); event.stopPropagation();"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between mt-3">
                                <h6 class="mb-0">Recently Viewed</h6>
                            </div>
                            <div class="list-group mt-2">
                                <div class="list-group-item" data-filename="Sales_Proposal_Template.docx">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Sales_Proposal_Template.docx</h6>
                                                <small>1.5 MB - Viewed: Today</small>
                                                <div>
                                                    <span class="badge bg-primary">Proposal</span>
                                                    <span class="badge bg-secondary">Template</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Sales_Proposal_Template.docx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Sales_Proposal_Template.docx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Sales_Proposal_Template.docx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Sales_Proposal_Template.docx'); event.stopPropagation();"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- File Viewer Modal is defined earlier in the document -->
        </div>
    </div>

    <!-- Include at the end of body, before other scripts -->
    <script src="/shared/js/isa-modal-manager.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Global utility for opening modals
        const ISAUIManager = {
            openModal: function(modalId) {
                console.log(`ISAUIManager: Opening modal ${modalId}`);

                try {
                    // Check if Bootstrap is available
                    if (typeof bootstrap === 'undefined') {
                        console.error("Bootstrap not found. Make sure bootstrap.bundle.min.js is loaded.");
                        alert("UI Framework initialization error. Please refresh the page.");
                        return false;
                    }

                    // Find modal element
                    const modalElement = document.getElementById(modalId);
                    if (!modalElement) {
                        console.error(`Modal with ID ${modalId} not found in DOM`);
                        return false;
                    }

                    // Close any open modals first
                    document.querySelectorAll('.modal.show').forEach(modal => {
                        const instance = bootstrap.Modal.getInstance(modal);
                        if (instance) {
                            console.log(`Closing open modal: ${modal.id}`);
                            instance.hide();
                        }
                    });

                    // Remove any stray backdrops
                    document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());

                    // Reset body state
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';

                    // Create new modal instance
                    const modal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static',
                        keyboard: false,
                        focus: true
                    });

                    // Add events for debugging
                    modalElement.addEventListener('shown.bs.modal', function() {
                        console.log(`Modal ${modalId} shown successfully`);
                    });

                    modalElement.addEventListener('show.bs.modal', function() {
                        console.log(`Modal ${modalId} show event triggered`);
                    });

                    modalElement.addEventListener('hide.bs.modal', function() {
                        console.log(`Modal ${modalId} hide event triggered`);
                    });

                    // Show the modal
                    modal.show();

                    // Double-check modal is displayed (forcing if needed)
                    setTimeout(() => {
                        if (!modalElement.classList.contains('show')) {
                            console.warn(`Modal ${modalId} not showing properly, forcing display`);
                            modalElement.classList.add('show');
                            modalElement.style.display = 'block';
                            document.body.classList.add('modal-open');

                            // Create backdrop manually if needed
                            if (!document.querySelector('.modal-backdrop')) {
                                const backdrop = document.createElement('div');
                                backdrop.className = 'modal-backdrop fade show';
                                document.body.appendChild(backdrop);
                            }
                        }
                    }, 300);

                    return true;
                } catch (error) {
                    console.error(`Error opening modal ${modalId}:`, error);
                    return false;
                }
            },

            closeModal: function(modalId) {
                try {
                    const modalElement = document.getElementById(modalId);
                    if (!modalElement) return false;

                    const instance = bootstrap.Modal.getInstance(modalElement);
                    if (instance) {
                        instance.hide();
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.error(`Error closing modal ${modalId}:`, error);
                    return false;
                }
            }
        };

        // Debug function to check if a modal can be opened
        function testModal(modalId) {
            console.log(`Testing modal: ${modalId}`);

            const modalElement = document.getElementById(modalId);
            if (!modalElement) {
                console.error(`Modal element with ID ${modalId} not found`);
                return false;
            }

            try {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                console.log(`Successfully opened modal: ${modalId}`);
                return true;
            } catch (error) {
                console.error(`Error opening modal ${modalId}:`, error);
                return false;
            }
        }

        // Initialize all modals on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM fully loaded - initializing modals");

            try {
                // First, make sure we have the Bootstrap object
                if (typeof bootstrap === 'undefined') {
                    console.error("Bootstrap is not defined. Make sure bootstrap.bundle.min.js is loaded.");
                    return;
                }

                // Debug modal structure
                const allModals = document.querySelectorAll('.modal');
                console.log(`Found ${allModals.length} modals in the DOM`);
                allModals.forEach(modal => {
                    console.log(`Modal ID: ${modal.id}, Classes: ${modal.className}`);
            // Initialize ISA Suite Enhancements
            if (typeof ISASuiteEnhancements === 'function') {
                const enhancements = new ISASuiteEnhancements({
                    appName: 'CRM',
                    appPrefix: 'crm',
                    appColor: '#ff9800',
                    enableNotifications: true,
                    enableAnalytics: true,
                    enableExport: true,
                    enableSearch: true,
                    debug: true
                });
                enhancements.init();
                console.log('ISA Suite Enhancements initialized for CRM application');
                
                // Welcome notification removed to prevent spam
            } else {
                console.warn('ISA Suite Enhancements not available');
            }
                });

                // Initialize all modals explicitly
                allModals.forEach(modalElement => {
                    try {
                        console.log(`Initializing modal: ${modalElement.id}`);
                        new bootstrap.Modal(modalElement);
                    } catch (modalError) {
                        console.error(`Error initializing modal ${modalElement.id}:`, modalError);
                    }
                });

                // Ensure all modals with triggers are properly initialized
                const modalTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="modal"]'));
                console.log(`Found ${modalTriggerList.length} modal triggers`);

                modalTriggerList.map(function(modalTriggerEl) {
                    const targetId = modalTriggerEl.getAttribute('data-bs-target');
                    console.log(`Initializing modal for trigger targeting: ${targetId}`);
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        return new bootstrap.Modal(targetElement);
                    } else {
                        console.error(`Modal target not found: ${targetId}`);
                    }
                });

                // Add global click handlers for modal buttons that might not use data attributes
                document.addEventListener('click', function(event) {
                    // Handle attachments button
                    if (event.target.matches('[onclick*="openAttachmentsModal"]') ||
                        event.target.parentElement.matches('[onclick*="openAttachmentsModal"]')) {
                        event.preventDefault();
                        openAttachmentsModal();
                    }

                    // Handle maps button
                    if (event.target.matches('[onclick*="openMapsModal"]') ||
                        event.target.parentElement.matches('[onclick*="openMapsModal"]')) {
                        event.preventDefault();
                        openMapsModal();
                    }
                });
            } catch (error) {
                console.error("Error during modal initialization:", error);
            }

            // Specifically ensure the attachments modal is initialized and visible
            const attachmentsModal = document.getElementById('attachmentsModal');
            if (attachmentsModal) {
                console.log("Attachments modal found, initializing...");
                new bootstrap.Modal(attachmentsModal);

                // Add event listeners to debug modal events
                attachmentsModal.addEventListener('show.bs.modal', function() {
                    console.log('Attachments modal show event fired');
                });

                attachmentsModal.addEventListener('shown.bs.modal', function() {
                    console.log('Attachments modal shown event fired');
                });

                attachmentsModal.addEventListener('hide.bs.modal', function() {
                    console.log('Attachments modal hide event fired');
                });

                attachmentsModal.addEventListener('hidden.bs.modal', function() {
                    console.log('Attachments modal hidden event fired');
                });
            } else {
                console.error("Attachments modal not found in DOM");
            }

            // Specifically ensure the maps modal is initialized and visible
            const mapsModal = document.getElementById('mapsModal');
            if (mapsModal) {
                console.log("Maps modal found, initializing...");
                new bootstrap.Modal(mapsModal);

                // Add event listeners to debug modal events
                mapsModal.addEventListener('show.bs.modal', function() {
                    console.log('Maps modal show event fired');
                });

                mapsModal.addEventListener('shown.bs.modal', function() {
                    console.log('Maps modal shown event fired');
                });

                mapsModal.addEventListener('hide.bs.modal', function() {
                    console.log('Maps modal hide event fired');
                });

                mapsModal.addEventListener('hidden.bs.modal', function() {
                    console.log('Maps modal hidden event fired');
                });
            } else {
                console.error("Maps modal not found in DOM");
            }
        });

        // Function to explicitly open the attachments modal - Updated version
        function openAttachmentsModal() {
            return ISAUIManager.openModal('attachmentsModal');
        }

        // Function to explicitly open the maps modal - Updated version
        function openMapsModal() {
            return ISAUIManager.openModal('mapsModal');
        }

        // Initialize the application properly
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM fully loaded - initializing ISA CRM application");

            // Create global modal mount point if it doesn't exist
            if (!document.getElementById('modal-mount-point')) {
                const mountPoint = document.createElement('div');
                mountPoint.id = 'modal-mount-point';
                document.body.appendChild(mountPoint);
            }

            // Ensure all system modals are properly mounted
            const modalMountPoint = document.getElementById('modal-mount-point');
            document.querySelectorAll('.modal').forEach(modal => {
                console.log(`Processing modal: ${modal.id}`);
                // Move modal to mount point to avoid z-index issues
                modalMountPoint.appendChild(modal);
            });

            // Set up system event handlers
            document.addEventListener('click', function(event) {
                // Handle attachments links
                if (event.target.matches('[data-isa-action="open-attachments"]') ||
                    (event.target.parentElement && event.target.parentElement.matches('[data-isa-action="open-attachments"]'))) {
                    event.preventDefault();
                    openAttachmentsModal();
                }

                // Handle maps links
                if (event.target.matches('[data-isa-action="open-maps"]') ||
                    (event.target.parentElement && event.target.parentElement.matches('[data-isa-action="open-maps"]'))) {
                    event.preventDefault();
                    openMapsModal();
                }
            });

            // Initialize Attachments functionality
            AttachmentsManager.init();
        });

        // Standardized Google Integration Functions
        // Note: These functions are defined later in the file

        // Get the appropriate icon for a file type
        function getFileTypeIcon(type) {
            switch(type) {
                case 'pdf':
                    return 'bi-file-earmark-pdf';
                case 'document':
                    return 'bi-file-earmark-word';
                case 'spreadsheet':
                    return 'bi-file-earmark-excel';
                case 'image':
                    return 'bi-file-earmark-image';
                case 'presentation':
                    return 'bi-file-earmark-slides';
                default:
                    return 'bi-file-earmark';
            }
        }

        // Get the appropriate icon for a Google app and file type
        function getFileIcon(app, type) {
            if (app === 'drive') {
                return 'bi-google';
            } else if (app === 'docs') {
                return 'bi-file-earmark-word';
            } else if (app === 'sheets') {
                return 'bi-file-earmark-excel';
            } else if (app === 'slides') {
                return 'bi-file-earmark-slides';
            } else if (app === 'attachments') {
                return getFileTypeIcon(type);
            } else {
                return 'bi-file-earmark';
            }
        }

        // Get content for file viewer based on app and type
        function getFileContent(app, type, itemId) {
            // Format the item ID to make it more readable
            const readableId = itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            if (app === 'drive') {
                return `<div class="text-center">
                    <i class="bi bi-google text-primary" style="font-size: 48px;"></i>
                    <h4 class="mt-3">${readableId}</h4>
                    <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                        <h5>File Preview</h5>
                        <p>This is a preview of the Google Drive file. The actual content would be displayed here in a real application.</p>
                    </div>
                </div>`;
            } else if (app === 'docs') {
                return `<div class="border p-3" style="background-color: #f8f9fa;">
                    <h4>${readableId}</h4>
                    <hr>
                    <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                    <p>The document contains information about team roles and responsibilities.</p>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                </div>`;
            } else if (app === 'sheets') {
                return `<div class="border p-3" style="background-color: #f8f9fa;">
                    <h4>${readableId}</h4>
                    <hr>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Acme Corporation</td>
                                    <td>Active</td>
                                    <td>2025-05-01</td>
                                    <td>$15,000</td>
                                </tr>
                                <tr>
                                    <td>Global Industries</td>
                                    <td>Pending</td>
                                    <td>2025-05-16</td>
                                    <td>$22,500</td>
                                </tr>
                                <tr>
                                    <td>Tech Solutions</td>
                                    <td>Completed</td>
                                    <td>2025-05-10</td>
                                    <td>$8,750</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>`;
            } else {
                return `<div class="alert alert-info">No preview available for this content type.</div>`;
            }
        }

        function getAttachmentContent(type, fileName) {
            if (type === 'pdf') {
                return `<div class="text-center">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                    <h4 class="mt-3">${fileName}</h4>
                    <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                        <h5>Document Preview</h5>
                        <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                    </div>
                </div>`;
            } else if (type === 'document') {
                return `<div class="border p-3" style="background-color: #f8f9fa;">
                    <h4>${fileName}</h4>
                    <hr>
                    <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                    <p>The document contains information related to customer relationship management.</p>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                </div>`;
            } else if (type === 'spreadsheet') {
                return `<div class="border p-3" style="background-color: #f8f9fa;">
                    <h4>${fileName}</h4>
                    <hr>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Acme Corporation</td>
                                    <td>Active</td>
                                    <td>2025-05-01</td>
                                    <td>$15,000</td>
                                </tr>
                                <tr>
                                    <td>Global Industries</td>
                                    <td>Pending</td>
                                    <td>2025-05-16</td>
                                    <td>$22,500</td>
                                </tr>
                                <tr>
                                    <td>Tech Solutions</td>
                                    <td>Completed</td>
                                    <td>2025-05-10</td>
                                    <td>$8,750</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>`;
            } else if (type === 'image') {
                return `<div class="text-center">
                    <h4 class="mt-3">${fileName}</h4>
                    <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                        <img src="https://via.placeholder.com/800x600.png?text=${fileName}" class="img-fluid" alt="${fileName}">
                    </div>
                </div>`;
            } else {
                return `<div class="alert alert-info">No preview available for this file type.</div>`;
            }
        }

        // This function is no longer needed as we've consolidated the functionality
        // into a single openGoogleItem function at line ~5036
        // Removing this to avoid duplicate function definitions

        // Enhanced viewAttachment function using the new modal system
        function viewAttachment(type, fileName) {
            console.log(`Enhanced view for: ${fileName} of type ${type}`);

            // Determine file type for the enhanced system
            let fileType = type;
            if (type === 'pdf') fileType = 'pdf';
            else if (type === 'spreadsheet') fileType = 'spreadsheet';
            else if (type === 'document') fileType = 'document';
            else if (type === 'image') fileType = 'image';
            else fileType = 'file';

            // Call the enhanced preview modal from google-drive-actions.js
            if (typeof showFilePreviewModal === 'function') {
                showFilePreviewModal(fileName, fileType);
            } else {
                // Fallback to old system if enhanced system not loaded
                console.log('Enhanced system not loaded, using fallback');
                try {
                    // Always close all other modals before opening the file viewer
                    document.querySelectorAll('.modal.show').forEach(m => {
                        if (m.id !== 'fileViewerModal') {
                            console.log(`Closing modal: ${m.id}`);
                            const modalInstance = bootstrap.Modal.getInstance(m);
                            if (modalInstance) {
                                modalInstance.hide();
                            } else {
                                // Force hide if no instance exists
                                m.classList.remove('show');
                                m.style.display = 'none';
                                document.body.classList.remove('modal-open');
                                const backdrops = document.querySelectorAll('.modal-backdrop');
                                backdrops.forEach(backdrop => backdrop.remove());
                            }
                        }
                    });

                    // Get the file viewer modal
                    let fileViewerModal = document.getElementById('fileViewerModal');
                    if (!fileViewerModal) {
                        console.error('File viewer modal not found in the DOM');
                        alert('File viewer is not available. Please try again later.');
                        return;
                    }

                    // Update modal content
                    const fileTitle = document.getElementById('fileViewerTitle');
                    const fileContent = document.getElementById('fileViewerContent');
                    if (!fileTitle || !fileContent) {
                        console.error('File viewer components not found');
                        alert('File viewer components are missing. Please try again later.');
                        return;
                    }

                    fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;
                    fileContent.innerHTML = getAttachmentContent(type, fileName);

                    // Store current file info
                    window.currentFile = {
                        app: 'attachments',
                    type: type,
                    id: fileName.toLowerCase().replace(/\s+/g, '-'),
                    name: fileName
                    };

                    // Show modal using Bootstrap API
                    const modal = new bootstrap.Modal(fileViewerModal);
                    modal.show();
                    console.log("File viewer modal should now be visible");

                } catch (error) {
                    console.error('Error displaying attachment:', error);
                    alert(`Could not display ${fileName}. Error: ${error.message}`);
                }
            }
        }

        // Get the file viewer modal or return null if it doesn't exist
        function createFileViewerModal() {
            // Check if the modal already exists
            let modal = document.getElementById('fileViewerModal');
            if (modal) {
                return modal;
            }

            // If the modal doesn't exist, create it
            console.error('File viewer modal not found in the DOM. It should be defined in the HTML.');
            return null;
        }

        // Note: These functions are duplicated later in the file.
        // We'll keep the implementations at line ~4935 and remove these to avoid duplication.

        // Initialize the page
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize sidebar toggle functionality
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebarMenu');

            // Calendar modal sidebar link logic
            const calendarModal = document.getElementById('calendarModal');
            if (calendarModal) {
                const viewLinks = [
                    { id: 'month-view-link', view: 'month' },
                    { id: 'week-view-link', view: 'week' },
                    { id: 'day-view-link', view: 'day' },
                    { id: 'agenda-view-link', view: 'agenda' }
                ];
                viewLinks.forEach(({ id, view }) => {
                    const link = document.getElementById(id);
                    if (link) {
                        link.addEventListener('click', function(e) {
                            e.preventDefault();
                            // Remove active from all links
                            viewLinks.forEach(({ id }) => {
                                const l = document.getElementById(id);
                                if (l) l.classList.remove('active');
                            });
                            // Set active on clicked link
                            link.classList.add('active');
                            // Show the correct calendar view (for demo, just update the main view area)
                            const mainView = document.getElementById('calendar-main-view');
                            if (mainView) {
                                mainView.innerHTML = `<div class='calendar-container'><div class='alert alert-info mb-0'>${view.charAt(0).toUpperCase() + view.slice(1)} view (demo placeholder)</div></div>`;
                            }
                        });
                    }
                });
            }

            // Gmail modal sidebar link/tab logic
            const gmailModal = document.getElementById('gmailModal');
            if (gmailModal) {
                const tabMap = {
                    'inbox-link': 'inbox-tab',
                    'starred-link': 'inbox-tab', // For demo, use inbox-tab for starred
                    'sent-link': 'sent-tab',
                    'drafts-link': 'inbox-tab', // For demo, use inbox-tab for drafts
                    'important-link': 'inbox-tab', // For demo, use inbox-tab for important
                };
                Object.keys(tabMap).forEach(linkId => {
                    const link = document.getElementById(linkId);
                    if (link) {
                        link.addEventListener('click', function(e) {
                            e.preventDefault();
                            const tabId = tabMap[linkId];
                            if (tabId) {
                                const tab = document.getElementById(tabId);
                                if (tab) {
                                    new bootstrap.Tab(tab).show();
                                }
                            }
                        });
                    }
                });
                // Email list click logic
                const emailListSection = document.getElementById('email-list-section');
                if (emailListSection) {
                    emailListSection.querySelectorAll('.list-group-item').forEach(item => {
                        item.addEventListener('click', function(e) {
                            e.preventDefault();

                            // Extract sender and subject from the clicked item
                            const sender = this.getAttribute('data-sender') ||
                                          this.querySelector('.fw-bold')?.textContent.toLowerCase().replace(/\s+/g, '-') ||
                                          'unknown-sender';

                            const subject = this.getAttribute('data-subject') ||
                                           this.querySelector('p.mb-1')?.textContent ||
                                           'No Subject';

                            // Call the openEmail function with the extracted sender and subject
                            openEmail(sender, subject);
                        });
                    });
                }

                // Initialize Gmail tabs
                const gmailTabs = document.getElementById('gmailTab');
                if (gmailTabs) {
                    const tabList = [].slice.call(gmailTabs.querySelectorAll('button[data-bs-toggle="tab"]'));
                    tabList.forEach(function (tabEl) {
                        tabEl.addEventListener('shown.bs.tab', function (event) {
                            // Handle tab change events if needed
                            const activeTab = event.target.getAttribute('id');
                            console.log('Active Gmail tab:', activeTab);

                            // Update sidebar active state
                            if (activeTab === 'inbox-tab') {
                                document.getElementById('inbox-link').classList.add('active');
                                document.getElementById('sent-link').classList.remove('active');
                            } else if (activeTab === 'sent-tab') {
                                document.getElementById('sent-link').classList.add('active');
                                document.getElementById('inbox-link').classList.remove('active');
                            }
                        });
                    });
                }
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    sidebar.classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 768 && sidebar) {
                    const isClickInsideSidebar = sidebar.contains(event.target);
                    const isClickOnToggle = sidebarToggle && sidebarToggle.contains(event.target);

                    if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768 && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });

            // Fetch dashboard data from API
            fetch('/api/dashboard')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const dashboardData = data.data;

                        // Populate upcoming deals
                        populateDeals(dashboardData.upcomingDeals);

                        // Populate customer table
                        populateCustomerTable(dashboardData.recentCustomers);

                        // Populate activities list
                        populateActivitiesList(dashboardData.activities);

                        // Create sales chart
                        createSalesChart(dashboardData.salesTrends);

                        // Create lead sources chart
                        createLeadSourcesChart(dashboardData.leadSources);
                    }
                })
                .catch(error => {
                    console.error('Error fetching dashboard data:', error);
                });

            // Fetch AI insights
            fetchAIInsights();

            // Add event listener for refresh insights button
            document.getElementById('refresh-insights-btn').addEventListener('click', fetchAIInsights);

            // Add event listeners for expand/collapse all insights
            document.getElementById('expand-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.show();
                });
            });

            document.getElementById('collapse-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.hide();
                });
            });

            // Add event listener for export insights button
            document.getElementById('export-insights').addEventListener('click', function() {
                if (!window.aiInsightsData) return;

                const dataStr = JSON.stringify(window.aiInsightsData, null, 2);
                const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

                const exportFileDefaultName = 'customer-insights-' + new Date().toISOString().split('T')[0] + '.json';

                const linkElement = document.createElement('a');
                linkElement.setAttribute('href', dataUri);
                linkElement.setAttribute('download', exportFileDefaultName);
                linkElement.click();
            });
        });

        // Populate upcoming deals
        function populateDeals(deals) {
            const dealsContainer = document.getElementById('dealCards');
            dealsContainer.innerHTML = '';

            deals.forEach(deal => {
                const dealCard = document.createElement('div');
                dealCard.className = 'col-md-4';

                // Calculate probability class
                let probabilityClass = 'bg-success';
                if (deal.probability < 50) {
                    probabilityClass = 'bg-danger';
                } else if (deal.probability < 75) {
                    probabilityClass = 'bg-warning';
                }

                dealCard.innerHTML = `
                    <div class="deal-card">
                        <div class="deal-header">
                            <h5>${deal.description}</h5>
                            <p class="mb-0">${deal.customer}</p>
                        </div>
                        <div class="deal-body">
                            <p><strong>Value:</strong> $${deal.value.toLocaleString()}</p>
                            <p><strong>Expected Close:</strong> ${deal.expectedCloseDate}</p>
                            <p><strong>Probability:</strong> ${deal.probability}%</p>
                            <div class="progress">
                                <div class="progress-bar ${probabilityClass}" role="progressbar" style="width: ${deal.probability}%"
                                    aria-valuenow="${deal.probability}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-primary btn-sm">View Details</button>
                                <button class="btn btn-outline-secondary btn-sm">Edit</button>
                            </div>
                        </div>
                    </div>
                `;

                dealsContainer.appendChild(dealCard);
            });
        }

        // Populate customer table
        function populateCustomerTable(customers) {
            const tableBody = document.getElementById('customerTableBody');
            tableBody.innerHTML = '';

            customers.forEach(customer => {
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${customer.id}</td>
                    <td>${customer.name}</td>
                    <td>${customer.contact}</td>
                    <td>${customer.email}</td>
                    <td>${customer.phone}</td>
                    <td><span class="badge bg-${customer.status === 'Active' ? 'success' : 'secondary'}">${customer.status}</span></td>
                    <td>${customer.lastContact}</td>
                `;

                tableBody.appendChild(row);
            });
        }

        // Populate activities list
        function populateActivitiesList(activities) {
            const activitiesContainer = document.getElementById('activitiesList');
            activitiesContainer.innerHTML = '';

            activities.forEach(activity => {
                const activityItem = document.createElement('div');
                activityItem.className = `activity-item ${activity.type.toLowerCase()}`;

                activityItem.innerHTML = `
                    <h5>${activity.type} with ${activity.customer}</h5>
                    <p><strong>Contact:</strong> ${activity.contact}</p>
                    <p><strong>Date:</strong> ${activity.date}</p>
                    <p><strong>Notes:</strong> ${activity.notes}</p>
                `;

                activitiesContainer.appendChild(activityItem);
            });
        }

        // Create sales chart
        function createSalesChart(salesData) {
            const ctx = document.getElementById('salesChart').getContext('2d');

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: salesData.labels,
                    datasets: [{
                        label: 'Sales ($)',
                        data: salesData.data,
                        borderColor: 'rgba(52, 152, 219, 1)',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create lead sources chart
        function createLeadSourcesChart(leadSourcesData) {
            const ctx = document.getElementById('leadSourcesChart').getContext('2d');

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: leadSourcesData.labels,
                    datasets: [{
                        data: leadSourcesData.data,
                        backgroundColor: [
                            'rgba(52, 152, 219, 0.7)',
                            'rgba(46, 204, 113, 0.7)',
                            'rgba(155, 89, 182, 0.7)',
                            'rgba(241, 196, 15, 0.7)',
                            'rgba(230, 126, 34, 0.7)',
                            'rgba(149, 165, 166, 0.7)'
                        ],
                        borderColor: [
                            'rgba(52, 152, 219, 1)',
                            'rgba(46, 204, 113, 1)',
                            'rgba(155, 89, 182, 1)',
                            'rgba(241, 196, 15, 1)',
                            'rgba(230, 126, 34, 1)',
                            'rgba(149, 165, 166, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Function to fetch AI insights from the Integration Hub
        function fetchAIInsights() {
            const insightsContainer = document.getElementById('ai-insights-container');
            insightsContainer.innerHTML = '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

            fetch('http://localhost:8000/api/ai-analytics/customer-insights')
                .then(response => response.json())
                .then(data => {
                    console.log('AI insights:', data);

                    // Store insights data globally for export
                    window.aiInsightsData = data;

                    // Clear the container
                    insightsContainer.innerHTML = '';

                    // Generate sample insights if the API doesn't return the expected data structure
                    if (!data.insights) {
                        generateSampleInsights();
                        return;
                    }

                    // Create insights sections
                    createCustomerSegmentationSection(data, insightsContainer);
                    createCustomerJourneySection(data, insightsContainer);
                    createSentimentAnalysisSection(data, insightsContainer);
                    createRecommendationsSection(data, insightsContainer);

                    // Add event listeners for interactive elements
                    addInteractiveEventListeners(data);
                })
                .catch(error => {
                    console.error('Error fetching AI insights:', error);
                    generateSampleInsights();
                });
        }

        // Function to generate sample insights when the API is not available
        function generateSampleInsights() {
            const sampleData = {
                insights: [
                    {
                        type: 'customer_segmentation',
                        segments: [
                            {
                                name: 'High-Value Loyalists',
                                percentage: 15,
                                avg_revenue: 12500,
                                retention_rate: 92,
                                growth_potential: 'Medium',
                                characteristics: [
                                    'Purchase frequency: 2.3x per month',
                                    'Average order value: $850',
                                    'Product categories: Premium, Enterprise',
                                    'Customer tenure: 4.5+ years'
                                ],
                                recommendations: [
                                    'Implement VIP loyalty program with exclusive benefits',
                                    'Provide dedicated account management',
                                    'Offer early access to new products/features',
                                    'Create personalized upsell opportunities'
                                ]
                            },
                            {
                                name: 'Growth Opportunities',
                                percentage: 35,
                                avg_revenue: 5200,
                                retention_rate: 78,
                                growth_potential: 'High',
                                characteristics: [
                                    'Purchase frequency: 1.5x per month',
                                    'Average order value: $450',
                                    'Product categories: Standard, Premium',
                                    'Customer tenure: 1-3 years'
                                ],
                                recommendations: [
                                    'Develop targeted cross-sell campaigns',
                                    'Implement usage-based incentives',
                                    'Create educational content on advanced features',
                                    'Offer bundle discounts on complementary products'
                                ]
                            },
                            {
                                name: 'At-Risk Customers',
                                percentage: 22,
                                avg_revenue: 3800,
                                retention_rate: 45,
                                growth_potential: 'Low',
                                characteristics: [
                                    'Purchase frequency: 0.7x per month',
                                    'Average order value: $350',
                                    'Product categories: Basic, Standard',
                                    'Customer tenure: 1-2 years',
                                    'Declining engagement patterns'
                                ],
                                recommendations: [
                                    'Implement proactive retention outreach',
                                    'Offer renewal incentives',
                                    'Conduct satisfaction surveys',
                                    'Provide product training and support'
                                ]
                            }
                        ]
                    },
                    {
                        type: 'customer_journey_analysis',
                        touchpoints: [
                            {
                                stage: 'Awareness',
                                effectiveness: 72,
                                friction_points: [
                                    'Limited brand recognition in new markets',
                                    'Inconsistent messaging across channels',
                                    'Low conversion from ad impressions to website visits'
                                ],
                                recommendations: [
                                    'Increase targeted digital advertising in key markets',
                                    'Develop consistent brand messaging guidelines',
                                    'Optimize ad creative and targeting parameters'
                                ]
                            },
                            {
                                stage: 'Consideration',
                                effectiveness: 65,
                                friction_points: [
                                    'Complex product comparison process',
                                    'Limited product information availability',
                                    'High cart abandonment rate (68%)'
                                ],
                                recommendations: [
                                    'Simplify product comparison tools',
                                    'Enhance product detail pages with comprehensive information',
                                    'Implement cart abandonment recovery campaigns'
                                ]
                            },
                            {
                                stage: 'Purchase',
                                effectiveness: 81,
                                friction_points: [
                                    'Multi-step checkout process',
                                    'Limited payment options',
                                    'Shipping cost transparency issues'
                                ],
                                recommendations: [
                                    'Streamline checkout to 3 steps or fewer',
                                    'Expand payment options to include digital wallets',
                                    'Display shipping costs earlier in the purchase process'
                                ]
                            },
                            {
                                stage: 'Retention',
                                effectiveness: 58,
                                friction_points: [
                                    'Inconsistent post-purchase communication',
                                    'Limited self-service support options',
                                    'Delayed response to service issues'
                                ],
                                recommendations: [
                                    'Develop automated post-purchase communication sequence',
                                    'Enhance knowledge base and self-service tools',
                                    'Implement service level agreements for support response'
                                ]
                            }
                        ]
                    },
                    {
                        type: 'sentiment_analysis',
                        overall_sentiment: 'Positive',
                        sentiment_score: 72,
                        sentiment_trends: {
                            '3_months_ago': 68,
                            '2_months_ago': 70,
                            '1_month_ago': 71,
                            'current': 72
                        },
                        key_topics: [
                            {
                                topic: 'Product Quality',
                                sentiment: 'Very Positive',
                                score: 85,
                                sample_feedback: 'The product consistently exceeds our expectations in terms of reliability and performance.'
                            },
                            {
                                topic: 'Ease of Use',
                                sentiment: 'Positive',
                                score: 78,
                                sample_feedback: 'The interface is intuitive and makes complex tasks simple to execute.'
                            },
                            {
                                topic: 'Customer Support',
                                sentiment: 'Neutral',
                                score: 60,
                                sample_feedback: 'Support is knowledgeable but response times could be improved.'
                            },
                            {
                                topic: 'Pricing',
                                sentiment: 'Negative',
                                score: 42,
                                sample_feedback: 'The product is excellent but priced higher than comparable alternatives.'
                            }
                        ]
                    }
                ],
                recommendations: [
                    'Implement a tiered loyalty program to increase retention of high-value customers',
                    'Streamline the checkout process to reduce cart abandonment by 25%',
                    'Develop personalized email campaigns based on customer segment and behavior',
                    'Enhance self-service support options to improve customer satisfaction',
                    'Create targeted win-back campaigns for at-risk customers'
                ],
                timestamp: new Date().toISOString()
            };

            const insightsContainer = document.getElementById('ai-insights-container');
            insightsContainer.innerHTML = '';

            // Store insights data globally for export
            window.aiInsightsData = sampleData;

            // Create insights sections
            createCustomerSegmentationSection(sampleData, insightsContainer);
            createCustomerJourneySection(sampleData, insightsContainer);
            createSentimentAnalysisSection(sampleData, insightsContainer);
            createRecommendationsSection(sampleData, insightsContainer);

            // Add event listeners for interactive elements
            addInteractiveEventListeners(sampleData);
        }

        // Function to create the Customer Segmentation section
        function createCustomerSegmentationSection(data, container) {
            const customerSegmentation = data.insights.find(insight => insight.type === 'customer_segmentation');
            if (!customerSegmentation || !customerSegmentation.segments || customerSegmentation.segments.length === 0) return;

            const section = document.createElement('div');
            section.className = 'mb-4';
            section.innerHTML = `
                <h5 class="mb-3">
                    <a href="#customerSegmentationInsights" class="text-decoration-none text-dark" data-bs-toggle="collapse">
                        <i class="bi bi-people me-2"></i>Customer Segmentation
                        <i class="bi bi-chevron-down ms-2 small"></i>
                    </a>
                </h5>
                <div class="collapse show" id="customerSegmentationInsights">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                ${customerSegmentation.segments.map(segment => `
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">${segment.name}</h6>
                                                <span class="badge bg-primary rounded-pill">${segment.percentage}%</span>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <div class="d-flex justify-content-between mb-1">
                                                        <span>Avg. Revenue:</span>
                                                        <span class="fw-bold">$${segment.avg_revenue.toLocaleString()}</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-1">
                                                        <span>Retention Rate:</span>
                                                        <span class="fw-bold">${segment.retention_rate}%</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-1">
                                                        <span>Growth Potential:</span>
                                                        <span class="fw-bold">${segment.growth_potential}</span>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <h6 class="mb-2">Characteristics:</h6>
                                                    <ul class="small">
                                                        ${segment.characteristics.map(characteristic => `
                                                            <li>${characteristic}</li>
                                                        `).join('')}
                                                    </ul>
                                                </div>
                                                <button class="btn btn-primary btn-sm view-segment-details-btn"
                                                    data-segment-name="${segment.name}">
                                                    View Strategy
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(section);
        }

        // Function to create the Customer Journey section
        function createCustomerJourneySection(data, container) {
            const customerJourney = data.insights.find(insight => insight.type === 'customer_journey_analysis');
            if (!customerJourney || !customerJourney.touchpoints || customerJourney.touchpoints.length === 0) return;

            const section = document.createElement('div');
            section.className = 'mb-4';
            section.innerHTML = `
                <h5 class="mb-3">
                    <a href="#customerJourneyInsights" class="text-decoration-none text-dark" data-bs-toggle="collapse">
                        <i class="bi bi-signpost-split me-2"></i>Customer Journey Analysis
                        <i class="bi bi-chevron-down ms-2 small"></i>
                    </a>
                </h5>
                <div class="collapse show" id="customerJourneyInsights">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Stage</th>
                                            <th>Effectiveness</th>
                                            <th>Friction Points</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${customerJourney.touchpoints.map(touchpoint => `
                                            <tr>
                                                <td>${touchpoint.stage}</td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar ${touchpoint.effectiveness < 60 ? 'bg-danger' : touchpoint.effectiveness < 75 ? 'bg-warning' : 'bg-success'}"
                                                            role="progressbar"
                                                            style="width: ${touchpoint.effectiveness}%;"
                                                            aria-valuenow="${touchpoint.effectiveness}"
                                                            aria-valuemin="0"
                                                            aria-valuemax="100">
                                                            ${touchpoint.effectiveness}%
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <ul class="mb-0 small">
                                                        ${touchpoint.friction_points.map(point => `
                                                            <li>${point}</li>
                                                        `).join('')}
                                                    </ul>
                                                </td>
                                                <td>
                                                    <button class="btn btn-primary btn-sm view-touchpoint-details-btn"
                                                        data-touchpoint-stage="${touchpoint.stage}">
                                                        Improvement Plan
                                                    </button>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(section);
        }

        // Function to create the Sentiment Analysis section
        function createSentimentAnalysisSection(data, container) {
            const sentimentAnalysis = data.insights.find(insight => insight.type === 'sentiment_analysis');
            if (!sentimentAnalysis) return;

            const section = document.createElement('div');
            section.className = 'mb-4';
            section.innerHTML = `
                <h5 class="mb-3">
                    <a href="#sentimentAnalysisInsights" class="text-decoration-none text-dark" data-bs-toggle="collapse">
                        <i class="bi bi-emoji-smile me-2"></i>Sentiment Analysis
                        <i class="bi bi-chevron-down ms-2 small"></i>
                    </a>
                </h5>
                <div class="collapse show" id="sentimentAnalysisInsights">
                    <div class="card">
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Overall Sentiment</h6>
                                        </div>
                                        <div class="card-body text-center">
                                            <div class="display-4 mb-2 ${sentimentAnalysis.sentiment_score >= 70 ? 'text-success' : sentimentAnalysis.sentiment_score >= 50 ? 'text-warning' : 'text-danger'}">
                                                ${sentimentAnalysis.overall_sentiment}
                                            </div>
                                            <div class="progress" style="height: 30px;">
                                                <div class="progress-bar ${sentimentAnalysis.sentiment_score >= 70 ? 'bg-success' : sentimentAnalysis.sentiment_score >= 50 ? 'bg-warning' : 'bg-danger'}"
                                                    role="progressbar"
                                                    style="width: ${sentimentAnalysis.sentiment_score}%;"
                                                    aria-valuenow="${sentimentAnalysis.sentiment_score}"
                                                    aria-valuemin="0"
                                                    aria-valuemax="100">
                                                    ${sentimentAnalysis.sentiment_score}%
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <span class="badge bg-success me-1">Positive: ${sentimentAnalysis.sentiment_score}%</span>
                                                <span class="badge bg-danger">Negative: ${100 - sentimentAnalysis.sentiment_score}%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-header">
                                            <h6 class="mb-0">Sentiment Trends</h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Period</th>
                                                        <th>Score</th>
                                                        <th>Change</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>3 Months Ago</td>
                                                        <td>${sentimentAnalysis.sentiment_trends['3_months_ago']}%</td>
                                                        <td>-</td>
                                                    </tr>
                                                    <tr>
                                                        <td>2 Months Ago</td>
                                                        <td>${sentimentAnalysis.sentiment_trends['2_months_ago']}%</td>
                                                        <td class="${sentimentAnalysis.sentiment_trends['2_months_ago'] > sentimentAnalysis.sentiment_trends['3_months_ago'] ? 'text-success' : 'text-danger'}">
                                                            <i class="bi ${sentimentAnalysis.sentiment_trends['2_months_ago'] > sentimentAnalysis.sentiment_trends['3_months_ago'] ? 'bi-arrow-up' : 'bi-arrow-down'}"></i>
                                                            ${Math.abs(sentimentAnalysis.sentiment_trends['2_months_ago'] - sentimentAnalysis.sentiment_trends['3_months_ago'])}%
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>1 Month Ago</td>
                                                        <td>${sentimentAnalysis.sentiment_trends['1_month_ago']}%</td>
                                                        <td class="${sentimentAnalysis.sentiment_trends['1_month_ago'] > sentimentAnalysis.sentiment_trends['2_months_ago'] ? 'text-success' : 'text-danger'}">
                                                            <i class="bi ${sentimentAnalysis.sentiment_trends['1_month_ago'] > sentimentAnalysis.sentiment_trends['2_months_ago'] ? 'bi-arrow-up' : 'bi-arrow-down'}"></i>
                                                            ${Math.abs(sentimentAnalysis.sentiment_trends['1_month_ago'] - sentimentAnalysis.sentiment_trends['2_months_ago'])}%
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>Current</td>
                                                        <td>${sentimentAnalysis.sentiment_trends['current']}%</td>
                                                        <td class="${sentimentAnalysis.sentiment_trends['current'] > sentimentAnalysis.sentiment_trends['1_month_ago'] ? 'text-success' : 'text-danger'}">
                                                            <i class="bi ${sentimentAnalysis.sentiment_trends['current'] > sentimentAnalysis.sentiment_trends['1_month_ago'] ? 'bi-arrow-up' : 'bi-arrow-down'}"></i>
                                                            ${Math.abs(sentimentAnalysis.sentiment_trends['current'] - sentimentAnalysis.sentiment_trends['1_month_ago'])}%
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <h6 class="mb-3">Key Topics</h6>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Topic</th>
                                            <th>Sentiment</th>
                                            <th>Score</th>
                                            <th>Sample Feedback</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${sentimentAnalysis.key_topics.map(topic => `
                                            <tr>
                                                <td>${topic.topic}</td>
                                                <td>
                                                    <span class="badge ${topic.sentiment === 'Very Positive' || topic.sentiment === 'Positive' ? 'bg-success' : topic.sentiment === 'Neutral' ? 'bg-secondary' : 'bg-danger'}">
                                                        ${topic.sentiment}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar ${topic.score >= 70 ? 'bg-success' : topic.score >= 50 ? 'bg-warning' : 'bg-danger'}"
                                                            role="progressbar"
                                                            style="width: ${topic.score}%;"
                                                            aria-valuenow="${topic.score}"
                                                            aria-valuemin="0"
                                                            aria-valuemax="100">
                                                            ${topic.score}%
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>${topic.sample_feedback}</td>
                                                <td>
                                                    <button class="btn btn-primary btn-sm view-topic-feedback-btn"
                                                        data-topic="${topic.topic}">
                                                        View All Feedback
                                                    </button>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(section);
        }

        // Function to create the Recommendations section
        function createRecommendationsSection(data, container) {
            if (!data.recommendations || data.recommendations.length === 0) return;

            const section = document.createElement('div');
            section.className = 'mb-4';
            section.innerHTML = `
                <h5 class="mb-3">
                    <a href="#recommendationsSection" class="text-decoration-none text-dark" data-bs-toggle="collapse">
                        <i class="bi bi-lightbulb me-2"></i>Recommendations
                        <i class="bi bi-chevron-down ms-2 small"></i>
                    </a>
                </h5>
                <div class="collapse show" id="recommendationsSection">
                    <div class="card">
                        <div class="card-body">
                            <ul class="list-group">
                                ${data.recommendations.map((recommendation, index) => `
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">${index + 1}</span>
                                            ${recommendation}
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary create-action-btn"
                                            data-recommendation-id="${index}"
                                            data-recommendation-text="${recommendation}">
                                            Create Action Plan
                                        </button>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(section);
        }

        // Function to add interactive event listeners to the AI insights
        function addInteractiveEventListeners(data) {
            // Add event listeners for view segment details buttons
            document.querySelectorAll('.view-segment-details-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const segmentName = this.dataset.segmentName;

                    // Find the segment in the data
                    let segment;
                    const customerSegmentation = data.insights.find(insight => insight.type === 'customer_segmentation');
                    if (customerSegmentation && customerSegmentation.segments) {
                        segment = customerSegmentation.segments.find(s => s.name === segmentName);
                    }

                    if (!segment) return;

                    // Create modal for segment strategy
                    const modalId = `segmentStrategyModal-${segmentName.replace(/\s+/g, '-').toLowerCase()}`;
                    const modalHtml = `
                        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="${modalId}Label">Strategy for ${segmentName}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row mb-4">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Segment Overview</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>Percentage of Customers:</strong> ${segment.percentage}%</p>
                                                        <p><strong>Average Revenue:</strong> $${segment.avg_revenue.toLocaleString()}</p>
                                                        <p><strong>Retention Rate:</strong> ${segment.retention_rate}%</p>
                                                        <p><strong>Growth Potential:</strong> ${segment.growth_potential}</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card h-100">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Key Characteristics</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <ul>
                                                            ${segment.characteristics.map(characteristic => `
                                                                <li>${characteristic}</li>
                                                            `).join('')}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card mb-4">
                                            <div class="card-header">
                                                <h6 class="mb-0">Recommended Strategies</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th>Strategy</th>
                                                                <th>Implementation</th>
                                                                <th>Expected Outcome</th>
                                                                <th>Priority</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            ${segment.recommendations.map((recommendation, index) => `
                                                                <tr>
                                                                    <td>${recommendation}</td>
                                                                    <td>${['30 days', '60 days', '90 days', '120 days'][index % 4]}</td>
                                                                    <td>${['Increased retention', 'Higher LTV', 'Improved engagement', 'Reduced churn'][index % 4]}</td>
                                                                    <td><span class="badge ${index === 0 ? 'bg-danger' : index === 1 ? 'bg-warning' : 'bg-info'}">${index === 0 ? 'High' : index === 1 ? 'Medium' : 'Low'}</span></td>
                                                                </tr>
                                                            `).join('')}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="implementationNotes" class="form-label">Implementation Notes</label>
                                            <textarea class="form-control" id="implementationNotes" rows="3" placeholder="Enter implementation details and notes..."></textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        <button type="button" class="btn btn-primary create-strategy-btn">Create Strategy Plan</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add modal to the document if it doesn't exist
                    if (!document.getElementById(modalId)) {
                        document.body.insertAdjacentHTML('beforeend', modalHtml);
                    }

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById(modalId));
                    modal.show();

                    // Add event listener for create strategy button
                    document.querySelector(`#${modalId} .create-strategy-btn`).addEventListener('click', function() {
                        const notes = document.getElementById('implementationNotes').value;
                        alert(`Strategy plan created for ${segmentName} segment.\nImplementation notes: ${notes || 'None'}`);
                        modal.hide();
                    });
                });
            });

            // Add event listeners for view touchpoint details buttons
            document.querySelectorAll('.view-touchpoint-details-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const touchpointStage = this.dataset.touchpointStage;

                    // Find the touchpoint in the data
                    let touchpoint;
                    const customerJourney = data.insights.find(insight => insight.type === 'customer_journey_analysis');
                    if (customerJourney && customerJourney.touchpoints) {
                        touchpoint = customerJourney.touchpoints.find(t => t.stage === touchpointStage);
                    }

                    if (!touchpoint) return;

                    // Create modal for touchpoint improvement plan
                    const modalId = `touchpointModal-${touchpointStage.replace(/\s+/g, '-').toLowerCase()}`;
                    const modalHtml = `
                        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="${modalId}Label">Improvement Plan for ${touchpointStage} Stage</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row mb-4">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Current Performance</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>Effectiveness:</strong> ${touchpoint.effectiveness}%</p>
                                                        <div class="progress mb-3" style="height: 20px;">
                                                            <div class="progress-bar ${touchpoint.effectiveness < 60 ? 'bg-danger' : touchpoint.effectiveness < 75 ? 'bg-warning' : 'bg-success'}"
                                                                role="progressbar"
                                                                style="width: ${touchpoint.effectiveness}%;"
                                                                aria-valuenow="${touchpoint.effectiveness}"
                                                                aria-valuemin="0"
                                                                aria-valuemax="100">
                                                                ${touchpoint.effectiveness}%
                                                            </div>
                                                        </div>
                                                        <p><strong>Target Effectiveness:</strong> ${Math.min(touchpoint.effectiveness + 15, 100)}%</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card h-100">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Friction Points</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <ul>
                                                            ${touchpoint.friction_points.map(point => `
                                                                <li>${point}</li>
                                                            `).join('')}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card mb-4">
                                            <div class="card-header">
                                                <h6 class="mb-0">Recommended Improvements</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th>Recommendation</th>
                                                                <th>Effort</th>
                                                                <th>Impact</th>
                                                                <th>Timeline</th>
                                                                <th>Status</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            ${touchpoint.recommendations.map((recommendation, index) => `
                                                                <tr>
                                                                    <td>${recommendation}</td>
                                                                    <td><span class="badge ${index === 0 ? 'bg-success' : index === 1 ? 'bg-warning' : 'bg-danger'}">${index === 0 ? 'Low' : index === 1 ? 'Medium' : 'High'}</span></td>
                                                                    <td><span class="badge ${index === 0 ? 'bg-danger' : index === 1 ? 'bg-warning' : 'bg-success'}">${index === 0 ? 'High' : index === 1 ? 'Medium' : 'Low'}</span></td>
                                                                    <td>${['2 weeks', '1 month', '3 months'][index % 3]}</td>
                                                                    <td>
                                                                        <select class="form-select form-select-sm status-select">
                                                                            <option value="not-started">Not Started</option>
                                                                            <option value="in-progress">In Progress</option>
                                                                            <option value="completed">Completed</option>
                                                                        </select>
                                                                    </td>
                                                                </tr>
                                                            `).join('')}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="improvementNotes" class="form-label">Implementation Notes</label>
                                            <textarea class="form-control" id="improvementNotes" rows="3" placeholder="Enter implementation details and notes..."></textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        <button type="button" class="btn btn-primary save-improvement-plan-btn">Save Improvement Plan</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add modal to the document if it doesn't exist
                    if (!document.getElementById(modalId)) {
                        document.body.insertAdjacentHTML('beforeend', modalHtml);
                    }

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById(modalId));
                    modal.show();

                    // Add event listener for save improvement plan button
                    document.querySelector(`#${modalId} .save-improvement-plan-btn`).addEventListener('click', function() {
                        const notes = document.getElementById('improvementNotes').value;
                        const statuses = Array.from(document.querySelectorAll(`#${modalId} .status-select`)).map(select => select.value);
                        alert(`Improvement plan saved for ${touchpointStage} stage.\nImplementation notes: ${notes || 'None'}\nStatuses updated.`);
                        modal.hide();
                    });
                });
            });

            // Add event listeners for view topic feedback buttons
            document.querySelectorAll('.view-topic-feedback-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const topic = this.dataset.topic;

                    // Find the topic in the data
                    let topicData;
                    const sentimentAnalysis = data.insights.find(insight => insight.type === 'sentiment_analysis');
                    if (sentimentAnalysis && sentimentAnalysis.key_topics) {
                        topicData = sentimentAnalysis.key_topics.find(t => t.topic === topic);
                    }

                    if (!topicData) return;

                    // Create modal for topic feedback
                    const modalId = `topicFeedbackModal-${topic.replace(/\s+/g, '-').toLowerCase()}`;
                    const modalHtml = `
                        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="${modalId}Label">Customer Feedback: ${topic}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="card mb-4">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">Sentiment Overview</h6>
                                                <span class="badge ${topicData.sentiment === 'Very Positive' || topicData.sentiment === 'Positive' ? 'bg-success' : topicData.sentiment === 'Neutral' ? 'bg-secondary' : 'bg-danger'}">
                                                    ${topicData.sentiment}
                                                </span>
                                            </div>
                                            <div class="card-body">
                                                <div class="progress mb-3" style="height: 25px;">
                                                    <div class="progress-bar ${topicData.score >= 70 ? 'bg-success' : topicData.score >= 50 ? 'bg-warning' : 'bg-danger'}"
                                                        role="progressbar"
                                                        style="width: ${topicData.score}%;"
                                                        aria-valuenow="${topicData.score}"
                                                        aria-valuemin="0"
                                                        aria-valuemax="100">
                                                        ${topicData.score}%
                                                    </div>
                                                </div>
                                                <div class="row text-center">
                                                    <div class="col-md-4">
                                                        <div class="card bg-success text-white">
                                                            <div class="card-body">
                                                                <h5>${Math.round(topicData.score * 0.8)}%</h5>
                                                                <p class="mb-0">Positive</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="card bg-secondary text-white">
                                                            <div class="card-body">
                                                                <h5>${Math.round(topicData.score * 0.2)}%</h5>
                                                                <p class="mb-0">Neutral</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="card bg-danger text-white">
                                                            <div class="card-body">
                                                                <h5>${100 - Math.round(topicData.score)}%</h5>
                                                                <p class="mb-0">Negative</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card mb-4">
                                            <div class="card-header">
                                                <h6 class="mb-0">Sample Feedback</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="feedback-item p-3 mb-3 border-start border-5 border-success">
                                                    <p class="mb-1">${topicData.sample_feedback}</p>
                                                    <small class="text-muted">Customer ID: 12345 | Date: ${new Date().toLocaleDateString()}</small>
                                                </div>
                                                <div class="feedback-item p-3 mb-3 border-start border-5 ${topicData.sentiment === 'Very Positive' || topicData.sentiment === 'Positive' ? 'border-success' : topicData.sentiment === 'Neutral' ? 'border-secondary' : 'border-danger'}">
                                                    <p class="mb-1">Additional feedback would be shown here based on the selected topic.</p>
                                                    <small class="text-muted">Customer ID: 23456 | Date: ${new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}</small>
                                                </div>
                                                <div class="feedback-item p-3 mb-3 border-start border-5 ${topicData.sentiment === 'Very Positive' || topicData.sentiment === 'Positive' ? 'border-success' : topicData.sentiment === 'Neutral' ? 'border-secondary' : 'border-danger'}">
                                                    <p class="mb-1">More customer feedback examples related to ${topic} would appear here.</p>
                                                    <small class="text-muted">Customer ID: 34567 | Date: ${new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toLocaleDateString()}</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="actionPlan" class="form-label">Action Plan</label>
                                            <textarea class="form-control" id="actionPlan" rows="3" placeholder="Enter action plan to address feedback..."></textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        <button type="button" class="btn btn-primary save-action-plan-btn">Save Action Plan</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add modal to the document if it doesn't exist
                    if (!document.getElementById(modalId)) {
                        document.body.insertAdjacentHTML('beforeend', modalHtml);
                    }

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById(modalId));
                    modal.show();

                    // Add event listener for save action plan button
                    document.querySelector(`#${modalId} .save-action-plan-btn`).addEventListener('click', function() {
                        const actionPlan = document.getElementById('actionPlan').value;
                        alert(`Action plan saved for ${topic}.\nPlan details: ${actionPlan || 'None'}`);
                        modal.hide();
                    });
                });
            });

            // Add event listeners for create action plan buttons
            document.querySelectorAll('.create-action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const recommendationId = this.dataset.recommendationId;
                    const recommendationText = this.dataset.recommendationText;

                    // Create modal for action plan
                    const modalId = `actionPlanModal-${recommendationId}`;
                    const modalHtml = `
                        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="${modalId}Label">Create Action Plan</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label class="form-label">Recommendation:</label>
                                            <p>${recommendationText}</p>
                                        </div>
                                        <div class="mb-3">
                                            <label for="actionTitle" class="form-label">Action Title:</label>
                                            <input type="text" class="form-control" id="actionTitle" value="Action Plan for ${recommendationText.substring(0, 30)}...">
                                        </div>
                                        <div class="mb-3">
                                            <label for="assignee" class="form-label">Assignee:</label>
                                            <select class="form-select" id="assignee">
                                                <option value="sales-manager">Sales Manager</option>
                                                <option value="marketing-manager">Marketing Manager</option>
                                                <option value="customer-success">Customer Success Manager</option>
                                                <option value="product-manager">Product Manager</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="dueDate" class="form-label">Due Date:</label>
                                            <input type="date" class="form-control" id="dueDate" value="${new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">Priority:</label>
                                            <select class="form-select" id="priority">
                                                <option value="low">Low</option>
                                                <option value="medium" selected>Medium</option>
                                                <option value="high">High</option>
                                                <option value="critical">Critical</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="actionDescription" class="form-label">Description:</label>
                                            <textarea class="form-control" id="actionDescription" rows="3" placeholder="Enter action plan details...">${recommendationText}</textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary confirm-action-btn">Create Action Plan</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add modal to the document if it doesn't exist
                    if (!document.getElementById(modalId)) {
                        document.body.insertAdjacentHTML('beforeend', modalHtml);
                    }

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById(modalId));
                    modal.show();

                    // Add event listener for confirm button
                    document.querySelector(`#${modalId} .confirm-action-btn`).addEventListener('click', function() {
                        const title = document.querySelector(`#${modalId} #actionTitle`).value;
                        const assignee = document.querySelector(`#${modalId} #assignee`).value;
                        const dueDate = document.querySelector(`#${modalId} #dueDate`).value;
                        const priority = document.querySelector(`#${modalId} #priority`).value;
                        const description = document.querySelector(`#${modalId} #actionDescription`).value;

                        // Here you would typically send the data to the server
                        alert(`Action plan "${title}" created and assigned to ${assignee} with ${priority} priority, due on ${dueDate}`);
                        modal.hide();
                    });
                });
            });
        }

        // Google Maps Handler
        const GoogleMapsHandler = {
            init: function() {
                console.log('Initializing Google Maps integration');
                // Initialize map functionality
                // This would typically include API initialization and event listeners
            }
        };

        // Google Sheets Handler
        const GoogleSheetsHandler = {
            init: function() {
                console.log('Initializing Google Sheets integration');
                // Initialize sheets functionality
            }
        };

        // Google Calendar Handler
        const GoogleCalendarHandler = {
            init: function() {
                console.log('Initializing Google Calendar integration');
                // Initialize calendar functionality
            }
        };

        // Google Gmail Handler
        const GoogleGmailHandler = {
            init: function() {
                console.log('Initializing Gmail integration');
                // Enhanced Gmail implementation is loaded separately
            }
        };

        // Google Drive Handler
        const GoogleDriveHandler = {
            init: function() {
                console.log('Initializing Google Drive integration');
                // Initialize drive functionality
            }
        };

        // Google Docs Handler
        const GoogleDocsHandler = {
            init: function() {
                console.log('Initializing Google Docs integration');
                // Initialize docs functionality
            }
        };

        // Initialize Google Integration Components
        document.addEventListener('DOMContentLoaded', () => {
            GoogleMapsHandler.init();
            GoogleSheetsHandler.init();
            GoogleCalendarHandler.init();
            GoogleGmailHandler.init();
            GoogleDriveHandler.init();
            GoogleDocsHandler.init();

            // Fetch AI insights
            fetchAIInsights();

            // Add toast container to the document
            if (!document.getElementById('toast-container')) {
                const toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }
        });

        // File operations functions
        // Note: The viewAttachment function is defined earlier in the file at line ~3437

        // Enhanced file operations using the new modal system
        function downloadFile(fileName) {
            console.log(`Enhanced download for: ${fileName}`);
            // Determine file type from filename
            let fileType = 'file';
            if (fileName.includes('.pdf')) fileType = 'pdf';
            else if (fileName.includes('.xlsx') || fileName.includes('.xls')) fileType = 'spreadsheet';
            else if (fileName.includes('.docx') || fileName.includes('.doc')) fileType = 'document';
            else if (fileName.includes('.png') || fileName.includes('.jpg') || fileName.includes('.jpeg')) fileType = 'image';

            // Call the enhanced download modal from google-drive-actions.js
            if (typeof showDownloadModal === 'function') {
                showDownloadModal(fileName, fileType);
            } else {
                // Fallback to simple toast if enhanced system not loaded
                showToast(`File "${fileName}" is being downloaded.`);
            }
        }

        function downloadCurrentFile() {
            if (window.currentFile) {
                downloadFile(window.currentFile.name);
            }
        }

        function shareFile(fileName) {
            console.log(`Enhanced share for: ${fileName}`);
            // Determine file type from filename
            let fileType = 'file';
            if (fileName.includes('.pdf')) fileType = 'pdf';
            else if (fileName.includes('.xlsx') || fileName.includes('.xls')) fileType = 'spreadsheet';
            else if (fileName.includes('.docx') || fileName.includes('.doc')) fileType = 'document';
            else if (fileName.includes('.png') || fileName.includes('.jpg') || fileName.includes('.jpeg')) fileType = 'image';

            // Call the enhanced share modal from google-drive-actions.js
            if (typeof showShareModal === 'function') {
                showShareModal(fileName, fileType);
            } else {
                // Fallback to simple prompt if enhanced system not loaded
                const email = prompt(`Enter email address to share ${fileName} with:`);
                if (email) {
                    showToast(`${fileName} has been shared with ${email}.`);
                }
            }
        }

        function shareCurrentFile() {
            if (window.currentFile) {
                shareFile(window.currentFile.name);
            }
        }

        function deleteAttachment(filename) {
            console.log(`Enhanced delete for: ${filename}`);
            // Determine file type from filename
            let fileType = 'file';
            if (filename.includes('.pdf')) fileType = 'pdf';
            else if (filename.includes('.xlsx') || filename.includes('.xls')) fileType = 'spreadsheet';
            else if (filename.includes('.docx') || filename.includes('.doc')) fileType = 'document';
            else if (filename.includes('.png') || filename.includes('.jpg') || filename.includes('.jpeg')) fileType = 'image';

            // Call the enhanced delete modal from google-drive-actions.js
            if (typeof showDeleteConfirmationModal === 'function') {
                showDeleteConfirmationModal(filename, fileType);
            } else {
                // Fallback to simple confirm if enhanced system not loaded
                if (confirm(`Are you sure you want to delete "${filename}"?`)) {
                    console.log(`Deleting file: ${filename}`);
                    showToast(`File "${filename}" has been deleted.`);

                    // Close the file viewer modal if open
                    const fileViewerModal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
                    if (fileViewerModal && window.currentFile && window.currentFile.name === filename) {
                        fileViewerModal.hide();
                    }
                }
            }
        }

        function deleteCurrentFile() {
            if (window.currentFile) {
                deleteAttachment(window.currentFile.name);
            }
        }

        // Enhanced functions for fileViewerModal that use the enhanced modal system
        function downloadCurrentFileEnhanced() {
            if (window.currentFile) {
                // Determine file type from the current file
                let fileType = 'file';
                if (window.currentFile.type === 'spreadsheet') fileType = 'spreadsheet';
                else if (window.currentFile.type === 'document') fileType = 'document';
                else if (window.currentFile.app === 'sheets') fileType = 'spreadsheet';
                else if (window.currentFile.app === 'docs') fileType = 'document';

                // Call the enhanced download modal from google-drive-actions.js
                if (typeof showDownloadModal === 'function') {
                    showDownloadModal(window.currentFile.name, fileType);
                } else {
                    // Fallback to simple download if enhanced system not loaded
                    downloadFile(window.currentFile.name);
                }
            }
        }

        function shareCurrentFileEnhanced() {
            if (window.currentFile) {
                // Determine file type from the current file
                let fileType = 'file';
                if (window.currentFile.type === 'spreadsheet') fileType = 'spreadsheet';
                else if (window.currentFile.type === 'document') fileType = 'document';
                else if (window.currentFile.app === 'sheets') fileType = 'spreadsheet';
                else if (window.currentFile.app === 'docs') fileType = 'document';

                // Call the enhanced share modal from google-drive-actions.js
                if (typeof showShareModal === 'function') {
                    showShareModal(window.currentFile.name, fileType);
                } else {
                    // Fallback to simple share if enhanced system not loaded
                    shareFile(window.currentFile.name);
                }
            }
        }

        // Function to open specific Google items
        function openGoogleItem(service, type, id) {
            console.log(`Opening ${service} ${type}: ${id}`);

            // For general app buttons (without specific item), show the modal
            if (service === 'drive' && !type && !id) {
                const driveModal = new bootstrap.Modal(document.getElementById('driveModal'));
                driveModal.show();
                return;
            } else if (service === 'docs' && !type && !id) {
                const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
                docsModal.show();
                return;
            } else if (service === 'sheets' && !type && !id) {
                const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
                sheetsModal.show();
                return;
            } else if (service === 'attachments' && !type && !id) {
                const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
                attachmentsModal.show();
                return;
            }

            // For specific items, directly open the file in a viewer
            try {
                // Always close all open modals before opening the file viewer
                document.querySelectorAll('.modal.show').forEach(m => {
                    if (m.id !== 'fileViewerModal') {
                        bootstrap.Modal.getInstance(m)?.hide();
                    }
                });
                let fileViewerModal = document.getElementById('fileViewerModal');
                if (!fileViewerModal) {
                    console.error('File viewer modal not found in the DOM');
                    return;
                }
                const fileTitle = document.getElementById('fileViewerTitle');
                const fileContent = document.getElementById('fileViewerContent');
                // Format the item ID to make it more readable
                const readableId = id.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                fileTitle.innerHTML = `<i class="bi ${getFileIcon(service, type)}"></i> ${readableId}`;
                fileContent.innerHTML = getFileContent(service, type, id);
                window.currentFile = {
                    app: service,
                    type: type,
                    id: id,
                    name: readableId
                };
                // Ensure fileViewerModal is at the end of body so it is above all others
                document.body.appendChild(fileViewerModal);
                const modal = new bootstrap.Modal(fileViewerModal, { backdrop: 'static', focus: true });
                modal.show();
            } catch (error) {
                console.error('Error opening file:', error);
                alert('Could not open the file. Please try again later.');
            }
        }

        function openEmail(sender, subject) {
            // Switch to the read tab and populate it with the email content
            const readTab = document.getElementById('read-tab');
            const readContent = document.getElementById('read-content');

            // Show the Gmail modal if it's not already open
            const gmailModal = document.getElementById('gmailModal');
            if (gmailModal && !gmailModal.classList.contains('show')) {
                const modal = new bootstrap.Modal(gmailModal);
                modal.show();
            }

            // Activate the read tab
            const tabTrigger = new bootstrap.Tab(readTab);
            tabTrigger.show();

            // Populate the read content based on the sender and subject
            let emailContent = '';

            if (sender === 'john-davis' && subject === 'Meeting follow-up: Product demo feedback') {
                emailContent = `
                    <div class="email-header mb-3">
                        <h5>${subject}</h5>
                        <div class="d-flex justify-content-between">
                            <div>
                                <span class="fw-bold">From:</span> John Davis &lt;<EMAIL>&gt;
                            </div>
                            <div>
                                <span class="text-muted">10 minutes ago</span>
                            </div>
                        </div>
                    </div>
                    <div class="email-body">
                        <p>Hello,</p>
                        <p>Thank you for the product demonstration yesterday. Our team was impressed with the features and capabilities of your CRM system.</p>
                        <p>We particularly liked the AI-powered analytics and the integration capabilities with our existing tools. The user interface is also very intuitive and should require minimal training for our team.</p>
                        <p>We have a few questions about customization options and pricing tiers that we'd like to discuss further. Would you be available for a follow-up call next week?</p>
                        <p>Best regards,<br>John Davis<br>Product Manager<br>ABC Corporation</p>
                    </div>
                    <div class="email-actions mt-3">
                        <button class="btn btn-primary" onclick="replyEmail('john-davis', 'Meeting follow-up: Product demo feedback')">
                            <i class="bi bi-reply me-2"></i>Reply
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-reply-all me-2"></i>Reply All
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-forward me-2"></i>Forward
                        </button>
                    </div>
                `;
            } else if (sender === 'sarah-miller' && subject === 'Contract renewal discussion') {
                emailContent = `
                    <div class="email-header mb-3">
                        <h5>${subject}</h5>
                        <div class="d-flex justify-content-between">
                            <div>
                                <span class="fw-bold">From:</span> Sarah Miller &lt;<EMAIL>&gt;
                            </div>
                            <div>
                                <span class="text-muted">1 hour ago</span>
                            </div>
                        </div>
                    </div>
                    <div class="email-body">
                        <p>Hi there,</p>
                        <p>I'd like to schedule a call to discuss the upcoming contract renewal for our enterprise subscription.</p>
                        <p>Our current contract expires in 45 days, and we're interested in exploring some of the new features you've added since our last renewal. We're also considering expanding the number of user licenses.</p>
                        <p>Could you please provide some available times next week for a discussion? Also, it would be helpful if you could send over the current pricing structure for enterprise plans.</p>
                        <p>Thank you,<br>Sarah Miller<br>Operations Director<br>XYZ Company</p>
                    </div>
                    <div class="email-actions mt-3">
                        <button class="btn btn-primary" onclick="replyEmail('sarah-miller', 'Contract renewal discussion')">
                            <i class="bi bi-reply me-2"></i>Reply
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-reply-all me-2"></i>Reply All
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-forward me-2"></i>Forward
                        </button>
                    </div>
                `;
            } else if (sender === 'alex-chen' && subject === 'New customer inquiry about enterprise plan') {
                emailContent = `
                    <div class="email-header mb-3">
                        <h5>${subject}</h5>
                        <div class="d-flex justify-content-between">
                            <div>
                                <span class="fw-bold">From:</span> Alex Chen &lt;<EMAIL>&gt;
                            </div>
                            <div>
                                <span class="text-muted">3 hours ago</span>
                            </div>
                        </div>
                    </div>
                    <div class="email-body">
                        <p>Hello,</p>
                        <p>We're interested in learning more about your enterprise plan options. Our company, Tech Innovations, is growing rapidly and we need a robust CRM solution that can scale with us.</p>
                        <p>Currently, we have about 50 employees who would need access to the system, primarily in sales, marketing, and customer support roles. We're particularly interested in:</p>
                        <ul>
                            <li>Advanced reporting and analytics capabilities</li>
                            <li>Integration with our existing tech stack (Slack, Google Workspace, Jira)</li>
                            <li>Customizable workflows and automation</li>
                            <li>Mobile access for our field sales team</li>
                        </ul>
                        <p>Could you provide information about your enterprise offerings and possibly schedule a demo for our team?</p>
                        <p>Best regards,<br>Alex Chen<br>Director of Operations<br>Tech Innovations</p>
                    </div>
                    <div class="email-actions mt-3">
                        <button class="btn btn-primary" onclick="replyEmail('alex-chen', 'New customer inquiry about enterprise plan')">
                            <i class="bi bi-reply me-2"></i>Reply
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-reply-all me-2"></i>Reply All
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-forward me-2"></i>Forward
                        </button>
                    </div>
                `;
            } else {
                emailContent = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i> Email content not available.
                    </div>
                `;
            }

            readContent.innerHTML = emailContent;
        }

        function replyEmail(sender, subject) {
            // Show the Gmail modal if it's not already open
            const gmailModal = document.getElementById('gmailModal');
            if (gmailModal && !gmailModal.classList.contains('show')) {
                const modal = new bootstrap.Modal(gmailModal);
                modal.show();
            }

            // Switch to the compose tab and populate it with reply information
            const composeTab = document.getElementById('compose-tab');
            const emailTo = document.getElementById('email-to');
            const emailSubject = document.getElementById('email-subject');
            const emailBody = document.getElementById('email-body');

            // Activate the compose tab
            const tabTrigger = new bootstrap.Tab(composeTab);
            tabTrigger.show();

            // Set the recipient and subject
            let recipient = '';
            let senderName = '';

            if (sender === 'john-davis') {
                recipient = '<EMAIL>';
                senderName = 'John Davis';
            } else if (sender === 'sarah-miller') {
                recipient = '<EMAIL>';
                senderName = 'Sarah Miller';
            } else if (sender === 'alex-chen') {
                recipient = '<EMAIL>';
                senderName = 'Alex Chen';
            } else {
                // Handle generic case
                senderName = sender.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                recipient = `${sender.replace(/-/g, '.')}@example.com`;
            }

            emailTo.value = recipient;
            emailSubject.value = `Re: ${subject}`;

            // Set a reply template in the email body
            emailBody.value = `\n\n-------- Original Message --------\nFrom: ${senderName}\nSubject: ${subject}\n`;

            // Focus on the email body
            emailBody.focus();

            // Show a toast notification
            showToast(`Replying to: ${subject}`);
        }

        function viewCalendarEvent(eventId) {
            const calendarModal = bootstrap.Modal.getInstance(document.getElementById('calendarModal'));
            if (calendarModal) {
                calendarModal.show();
            }

            // In a real application, this would show the specific event details
            console.log(`Viewing calendar event: ${eventId}`);
        }

        function refreshMap() {
            // In a real application, this would refresh the map data
            console.log('Refreshing map data');
            showToast('Map data refreshed');
        }

        function viewLocation(locationId) {
            // In a real application, this would show the location details
            console.log(`Viewing location: ${locationId}`);

            // Open the maps modal and show the location
            const mapsModal = bootstrap.Modal.getInstance(document.getElementById('mapsModal'));
            if (!mapsModal) {
                const mapsModalElement = document.getElementById('mapsModal');
                new bootstrap.Modal(mapsModalElement).show();
            }
        }

        function shareCalendarEvent(eventId) {
            // In a real application, this would open a sharing dialog for the calendar event
            console.log(`Sharing calendar event: ${eventId}`);

            // Show a toast notification
            showToast(`Calendar event link copied to clipboard.`);
        }

        function showToast(message, type = 'success') {
            // Create toast container if it doesn't exist
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            // Create a unique ID for this toast
            const toastId = 'toast-' + Date.now();

            // Create toast element
            const toastHtml = `
                <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header">
                        <i class="bi bi-${type === 'success' ? 'check-circle-fill text-success' : 'exclamation-circle-fill text-danger'} me-2"></i>
                        <strong class="me-auto">${type === 'success' ? 'Success' : 'Error'}</strong>
                        <small>Just now</small>
                        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            // Add toast to container
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // Initialize and show the toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // Remove toast from DOM after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function () {
                toastElement.remove();
            });
        }
    </script>

    <!-- Unified Gmail Integration - Single working system -->
    <script src="../../SharedFeatures/ui/gmail-integration-unified.js"></script>

    <!-- ISA Suite Enhancements -->
    <script src="../../SharedFeatures/enhancements/cross-app-enhancements.js"></script>

    <!-- Initialize Unified Gmail Integration -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the unified Gmail integration for CRM
            if (typeof initializeUnifiedGmail === 'function') {
                const gmail = initializeUnifiedGmail({
                    appName: 'CRM',
                    appPrefix: 'crm',
                    appColor: '#e67e22', // Orange theme for CRM
                    modalId: 'crm-gmailModal',
                    triggerId: 'gmail-link', // The sidebar Gmail link
                    debug: true
                });
                console.log('Unified Gmail integration initialized for CRM application');

                // Make globally available for debugging
                window.crmGmail = gmail;
            } else {
                console.warn('Unified Gmail integration not available');
            }
        });
    </script>
    <script src="js/crm-gmail-attachments.js"></script>
    <script src="js/crm-gmail-sort-filter.js"></script>

    <!-- Enhanced Google Drive Actions -->
    <script src="js/google-drive-actions.js"></script>

    <!-- Data Utilities -->
    <script src="../../shared/js/isa-data-utils.js"></script>
    <script src="js/crm-data-functions.js"></script>
    <script src="js/enhanced-search-sort-filter.js"></script>
</body>
</html>
