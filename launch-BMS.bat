@echo off
echo Starting Business Management System (BMS)...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Change to the BMS directory
cd /d C:\ISASUITE\apps\BMS\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call C:\ISASUITE\PortableNodeJS\pnpm install
)

REM Function to check and install missing modules
:check_missing_modules
node index.js > missing_modules.log 2>&1
findstr /C:"Cannot find module '" missing_modules.log > nul
if %errorlevel% == 0 (
    for /f "tokens=4 delims='" %%M in ('findstr /C:"Cannot find module '" missing_modules.log') do (
        echo Installing missing module: %%M
        call C:\ISASUITE\PortableNodeJS\pnpm add %%M
        del missing_modules.log
        goto check_missing_modules
    )
)
if exist missing_modules.log del missing_modules.log

REM Run the application with production mode by default
echo Starting BMS in production mode...
echo To access BMS, go to: http://localhost:3001
start "" http://localhost:3001

REM Start using pnpm instead of directly invoking Node
pnpm start

echo.
echo BMS application has been closed.
pause
