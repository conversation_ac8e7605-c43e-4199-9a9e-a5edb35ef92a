# ISA Suite Service Manager

This document explains how to use the new service management scripts for your ISA Suite applications.

## Available Scripts

### 1. service-manager.bat

This is the main script for managing your ISA Suite applications. It provides a user-friendly menu to:

- Start all applications
- Stop all applications
- Check application status
- Start individual applications
- Stop individual applications
- Open the Integration Hub in browser

**Usage:**
```
C:\ISASUITE\service-manager.bat
```

### 2. start-apps-consolidated.bat

This script starts all ISA Suite applications in the background with only one visible console window for the Integration Hub. All other applications run in the background.

**Usage:**
```
C:\ISASUITE\start-apps-consolidated.bat
```

### 3. stop-apps.bat

This script stops all running ISA Suite applications.

**Usage:**
```
C:\ISASUITE\stop-apps.bat
```

## Logs

All application logs are saved in the `C:\ISASUITE\logs` directory. You can check these logs if you encounter any issues with the applications.

## Application URLs

Once the applications are running, you can access them at:

- Integration Hub: http://localhost:8000
- Business Management System: http://localhost:3001
- Materials Requirements Planning: http://localhost:3002
- Customer Relationship Management: http://localhost:3003
- Warehouse Management System: http://localhost:3004
- Advanced Planning and Scheduling: http://localhost:3005
- Asset Performance Management: http://localhost:3006
- Project Management System: http://localhost:3007
- Supply Chain Management: http://localhost:3008
- Task Management System: http://localhost:3009

## New Features

1. **Consolidated Application Management**: All applications can now be managed from a single interface.
2. **Background Execution**: Applications run in the background without cluttering your desktop with multiple console windows.
3. **Application Logs**: All application output is saved to log files for easier troubleshooting.
4. **Open in New Tabs**: Applications now open in new browser tabs when accessed from the Integration Hub.
5. **Individual Application Control**: Start or stop individual applications as needed.

## Troubleshooting

If you encounter any issues:

1. Check the application logs in the `C:\ISASUITE\logs` directory.
2. Use the "Check application status" option in the service manager to see which applications are running.
3. Try stopping all applications and starting them again.
4. If an application fails to start, try starting it individually to see any error messages.
