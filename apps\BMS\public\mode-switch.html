<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Switching Application Mode</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
      }
      .card {
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        text-align: center;
        max-width: 500px;
      }
      .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        animation: spin 2s linear infinite;
        margin: 1rem auto;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div class="card">
      <h1>Switching Application Mode</h1>
      <div class="spinner"></div>
      <p>Please wait while the application restarts in the selected mode...</p>
      <p>This may take a few moments.</p>
    </div>

    <script>
      // Get the mode from the URL
      const urlParams = new URLSearchParams(window.location.search);
      const mode = urlParams.get('mode');

      // Redirect to the API endpoint to restart the server
      if (mode) {
          fetch(/api/restart?mode=, {
              method: 'POST'
          })
          .then(response => response.json())
          .then(data => {
              console.log('Server restarting:', data);

              // Wait a few seconds and then redirect to the home page
              setTimeout(() => {
                  window.location.href = '/';
              }, 5000);
          })
          .catch(error => {
              console.error('Error restarting server:', error);
              alert('Error restarting server. Please try again.');
              window.location.href = '/';
          });
      } else {
          // If no mode is specified, redirect to the home page
          window.location.href = '/';
      }
    </script>
  </body>
</html>
