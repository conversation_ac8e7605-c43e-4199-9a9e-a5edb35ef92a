// Google Token Monitor and Auto-Refresh Script
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const TOKEN_PATH = path.join(__dirname, 'SharedFeatures', 'integrations', 'google-token.json');
const REFRESH_THRESHOLD_MINUTES = 10; // Refresh token if it will expire within this many minutes
const CHECK_INTERVAL_MINUTES = 5; // Check token expiry every this many minutes

// Function to check token expiry
function checkTokenExpiry() {
  console.log(`[${new Date().toLocaleString()}] Checking Google token status...`);
  
  // Check if token file exists
  if (!fs.existsSync(TOKEN_PATH)) {
    console.error(`Token file not found at: ${TOKEN_PATH}`);
    return false;
  }
  
  try {
    // Read and parse token
    const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH, 'utf8'));
    
    // Check if token has expiry date
    if (!tokenData.expiry_date) {
      console.error('Token does not have an expiry date');
      return false;
    }
    
    // Calculate time until expiry
    const expiryDate = new Date(tokenData.expiry_date);
    const now = new Date();
    const minutesUntilExpiry = Math.round((expiryDate - now) / 1000 / 60);
    
    console.log(`Token expires in ${minutesUntilExpiry} minutes (at ${expiryDate.toLocaleString()})`);
    
    // Return true if token will expire soon
    return minutesUntilExpiry <= REFRESH_THRESHOLD_MINUTES;
  } catch (error) {
    console.error('Error reading or parsing token file:', error.message);
    return false;
  }
}

// Function to refresh token
function refreshToken() {
  console.log(`[${new Date().toLocaleString()}] Refreshing Google token...`);
  
  try {
    // Use the refresh_token to get a new access token
    // This requires the token to have a refresh_token field
    const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH, 'utf8'));
    
    if (!tokenData.refresh_token) {
      console.error('Token does not have a refresh_token field. Cannot refresh automatically.');
      console.log('Please run the authentication flow manually:');
      console.log('node SharedFeatures/integrations/google.js');
      return false;
    }
    
    // Create a temporary script to refresh the token
    const tempScriptPath = path.join(__dirname, 'temp-refresh-token.js');
    const scriptContent = `
      const { google } = require('googleapis');
      const fs = require('fs');
      const path = require('path');
      
      const TOKEN_PATH = '${TOKEN_PATH.replace(/\\/g, '\\\\')}';
      const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH, 'utf8'));
      
      async function refreshToken() {
        const oauth2Client = new google.auth.OAuth2(
          '${tokenData.client_id || '532929345906-8ueuqnt6amntmqoifsmv98cr66vb2usf.apps.googleusercontent.com'}',
          '${tokenData.client_secret || '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'}',
          '${tokenData.redirect_uri || 'http://localhost'}'
        );
        
        oauth2Client.setCredentials({
          refresh_token: tokenData.refresh_token
        });
        
        try {
          const { tokens } = await oauth2Client.refreshAccessToken();
          fs.writeFileSync(TOKEN_PATH, JSON.stringify(tokens));
          console.log('Token refreshed successfully');
          return true;
        } catch (error) {
          console.error('Error refreshing token:', error.message);
          return false;
        }
      }
      
      refreshToken();
    `;
    
    fs.writeFileSync(tempScriptPath, scriptContent);
    
    // Execute the script
    execSync(`node ${tempScriptPath}`, { stdio: 'inherit' });
    
    // Clean up
    fs.unlinkSync(tempScriptPath);
    
    console.log('Token refreshed successfully');
    return true;
  } catch (error) {
    console.error('Error refreshing token:', error.message);
    return false;
  }
}

// Main function to monitor and refresh token
function monitorToken() {
  console.log(`[${new Date().toLocaleString()}] Starting Google token monitor...`);
  console.log(`Checking token every ${CHECK_INTERVAL_MINUTES} minutes`);
  console.log(`Will refresh token if it expires within ${REFRESH_THRESHOLD_MINUTES} minutes`);
  
  // Initial check
  if (checkTokenExpiry()) {
    refreshToken();
  }
  
  // Set up interval to check token
  setInterval(() => {
    if (checkTokenExpiry()) {
      refreshToken();
    }
  }, CHECK_INTERVAL_MINUTES * 60 * 1000);
}

// Start monitoring
monitorToken();
