@echo off
echo ===================================
echo    ISA Suite - Open All Applications
echo ===================================
echo.
echo This script will open all ISA Suite applications in your default browser.
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause > nul

echo Opening Integration Hub (http://localhost:8000)...
start "" http://localhost:8000
timeout /t 1 > nul

echo Opening Business Management System (http://localhost:3001)...
start "" http://localhost:3001
timeout /t 1 > nul

echo Opening Materials Requirements Planning (http://localhost:3002)...
start "" http://localhost:3002
timeout /t 1 > nul

echo Opening Customer Relationship Management (http://localhost:3003)...
start "" http://localhost:3003
timeout /t 1 > nul

echo Opening Warehouse Management System (http://localhost:3004)...
start "" http://localhost:3004
timeout /t 1 > nul

echo Opening Advanced Planning and Scheduling (http://localhost:3005)...
start "" http://localhost:3005
timeout /t 1 > nul

echo Opening Asset Performance Management (http://localhost:3006)...
start "" http://localhost:3006
timeout /t 1 > nul

echo Opening Project Management System (http://localhost:3007)...
start "" http://localhost:3007
timeout /t 1 > nul

echo Opening Supply Chain Management (http://localhost:3008)...
start "" http://localhost:3008
timeout /t 1 > nul

echo Opening Task Management System (http://localhost:3009)...
start "" http://localhost:3009
timeout /t 1 > nul

echo.
echo All applications have been opened in your browser.
echo.
pause
