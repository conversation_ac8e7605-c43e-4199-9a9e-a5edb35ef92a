import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/mongodb';

const apps = [
  {
    name: 'Business Intelligence',
    url: process.env.BI_URL,
    description: 'Enterprise Resource Planning and Analytics',
  },
  {
    name: 'Business Management',
    url: process.env.BMS_URL,
    description: 'Core Business Operations',
  },
  {
    name: 'Customer Relationship',
    url: process.env.CRM_URL,
    description: 'Customer Management and Sales',
  },
  {
    name: 'Project Management',
    url: process.env.PMS_URL,
    description: 'Project Planning and Milestones',
  },
  {
    name: 'Task Management',
    url: process.env.TMS_URL,
    description: 'Task Tracking and Team Management',
  },
  {
    name: 'Supply Chain',
    url: process.env.SCM_URL,
    description: 'Inventory and Supplier Management',
  },
  { name: 'Product Data', url: process.env.PDM_URL, description: 'Product Lifecycle Management' },
];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { db } = await connectToDatabase();
    const appStatuses = await db.collection('app_status').find({}).toArray();

    const appsWithStatus = apps.map((app) => {
      const status = appStatuses.find((s) => s.name === app.name);
      return {
        ...app,
        status: status?.status || 'offline',
        lastChecked: status?.lastChecked || new Date().toISOString(),
      };
    });

    res.status(200).json(appsWithStatus);
  } catch (error) {
    console.error('Error fetching apps:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
