/**
 * CRM Gmail Sort and Filter
 * 
 * This file provides sort and filter functionality for the Gmail modal in the CRM application.
 * It allows users to sort emails by date, sender, or subject, and filter emails by various criteria.
 */

// Execute when the document is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('CRM Gmail Sort and Filter loaded');
    
    // Initialize Gmail sort and filter functionality
    CRMGmailSortFilter.init();
});

/**
 * CRMGmailSortFilter - Namespace for Gmail sort and filter functionality
 */
const CRMGmailSortFilter = {
    /**
     * Initialize Gmail sort and filter functionality
     */
    init: function() {
        // Initialize event listeners for sort options
        this.initSortEventListeners();
        
        // Initialize event listeners for filter options
        this.initFilterEventListeners();
        
        // Initialize search functionality
        this.initSearchFunctionality();
        
        console.log('CRM Gmail Sort and Filter initialized');
    },
    
    /**
     * Initialize event listeners for sort options
     */
    initSortEventListeners: function() {
        // Find all sort option links
        const sortOptions = document.querySelectorAll('#crm-sort-dropdown + .dropdown-menu .sort-option');
        
        // Add click event listener to each sort option
        sortOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get the sort criteria from the data attribute
                const sortCriteria = this.getAttribute('data-sort');
                
                // Sort the emails
                CRMGmailSortFilter.sortEmails(sortCriteria);
                
                // Update the sort dropdown button text
                const sortDropdown = document.getElementById('crm-sort-dropdown');
                if (sortDropdown) {
                    sortDropdown.innerHTML = `<i class="bi bi-sort-down"></i> ${this.textContent}`;
                }
            });
        });
    },
    
    /**
     * Initialize event listeners for filter options
     */
    initFilterEventListeners: function() {
        // Find all filter option links
        const filterOptions = document.querySelectorAll('#crm-filter-dropdown + .dropdown-menu .filter-option');
        
        // Add click event listener to each filter option
        filterOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get the filter criteria from the data attribute
                const filterCriteria = this.getAttribute('data-filter');
                
                // Filter the emails
                CRMGmailSortFilter.filterEmails(filterCriteria);
                
                // Update the filter dropdown button text
                const filterDropdown = document.getElementById('crm-filter-dropdown');
                if (filterDropdown) {
                    filterDropdown.innerHTML = `<i class="bi bi-funnel"></i> ${this.textContent}`;
                }
            });
        });
        
        // Add click event listener to refresh button
        const refreshButton = document.getElementById('crm-refresh-gmail');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                // Reset filters and show all emails
                CRMGmailSortFilter.resetFilters();
            });
        }
    },
    
    /**
     * Initialize search functionality
     */
    initSearchFunctionality: function() {
        // Find the search input and button
        const searchInput = document.getElementById('crm-email-search');
        const searchButton = document.getElementById('crm-search-btn');
        
        if (searchInput && searchButton) {
            // Add click event listener to search button
            searchButton.addEventListener('click', function() {
                // Get the search query
                const searchQuery = searchInput.value.trim().toLowerCase();
                
                // Search the emails
                CRMGmailSortFilter.searchEmails(searchQuery);
            });
            
            // Add keypress event listener to search input
            searchInput.addEventListener('keypress', function(e) {
                // If Enter key is pressed
                if (e.key === 'Enter') {
                    // Get the search query
                    const searchQuery = this.value.trim().toLowerCase();
                    
                    // Search the emails
                    CRMGmailSortFilter.searchEmails(searchQuery);
                }
            });
        }
    },
    
    /**
     * Sort emails based on the given criteria
     * @param {string} criteria - The sort criteria (e.g., 'date-desc', 'sender-asc', etc.)
     */
    sortEmails: function(criteria) {
        // Find the email list container
        const emailListContainer = document.querySelector('#email-list-section .list-group');
        if (!emailListContainer) return;
        
        // Get all email items
        const emailItems = Array.from(emailListContainer.querySelectorAll('.list-group-item'));
        
        // Sort the email items based on the criteria
        emailItems.sort((a, b) => {
            switch (criteria) {
                case 'date-desc':
                    // Sort by date (newest first)
                    const dateA = a.querySelector('small.text-muted').textContent;
                    const dateB = b.querySelector('small.text-muted').textContent;
                    return this.compareDates(dateB, dateA); // Reverse order for descending
                
                case 'date-asc':
                    // Sort by date (oldest first)
                    const dateC = a.querySelector('small.text-muted').textContent;
                    const dateD = b.querySelector('small.text-muted').textContent;
                    return this.compareDates(dateC, dateD);
                
                case 'sender-asc':
                    // Sort by sender (A-Z)
                    const senderA = a.querySelector('h6.mb-1').textContent;
                    const senderB = b.querySelector('h6.mb-1').textContent;
                    return senderA.localeCompare(senderB);
                
                case 'sender-desc':
                    // Sort by sender (Z-A)
                    const senderC = a.querySelector('h6.mb-1').textContent;
                    const senderD = b.querySelector('h6.mb-1').textContent;
                    return senderD.localeCompare(senderC);
                
                case 'subject-asc':
                    // Sort by subject (A-Z)
                    const subjectA = a.querySelector('p.mb-1').textContent;
                    const subjectB = b.querySelector('p.mb-1').textContent;
                    return subjectA.localeCompare(subjectB);
                
                case 'subject-desc':
                    // Sort by subject (Z-A)
                    const subjectC = a.querySelector('p.mb-1').textContent;
                    const subjectD = b.querySelector('p.mb-1').textContent;
                    return subjectD.localeCompare(subjectC);
                
                default:
                    return 0;
            }
        });
        
        // Remove all email items from the container
        emailItems.forEach(item => item.remove());
        
        // Add the sorted email items back to the container
        emailItems.forEach(item => emailListContainer.appendChild(item));
        
        console.log(`Emails sorted by ${criteria}`);
    },
    
    /**
     * Filter emails based on the given criteria
     * @param {string} criteria - The filter criteria (e.g., 'all', 'unread', 'read', etc.)
     */
    filterEmails: function(criteria) {
        // Find the email list container
        const emailListContainer = document.querySelector('#email-list-section .list-group');
        if (!emailListContainer) return;
        
        // Get all email items
        const emailItems = emailListContainer.querySelectorAll('.list-group-item');
        
        // Show all emails first
        emailItems.forEach(item => item.style.display = '');
        
        // If criteria is 'all', we're done
        if (criteria === 'all') {
            console.log('Showing all emails');
            return;
        }
        
        // Filter the email items based on the criteria
        emailItems.forEach(item => {
            let shouldShow = false;
            
            switch (criteria) {
                case 'unread':
                    // Show only unread emails
                    shouldShow = item.classList.contains('unread');
                    break;
                
                case 'read':
                    // Show only read emails
                    shouldShow = !item.classList.contains('unread');
                    break;
                
                case 'client':
                    // Show only emails with 'Client' label
                    const clientBadge = item.querySelector('.badge.bg-success');
                    shouldShow = clientBadge && clientBadge.textContent === 'Client';
                    break;
                
                case 'urgent':
                    // Show only emails with 'Urgent' label
                    const urgentBadge = item.querySelector('.badge.bg-danger');
                    shouldShow = urgentBadge && urgentBadge.textContent === 'Urgent';
                    break;
                
                case 'follow-up':
                    // Show only emails with 'Follow-up' label
                    const followUpBadge = item.querySelector('.badge.bg-warning');
                    shouldShow = followUpBadge && followUpBadge.textContent === 'Follow-up';
                    break;
                
                default:
                    shouldShow = true;
            }
            
            // Show or hide the email item
            item.style.display = shouldShow ? '' : 'none';
        });
        
        console.log(`Emails filtered by ${criteria}`);
    },
    
    /**
     * Search emails based on the given query
     * @param {string} query - The search query
     */
    searchEmails: function(query) {
        // If query is empty, show all emails
        if (!query) {
            this.resetFilters();
            return;
        }
        
        // Find the email list container
        const emailListContainer = document.querySelector('#email-list-section .list-group');
        if (!emailListContainer) return;
        
        // Get all email items
        const emailItems = emailListContainer.querySelectorAll('.list-group-item');
        
        // Search the email items
        emailItems.forEach(item => {
            // Get the sender, subject, and content
            const sender = item.querySelector('h6.mb-1').textContent.toLowerCase();
            const subject = item.querySelector('p.mb-1').textContent.toLowerCase();
            const content = item.querySelector('small.text-muted:last-child').textContent.toLowerCase();
            
            // Check if the query matches any of the fields
            const matches = sender.includes(query) || subject.includes(query) || content.includes(query);
            
            // Show or hide the email item
            item.style.display = matches ? '' : 'none';
        });
        
        console.log(`Emails searched for "${query}"`);
    },
    
    /**
     * Reset all filters and show all emails
     */
    resetFilters: function() {
        // Find the email list container
        const emailListContainer = document.querySelector('#email-list-section .list-group');
        if (!emailListContainer) return;
        
        // Get all email items
        const emailItems = emailListContainer.querySelectorAll('.list-group-item');
        
        // Show all emails
        emailItems.forEach(item => item.style.display = '');
        
        // Reset the search input
        const searchInput = document.getElementById('crm-email-search');
        if (searchInput) {
            searchInput.value = '';
        }
        
        // Reset the sort dropdown button text
        const sortDropdown = document.getElementById('crm-sort-dropdown');
        if (sortDropdown) {
            sortDropdown.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
        }
        
        // Reset the filter dropdown button text
        const filterDropdown = document.getElementById('crm-filter-dropdown');
        if (filterDropdown) {
            filterDropdown.innerHTML = '<i class="bi bi-funnel"></i> Filter';
        }
        
        console.log('Filters reset, showing all emails');
    },
    
    /**
     * Compare two date strings for sorting
     * @param {string} dateA - The first date string
     * @param {string} dateB - The second date string
     * @returns {number} - Negative if dateA is earlier, positive if dateA is later, 0 if equal
     */
    compareDates: function(dateA, dateB) {
        // Convert relative date strings to numeric values for comparison
        const getDateValue = (dateStr) => {
            dateStr = dateStr.trim().toLowerCase();
            
            if (dateStr.includes('now') || dateStr.includes('just')) return 0;
            if (dateStr.includes('min')) return parseInt(dateStr) || 1;
            if (dateStr.includes('hour')) return (parseInt(dateStr) || 1) * 60;
            if (dateStr === 'today') return 24 * 60;
            if (dateStr === 'yesterday') return 2 * 24 * 60;
            if (dateStr.includes('day')) return (parseInt(dateStr) || 1) * 24 * 60;
            if (dateStr.includes('week')) return (parseInt(dateStr) || 1) * 7 * 24 * 60;
            if (dateStr.includes('month')) return (parseInt(dateStr) || 1) * 30 * 24 * 60;
            
            // If it's a specific date (e.g., "May 10"), assign a large value
            return 1000 * 24 * 60;
        };
        
        return getDateValue(dateA) - getDateValue(dateB);
    }
};

// Make the functionality available globally
window.CRMGmailSortFilter = CRMGmailSortFilter;
