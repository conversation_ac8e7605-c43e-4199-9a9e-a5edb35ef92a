// Warehouse Management System - Main Entry Point

const express = require('express');
const cors = require('cors');
const app = express();
const port = 3004;

// Import shared features
const sharedFeatures = require('../../SharedFeatures');
const auth = sharedFeatures.auth;
const logger = sharedFeatures.logger.createLogger('WMS');
const google = require('../../SharedFeatures/integrations/google');
const slack = sharedFeatures.slack;

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));

// Initialize integrations
google.initGoogleAPI().catch((err) => {
  logger && logger.error ? logger.error('Failed to initialize Google API', { error: err.message }) : console.error('Failed to initialize Google API', err);
});

// Define routes
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/api/warehouses', (req, res) => {
  res.json({
    status: 'success',
    data: {
      warehouses: [
        { id: 1, name: 'Main Warehouse', location: 'New York', capacity: 10000, utilization: 75 },
        {
          id: 2,
          name: 'West Coast Warehouse',
          location: 'Los Angeles',
          capacity: 8000,
          utilization: 60,
        },
        {
          id: 3,
          name: 'East Coast Warehouse',
          location: 'Boston',
          capacity: 5000,
          utilization: 80,
        },
      ],
    },
  });
});

// Google Drive Integration for Warehouse Documents
app.get('/api/documents', auth.authenticate, async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger.error('Failed to fetch warehouse documents', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Sheets Integration for Inventory Tracking
app.get('/api/inventory/spreadsheet/:spreadsheetId', auth.authenticate, async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Inventory!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger.error('Failed to fetch inventory spreadsheet', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Sample Google Sheets endpoint
app.get('/api/google-sheets/:spreadsheetId', async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Sheets data', { error: error.message }) : console.error('Failed to fetch Google Sheets data', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration for Shipping and Receiving Schedules
app.get('/api/schedules', auth.authenticate, async (req, res) => {
  try {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 14); // Two weeks ahead

    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger.error('Failed to fetch shipping and receiving schedules', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Slack Integration for Warehouse Alerts
app.post('/api/warehouse/alert', auth.authenticate, async (req, res) => {
  try {
    const { warehouseId, warehouseName, issue, priority, channel } = req.body;

    const message = `*WAREHOUSE ALERT*: Issue in ${warehouseName} (ID: ${warehouseId})\nIssue: ${issue}\nPriority: ${priority}`;
    const result = await slack.sendMessage(channel, message);

    res.json({ status: 'success', data: { alerted: true, result } });
  } catch (error) {
    logger.error('Failed to send warehouse alert', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Drive files', { error: error.message }) : console.error('Failed to fetch Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration
app.get('/api/google-calendar/events', async (req, res) => {
  try {
    const startDate = new Date(req.query.startDate || Date.now());
    const endDate = new Date(req.query.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Calendar events', { error: error.message }) : console.error('Failed to fetch Google Calendar events', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/google-gmail/send', async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to send Gmail email', { error: error.message }) : console.error('Failed to send Gmail email', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration
app.post('/api/google-docs/create', async (req, res) => {
  try {
    const { title } = req.body;
    const doc = await google.Docs.createDocument(title);
    res.json({ status: 'success', data: doc });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to create Google Doc', { error: error.message }) : console.error('Failed to create Google Doc', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google-contacts', async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Contacts', { error: error.message }) : console.error('Failed to fetch Google Contacts', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Start server
app.listen(port, () => {
  console.log(`WMS running at http://localhost:${port}`);
  console.log('Connected to IntegrationHub');
});
