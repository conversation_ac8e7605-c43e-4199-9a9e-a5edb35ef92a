// Test script for new Google services (Maps, Analytics, Translate)
const path = require('path');
const fs = require('fs');

// Import the Google integration module
const google = require('./SharedFeatures/integrations/google');

// Test function for Google Maps
async function testGoogleMaps() {
  console.log('\n--- Testing Google Maps Integration ---');
  try {
    // Test geocoding
    console.log('Testing geocoding...');
    const address = '1600 Amphitheatre Parkway, Mountain View, CA';
    const geocodeResult = await google.Maps.geocode(address);
    console.log(`✅ Successfully geocoded address: ${address}`);
    
    // Test directions
    console.log('Testing directions...');
    const origin = 'San Francisco, CA';
    const destination = 'Mountain View, CA';
    const directionsResult = await google.Maps.getDirections(origin, destination);
    console.log(`✅ Successfully retrieved directions from ${origin} to ${destination}`);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing Google Maps integration:', error.message);
    return false;
  }
}

// Test function for Google Analytics
async function testGoogleAnalytics() {
  console.log('\n--- Testing Google Analytics Integration ---');
  try {
    // Test analytics data retrieval
    console.log('Testing analytics data retrieval...');
    const viewId = '123456789'; // Replace with a real view ID
    const startDate = '30daysAgo';
    const endDate = 'today';
    const metrics = ['ga:sessions', 'ga:pageviews'];
    const dimensions = ['ga:date'];
    
    const analyticsResult = await google.Analytics.getData(viewId, startDate, endDate, metrics, dimensions);
    console.log(`✅ Successfully retrieved analytics data for view ID: ${viewId}`);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing Google Analytics integration:', error.message);
    return false;
  }
}

// Test function for Google Translate
async function testGoogleTranslate() {
  console.log('\n--- Testing Google Translate Integration ---');
  try {
    // Test translation
    console.log('Testing translation...');
    const text = 'Hello, world!';
    const targetLanguage = 'es'; // Spanish
    
    const translateResult = await google.Translate.translateText(text, targetLanguage);
    console.log(`✅ Successfully translated text from English to Spanish`);
    
    // Test language detection
    console.log('Testing language detection...');
    const textToDetect = 'Bonjour le monde';
    const detectResult = await google.Translate.translateText(textToDetect, 'en');
    console.log(`✅ Successfully detected language and translated to English`);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing Google Translate integration:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('=== ISA Suite New Google Services Test ===');
  console.log('Testing Google API initialization...');
  
  try {
    await google.initGoogleAPI();
    console.log('✅ Google API initialized successfully!');
    
    // Run tests for each new Google service
    const results = {
      maps: await testGoogleMaps(),
      analytics: await testGoogleAnalytics(),
      translate: await testGoogleTranslate()
    };
    
    // Print summary
    console.log('\n=== Test Summary ===');
    Object.entries(results).forEach(([service, success]) => {
      console.log(`${service}: ${success ? '✅ PASSED' : '❌ FAILED'}`);
    });
    
    const passedCount = Object.values(results).filter(Boolean).length;
    console.log(`\nPassed ${passedCount} out of ${Object.keys(results).length} tests`);
    
  } catch (error) {
    console.error('❌ Error initializing Google API:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the tests
runTests();
