// Materials Requirements Planning System - Main Entry Point

const express = require('express');
const cors = require('cors');
const app = express();
const port = 3002;

// Import shared features
const sharedFeatures = require('../../SharedFeatures');
const auth = sharedFeatures.auth;
const logger = sharedFeatures.logger.createLogger('MRP');
const shopify = sharedFeatures.shopify;
const google = require('../../SharedFeatures/integrations/google');
const slack = sharedFeatures.slack;

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));
// Serve shared files
app.use('/shared', express.static('../shared'));

// Initialize integrations
shopify.initShopifyAPI().catch((err) => {
  logger.error('Failed to initialize Shopify API', { error: err.message });
});

google.initGoogleAPI().catch((err) => {
  logger.error('Failed to initialize Google API', { error: err.message });
});

// Define routes
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/api/inventory', (req, res) => {
  res.json({
    status: 'success',
    data: {
      items: [
        { id: 1, name: 'Raw Material A', quantity: 500, reorderLevel: 100 },
        { id: 2, name: 'Component B', quantity: 350, reorderLevel: 75 },
        { id: 3, name: 'Finished Product C', quantity: 200, reorderLevel: 50 },
      ],
    },
  });
});

// Shopify Product Integration
app.get('/api/shopify/products', auth.authenticate, async (req, res) => {
  try {
    const products = await shopify.Product.list();
    res.json({ status: 'success', data: products });
  } catch (error) {
    logger.error('Failed to fetch Shopify products', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Shopify Inventory Integration
app.get('/api/shopify/inventory/:inventoryItemId', auth.authenticate, async (req, res) => {
  try {
    const { inventoryItemId } = req.params;
    const locationId = req.query.locationId || 'default-location';
    const inventory = await shopify.Inventory.getLevel(inventoryItemId, locationId);
    res.json({ status: 'success', data: inventory });
  } catch (error) {
    logger.error('Failed to fetch Shopify inventory', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Shopify Order Integration
app.get('/api/shopify/orders', auth.authenticate, async (req, res) => {
  try {
    const orders = await shopify.Order.list();
    res.json({ status: 'success', data: orders });
  } catch (error) {
    logger.error('Failed to fetch Shopify orders', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Sheets Integration for Inventory Planning
app.get('/api/planning/spreadsheet/:spreadsheetId', auth.authenticate, async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger.error('Failed to fetch planning spreadsheet', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Sample Google Sheets endpoint
app.get('/api/google-sheets/:spreadsheetId', async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger.error('Failed to fetch Google Sheets data', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Drive files', { error: error.message }) : console.error('Failed to fetch Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration
app.get('/api/google-calendar/events', async (req, res) => {
  try {
    const startDate = new Date(req.query.startDate || Date.now());
    const endDate = new Date(req.query.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Calendar events', { error: error.message }) : console.error('Failed to fetch Google Calendar events', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/google-gmail/send', async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to send Gmail email', { error: error.message }) : console.error('Failed to send Gmail email', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration
app.post('/api/google-docs/create', async (req, res) => {
  try {
    const { title } = req.body;
    const doc = await google.Docs.createDocument(title);
    res.json({ status: 'success', data: doc });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to create Google Doc', { error: error.message }) : console.error('Failed to create Google Doc', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google-contacts', async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Contacts', { error: error.message }) : console.error('Failed to fetch Google Contacts', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Slack Integration for Inventory Alerts
app.post('/api/inventory/alert', auth.authenticate, async (req, res) => {
  try {
    const { itemId, itemName, currentQuantity, reorderLevel, channel } = req.body;

    if (currentQuantity <= reorderLevel) {
      const message = `*INVENTORY ALERT*: ${itemName} (ID: ${itemId}) is low on stock!\nCurrent quantity: ${currentQuantity}\nReorder level: ${reorderLevel}`;
      const result = await slack.sendMessage(channel, message);
      res.json({ status: 'success', data: { alerted: true, result } });
    } else {
      res.json({
        status: 'success',
        data: { alerted: false, message: 'Inventory level is sufficient' },
      });
    }
  } catch (error) {
    logger.error('Failed to send inventory alert', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Start server
app.listen(port, () => {
  console.log(`MRP running at http://localhost:${port}`);
  console.log('Connected to IntegrationHub');
});
