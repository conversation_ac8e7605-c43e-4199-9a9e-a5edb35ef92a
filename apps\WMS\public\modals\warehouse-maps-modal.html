<div class="modal fade isa-modal" id="warehouseMapsModal" tabindex="-1" aria-labelledby="warehouseMapsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                <h5 class="modal-title" id="warehouseMapsModalLabel"><i class="bi bi-geo-alt"></i> Warehouse Maps</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="warehouseMapsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations-content" type="button" role="tab" aria-controls="locations-content" aria-selected="true">Warehouse Locations</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="inventory-locations-tab" data-bs-toggle="tab" data-bs-target="#inventory-locations-content" type="button" role="tab" aria-controls="inventory-locations-content" aria-selected="false">Inventory Map</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="directions-tab" data-bs-toggle="tab" data-bs-target="#directions-content" type="button" role="tab" aria-controls="directions-content" aria-selected="false">Directions</button>
                    </li>
                </ul>
                <div class="tab-content pt-3" id="warehouseMapsTabContent">
                    <!-- Warehouse Locations Tab -->
                    <div class="tab-pane fade show active" id="locations-content" role="tabpanel" aria-labelledby="locations-tab">
                        <div class="row">
                            <div class="col-md-9">
                                <div class="ratio ratio-16x9 mb-3">
                                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h6 class="mb-0">Warehouse Facilities</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="input-group">
                                                <input type="text" class="form-control" placeholder="Search warehouses..." id="warehouse-search">
                                                <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                            </div>
                                        </div>
                                        <div class="list-group" id="warehouse-locations-list">
                                            <!-- This will be populated by the MapsHandler -->
                                            <div class="list-group-item">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <i class="bi bi-building me-2"></i>
                                                        <span>Main Distribution Center</span>
                                                        <div class="small text-muted">123 Logistics Ave, New York</div>
                                                    </div>
                                                    <div>
                                                        <span class="badge bg-primary rounded-pill me-2">Primary</span>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                            <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-signpost-2"></i></button>
                                                            <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Map Tab -->
                    <div class="tab-pane fade" id="inventory-locations-content" role="tabpanel" aria-labelledby="inventory-locations-tab">
                        <div class="row">
                            <div class="col-md-9">
                                <div class="card">
                                    <div class="card-body p-0">
                                        <img src="/img/warehouse-layout.png" alt="Warehouse Layout" class="img-fluid" onerror="this.src='https://via.placeholder.com/800x500?text=Warehouse+Layout'">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h6 class="mb-0">Inventory Zones</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="show-receiving" checked>
                                                <label class="form-check-label" for="show-receiving">Receiving Area</label>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="show-storage" checked>
                                                <label class="form-check-label" for="show-storage">Storage Racks</label>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="show-picking" checked>
                                                <label class="form-check-label" for="show-picking">Picking Zone</label>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="show-packing" checked>
                                                <label class="form-check-label" for="show-packing">Packing Area</label>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="show-shipping" checked>
                                                <label class="form-check-label" for="show-shipping">Shipping Docks</label>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="mb-3">
                                            <label class="form-label">Filter by SKU</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" placeholder="Enter SKU...">
                                                <button class="btn btn-outline-primary" type="button">Find</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Directions Tab -->
                    <div class="tab-pane fade" id="directions-content" role="tabpanel" aria-labelledby="directions-tab">
                        <div class="mb-3">
                            <div class="input-group mb-3">
                                <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>
                                <input type="text" class="form-control" id="directions-start" placeholder="Starting point">
                                <button class="btn btn-outline-secondary" type="button" id="current-location-btn">
                                    <i class="bi bi-geo"></i> Current
                                </button>
                            </div>
                            <div class="input-group mb-3">
                                <span class="input-group-text"><i class="bi bi-geo-alt-fill"></i></span>
                                <input type="text" class="form-control" id="directions-end" placeholder="Warehouse destination">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    Select
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="#" onclick="document.getElementById('directions-end').value='Main Distribution Center'">Main Distribution Center</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="document.getElementById('directions-end').value='West Coast Fulfillment'">West Coast Fulfillment</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="document.getElementById('directions-end').value='Midwest Distribution Hub'">Midwest Distribution Hub</a></li>
                                </ul>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <div class="btn-group" role="group" aria-label="Travel mode">
                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-driving" autocomplete="off" checked>
                                    <label class="btn btn-outline-primary" for="travel-driving"><i class="bi bi-car-front"></i></label>

                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-transit" autocomplete="off">
                                    <label class="btn btn-outline-primary" for="travel-transit"><i class="bi bi-bus-front"></i></label>

                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-walking" autocomplete="off">
                                    <label class="btn btn-outline-primary" for="travel-walking"><i class="bi bi-person-walking"></i></label>

                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-cycling" autocomplete="off">
                                    <label class="btn btn-outline-primary" for="travel-cycling"><i class="bi bi-bicycle"></i></label>
                                </div>
                                <button class="btn btn-primary" id="get-directions-btn">
                                    <i class="bi bi-signpost-2 me-2"></i>Get Directions
                                </button>
                            </div>
                        </div>
                        <div class="ratio ratio-16x9 mb-3">
                            <iframe src="https://www.google.com/maps/embed?pb=!1m28!1m12!1m3!1d48369.59254503551!2d-74.0056!3d40.7128!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!4m13!3e0!4m5!1s0x89c259a9b3117469%3A0xd134e199a405a163!2sEmpire%20State%20Building%2C%20New%20York%2C%20NY!3m2!1d40.748817!2d-73.985428!4m5!1s0x89c25903e47b9657%3A0xd8c988bf42e909d4!2sOne%20World%20Trade%20Center%2C%20New%20York%2C%20NY!3m2!1d40.7127431!2d-74.0133795!5e0!3m2!1sen!2sus!4v1682531529270!5m2!1sen!2sus" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="window.open('https://maps.google.com', '_blank')">
                    <i class="bi bi-box-arrow-up-right me-1"></i>Open in Google Maps
                </button>
            </div>
        </div>
    </div>
</div>
