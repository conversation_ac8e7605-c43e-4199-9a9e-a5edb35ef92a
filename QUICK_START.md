# ISA Suite Quick Start Guide

This guide provides simple instructions to get your ISA Suite up and running quickly.

## Starting the Applications

### Option 1: Service Manager (Recommended)

1. Double-click the "ISA Suite Service Manager" shortcut on your desktop (or run `service-manager.bat`).
2. Select the desired mode:
   - **Production Mode**: For normal business operations with real data.
   - **Sandbox/Training Mode**: For training and testing without affecting production data.
   - **Demo Mode**: For demonstrations with sample data.
3. Choose "Start all applications" from the menu.
4. The Integration Hub will open in your browser automatically.

### Option 2: Single Window Launcher

1. Double-click the "ISA Suite Single Window" shortcut on your desktop (or run `single-window-launcher.bat`).
2. Select the desired mode.
3. All applications will start in a single window.
4. The Integration Hub will open in your browser automatically.

### Option 3: Consolidated Start (No Individual Windows)

1. Run `start-apps-consolidated.bat` from the root directory.
2. Select the desired mode.
3. This will start all applications without opening individual console windows.
4. The Integration Hub will open in your browser automatically.

### Option 4: Traditional Start (Individual Windows)

1. Run `start-apps.bat` from the root directory.
2. This will start all applications with individual console windows.
3. The Integration Hub will open in your browser automatically.

### Option 5: Manual Start

If you prefer to start applications individually:

1. Start the Integration Hub:
```
cd C:\ISASUITE\apps\hub
node index.js
```

2. Start other applications as needed:
```
cd C:\ISASUITE\apps\[APP_FOLDER]
node index.js
```

3. Open the applications in your browser:
- Integration Hub: http://localhost:8000
- Business Management System: http://localhost:3001
- Materials Requirements Planning: http://localhost:3002
- Customer Relationship Management: http://localhost:3003
- Warehouse Management System: http://localhost:3004
- Advanced Planning and Scheduling: http://localhost:3005
- Asset Performance Management: http://localhost:3006
- Project Management System: http://localhost:3007
- Supply Chain Management: http://localhost:3008
- Task Management System: http://localhost:3009

## Integration Hub Dashboard

The Integration Hub now features a comprehensive dashboard that provides:

1. **System Metrics**: Real-time monitoring of CPU usage, memory usage, uptime, and active users
2. **Application Status**: Status monitoring for all ISA Suite applications
3. **Quick Access**: Direct links to all applications

## Integration Features

The ISA Suite includes powerful integration features:

1. **Cross-Application Data**: The Integration Hub provides a unified API endpoint (`/api/cross-app-data`) that combines data from multiple applications
2. **Real-time Notifications**: Applications can send and receive notifications through the Integration Hub
3. **Status Monitoring**: The Integration Hub continuously monitors the status of all applications
4. **Shared Authentication**: All applications use a shared authentication system
5. **Unified UI Components**: Applications share common UI components for a consistent user experience
6. **Unified Reporting**: Generate reports that combine data from multiple applications (`/api/unified-reports/{reportType}`)
7. **AI-powered Analytics**: Advanced analytics using artificial intelligence to provide insights and recommendations:
   - Inventory Optimization (`/api/ai-analytics/inventory-optimization`)
   - Predictive Maintenance (`/api/ai-analytics/maintenance-prediction`)
   - Sales Forecasting (`/api/ai-analytics/sales-forecast`)
   - Business Intelligence (`/api/ai-analytics/business-intelligence`)
8. **Cross-Application Workflows**: Create workflows that span multiple applications:
   - Order-to-Cash Workflow (`/api/workflows/order-to-cash`): Integrates CRM, BMS, MRP, WMS, and TM to process customer orders
   - Procurement Workflow (`/api/workflows/procurement`): Integrates SCM, BMS, MRP, WMS, and TM to manage purchase orders

## Available Applications

The following applications are currently available:

1. **Integration Hub** (port 8000): Central connection point for all systems
2. **Business Management System (BMS)** (port 3001): Core business operations and financial tracking
3. **Materials Requirements Planning (MRP)** (port 3002): Inventory and materials planning
4. **Customer Relationship Management (CRM)** (port 3003): Customer and sales management
5. **Warehouse Management System (WMS)** (port 3004): Warehouse operations and inventory control
6. **Advanced Planning and Scheduling (APS)** (port 3005): Production scheduling and resource allocation
7. **Asset Performance Management (APM)** (port 3006): Asset monitoring and maintenance
8. **Project Management System (PMS)** (port 3007): Project planning and tracking
9. **Supply Chain Management (SCM)** (port 3008): Supply chain operations and logistics
10. **Task Management System (TM)** (port 3009): Task tracking and team management

## Troubleshooting

If you encounter any issues:

1. Make sure the portable Node.js is in the correct location (C:\ISASUITE\PortableNodeJS)
2. Check that all required dependencies are installed
3. Verify that no other applications are using the same ports (8000, 3001-3009)
4. Check the application logs in the C:\ISASUITE\logs directory
5. Use the Service Manager to check application status
6. Try stopping all applications and starting them again
7. Try running in a different mode (e.g., sandbox mode instead of production)
8. Check if your firewall is blocking any of the applications
9. Make sure you have sufficient permissions to run the applications
10. If using the installer, try running it as administrator

## Performance Monitoring

The Business Management System includes advanced performance monitoring features:

1. **Server-Side Performance**: Monitors API response times and system resource usage
2. **Client-Side Performance**: Tracks page load times and browser performance
3. **Memory Leak Detection**: Identifies potential memory leaks in the application

## Next Steps

Once you have the applications running, you can:

1. **Explore the Integration Hub dashboard** to monitor all systems
2. **Use the Business Management System** for core business operations
3. **Use the Materials Requirements Planning system** for inventory management
4. **Use the CRM application** to manage customer relationships
5. **Use the Warehouse Management System** for warehouse operations
6. **Use the Advanced Planning and Scheduling system** for production scheduling
7. **Use the Asset Performance Management system** for asset monitoring
8. **Use the Project Management System** for project planning and tracking
9. **Use the Supply Chain Management system** for supply chain operations and logistics
10. **Use the Task Management System** for task tracking and team management
11. **Explore the AI-powered analytics** for insights and recommendations

## Application Modes

The ISA Suite supports three different modes:

1. **Production Mode**:
   - For normal business operations with real data
   - Uses production databases and services
   - All features are enabled
   - Optimized for performance and reliability
   - Suitable for day-to-day business operations

2. **Sandbox/Training Mode**:
   - For training and testing without affecting production data
   - Uses separate databases from production
   - All features are enabled, but with sample or copied data
   - Suitable for training new users or testing new features
   - Changes made in this mode do not affect production data

3. **Demo Mode**:
   - For demonstrations and presentations
   - Uses pre-populated sample data
   - All features are enabled with simulated data
   - Optimized for showcasing features and capabilities
   - Automatically resets data periodically
   - Suitable for demonstrations to clients or stakeholders

You can switch between modes using the Service Manager or by specifying the mode when starting the applications.

## Advanced Features

The ISA Suite includes several advanced features:

1. **Performance Monitoring**: The Business Management System includes advanced performance monitoring
2. **Real-time Data Synchronization**: All applications synchronize data in real-time through the Integration Hub
3. **Cross-Application Workflows**: Create workflows that span multiple applications:
   - Order-to-Cash Workflow: Streamlines the process from customer order to fulfillment
   - Procurement Workflow: Automates the purchasing process from requisition to receipt
4. **Unified Reporting**: Generate reports that combine data from multiple applications
5. **AI-powered Analytics**: Advanced analytics using artificial intelligence:
   - Inventory Optimization: Analyzes inventory levels and suggests optimizations
   - Predictive Maintenance: Predicts equipment failures before they occur
   - Sales Forecasting: Predicts future sales based on historical data and market trends
   - Business Intelligence: Provides insights into business performance and opportunities
6. **Mobile Access**: All applications are accessible from mobile devices
7. **Real-time Notifications**: Get notified of important events across all applications
8. **Multi-Mode Operation**: Run in production, sandbox/training, or demo mode
9. **Consolidated Launcher**: Run all applications without individual console windows
10. **Easy Installation**: Install on any laptop with a simple installer

For more detailed information, refer to the full documentation in the `docs` directory or see the [Installation Guide](INSTALLATION_GUIDE.md).
