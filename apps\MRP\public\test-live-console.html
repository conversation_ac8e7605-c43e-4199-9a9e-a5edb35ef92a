<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Modal Live Test Console</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-console {
            background-color: #1e1e1e;
            color: #fff;
            font-family: 'Courier New', monospace;
            border-radius: 5px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
        }
        .btn-test {
            margin: 5px;
        }
        .status-good { color: #28a745; }
        .status-warn { color: #ffc107; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Gmail Modal Live Test Console</h1>
        <p class="text-muted">Test the Gmail modal dropdown functionality in real-time</p>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Quick Tests</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary btn-test" onclick="runQuickDebug()">
                            🔧 Quick Debug Info
                        </button>
                        <button class="btn btn-success btn-test" onclick="runDropdownTest()">
                            📋 Test Dropdowns
                        </button>
                        <button class="btn btn-info btn-test" onclick="runUtilityTest()">
                            ⚙️ Test Utilities
                        </button>
                        <button class="btn btn-warning btn-test" onclick="runFullTest()">
                            🚀 Full Test Suite
                        </button>
                        <button class="btn btn-secondary btn-test" onclick="clearConsole()">
                            🗑️ Clear Console
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Manual Tests</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary btn-test" onclick="testSortDropdown()">
                            Sort Dropdown
                        </button>
                        <button class="btn btn-outline-primary btn-test" onclick="testFilterDropdown()">
                            Filter Dropdown
                        </button>
                        <button class="btn btn-outline-success btn-test" onclick="testSearch()">
                            Search Function
                        </button>
                        <button class="btn btn-outline-info btn-test" onclick="testNotification()">
                            Show Notification
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <h5>Test Console Output</h5>
                <small class="text-muted">Real-time test results appear here</small>
            </div>
            <div class="card-body">
                <div id="test-console" class="test-console"></div>
            </div>
        </div>
        
        <div class="alert alert-info mt-3">
            <strong>Instructions:</strong>
            <ol>
                <li>Open the MRP application in another tab: <a href="http://localhost:3002" target="_blank">http://localhost:3002</a></li>
                <li>Open the Gmail modal in the MRP application</li>
                <li>Return to this tab and run the tests</li>
                <li>Watch the console output for test results</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="test-gmail-modal-live.js"></script>
    <script>
        // Console output management
        const console = window.console;
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        const testConsole = document.getElementById('test-console');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            
            let cssClass = '';
            let prefix = '';
            
            switch(type) {
                case 'error':
                    cssClass = 'status-error';
                    prefix = '❌ ';
                    break;
                case 'warn':
                    cssClass = 'status-warn';
                    prefix = '⚠️ ';
                    break;
                case 'success':
                    cssClass = 'status-good';
                    prefix = '✅ ';
                    break;
                default:
                    prefix = '📝 ';
            }
            
            div.className = cssClass;
            div.innerHTML = `[${timestamp}] ${prefix}${message}`;
            testConsole.appendChild(div);
            testConsole.scrollTop = testConsole.scrollHeight;
        }
        
        // Override console methods to show in our console
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        // Test functions
        function runQuickDebug() {
            addToConsole('Starting quick debug...', 'log');
            if (typeof testGmailModal !== 'undefined') {
                testGmailModal.debugInfo();
            } else {
                addToConsole('Test suite not loaded', 'error');
            }
        }
        
        function runDropdownTest() {
            addToConsole('Testing dropdown functionality...', 'log');
            if (typeof testGmailModal !== 'undefined') {
                testGmailModal.testAllDropdowns();
            } else {
                addToConsole('Test suite not loaded', 'error');
            }
        }
        
        function runUtilityTest() {
            addToConsole('Testing utility functions...', 'log');
            if (typeof testGmailModal !== 'undefined') {
                testGmailModal.testUtilityFunctions();
            } else {
                addToConsole('Test suite not loaded', 'error');
            }
        }
        
        function runFullTest() {
            addToConsole('Starting full test suite...', 'log');
            if (typeof testGmailModal !== 'undefined') {
                testGmailModal.runAllTests();
            } else {
                addToConsole('Test suite not loaded', 'error');
            }
        }
        
        function testSortDropdown() {
            addToConsole('Testing sort dropdown click...', 'log');
            if (typeof testGmailModal !== 'undefined') {
                testGmailModal.testManualDropdownClick('sort-dropdown');
            } else {
                addToConsole('Test suite not loaded', 'error');
            }
        }
        
        function testFilterDropdown() {
            addToConsole('Testing filter dropdown click...', 'log');
            if (typeof testGmailModal !== 'undefined') {
                testGmailModal.testManualDropdownClick('filter-dropdown');
            } else {
                addToConsole('Test suite not loaded', 'error');
            }
        }
        
        function testSearch() {
            addToConsole('Testing search functionality...', 'log');
            if (typeof testGmailModal !== 'undefined') {
                testGmailModal.testSearchFunctionality();
            } else {
                addToConsole('Test suite not loaded', 'error');
            }
        }
        
        function testNotification() {
            addToConsole('Testing notification system...', 'log');
            try {
                if (typeof showNotification === 'function') {
                    showNotification('Test notification from live console!');
                    addToConsole('Notification sent successfully', 'success');
                } else {
                    addToConsole('showNotification function not found', 'error');
                }
            } catch (error) {
                addToConsole('Error sending notification: ' + error.message, 'error');
            }
        }
        
        function clearConsole() {
            testConsole.innerHTML = '';
            addToConsole('Console cleared', 'log');
        }
        
        // Initial setup
        addToConsole('Gmail Modal Live Test Console initialized', 'success');
        addToConsole('Open Gmail modal in MRP application, then run tests', 'log');
    </script>
</body>
</html>
