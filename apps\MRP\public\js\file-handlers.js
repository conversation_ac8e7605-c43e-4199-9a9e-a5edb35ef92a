/**
 * File Handlers for MRP Application
 * Handles all file-related operations like view, download, share, and delete
 * for attachments and Google integrations
 */

class FileHandlers {
    constructor() {
        this.currentFile = null;
        this.debug = true;
    }

    /**
     * Initialize the handler
     */
    init() {
        this.log('Initializing File Handlers');
        this.setupAttachmentsHandlers();
        this.setupGoogleIntegrationHandlers();
        this.setupModalOpeners();
    }

    /**
     * Set up modal openers for sidebar links
     */
    setupModalOpeners() {
        document.querySelectorAll('[data-bs-toggle="modal"]').forEach(element => {
            element.addEventListener('click', (event) => {
                const targetModal = element.getAttribute('data-bs-target');
                if (targetModal) {
                    const modalEl = document.querySelector(targetModal);
                    if (modalEl) {
                        // Clean up any existing modals
                        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                            backdrop.remove();
                        });
                        
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';

                        // Open the new modal
                        try {
                            const modal = new bootstrap.Modal(modalEl);
                            modal.show();
                        } catch (err) {
                            console.error(`Error opening modal ${targetModal}:`, err);
                        }
                    }
                }
            });
        });

        // Make sure the attachments link in sidebar works
        const attachmentsLink = document.querySelector('a[href="#attachments"]');
        if (attachmentsLink) {
            attachmentsLink.addEventListener('click', (event) => {
                event.preventDefault();
                const attachmentsModal = document.getElementById('attachmentsModal');
                if (attachmentsModal) {
                    // Clean up any existing modals
                    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                        backdrop.remove();
                    });
                    
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';

                    try {
                        const modal = new bootstrap.Modal(attachmentsModal);
                        modal.show();
                    } catch (err) {
                        console.error('Error opening attachments modal:', err);
                    }
                }
            });
        }
    }

    /**
     * Set up handlers for attachment actions
     */
    setupAttachmentsHandlers() {
        // Delegate event handling for attachment actions
        document.addEventListener('click', (event) => {
            // View action
            if (event.target.closest('.file-action-view')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    event.preventDefault();
                    const filename = fileItem.getAttribute('data-filename');
                    const fileType = fileItem.getAttribute('data-filetype') || this.getFileTypeFromName(filename);
                    this.viewFile(filename, fileType);
                }
            }
            
            // Download action
            if (event.target.closest('.file-action-download')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    event.preventDefault();
                    const filename = fileItem.getAttribute('data-filename');
                    this.downloadFile(filename);
                }
            }
            
            // Share action
            if (event.target.closest('.file-action-share')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    event.preventDefault();
                    const filename = fileItem.getAttribute('data-filename');
                    this.shareFile(filename);
                }
            }
            
            // Delete action
            if (event.target.closest('.file-action-delete')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    event.preventDefault();
                    const filename = fileItem.getAttribute('data-filename');
                    this.deleteFile(filename);
                }
            }
        });
    }

    /**
     * Set up handlers for Google integration actions
     */
    setupGoogleIntegrationHandlers() {
        // Delegate event handling for Google file actions
        document.addEventListener('click', (event) => {
            // View Google item
            if (event.target.closest('[onclick*="viewGoogleItem"]')) {
                const button = event.target.closest('[onclick*="viewGoogleItem"]');
                if (button) {
                    const onclickAttr = button.getAttribute('onclick');
                    const match = onclickAttr.match(/viewGoogleItem\('([^']+)',\s*'([^']+)',\s*'([^']+)'\)/);
                    if (match) {
                        event.preventDefault();
                        const [_, app, type, itemId] = match;
                        this.viewGoogleItem(app, type, itemId);
                    }
                }
            }
            
            // Download Google item
            if (event.target.closest('[onclick*="downloadGoogleItem"]')) {
                const button = event.target.closest('[onclick*="downloadGoogleItem"]');
                if (button) {
                    const onclickAttr = button.getAttribute('onclick');
                    const match = onclickAttr.match(/downloadGoogleItem\('([^']+)',\s*'([^']+)',\s*'([^']+)'\)/);
                    if (match) {
                        event.preventDefault();
                        const [_, app, type, itemId] = match;
                        this.downloadGoogleItem(app, type, itemId);
                    }
                }
            }
            
            // Share Google item
            if (event.target.closest('[onclick*="shareGoogleItem"]')) {
                const button = event.target.closest('[onclick*="shareGoogleItem"]');
                if (button) {
                    const onclickAttr = button.getAttribute('onclick');
                    const match = onclickAttr.match(/shareGoogleItem\('([^']+)',\s*'([^']+)',\s*'([^']+)'\)/);
                    if (match) {
                        event.preventDefault();
                        const [_, app, type, itemId] = match;
                        this.shareGoogleItem(app, type, itemId);
                    }
                }
            }
            
            // Delete Google item
            if (event.target.closest('[onclick*="deleteGoogleItem"]')) {
                const button = event.target.closest('[onclick*="deleteGoogleItem"]');
                if (button) {
                    const onclickAttr = button.getAttribute('onclick');
                    const match = onclickAttr.match(/deleteGoogleItem\('([^']+)',\s*'([^']+)',\s*'([^']+)'\)/);
                    if (match) {
                        event.preventDefault();
                        const [_, app, type, itemId] = match;
                        this.deleteGoogleItem(app, type, itemId);
                    }
                }
            }
        });

        // Replace window functions with our handlers
        window.viewGoogleItem = (app, type, itemId) => this.viewGoogleItem(app, type, itemId);
        window.downloadGoogleItem = (app, type, itemId) => this.downloadGoogleItem(app, type, itemId);
        window.shareGoogleItem = (app, type, itemId) => this.shareGoogleItem(app, type, itemId);
        window.deleteGoogleItem = (app, type, itemId) => this.deleteGoogleItem(app, type, itemId);
    }

    /**
     * View a file
     * @param {string} filename - Name of the file
     * @param {string} fileType - Type of file
     */
    viewFile(filename, fileType) {
        this.log(`Viewing file: ${filename} (${fileType})`);
        
        // Set current file for other operations
        this.currentFile = {
            name: filename,
            type: fileType
        };
        
        // Get or create file viewer modal
        let fileViewerModal = document.getElementById('fileViewerModal');
        
        if (!fileViewerModal) {
            this.createFileViewerModal();
            fileViewerModal = document.getElementById('fileViewerModal');
        }
        
        // Update modal content
        const fileTitle = document.getElementById('fileViewerTitle');
        const fileContent = document.getElementById('fileViewerContent');
        
        if (fileTitle && fileContent) {
            const iconClass = this.getFileIconClass(fileType);
            fileTitle.innerHTML = `<i class="bi ${iconClass}"></i> ${filename}`;
            fileContent.innerHTML = this.getPreviewContent(filename, fileType);
            
            // Show the modal
            try {
                let modal = bootstrap.Modal.getInstance(fileViewerModal);
                if (!modal) {
                    modal = new bootstrap.Modal(fileViewerModal);
                }
                modal.show();
            } catch (err) {
                this.log('Error showing file viewer modal:', err);
                alert('Could not open file viewer. Please try again.');
            }
        } else {
            this.log('File viewer elements not found');
            alert('File viewer is not available. Please try again later.');
        }
    }

    /**
     * Download a file
     * @param {string} filename - Name of the file to download
     */
    downloadFile(filename) {
        this.log(`Downloading file: ${filename}`);
        alert(`Downloading ${filename}...`);
        
        // In a real app, this would trigger an actual download
        // For demo purposes, we just show an alert
    }

    /**
     * Share a file
     * @param {string} filename - Name of the file to share
     */
    shareFile(filename) {
        this.log(`Sharing file: ${filename}`);
        const email = prompt(`Enter email address to share "${filename}" with:`);
        if (email) {
            alert(`${filename} has been shared with ${email}`);
        }
    }

    /**
     * Delete a file
     * @param {string} filename - Name of the file to delete
     */
    deleteFile(filename) {
        this.log(`Deleting file: ${filename}`);
        if (confirm(`Are you sure you want to delete "${filename}"?`)) {
            alert(`${filename} has been deleted`);
            
            // In a real app, this would remove the file from the server
            // For demo purposes, we'll remove the element from the DOM
            const fileItems = document.querySelectorAll(`[data-filename="${filename}"]`);
            fileItems.forEach(item => {
                item.remove();
            });
            
            // Close the file viewer if this is the current file
            if (this.currentFile && this.currentFile.name === filename) {
                const fileViewerModal = document.getElementById('fileViewerModal');
                if (fileViewerModal) {
                    const modalInstance = bootstrap.Modal.getInstance(fileViewerModal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
            }
        }
    }

    /**
     * View a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @param {string} itemId - Item identifier
     */
    viewGoogleItem(app, type, itemId) {
        this.log(`Viewing Google item: ${app} ${type} ${itemId}`);
        
        // Set current item for other operations
        this.currentGoogleItem = {
            app: app,
            type: type,
            id: itemId
        };
        
        // Hide any open modals
        const modals = ['driveModal', 'docsModal', 'sheetsModal', 'calendarModal', 'gmailModal', 'mapsModal'];
        modals.forEach(modalId => {
            const modalEl = document.getElementById(modalId);
            if (modalEl) {
                const instance = bootstrap.Modal.getInstance(modalEl);
                if (instance) {
                    instance.hide();
                }
            }
        });
        
        // Clean up modal artifacts
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });
        
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
        
        // Get or create the file viewer modal
        let fileViewerModal = document.getElementById('fileViewerModal');
        
        if (!fileViewerModal) {
            this.createFileViewerModal();
            fileViewerModal = document.getElementById('fileViewerModal');
        }
        
        // Update modal content
        const fileTitle = document.getElementById('fileViewerTitle');
        const fileContent = document.getElementById('fileViewerContent');
        
        if (fileTitle && fileContent) {
            const iconClass = this.getGoogleIconClass(app, type);
            const readableName = itemId.replace(/_/g, ' ');
            fileTitle.innerHTML = `<i class="bi ${iconClass}"></i> ${readableName}`;
            fileContent.innerHTML = this.getGoogleItemPreviewContent(app, type, itemId);
            
            // Store current item for future actions
            window.currentGoogleItem = this.currentGoogleItem;
            
            try {
                let modal = bootstrap.Modal.getInstance(fileViewerModal);
                if (!modal) {
                    modal = new bootstrap.Modal(fileViewerModal);
                }
                
                // Show the modal
                modal.show();
            } catch (err) {
                this.log('Error showing file viewer modal:', err);
                alert('Could not open file viewer. Please try again.');
            }
        } else {
            this.log('File viewer elements not found');
            alert('File viewer is not available. Please try again later.');
        }
    }

    /**
     * Download a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @param {string} itemId - Item identifier
     */
    downloadGoogleItem(app, type, itemId) {
        this.log(`Downloading Google item: ${app} ${type} ${itemId}`);
        
        // In a real app, this would trigger an API call to download the item
        // For demo purposes, we just show an alert
        const readableName = itemId.replace(/_/g, ' ');
        alert(`Download started for ${readableName}`);
    }

    /**
     * Share a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @param {string} itemId - Item identifier
     */
    shareGoogleItem(app, type, itemId) {
        this.log(`Sharing Google item: ${app} ${type} ${itemId}`);
        
        const readableName = itemId.replace(/_/g, ' ');
        const email = prompt(`Enter email address to share "${readableName}" with:`);
        
        if (email) {
            // In a real app, this would trigger an API call to share the item
            // For demo purposes, we just show an alert
            alert(`${readableName} has been shared with ${email}`);
        }
    }

    /**
     * Delete a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @param {string} itemId - Item identifier
     */
    deleteGoogleItem(app, type, itemId) {
        this.log(`Deleting Google item: ${app} ${type} ${itemId}`);
        
        const readableName = itemId.replace(/_/g, ' ');
        if (confirm(`Are you sure you want to delete "${readableName}"?`)) {
            // In a real app, this would trigger an API call to delete the item
            // For demo purposes, we just show an alert
            alert(`${readableName} has been deleted`);
            
            // Close the file viewer if viewing this item
            if (this.currentGoogleItem && this.currentGoogleItem.id === itemId) {
                const fileViewerModal = document.getElementById('fileViewerModal');
                if (fileViewerModal) {
                    const modalInstance = bootstrap.Modal.getInstance(fileViewerModal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
            }
            
            // Remove the element from the DOM if possible
            const listItems = document.querySelectorAll(`.list-group-item [onclick*="${itemId}"]`);
            listItems.forEach(item => {
                const listItem = item.closest('.list-group-item');
                if (listItem) {
                    listItem.remove();
                }
            });
        }
    }

    /**
     * Get file type from filename
     * @param {string} filename - Name of file
     * @returns {string} File type
     */
    getFileTypeFromName(filename) {
        if (!filename) return 'unknown';
        
        const extension = filename.split('.').pop().toLowerCase();
        
        switch(extension) {
            case 'pdf':
                return 'pdf';
            case 'doc':
            case 'docx':
            case 'txt':
            case 'rtf':
                return 'document';
            case 'xls':
            case 'xlsx':
            case 'csv':
                return 'spreadsheet';
            case 'ppt':
            case 'pptx':
                return 'presentation';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'svg':
                return 'image';
            case 'zip':
            case 'rar':
            case '7z':
            case 'tar':
            case 'gz':
                return 'archive';
            default:
                return 'unknown';
        }
    }

    /**
     * Get icon class for a file
     * @param {string} fileType - Type of file
     * @returns {string} Bootstrap icon class
     */
    getFileIconClass(fileType) {
        switch(fileType) {
            case 'pdf':
                return 'bi-file-earmark-pdf text-danger';
            case 'document':
                return 'bi-file-earmark-text text-primary';
            case 'spreadsheet':
                return 'bi-file-earmark-spreadsheet text-success';
            case 'presentation':
                return 'bi-file-earmark-slides text-warning';
            case 'image':
                return 'bi-file-earmark-image text-info';
            case 'archive':
                return 'bi-file-earmark-zip text-secondary';
            default:
                return 'bi-file-earmark text-secondary';
        }
    }

    /**
     * Get icon class for a Google item
     * @param {string} app - The Google app
     * @param {string} type - The item type
     * @returns {string} Bootstrap icon class
     */
    getGoogleIconClass(app, type) {
        if (app === 'drive') {
            switch(type) {
                case 'folder': return 'bi-folder-fill text-primary';
                case 'pdf': return 'bi-file-earmark-pdf text-danger';
                default: return 'bi-file-earmark text-secondary';
            }
        } else if (app === 'docs') {
            return 'bi-file-earmark-text text-primary';
        } else if (app === 'sheets') {
            return 'bi-file-earmark-spreadsheet text-success';
        } else if (app === 'slides') {
            return 'bi-file-earmark-slides text-warning';
        } else {
            return 'bi-file-earmark text-secondary';
        }
    }

    /**
     * Get preview content for a file
     * @param {string} filename - Name of file
     * @param {string} fileType - Type of file
     * @returns {string} HTML content for preview
     */
    getPreviewContent(filename, fileType) {
        switch(fileType) {
            case 'pdf':
                return `<div class="text-center p-5">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 64px;"></i>
                    <h4 class="mt-3">${filename}</h4>
                    <div class="border p-3 mt-3 text-start bg-light">
                        <h5>PDF Preview</h5>
                        <p>PDF preview would be displayed here in a production environment.</p>
                        <p>For this demo, we're just showing this placeholder.</p>
                    </div>
                </div>`;
                
            case 'document':
                return `<div class="border p-4 bg-light h-100">
                    <h4 class="mb-4">${filename}</h4>
                    <hr>
                    <p>Document content would be displayed here in a production environment.</p>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed vitae nisi eget nunc ultricies 
                    aliquet. Sed vitae nisi eget nunc ultricies aliquet.</p>
                    <p>Proin ac magna eu quam faucibus tempus id at purus. Sed vitae nisi eget nunc ultricies 
                    aliquet. Sed vitae nisi eget nunc ultricies aliquet.</p>
                </div>`;
                
            case 'spreadsheet':
                return `<div class="border p-3 bg-light h-100">
                    <h4 class="mb-3">${filename}</h4>
                    <hr>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Item ID</th>
                                    <th>Product Name</th>
                                    <th>Quantity</th>
                                    <th>Location</th>
                                    <th>Last Updated</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>MRP-1001</td>
                                    <td>Product A</td>
                                    <td>250</td>
                                    <td>A-12-03</td>
                                    <td>2024-05-10</td>
                                </tr>
                                <tr>
                                    <td>MRP-1002</td>
                                    <td>Product B</td>
                                    <td>175</td>
                                    <td>B-05-11</td>
                                    <td>2024-05-09</td>
                                </tr>
                                <tr>
                                    <td>MRP-1003</td>
                                    <td>Product C</td>
                                    <td>340</td>
                                    <td>A-14-07</td>
                                    <td>2024-05-08</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>`;
                
            case 'image':
                return `<div class="text-center p-3">
                    <h4 class="mb-3">${filename}</h4>
                    <img src="https://via.placeholder.com/800x600.png?text=MRP+Image" 
                        class="img-fluid border" alt="${filename}">
                </div>`;
                
            default:
                return `<div class="alert alert-info p-5">
                    <h4 class="alert-heading">${filename}</h4>
                    <p>Preview not available for this file type.</p>
                    <hr>
                    <p class="mb-0">Please download the file to view its contents.</p>
                </div>`;
        }
    }

    /**
     * Get preview content for a Google item
     * @param {string} app - The Google app
     * @param {string} type - The item type
     * @param {string} itemId - The item ID
     * @returns {string} HTML content for preview
     */
    getGoogleItemPreviewContent(app, type, itemId) {
        const readableName = itemId.replace(/_/g, ' ');
        
        if (app === 'drive') {
            if (type === 'folder') {
                return `<div class="text-center">
                    <i class="bi bi-folder-fill text-primary" style="font-size: 64px;"></i>
                    <h4 class="mt-3">${readableName}</h4>
                    <div class="list-group mt-4">
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-pdf text-danger me-3"></i>
                                    <div>
                                        <h6 class="mb-1">Document 1.pdf</h6>
                                        <small>1.2 MB - Last updated: Yesterday</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="fileHandlers.viewGoogleItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="fileHandlers.downloadGoogleItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="fileHandlers.shareGoogleItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="fileHandlers.deleteGoogleItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-3"></i>
                                    <div>
                                        <h6 class="mb-1">Document 2.docx</h6>
                                        <small>0.8 MB - Last updated: 2 days ago</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="fileHandlers.viewGoogleItem('docs', 'document', 'Document_2.docx')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="fileHandlers.downloadGoogleItem('docs', 'document', 'Document_2.docx')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="fileHandlers.shareGoogleItem('docs', 'document', 'Document_2.docx')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="fileHandlers.deleteGoogleItem('docs', 'document', 'Document_2.docx')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;
            } else if (type === 'pdf') {
                return `<div class="text-center p-4">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 64px;"></i>
                    <h4 class="mt-3">${readableName}</h4>
                    <div class="border p-3 mt-4 text-start bg-light">
                        <h5>PDF Preview</h5>
                        <p>PDF preview would be displayed here in a production environment.</p>
                        <p>For this demo, we're just showing this placeholder.</p>
                    </div>
                </div>`;
            }
        } else if (app === 'docs') {
            return `<div class="border p-4 bg-light h-100">
                <h4 class="mb-4">${readableName}</h4>
                <hr>
                <p>Document content would be displayed here in a production environment.</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed vitae nisi eget nunc ultricies 
                aliquet. Sed vitae nisi eget nunc ultricies aliquet.</p>
                <p>Proin ac magna eu quam faucibus tempus id at purus. Sed vitae nisi eget nunc ultricies 
                aliquet. Sed vitae nisi eget nunc ultricies aliquet.</p>
            </div>`;
        } else if (app === 'sheets') {
            return `<div class="border p-3 bg-light h-100">
                <h4 class="mb-3">${readableName}</h4>
                <hr>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Product</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>MRP-1001</td>
                                <td>Widget A</td>
                                <td>250</td>
                                <td>$10.00</td>
                                <td>$2,500.00</td>
                            </tr>
                            <tr>
                                <td>MRP-1002</td>
                                <td>Widget B</td>
                                <td>175</td>
                                <td>$15.00</td>
                                <td>$2,625.00</td>
                            </tr>
                            <tr>
                                <td>MRP-1003</td>
                                <td>Widget C</td>
                                <td>340</td>
                                <td>$8.50</td>
                                <td>$2,890.00</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>`;
        } else if (app === 'slides') {
            return `<div class="text-center">
                <i class="bi bi-file-earmark-slides text-warning" style="font-size: 64px;"></i>
                <h4 class="mt-3">${readableName}</h4>
                <div class="border p-3 mt-4 text-start bg-light">
                    <h5>Presentation Preview</h5>
                    <p>Presentation preview would be displayed here in a production environment.</p>
                    <p>For this demo, we're just showing this placeholder.</p>
                </div>
            </div>`;
        }
        
        return `<div class="alert alert-info p-5">
            <h4 class="alert-heading">${readableName}</h4>
            <p>Preview not available for this file type.</p>
            <hr>
            <p class="mb-0">Please download the file to view its contents.</p>
        </div>`;
    }

    /**
     * Create the file viewer modal
     */
    createFileViewerModal() {
        if (document.getElementById('fileViewerModal')) {
            return;
        }
        
        const modalHtml = `
            <div class="modal fade" id="fileViewerModal" tabindex="-1" aria-labelledby="fileViewerModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="fileViewerTitle"></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="fileViewerContent" style="min-height: 400px; overflow: auto;"></div>
                        </div>
                        <div class="modal-footer">
                            <div class="d-flex justify-content-start gap-2 me-auto">
                                <button type="button" class="btn btn-outline-success" id="download-current-file">
                                    <i class="bi bi-download me-1"></i> Download
                                </button>
                                <button type="button" class="btn btn-outline-info" id="share-current-file">
                                    <i class="bi bi-share me-1"></i> Share
                                </button>
                                <button type="button" class="btn btn-outline-danger" id="delete-current-file">
                                    <i class="bi bi-trash me-1"></i> Delete
                                </button>
                            </div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = modalHtml;
        document.body.appendChild(tempDiv.firstElementChild);
        
        // Add event listeners to the modal buttons
        document.getElementById('download-current-file').addEventListener('click', () => {
            if (this.currentFile) {
                this.downloadFile(this.currentFile.name);
            } else if (this.currentGoogleItem) {
                this.downloadGoogleItem(
                    this.currentGoogleItem.app,
                    this.currentGoogleItem.type,
                    this.currentGoogleItem.id
                );
            }
        });
        
        document.getElementById('share-current-file').addEventListener('click', () => {
            if (this.currentFile) {
                this.shareFile(this.currentFile.name);
            } else if (this.currentGoogleItem) {
                this.shareGoogleItem(
                    this.currentGoogleItem.app,
                    this.currentGoogleItem.type,
                    this.currentGoogleItem.id
                );
            }
        });
        
        document.getElementById('delete-current-file').addEventListener('click', () => {
            if (this.currentFile) {
                this.deleteFile(this.currentFile.name);
            } else if (this.currentGoogleItem) {
                this.deleteGoogleItem(
                    this.currentGoogleItem.app,
                    this.currentGoogleItem.type,
                    this.currentGoogleItem.id
                );
            }
        });
    }

    /**
     * Log a message to console if debug mode is enabled
     */
    log(...args) {
        if (this.debug) {
            console.log('FileHandlers:', ...args);
        }
    }
}

// Create and initialize the file handlers
const fileHandlers = new FileHandlers();
document.addEventListener('DOMContentLoaded', () => {
    fileHandlers.init();
});
