<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Dropdowns Live Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .instruction {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .clear-results-button {
            background: #ff9800;
        }
        .clear-results-button:hover {
            background: #e68900;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #ffeaa7;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .mrp-link {
            color: #2196f3;
        }
    </style>
</head>
<body>
    <h1>Gmail Dropdowns Live Test</h1>
    
            <ul>
                <li><strong>Open the MRP Application:</strong> <a href="http://localhost:3002" target="_blank" rel="noopener" class="mrp-link">http://localhost:3002</a></li>
            </ul>
        <h3>📋 Testing Instructions</h3>
        <ol>
            <li><strong>Open the MRP Application:</strong> <a href="http://localhost:3002" target="_blank" rel="noopener" class="mrp-link">http://localhost:3002</a></li>
            <li><strong>Open Gmail Modal:</strong> Click the "Open Gmail" button in the MRP application</li>
            <li><strong>Return to this tab</strong> and run the tests below</li>
            <li><strong>Watch results:</strong> Test results will appear in the console below</li>
        </ol>
    </div>

    <div class="test-container">
        <h3>🔧 Quick Tests</h3>
        <button class="test-button clear-results-button" onclick="clearResults()">Clear Results</button>
        <button class="test-button" onclick="testDropdownsExist()">2. Check Dropdowns Exist</button>
        <button class="test-button" onclick="testBootstrapLoaded()">3. Check Bootstrap</button>
        <button class="test-button" onclick="testScriptsLoaded()">4. Check Scripts</button>
        <button class="test-button" onclick="testDropdownFunctionality()">5. Test Functionality</button>
        <button class="test-button" onclick="runAllTests()">🚀 Run All Tests</button>
        <button class="test-button" onclick="clearResults()" style="background: #ff9800;">Clear Results</button>
    </div>

    <div class="test-container">
        <h3>🎯 Manual Tests</h3>
        <button class="test-button" onclick="testSortDropdownClick()">Test Sort Dropdown</button>
        <button class="test-button" onclick="testFilterDropdownClick()">Test Filter Dropdown</button>
        <button class="test-button" onclick="testDropdownOptions()">Test Dropdown Options</button>
        <button class="test-button" onclick="testUtilityFunctions()">Test Utility Functions</button>
    </div>

    <div class="test-container">
        <h3>📊 Test Results</h3>
        <div id="results" class="results">Ready to run tests...\nPlease open the Gmail modal first, then run tests.\n</div>
    </div>

    <script>
        // Test result logging
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '📝';
            results.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').textContent = 'Results cleared.\n';
        }

        // Test 1: Check if Gmail modal exists
        function testModalExists() {
            log('Testing if Gmail modal exists...');
            
            try {
                // Try multiple possible modal IDs
                const modalIds = ['mrp-gmailModal', 'gmailModal', 'gmail-modal'];
                let modalFound = false;
                
                modalIds.forEach(id => {
                    const modal = document.getElementById(id);
                    if (modal) {
                        modalFound = true;
                        log(`Found modal with ID: ${id}`, 'success');
                        log(`Modal visible: ${modal.classList.contains('show')}`, modal.classList.contains('show') ? 'success' : 'warning');
                    }
                });
                
                if (!modalFound) {
                    log('No Gmail modal found with any expected ID', 'error');
                    log('Available elements with "modal" in ID:', 'info');
                    Array.from(document.querySelectorAll('[id*="modal"]')).forEach(el => {
                        log(`  - ${el.id}`, 'info');
                    });
                }
            } catch (error) {
                log(`Error checking modal: ${error.message}`, 'error');
            }
        }

        // Test 2: Check if dropdown elements exist
        function testDropdownsExist() {
            log('Testing if dropdown elements exist...');
            
            try {
                const modal = document.getElementById('mrp-gmailModal') || document.getElementById('gmailModal');
                if (!modal) {
                    log('Gmail modal not found', 'error');
                    return;
                }

                // Check for sort dropdown
                const sortBtn = modal.querySelector('#sort-dropdown');
                const sortMenu = modal.querySelector('#sort-dropdown + .dropdown-menu');
                
                // Check for filter dropdown
                const filterBtn = modal.querySelector('#filter-dropdown');
                const filterMenu = modal.querySelector('#filter-dropdown + .dropdown-menu');
                
                log(`Sort button found: ${!!sortBtn}`, sortBtn ? 'success' : 'error');
                log(`Sort menu found: ${!!sortMenu}`, sortMenu ? 'success' : 'error');
                log(`Filter button found: ${!!filterBtn}`, filterBtn ? 'success' : 'error');
                log(`Filter menu found: ${!!filterMenu}`, filterMenu ? 'success' : 'error');
                
                if (sortBtn) {
                    log(`Sort button attributes: data-bs-toggle="${sortBtn.getAttribute('data-bs-toggle')}", aria-expanded="${sortBtn.getAttribute('aria-expanded')}"`, 'info');
                }
                if (filterBtn) {
                    log(`Filter button attributes: data-bs-toggle="${filterBtn.getAttribute('data-bs-toggle')}", aria-expanded="${filterBtn.getAttribute('aria-expanded')}"`, 'info');
                }
                
                // Count dropdown options
                const sortOptions = modal.querySelectorAll('.sort-option');
                const filterOptions = modal.querySelectorAll('.filter-option');
                log(`Sort options found: ${sortOptions.length}`, 'info');
                log(`Filter options found: ${filterOptions.length}`, 'info');
                
            } catch (error) {
                log(`Error checking dropdowns: ${error.message}`, 'error');
            }
        }

        // Test 3: Check if Bootstrap is loaded
        function testBootstrapLoaded() {
            log('Testing Bootstrap availability...');
            
            try {
                const hasBootstrap = typeof bootstrap !== 'undefined';
                const hasDropdown = hasBootstrap && typeof bootstrap.Dropdown !== 'undefined';
                
                log(`Bootstrap loaded: ${hasBootstrap}`, hasBootstrap ? 'success' : 'error');
                log(`Bootstrap Dropdown available: ${hasDropdown}`, hasDropdown ? 'success' : 'error');
                
                if (hasBootstrap) {
                    log(`Bootstrap version: ${bootstrap.Tooltip?.VERSION || 'unknown'}`, 'info');
                }
                
                // Check jQuery (sometimes used with Bootstrap)
                const hasJQuery = typeof $ !== 'undefined';
                log(`jQuery available: ${hasJQuery}`, hasJQuery ? 'success' : 'warning');
                
            } catch (error) {
                log(`Error checking Bootstrap: ${error.message}`, 'error');
            }
        }

        // Test 4: Check if our scripts are loaded
        function testScriptsLoaded() {
            log('Testing if our scripts are loaded...');
            
            try {
                // Check for global functions
                const hasDropdownDebug = typeof window.gmailDropdownDebug !== 'undefined';
                const hasSearchEmails = typeof window.searchEmails !== 'undefined';
                const hasSortEmails = typeof window.sortEmails !== 'undefined';
                const hasFilterEmails = typeof window.filterEmails !== 'undefined';
                const hasShowNotification = typeof window.showNotification !== 'undefined';
                
                log(`Gmail dropdown debug object: ${hasDropdownDebug}`, hasDropdownDebug ? 'success' : 'warning');
                log(`searchEmails function: ${hasSearchEmails}`, hasSearchEmails ? 'success' : 'warning');
                log(`sortEmails function: ${hasSortEmails}`, hasSortEmails ? 'success' : 'warning');
                log(`filterEmails function: ${hasFilterEmails}`, hasFilterEmails ? 'success' : 'warning');
                log(`showNotification function: ${hasShowNotification}`, hasShowNotification ? 'success' : 'warning');
                
                // Check script tags
                const scripts = Array.from(document.querySelectorAll('script[src]')).map(s => s.src);
                const gmailScripts = scripts.filter(src => src.includes('gmail'));
                log(`Gmail-related scripts found: ${gmailScripts.length}`, 'info');
                gmailScripts.forEach(src => {
                    log(`  - ${src.split('/').pop()}`, 'info');
                });
                
            } catch (error) {
                log(`Error checking scripts: ${error.message}`, 'error');
            }
        }

        // Test 5: Test dropdown functionality
        function testDropdownFunctionality() {
            log('Testing dropdown functionality...');
            
            try {
                const modal = document.getElementById('mrp-gmailModal') || document.getElementById('gmailModal');
                if (!modal) {
                    log('Gmail modal not found', 'error');
                    return;
                }

                if (!modal.classList.contains('show')) {
                    log('Gmail modal is not visible - please open it first', 'warning');
                    return;
                }

                // Test Bootstrap dropdown instances
                const sortBtn = modal.querySelector('#sort-dropdown');
                const filterBtn = modal.querySelector('#filter-dropdown');
                
                if (sortBtn && typeof bootstrap !== 'undefined') {
                    const sortInstance = bootstrap.Dropdown.getInstance(sortBtn);
                    log(`Sort dropdown Bootstrap instance: ${!!sortInstance}`, sortInstance ? 'success' : 'warning');
                }
                
                if (filterBtn && typeof bootstrap !== 'undefined') {
                    const filterInstance = bootstrap.Dropdown.getInstance(filterBtn);
                    log(`Filter dropdown Bootstrap instance: ${!!filterInstance}`, filterInstance ? 'success' : 'warning');
                }
                
                // Test manual click simulation
                if (sortBtn) {
                    log('Testing sort dropdown click...', 'info');
                    sortBtn.click();
                    setTimeout(() => {
                        const sortMenu = sortBtn.nextElementSibling;
                        const isOpen = sortMenu && sortMenu.classList.contains('show');
                        log(`Sort dropdown opened: ${isOpen}`, isOpen ? 'success' : 'error');
                        
                        // Close it
                        if (isOpen) {
                            sortBtn.click();
                        }
                    }, 100);
                }
                
            } catch (error) {
                log(`Error testing functionality: ${error.message}`, 'error');
            }
        }

        // Manual test: Sort dropdown click
        function testSortDropdownClick() {
            log('Manually testing sort dropdown click...');
            
            try {
                const modal = document.getElementById('mrp-gmailModal') || document.getElementById('gmailModal');
                if (!modal || !modal.classList.contains('show')) {
                    log('Gmail modal not open', 'warning');
                    return;
                }
                
                const sortBtn = modal.querySelector('#sort-dropdown');
                if (sortBtn) {
                    log('Clicking sort dropdown button...', 'info');
                    sortBtn.click();
                    
                    setTimeout(() => {
                        const sortMenu = sortBtn.nextElementSibling;
                        const isOpen = sortMenu && sortMenu.classList.contains('show');
                        log(`Sort dropdown is now: ${isOpen ? 'OPEN' : 'CLOSED'}`, isOpen ? 'success' : 'error');
                    }, 200);
                } else {
                    log('Sort dropdown button not found', 'error');
                }
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        // Manual test: Filter dropdown click
        function testFilterDropdownClick() {
            log('Manually testing filter dropdown click...');
            
            try {
                const modal = document.getElementById('mrp-gmailModal') || document.getElementById('gmailModal');
                if (!modal || !modal.classList.contains('show')) {
                    log('Gmail modal not open', 'warning');
                    return;
                }
                
                const filterBtn = modal.querySelector('#filter-dropdown');
                if (filterBtn) {
                    log('Clicking filter dropdown button...', 'info');
                    filterBtn.click();
                    
                    setTimeout(() => {
                        const filterMenu = filterBtn.nextElementSibling;
                        const isOpen = filterMenu && filterMenu.classList.contains('show');
                        log(`Filter dropdown is now: ${isOpen ? 'OPEN' : 'CLOSED'}`, isOpen ? 'success' : 'error');
                    }, 200);
                } else {
                    log('Filter dropdown button not found', 'error');
                }
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        // Test dropdown options
        function testDropdownOptions() {
            log('Testing dropdown option clicks...');
            
            try {
                const modal = document.getElementById('mrp-gmailModal') || document.getElementById('gmailModal');
                if (!modal || !modal.classList.contains('show')) {
                    log('Gmail modal not open', 'warning');
                    return;
                }
                
                // Test first sort option
                const firstSortOption = modal.querySelector('.sort-option');
                if (firstSortOption) {
                    log(`Testing sort option: ${firstSortOption.textContent}`, 'info');
                    firstSortOption.click();
                    log('Sort option clicked', 'success');
                } else {
                    log('No sort options found', 'error');
                }
                
                // Test first filter option
                setTimeout(() => {
                    const firstFilterOption = modal.querySelector('.filter-option');
                    if (firstFilterOption) {
                        log(`Testing filter option: ${firstFilterOption.textContent}`, 'info');
                        firstFilterOption.click();
                        log('Filter option clicked', 'success');
                    } else {
                        log('No filter options found', 'error');
                    }
                }, 500);
                
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        // Test utility functions
        function testUtilityFunctions() {
            log('Testing utility functions...');
            
            try {
                // Test notification
                if (typeof showNotification === 'function') {
                    showNotification('Test notification from live test!');
                    log('Notification function called successfully', 'success');
                } else {
                    log('showNotification function not available', 'warning');
                }
                
                // Test search
                if (typeof searchEmails === 'function') {
                    searchEmails('test search');
                    log('searchEmails function called successfully', 'success');
                } else {
                    log('searchEmails function not available', 'warning');
                }
                
                // Test sort
                if (typeof sortEmails === 'function') {
                    sortEmails('date-desc');
                    log('sortEmails function called successfully', 'success');
                } else {
                    log('sortEmails function not available', 'warning');
                }
                
                // Test filter
                if (typeof filterEmails === 'function') {
                    filterEmails('unread');
                    log('filterEmails function called successfully', 'success');
                } else {
                    log('filterEmails function not available', 'warning');
                }
                
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        // Run all tests
        function runAllTests() {
            log('=== RUNNING ALL TESTS ===', 'info');
            clearResults();
            log('Starting comprehensive test suite...', 'info');
            
            testModalExists();
            setTimeout(() => testDropdownsExist(), 200);
            setTimeout(() => testBootstrapLoaded(), 400);
            setTimeout(() => testScriptsLoaded(), 600);
            setTimeout(() => testDropdownFunctionality(), 800);
            setTimeout(() => {
                log('=== ALL TESTS COMPLETED ===', 'info');
                log('Review the results above. If any tests failed, check the browser console for more details.', 'info');
            }, 1000);
        }

        // Initialize
        log('Gmail Dropdowns Live Test ready!', 'success');
        log('Please open the Gmail modal in the MRP application, then run tests.', 'info');
    </script>
</body>
</html>
