{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "declaration": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@isa-suite/*": ["../*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}