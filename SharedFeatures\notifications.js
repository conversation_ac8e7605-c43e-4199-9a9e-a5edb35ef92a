/**
 * Shared Notifications Module
 *
 * This module provides a unified notification system for all applications in the ISA Suite.
 * It allows applications to send and receive notifications across the system.
 * Notifications are stored persistently using SQLite.
 */

const fetch = require('node-fetch');
const notificationStorage = require('./utils/notification-storage');

// Configuration
const HUB_URL = 'http://localhost:8000';
const NOTIFICATION_ENDPOINT = '/api/notifications';

/**
 * Send a notification to the Integration Hub
 *
 * @param {Object} notification - The notification object
 * @param {string} notification.source - The source application (e.g., 'BMS', 'CRM', 'SCM')
 * @param {string} notification.type - The notification type (e.g., 'info', 'warning', 'error', 'success')
 * @param {string} notification.message - The notification message
 * @param {string} [notification.link] - Optional link to the relevant page
 * @param {string} [notification.linkText] - Optional text for the link
 * @param {Object} [notification.data] - Optional additional data
 * @returns {Promise<Object>} - The response from the hub
 */
async function sendNotification(notification) {
  try {
    // Validate required fields
    if (!notification.source || !notification.type || !notification.message) {
      throw new Error('Missing required notification fields: source, type, and message are required');
    }

    // Add timestamp if not provided
    if (!notification.timestamp) {
      notification.timestamp = new Date().toISOString();
    }

    // Store notification in persistent storage
    try {
      await notificationStorage.saveNotification(notification);
    } catch (storageError) {
      console.error('Error storing notification in persistent storage:', storageError);
      // Continue even if storage fails
    }

    // Send notification to the hub
    const response = await fetch(`${HUB_URL}${NOTIFICATION_ENDPOINT}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(notification),
    });

    if (!response.ok) {
      throw new Error(`Failed to send notification: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending notification:', error);
    throw error;
  }
}

/**
 * Create a notification helper for a specific application
 *
 * @param {string} appName - The name of the application (e.g., 'BMS', 'CRM', 'SCM')
 * @returns {Object} - An object with methods for sending different types of notifications
 */
function createNotifier(appName) {
  return {
    /**
     * Send an info notification
     *
     * @param {string} message - The notification message
     * @param {Object} [options] - Optional parameters (link, linkText, data)
     * @returns {Promise<Object>} - The response from the hub
     */
    info: (message, options = {}) => {
      return sendNotification({
        source: appName,
        type: 'info',
        message,
        ...options,
      });
    },

    /**
     * Send a success notification
     *
     * @param {string} message - The notification message
     * @param {Object} [options] - Optional parameters (link, linkText, data)
     * @returns {Promise<Object>} - The response from the hub
     */
    success: (message, options = {}) => {
      return sendNotification({
        source: appName,
        type: 'success',
        message,
        ...options,
      });
    },

    /**
     * Send a warning notification
     *
     * @param {string} message - The notification message
     * @param {Object} [options] - Optional parameters (link, linkText, data)
     * @returns {Promise<Object>} - The response from the hub
     */
    warning: (message, options = {}) => {
      return sendNotification({
        source: appName,
        type: 'warning',
        message,
        ...options,
      });
    },

    /**
     * Send an error notification
     *
     * @param {string} message - The notification message
     * @param {Object} [options] - Optional parameters (link, linkText, data)
     * @returns {Promise<Object>} - The response from the hub
     */
    error: (message, options = {}) => {
      return sendNotification({
        source: appName,
        type: 'error',
        message,
        ...options,
      });
    },

    /**
     * Send a custom notification
     *
     * @param {string} type - The notification type
     * @param {string} message - The notification message
     * @param {Object} [options] - Optional parameters (link, linkText, data)
     * @returns {Promise<Object>} - The response from the hub
     */
    custom: (type, message, options = {}) => {
      return sendNotification({
        source: appName,
        type,
        message,
        ...options,
      });
    },
  };
}

/**
 * Get notifications from persistent storage
 *
 * @param {Object} options - Query options
 * @param {number} [options.limit=50] - Maximum number of notifications to return
 * @param {number} [options.offset=0] - Number of notifications to skip
 * @param {string} [options.source] - Filter by source
 * @param {string} [options.type] - Filter by type
 * @param {boolean} [options.includeRead=false] - Whether to include read notifications
 * @param {boolean} [options.includeDeleted=false] - Whether to include deleted notifications
 * @returns {Promise<Array>} - Array of notifications
 */
async function getNotifications(options = {}) {
  try {
    return await notificationStorage.getNotifications(options);
  } catch (error) {
    console.error('Error retrieving notifications:', error);
    throw error;
  }
}

/**
 * Get notification count from persistent storage
 *
 * @param {Object} options - Query options
 * @param {string} [options.source] - Filter by source
 * @param {string} [options.type] - Filter by type
 * @param {boolean} [options.includeRead=false] - Whether to include read notifications
 * @param {boolean} [options.includeDeleted=false] - Whether to include deleted notifications
 * @returns {Promise<number>} - Number of notifications
 */
async function getNotificationCount(options = {}) {
  try {
    return await notificationStorage.getNotificationCount(options);
  } catch (error) {
    console.error('Error retrieving notification count:', error);
    throw error;
  }
}

/**
 * Mark a notification as read
 *
 * @param {string} id - The notification ID
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
async function markNotificationAsRead(id) {
  try {
    return await notificationStorage.markAsRead(id);
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
}

/**
 * Mark a notification as deleted
 *
 * @param {string} id - The notification ID
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
async function markNotificationAsDeleted(id) {
  try {
    return await notificationStorage.markAsDeleted(id);
  } catch (error) {
    console.error('Error marking notification as deleted:', error);
    throw error;
  }
}

module.exports = {
  sendNotification,
  createNotifier,
  getNotifications,
  getNotificationCount,
  markNotificationAsRead,
  markNotificationAsDeleted
};
