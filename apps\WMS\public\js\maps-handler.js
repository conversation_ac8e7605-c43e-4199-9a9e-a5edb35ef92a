/**
 * Maps Handler for WMS Application
 * Manages all map-related operations
 */

class MapsHandler {
    constructor() {
        this.mapInstance = null;
        this.markers = [];
        this.warehouseLocations = [];
        this.currentLocation = null;
        this.routePath = null;
        this.debug = true;
    }

    /**
     * Initialize the maps handler
     */
    init() {
        this.log("Initializing Maps Handler for WMS");
        this.bindEvents();
        this.loadWarehouseLocations();
    }

    /**
     * Bind all event handlers
     */
    bindEvents() {
        // Bind tab change events
        const tabs = document.querySelectorAll('#mapsTab button[data-bs-toggle="tab"]');
        tabs.forEach(tab => {
            tab.addEventListener('shown.bs.tab', event => {
                this.onTabChange(event.target.id);
            });
        });

        // Bind search button
        const searchBtn = document.getElementById('search-location-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                const searchInput = document.getElementById('location-search');
                if (searchInput && searchInput.value) {
                    this.searchLocation(searchInput.value);
                }
            });
        }

        // Bind current location button
        const currentLocationBtn = document.getElementById('current-location-btn');
        if (currentLocationBtn) {
            currentLocationBtn.addEventListener('click', () => {
                this.getCurrentLocation();
            });
        }

        // Bind get directions button
        const getDirectionsBtn = document.getElementById('get-directions-btn');
        if (getDirectionsBtn) {
            getDirectionsBtn.addEventListener('click', () => {
                const start = document.getElementById('directions-start').value;
                const end = document.getElementById('directions-end').value;
                if (start && end) {
                    this.getDirections(start, end);
                } else {
                    alert('Please enter both starting point and destination');
                }
            });
        }
    }

    /**
     * Handle tab change
     * @param {string} tabId - ID of the selected tab
     */
    onTabChange(tabId) {
        this.log(`Tab changed to: ${tabId}`);
        
        switch(tabId) {
            case 'locations-tab':
                this.showAllWarehouses();
                break;
            case 'inventory-locations-tab':
                this.showInventoryLocations();
                break;
            case 'directions-tab':
                this.resetDirections();
                break;
            case 'saved-tab':
                this.loadSavedLocations();
                break;
        }
    }

    /**
     * Load warehouse locations
     */
    loadWarehouseLocations() {
        this.log("Loading warehouse locations");
        
        // Sample warehouse locations for WMS
        this.warehouseLocations = [
            {
                id: 'main-warehouse',
                name: 'Main Distribution Center',
                address: '123 Logistics Ave, New York, NY 10001',
                type: 'primary',
                lat: 40.7128,
                lng: -74.0060
            },
            {
                id: 'west-warehouse',
                name: 'West Coast Fulfillment Center',
                address: '456 Shipping Blvd, Los Angeles, CA 90001',
                type: 'fulfillment',
                lat: 34.0522,
                lng: -118.2437
            },
            {
                id: 'midwest-warehouse',
                name: 'Midwest Distribution Hub',
                address: '789 Supply Chain Dr, Chicago, IL 60007',
                type: 'distribution',
                lat: 41.8781,
                lng: -87.6298
            }
        ];
        
        // Populate location list
        this.populateLocationsList();
    }

    /**
     * Populate locations list in the UI
     */
    populateLocationsList() {
        const locationsContainer = document.getElementById('warehouse-locations-list');
        if (!locationsContainer) {
            this.log('Warehouse locations list container not found');
            return;
        }
        
        // Clear existing items
        locationsContainer.innerHTML = '';
        
        // Add warehouse locations
        this.warehouseLocations.forEach(location => {
            const item = document.createElement('div');
            item.className = 'list-group-item';
            
            let typeClass = 'bg-secondary';
            let typeLabel = 'Warehouse';
            
            switch (location.type) {
                case 'primary':
                    typeClass = 'bg-primary';
                    typeLabel = 'Primary';
                    break;
                case 'fulfillment':
                    typeClass = 'bg-success';
                    typeLabel = 'Fulfillment';
                    break;
                case 'distribution':
                    typeClass = 'bg-info';
                    typeLabel = 'Distribution';
                    break;
            }
            
            item.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-building me-2"></i>
                        <span>${location.name}</span>
                        <div class="small text-muted">${location.address}</div>
                    </div>
                    <div>
                        <span class="badge ${typeClass} rounded-pill me-2">${typeLabel}</span>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="mapsHandler.viewLocation('${location.id}')"><i class="bi bi-eye"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="mapsHandler.getDirectionsToLocation('${location.id}')"><i class="bi bi-signpost-2"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="mapsHandler.shareLocation('${location.id}')"><i class="bi bi-share"></i></button>
                        </div>
                    </div>
                </div>
            `;
            
            locationsContainer.appendChild(item);
        });
    }

    /**
     * Show all warehouses on the map
     */
    showAllWarehouses() {
        this.log("Showing all warehouses");
        
        // In a real app, we'd update the map with all warehouse locations
        // For demo purposes, we just log this action
    }

    /**
     * Show inventory locations within warehouses
     */
    showInventoryLocations() {
        this.log("Showing inventory locations");
        
        // In a real app, we'd show detailed inventory locations within warehouses
        // For demo purposes, we just log this action
    }

    /**
     * Reset directions form
     */
    resetDirections() {
        const startInput = document.getElementById('directions-start');
        const endInput = document.getElementById('directions-end');
        
        if (startInput) {
            startInput.value = '';
        }
        
        if (endInput) {
            endInput.value = '';
        }
    }

    /**
     * Load saved locations
     */
    loadSavedLocations() {
        this.log("Loading saved locations");
        
        // In a real app, we'd fetch saved locations from an API
        // For demo purposes, we use the warehouse locations
    }

    /**
     * Search for a location
     * @param {string} query - Search query
     */
    searchLocation(query) {
        this.log(`Searching for location: ${query}`);
        
        // In a real app, we'd use the Maps API to search for the location
        // For demo purposes, we just log this action
    }

    /**
     * Get current location
     */
    getCurrentLocation() {
        this.log("Getting current location");
        
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                position => {
                    const { latitude, longitude } = position.coords;
                    this.log(`Current location: ${latitude}, ${longitude}`);
                    this.currentLocation = { lat: latitude, lng: longitude };
                    
                    // In a real app, we'd center the map on this location
                    // For demo purposes, we just log this action
                },
                error => {
                    this.log(`Error getting location: ${error.message}`);
                    alert(`Could not get your location: ${error.message}`);
                }
            );
        } else {
            alert('Geolocation is not supported by this browser');
        }
    }

    /**
     * View a specific location
     * @param {string} locationId - Location ID to view
     */
    viewLocation(locationId) {
        this.log(`Viewing location: ${locationId}`);
        
        const location = this.warehouseLocations.find(loc => loc.id === locationId);
        if (!location) {
            alert(`Location ${locationId} not found`);
            return;
        }
        
        // In a real app, we'd center the map on this location
        // For demo purposes, we just log this action
    }

    /**
     * Get directions to a location
     * @param {string} locationId - Location ID to get directions to
     */
    getDirectionsToLocation(locationId) {
        this.log(`Getting directions to location: ${locationId}`);
        
        const location = this.warehouseLocations.find(loc => loc.id === locationId);
        if (!location) {
            alert(`Location ${locationId} not found`);
            return;
        }
        
        // Switch to directions tab
        const directionsTab = document.getElementById('directions-tab');
        if (directionsTab) {
            bootstrap.Tab.getOrCreateInstance(directionsTab).show();
        }
        
        // Set destination
        const endInput = document.getElementById('directions-end');
        if (endInput) {
            endInput.value = location.address;
        }
    }

    /**
     * Share a location
     * @param {string} locationId - Location ID to share
     */
    shareLocation(locationId) {
        this.log(`Sharing location: ${locationId}`);
        
        const location = this.warehouseLocations.find(loc => loc.id === locationId);
        if (!location) {
            alert(`Location ${locationId} not found`);
            return;
        }
        
        const shareText = `${location.name} - ${location.address}`;
        
        // In a real app, we'd show a share dialog
        // For demo purposes, we just prompt for an email
        const email = prompt(`Enter email address to share "${shareText}" with:`);
        if (email) {
            alert(`Location shared with ${email}`);
        }
    }

    /**
     * Get directions between locations
     * @param {string} start - Starting location
     * @param {string} end - Ending location
     */
    getDirections(start, end) {
        this.log(`Getting directions from ${start} to ${end}`);
        
        // In a real app, we'd use the Maps API to get directions
        // For demo purposes, we just log this action
    }

    /**
     * Logger function
     * @param {string} message - Message to log
     */
    log(message) {
        if (this.debug) {
            console.log(`WMS MapsHandler: ${message}`);
        }
    }
}

// Create and initialize the handler
const mapsHandler = new MapsHandler();
document.addEventListener('DOMContentLoaded', () => {
    mapsHandler.init();
});
