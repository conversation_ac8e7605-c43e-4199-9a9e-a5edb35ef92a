<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Supply Chain Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <style>
    :root {
      --primary-color: #6a3de8; /* Purple/Violet for SCM */
      --secondary-color: #3ecf8e; /* Mint green */
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-bg: #f8f9fa;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f7fa;
    }

    .sidebar {
      background-color: #4527a0; /* Darker purple for sidebar */
      color: white;
      height: 100vh;
      position: fixed;
      padding-top: 20px;
      overflow-y: auto; /* Add scrollbar when content overflows */
    }

    /* Custom scrollbar for sidebar */
    .sidebar::-webkit-scrollbar {
      width: 6px;
    }

    .sidebar::-webkit-scrollbar-track {
      background: #3a1f87; /* Darker shade of sidebar color */
    }

    .sidebar::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }

    .sidebar::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.5);
    }

    .sidebar .nav-link {
      color: #f8f9fa;
      padding: 0.75rem 1rem;
      font-weight: 500;
    }

    .sidebar .nav-link:hover {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar .nav-link.active {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.2);
    }

    .sidebar .nav-link i {
      margin-right: 10px;
    }

    @media (max-width: 767.98px) {
      .sidebar {
        position: fixed;
        top: 56px;
        left: -220px;
        height: calc(100vh - 56px);
        width: 220px;
        transition: left 0.3s;
        z-index: 1030;
      }

      .sidebar.show {
        left: 0;
      }

      .main-content {
        margin-left: 0;
        padding: 10px;
        padding-top: 70px;
      }

      .metric-card {
        margin-bottom: 15px;
      }

      .table-responsive {
        overflow-x: auto;
      }

      .d-flex.justify-content-between {
        flex-direction: column;
      }

      .d-flex.justify-content-between > div {
        margin-top: 10px;
      }
    }

    @media (min-width: 768px) {
      .main-content {
        margin-left: 220px;
        padding: 20px;
        padding-top: 70px;
      }

      .sidebar {
        width: 220px;
      }
    }

    .card {
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border: none;
    }

    .card-header {
      background-color: white;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      font-weight: 600;
    }

    .metric-card {
      text-align: center;
      padding: 15px;
    }

    .metric-value {
      font-size: 28px;
      font-weight: 700;
      margin: 10px 0;
    }

    .metric-label {
      font-size: 14px;
      color: #6c757d;
    }

    .trend-indicator {
      font-size: 12px;
      padding: 3px 8px;
      border-radius: 20px;
      display: inline-block;
      margin-left: 5px;
    }

    .trend-up {
      background-color: rgba(46, 204, 113, 0.2);
      color: #2ecc71;
    }

    .trend-down {
      background-color: rgba(231, 76, 60, 0.2);
      color: #e74c3c;
    }

    .status-badge {
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
    }

    .status-in-stock {
      background-color: rgba(46, 204, 113, 0.2);
      color: #2ecc71;
    }

    .status-low-stock {
      background-color: rgba(243, 156, 18, 0.2);
      color: #f39c12;
    }

    .status-out-of-stock {
      background-color: rgba(231, 76, 60, 0.2);
      color: #e74c3c;
    }

    .status-active {
      background-color: rgba(46, 204, 113, 0.2);
      color: #2ecc71;
    }

    .status-on-hold {
      background-color: rgba(243, 156, 18, 0.2);
      color: #f39c12;
    }

    .status-inactive {
      background-color: rgba(231, 76, 60, 0.2);
      color: #e74c3c;
    }

    .table th {
      font-weight: 600;
      color: #495057;
    }

    .rating {
      color: #f39c12;
    }

    .progress {
      height: 8px;
      margin-top: 5px;
    }

    .header-action-button {
      float: right;
      font-size: 14px;
      padding: 5px 10px;
    }

    .dashboard-card {
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .card-icon {
      font-size: 2rem;
      margin-bottom: 10px;
    }
  </style>
</head>
<body>
  <!-- Top Navbar -->
  <nav class="navbar navbar-dark fixed-top" style="background-color: #4527a0;">
    <div class="container-fluid">
      <div class="d-flex align-items-center">
        <button id="sidebarToggle" class="d-md-none me-2" style="background: transparent; border: none; color: white;">
          <i class="bi bi-list fs-4"></i>
        </button>
        <a class="navbar-brand" href="/">SCM System</a>
      </div>
      <div class="d-flex">
        <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3" style="background: transparent; border: 1px solid white; color: white; padding: 5px 10px;">
          <i class="bi bi-box-arrow-left me-1"></i> Back to Hub
        </a>
        <button class="btn position-relative me-2" style="background: transparent; border: none; color: white;">
          <i class="bi bi-bell fs-5"></i>
          <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
            4
          </span>
        </button>
        <button class="btn" style="background: transparent; border: none; color: white;">
          <i class="bi bi-person-circle fs-5"></i>
        </button>
      </div>
    </div>
  </nav>

  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div class="col-md-2 sidebar" id="sidebar">
        <h4 class="text-center mb-4">SCM Dashboard</h4>
        <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link active" href="#"><i class="bi bi-speedometer2"></i> Dashboard</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-box-seam"></i> Inventory</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-people"></i> Suppliers</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-truck"></i> Logistics</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-graph-up"></i> Reports</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-gear"></i> Settings</a>
          </li>

          <li class="nav-item mt-3">
            <h6 class="text-white-50 ms-3 mb-2">Integrations</h6>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal"><i class="bi bi-calendar3"></i> Google Calendar</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-folder"></i> Google Drive</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal"><i class="bi bi-file-earmark-text"></i> Google Docs</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#sheetsModal"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-maps" data-bs-toggle="modal" data-bs-target="#mapsModal"><i class="bi bi-geo-alt"></i> Google Maps</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#gmail" data-bs-toggle="modal" data-bs-target="#gmailModal"><i class="bi bi-envelope"></i> Gmail</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#attachments" data-bs-toggle="modal" data-bs-target="#attachmentsModal"><i class="bi bi-paperclip"></i> Attachments</a>
          </li>
          <li class="nav-item mt-3">
            <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();"><i class="bi bi-box-arrow-left"></i> Back to Hub</a>
          </li>
        </ul>
      </div>

      <!-- Main Content -->
      <div class="col-md-10 main-content">
        <div class="container-fluid px-4">
          <div class="d-flex justify-content-between align-items-center mb-4 mt-2">
            <h2>Supply Chain Dashboard</h2>
            <div>
              <button class="btn btn-outline-secondary me-2"><i class="bi bi-download"></i> Export</button>
              <button class="btn btn-primary"><i class="bi bi-plus"></i> New Order</button>
            </div>
          </div>

        <!-- Metrics Row -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card dashboard-card" style="background-color: var(--primary-color); color: white;">
              <div class="card-body text-center">
                <i class="bi bi-box-seam card-icon"></i>
                <h5 class="card-title">Total Inventory</h5>
                <h2 class="card-text" id="total-inventory">2550</h2>
                <p class="card-text">+5% from last month</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card dashboard-card" style="background-color: var(--secondary-color); color: white;">
              <div class="card-body text-center">
                <i class="bi bi-truck card-icon"></i>
                <h5 class="card-title">Active Suppliers</h5>
                <h2 class="card-text" id="active-suppliers">3</h2>
                <p class="card-text">+1 from last month</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card dashboard-card" style="background-color: var(--warning-color); color: white;">
              <div class="card-body text-center">
                <i class="bi bi-exclamation-triangle card-icon"></i>
                <h5 class="card-title">Low Stock Items</h5>
                <h2 class="card-text" id="low-stock-items">1</h2>
                <p class="card-text">-2 from last month</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card dashboard-card" style="background-color: #9c27b0; color: white;">
              <div class="card-body text-center">
                <i class="bi bi-check-circle card-icon"></i>
                <h5 class="card-title">Fulfillment Rate</h5>
                <h2 class="card-text">96%</h2>
                <p class="card-text">+2% from last month</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Google Integrations -->
        <div class="row mt-4 mb-4">
          <div class="col-12">
            <h4 class="mb-3">Google Integrations</h4>
          </div>

          <!-- Google Gmail -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-gmail-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: #673ab7; border-radius: 10px 10px 0 0;">
                <h5 class="mb-0 text-white"><i class="bi bi-envelope"></i> Gmail</h5>
                <div class="component-actions">
                  <button id="refresh-gmail" class="btn btn-sm btn-outline-light"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="compose-email" class="btn btn-sm btn-outline-light" data-bs-toggle="modal" data-bs-target="#gmailModal"><i class="bi bi-pencil"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">SP</div>
                        <div>
                          <div class="fw-bold">Supplier Portal</div>
                          <div class="small text-truncate" style="max-width: 200px;">New inventory request submitted</div>
                        </div>
                      </div>
                    </div>
                    <span class="badge bg-primary rounded-pill">5m</span>
                  </a>
                  <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">WH</div>
                        <div>
                          <div class="fw-bold">Warehouse Manager</div>
                          <div class="small text-truncate" style="max-width: 200px;">Inventory reconciliation report</div>
                        </div>
                      </div>
                    </div>
                    <span class="badge bg-primary rounded-pill">1h</span>
                  </a>
                  <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">LM</div>
                        <div>
                          <div class="fw-bold">Logistics Manager</div>
                          <div class="small text-truncate" style="max-width: 200px;">Shipping delay notification</div>
                        </div>
                      </div>
                    </div>
                    <span class="badge bg-primary rounded-pill">3h</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#gmailModal">
                    <i class="bi bi-envelope me-2"></i>Open Gmail
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Google Drive -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-drive-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: #673ab7; border-radius: 10px 10px 0 0;">
                <h5 class="mb-0 text-white"><i class="bi bi-folder"></i> Google Drive</h5>
                <div class="component-actions">
                  <button id="refresh-drive" class="btn btn-sm btn-outline-light"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="upload-file" class="btn btn-sm btn-outline-light" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-upload"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" onclick="openGoogleItem('drive', 'folder', 'supplier-contracts')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-folder-fill text-primary me-2"></i>
                      <span>Supplier Contracts</span>
                    </div>
                    <span class="badge bg-secondary rounded-pill">12 files</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('drive', 'folder', 'inventory-reports')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-folder-fill text-primary me-2"></i>
                      <span>Inventory Reports</span>
                    </div>
                    <span class="badge bg-secondary rounded-pill">8 files</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('drive', 'pdf', 'supply-chain-analysis-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                      <span>Supply_Chain_Analysis_Q2_2025.pdf</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">Yesterday</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
                    <i class="bi bi-folder me-2"></i>Open Drive
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Google Docs -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-docs-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: #673ab7; border-radius: 10px 10px 0 0;">
                <h5 class="mb-0 text-white"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                <div class="component-actions">
                  <button id="refresh-docs" class="btn btn-sm btn-outline-light"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="create-new-doc" class="btn btn-sm btn-outline-light" data-bs-toggle="modal" data-bs-target="#docsModal"><i class="bi bi-plus"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" onclick="openGoogleItem('docs', 'document', 'supply-chain-procedures')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-text text-primary me-2"></i>
                      <span>Supply Chain Procedures</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">Today</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('docs', 'document', 'supplier-onboarding-process')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-text text-primary me-2"></i>
                      <span>Supplier Onboarding Process</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">Yesterday</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('docs', 'document', 'inventory-management-guidelines')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-text text-primary me-2"></i>
                      <span>Inventory Management Guidelines</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">3 days ago</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#docsModal">
                    <i class="bi bi-file-earmark-text me-2"></i>View All Documents
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Google Sheets -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-sheets-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: #673ab7; border-radius: 10px 10px 0 0;">
                <h5 class="mb-0 text-white"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                <div class="component-actions">
                  <button id="refresh-sheets" class="btn btn-sm btn-outline-light"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="create-new-sheet" class="btn btn-sm btn-outline-light" data-bs-toggle="modal" data-bs-target="#sheetsModal"><i class="bi bi-plus"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'inventory-tracking-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                      <span>Inventory_Tracking_2025.xlsx</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">2 days ago</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'supplier-performance-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                      <span>Supplier_Performance_Q2_2025.xlsx</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">1 week ago</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'logistics-cost-analysis')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                      <span>Logistics_Cost_Analysis.xlsx</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">2 weeks ago</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                    <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Google Calendar -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-calendar-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: #673ab7; border-radius: 10px 10px 0 0;">
                <h5 class="mb-0 text-white"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                <div class="component-actions">
                  <button id="refresh-calendar" class="btn btn-sm btn-outline-light"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="create-new-event" class="btn btn-sm btn-outline-light" data-bs-toggle="modal" data-bs-target="#calendarModal"><i class="bi bi-plus"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" onclick="openGoogleItem('calendar', 'event', 'supplier-meeting-acme')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-calendar-event text-danger me-2"></i>
                      <span>Supplier Meeting: Acme Corp</span>
                    </div>
                    <span class="badge bg-warning rounded-pill">Today</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('calendar', 'event', 'inventory-audit')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-calendar-event text-primary me-2"></i>
                      <span>Inventory Audit</span>
                    </div>
                    <span class="badge bg-info rounded-pill">Tomorrow</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('calendar', 'event', 'quarterly-supply-chain-review')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-calendar-event text-success me-2"></i>
                      <span>Quarterly Supply Chain Review</span>
                    </div>
                    <span class="badge bg-secondary rounded-pill">Next Week</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#calendarModal">
                    <i class="bi bi-calendar3 me-2"></i>View Full Calendar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Insights Card -->
        <div class="card mb-4" style="border-left: 4px solid var(--primary-color);">
          <div class="card-header d-flex justify-content-between align-items-center">
            <div>
              <i class="bi bi-robot"></i> AI-Powered Supply Chain Optimization
              <span class="badge bg-primary ms-2">AI Insights</span>
            </div>
            <div>
              <button class="btn btn-sm btn-outline-primary" id="refresh-insights-btn">
                <i class="bi bi-arrow-clockwise"></i> Refresh
              </button>
              <div class="dropdown d-inline-block ms-2">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="insightsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="bi bi-gear"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="insightsDropdown">
                  <li><a class="dropdown-item" href="#" id="expand-all-insights">Expand All</a></li>
                  <li><a class="dropdown-item" href="#" id="collapse-all-insights">Collapse All</a></li>
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="#" id="export-insights">Export Insights</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div id="ai-insights-container">
              <div class="d-flex justify-content-center">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                Supply Chain Metrics
                <button class="btn btn-sm btn-outline-secondary header-action-button">
                  <i class="bi bi-three-dots"></i>
                </button>
              </div>
              <div class="card-body">
                <canvas id="supplyChainChart" height="250"></canvas>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                Inventory Trends
                <button class="btn btn-sm btn-outline-secondary header-action-button">
                  <i class="bi bi-three-dots"></i>
                </button>
              </div>
              <div class="card-body">
                <canvas id="inventoryTrendsChart" height="250"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Tables Row -->
        <div class="row">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                Inventory Levels
                <button class="btn btn-sm btn-outline-primary header-action-button">
                  View All
                </button>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-hover" id="inventory-table">
                    <thead>
                      <tr>
                        <th>Item</th>
                        <th>Location</th>
                        <th>Stock Level</th>
                        <th>Status</th>
                        <th>Reorder Point</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- Will be populated by JavaScript -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                Supplier Information
                <button class="btn btn-sm btn-outline-primary header-action-button">
                  View All
                </button>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-hover" id="suppliers-table">
                    <thead>
                      <tr>
                        <th>Supplier</th>
                        <th>Category</th>
                        <th>Status</th>
                        <th>Lead Time</th>
                        <th>Rating</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- Will be populated by JavaScript -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Cross-Application Integration Row -->
        <div class="row mt-4">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                  <i class="bi bi-link-45deg"></i> Related Tasks from Task Management System
                  <span class="badge bg-primary ms-2">Cross-App Integration</span>
                </div>
                <button class="btn btn-sm btn-outline-primary header-action-button" id="refresh-tasks-btn">
                  <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
              </div>
              <div class="card-body">
                <div id="tasks-container">
                  <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Integration Modals -->
  <!-- Gmail Modal -->
  <div class="modal fade" id="gmailModal" tabindex="-1" aria-labelledby="gmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="gmailModalLabel"><i class="bi bi-envelope"></i> Gmail</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="gmailTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox-content" type="button" role="tab" aria-controls="inbox-content" aria-selected="true">Inbox</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose-content" type="button" role="tab" aria-controls="compose-content" aria-selected="false">Compose</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent-content" type="button" role="tab" aria-controls="sent-content" aria-selected="false">Sent</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="read-email-tab" data-bs-toggle="tab" data-bs-target="#read-email-content" type="button" role="tab" aria-controls="read-email-content" aria-selected="false" style="display: none;">Read Email</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="reply-email-tab" data-bs-toggle="tab" data-bs-target="#reply-email-content" type="button" role="tab" aria-controls="reply-email-content" aria-selected="false" style="display: none;">Reply</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="gmailTabContent">
            <!-- Inbox Tab -->
            <div class="tab-pane fade show active" id="inbox-content" role="tabpanel" aria-labelledby="inbox-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="email-search" class="form-control" placeholder="Search emails...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button class="btn btn-primary" id="compose-new-email" onclick="document.getElementById('compose-tab').click();">
                    <i class="bi bi-pencil-square me-2"></i>Compose
                  </button>
                  <button class="btn btn-outline-secondary ms-2" id="refresh-gmail-modal">
                    <i class="bi bi-arrow-clockwise"></i>
                  </button>
                </div>
              </div>
              <div class="list-group">
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('supplier-portal-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1 fw-bold">Supplier Portal</h6>
                    <small class="text-muted">10 minutes ago</small>
                  </div>
                  <p class="mb-1 fw-bold">New inventory request submitted</p>
                  <small class="text-muted">A new inventory request has been submitted by Acme Corp...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('warehouse-manager-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Warehouse Manager</h6>
                    <small class="text-muted">1 hour ago</small>
                  </div>
                  <p class="mb-1">Inventory reconciliation report</p>
                  <small class="text-muted">The inventory reconciliation report for Q2 2025 is now available...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('logistics-manager-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Logistics Manager</h6>
                    <small class="text-muted">3 hours ago</small>
                  </div>
                  <p class="mb-1">Shipping delay notification</p>
                  <small class="text-muted">There is a shipping delay for order #12345 due to weather conditions...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('procurement-team-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Procurement Team</h6>
                    <small class="text-muted">Yesterday</small>
                  </div>
                  <p class="mb-1">New supplier onboarding</p>
                  <small class="text-muted">We have completed the onboarding process for the new packaging supplier...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('quality-control-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Quality Control</h6>
                    <small class="text-muted">2 days ago</small>
                  </div>
                  <p class="mb-1">Quality inspection results</p>
                  <small class="text-muted">The quality inspection results for the latest shipment from XYZ Suppliers are now available...</small>
                </a>
              </div>
            </div>

            <!-- Compose Tab -->
            <div class="tab-pane fade" id="compose-content" role="tabpanel" aria-labelledby="compose-tab">
              <form>
                <div class="mb-3">
                  <label for="email-to" class="form-label">To</label>
                  <input type="email" class="form-control" id="email-to" placeholder="Enter recipient email">
                </div>
                <div class="mb-3">
                  <label for="email-cc" class="form-label">Cc</label>
                  <input type="email" class="form-control" id="email-cc" placeholder="Enter cc email addresses">
                </div>
                <div class="mb-3">
                  <label for="email-subject" class="form-label">Subject</label>
                  <input type="text" class="form-control" id="email-subject" placeholder="Enter subject">
                </div>
                <div class="mb-3">
                  <label for="email-body" class="form-label">Message</label>
                  <textarea class="form-control" id="email-body" rows="10" placeholder="Type your message here"></textarea>
                </div>
                <div class="mb-3">
                  <label for="email-attachments" class="form-label">Attachments</label>
                  <input class="form-control" type="file" id="email-attachments" multiple>
                  <div id="attachment-list" class="mt-2">
                    <!-- Attachments will be listed here -->
                  </div>
                </div>
                <div class="d-flex justify-content-between">
                  <button type="button" class="btn btn-primary" id="send-email-btn" onclick="sendEmail()">Send</button>
                  <button type="button" class="btn btn-outline-secondary" id="save-draft-btn">Save as Draft</button>
                </div>
              </form>
            </div>

            <!-- Sent Tab -->
            <div class="tab-pane fade" id="sent-content" role="tabpanel" aria-labelledby="sent-tab">
              <div class="list-group">
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sent-supplier-team')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">To: Supplier Team</h6>
                    <small class="text-muted">1 day ago</small>
                  </div>
                  <p class="mb-1">Quarterly Supplier Review Meeting</p>
                  <small class="text-muted">Please find attached the agenda for our upcoming quarterly supplier review meeting...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sent-warehouse-staff')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">To: Warehouse Staff</h6>
                    <small class="text-muted">3 days ago</small>
                  </div>
                  <p class="mb-1">Updated Inventory Procedures</p>
                  <small class="text-muted">I've attached the updated inventory procedures document. Please review and implement these changes...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sent-logistics-department')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">To: Logistics Department</h6>
                    <small class="text-muted">1 week ago</small>
                  </div>
                  <p class="mb-1">Shipping Route Optimization</p>
                  <small class="text-muted">Based on our recent analysis, I'm proposing some changes to our shipping routes to improve efficiency...</small>
                </a>
              </div>
            </div>

            <!-- Read Email Tab -->
            <div class="tab-pane fade" id="read-email-content" role="tabpanel" aria-labelledby="read-email-tab">
              <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h5 class="mb-0" id="read-email-subject">New inventory request submitted</h5>
                  <div>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="replyToEmail()"><i class="bi bi-reply"></i> Reply</button>
                    <button class="btn btn-sm btn-outline-primary me-1"><i class="bi bi-reply-all"></i> Reply All</button>
                    <button class="btn btn-sm btn-outline-primary"><i class="bi bi-forward"></i> Forward</button>
                  </div>
                </div>
                <div class="card-body">
                  <div class="d-flex justify-content-between mb-3">
                    <div>
                      <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;" id="read-email-avatar">SP</div>
                        <div>
                          <div class="fw-bold" id="read-email-from">Supplier Portal</div>
                          <div class="small text-muted">To: <span id="read-email-to">me</span></div>
                        </div>
                      </div>
                    </div>
                    <div class="text-muted" id="read-email-time">10 minutes ago</div>
                  </div>
                  <div class="email-body mb-4" id="read-email-body">
                    <p>Hello,</p>
                    <p>A new inventory request has been submitted by Acme Corp through the supplier portal.</p>
                    <p>Request details:</p>
                    <ul>
                      <li>Supplier: Acme Corp</li>
                      <li>Request ID: REQ-2025-0587</li>
                      <li>Items:
                        <ul>
                          <li>Widget A (SKU: W-A-2025) - 500 units</li>
                          <li>Widget B (SKU: W-B-2025) - 300 units</li>
                          <li>Component C (SKU: C-C-2025) - 1,000 units</li>
                        </ul>
                      </li>
                      <li>Requested Delivery Date: June 15, 2025</li>
                      <li>Priority: Medium</li>
                    </ul>
                    <p>Please review and approve this request at your earliest convenience. The supplier has indicated that they need confirmation within 48 hours to ensure timely delivery.</p>
                    <p>Best regards,<br>Supplier Portal System</p>
                  </div>
                  <div class="email-attachments" id="read-email-attachments">
                    <h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                    <div class="list-group">
                      <a href="#" onclick="openGoogleItem('drive', 'pdf', 'inventory-request-form')" class="list-group-item list-group-item-action d-flex align-items-center">
                        <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                        <div>
                          <div>inventory_request_form.pdf</div>
                          <small class="text-muted">1.2 MB</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                      </a>
                      <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'inventory-details')" class="list-group-item list-group-item-action d-flex align-items-center">
                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                        <div>
                          <div>inventory_details.xlsx</div>
                          <small class="text-muted">0.8 MB</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-flex justify-content-between mt-3">
                <button class="btn btn-outline-secondary" onclick="document.getElementById('inbox-tab').click();"><i class="bi bi-arrow-left"></i> Back to Inbox</button>
                <div>
                  <button class="btn btn-outline-danger me-2"><i class="bi bi-trash"></i> Delete</button>
                  <button class="btn btn-outline-secondary"><i class="bi bi-archive"></i> Archive</button>
                </div>
              </div>
            </div>

            <!-- Reply Email Tab -->
            <div class="tab-pane fade" id="reply-email-content" role="tabpanel" aria-labelledby="reply-email-tab">
              <div class="card mb-3">
                <div class="card-header bg-light">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <span class="fw-bold">Re: </span>
                      <span id="reply-email-subject">New inventory request submitted</span>
                    </div>
                  </div>
                </div>
                <div class="card-body bg-light">
                  <div class="d-flex">
                    <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;" id="reply-email-avatar">SP</div>
                    <div>
                      <div class="fw-bold" id="reply-email-from">Supplier Portal</div>
                      <div class="small text-muted" id="reply-email-time">10 minutes ago</div>
                      <div class="mt-2" id="reply-email-original-body">
                        <p>Hello,</p>
                        <p>A new inventory request has been submitted by Acme Corp through the supplier portal.</p>
                        <p>Request details:</p>
                        <ul>
                          <li>Supplier: Acme Corp</li>
                          <li>Request ID: REQ-2025-0587</li>
                          <li>Items:
                            <ul>
                              <li>Widget A (SKU: W-A-2025) - 500 units</li>
                              <li>Widget B (SKU: W-B-2025) - 300 units</li>
                              <li>Component C (SKU: C-C-2025) - 1,000 units</li>
                            </ul>
                          </li>
                          <li>Requested Delivery Date: June 15, 2025</li>
                          <li>Priority: Medium</li>
                        </ul>
                        <p>Please review and approve this request at your earliest convenience. The supplier has indicated that they need confirmation within 48 hours to ensure timely delivery.</p>
                        <p>Best regards,<br>Supplier Portal System</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <label for="reply-email-body" class="form-label">Your Reply</label>
                <textarea class="form-control" id="reply-email-body" rows="6" placeholder="Type your reply here..."></textarea>
              </div>
              <div class="mb-3">
                <label for="reply-email-attachments" class="form-label">Attachments</label>
                <input class="form-control" type="file" id="reply-email-attachments" multiple>
                <div id="reply-attachment-list" class="mt-2">
                  <!-- Attachments will be listed here -->
                </div>
              </div>
              <div class="d-flex justify-content-between">
                <button class="btn btn-outline-secondary" onclick="document.getElementById('read-email-tab').click();"><i class="bi bi-arrow-left"></i> Back</button>
                <div>
                  <button class="btn btn-outline-secondary me-2" id="save-reply-draft-btn">Save Draft</button>
                  <button class="btn btn-primary" id="send-reply-btn" onclick="sendReply()">Send Reply</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://mail.google.com" target="_blank" class="btn btn-primary">Open in Gmail</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Drive Modal -->
  <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="driveModalLabel"><i class="bi bi-folder"></i> Google Drive</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="driveTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content" type="button" role="tab" aria-controls="files-content" aria-selected="true">Files</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button" role="tab" aria-controls="upload-content" aria-selected="false">Upload</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="shared-tab" data-bs-toggle="tab" data-bs-target="#shared-content" type="button" role="tab" aria-controls="shared-content" aria-selected="false">Shared with me</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="driveTabContent">
            <!-- Files Tab -->
            <div class="tab-pane fade show active" id="files-content" role="tabpanel" aria-labelledby="files-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="file-search" class="form-control" placeholder="Search files...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-sort-down"></i> Sort by
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="date">Date</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="size">Size</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="type">Type</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <a href="#" onclick="openGoogleItem('drive', 'folder', 'supplier-contracts')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supplier Contracts</h6>
                      <small>12 files - Last updated: Yesterday</small>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'supplier-contracts'); event.stopPropagation();">
                      <i class="bi bi-folder-symlink"></i> Open
                    </button>
                  </div>
                </a>
                <a href="#" onclick="openGoogleItem('drive', 'folder', 'inventory-reports')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Inventory Reports</h6>
                      <small>8 files - Last updated: 3 days ago</small>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'inventory-reports'); event.stopPropagation();">
                      <i class="bi bi-folder-symlink"></i> Open
                    </button>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('sheets', 'spreadsheet', 'supply-chain-analysis-q2-2025')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supply_Chain_Analysis_Q2_2025.xlsx</h6>
                      <small>2.4 MB - Last updated: Yesterday</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'supply-chain-analysis-q2-2025'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Supply_Chain_Analysis_Q2_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supply_Chain_Analysis_Q2_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Supply_Chain_Analysis_Q2_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('drive', 'document', 'supplier-onboarding-guide')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supplier_Onboarding_Guide.docx</h6>
                      <small>1.2 MB - Last updated: 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'supplier-onboarding-guide'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Supplier_Onboarding_Guide.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supplier_Onboarding_Guide.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Supplier_Onboarding_Guide.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload-content" role="tabpanel" aria-labelledby="upload-tab">
              <div class="row">
                <div class="col-md-7">
                  <div class="upload-area p-5 mb-3 text-center" id="dropzone" style="border: 2px dashed #ccc; border-radius: 5px; background-color: #f8f9fa;">
                    <i class="bi bi-cloud-arrow-up fs-1 text-muted mb-3"></i>
                    <h5>Drag & Drop Files Here</h5>
                    <p class="text-muted">or</p>
                    <label for="file-upload" class="btn btn-primary">
                      <i class="bi bi-folder-plus me-2"></i>Browse Files
                    </label>
                    <input id="file-upload" type="file" multiple style="display: none;">
                    <p class="text-muted mt-3 small">Maximum file size: 50MB</p>
                  </div>
                  <div class="progress mb-3" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                  </div>
                </div>
                <div class="col-md-5">
                  <div class="card">
                    <div class="card-header">Upload Options</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="upload-folder" class="form-label">Destination folder</label>
                        <select class="form-select" id="upload-folder">
                          <option selected>My Drive</option>
                          <option>Supplier Contracts</option>
                          <option>Inventory Reports</option>
                          <option>Team Shared Folder</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="convert-to-google-format">
                          <label class="form-check-label" for="convert-to-google-format">
                            Convert to Google format
                          </label>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="share-with-team">
                          <label class="form-check-label" for="share-with-team">
                            Share with team
                          </label>
                        </div>
                      </div>
                      <button type="button" class="btn btn-primary" id="start-upload" disabled>
                        <i class="bi bi-upload me-2"></i>Start Upload
                      </button>
                    </div>
                  </div>
                  <div id="upload-list" class="mt-3">
                    <!-- Files to upload will be listed here -->
                  </div>
                </div>
              </div>
            </div>

            <!-- Shared with me Tab -->
            <div class="tab-pane fade" id="shared-content" role="tabpanel" aria-labelledby="shared-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="shared-search" class="form-control" placeholder="Search shared files...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-filter"></i> Filter
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="other">Other</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <a href="#" ondblclick="openGoogleItem('drive', 'pdf', 'supplier-evaluation-criteria')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supplier_Evaluation_Criteria.pdf</h6>
                      <small>Shared by: John Davis - 3 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'supplier-evaluation-criteria'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Supplier_Evaluation_Criteria.pdf'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supplier_Evaluation_Criteria.pdf'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Supplier_Evaluation_Criteria.pdf'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('drive', 'spreadsheet', 'inventory-forecast-2025')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Inventory_Forecast_2025.xlsx</h6>
                      <small>Shared by: Sarah Wilson - 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'spreadsheet', 'inventory-forecast-2025'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Inventory_Forecast_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Inventory_Forecast_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Inventory_Forecast_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'logistics-strategy-document')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Logistics_Strategy_Document.docx</h6>
                      <small>Shared by: David Chen - 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'logistics-strategy-document'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Logistics_Strategy_Document.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Logistics_Strategy_Document.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Logistics_Strategy_Document.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://drive.google.com" target="_blank" class="btn btn-primary">Open in Google Drive</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Docs Modal -->
  <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="docsModalLabel"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="docsTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="my-docs-tab" data-bs-toggle="tab" data-bs-target="#my-docs-content" type="button" role="tab" aria-controls="my-docs-content" aria-selected="true">My Docs</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="create-doc-tab" data-bs-toggle="tab" data-bs-target="#create-doc-content" type="button" role="tab" aria-controls="create-doc-content" aria-selected="false">Create New</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="shared-docs-tab" data-bs-toggle="tab" data-bs-target="#shared-docs-content" type="button" role="tab" aria-controls="shared-docs-content" aria-selected="false">Shared with me</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="docsTabContent">
            <!-- My Docs Tab -->
            <div class="tab-pane fade show active" id="my-docs-content" role="tabpanel" aria-labelledby="my-docs-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="docs-search" class="form-control" placeholder="Search documents...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <button class="btn btn-outline-secondary" id="refresh-docs-list">
                  <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                </button>
              </div>
              <div class="list-group">
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'supply-chain-procedures')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supply Chain Procedures</h6>
                      <small>Last edited: Today</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'supply-chain-procedures'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Supply_Chain_Procedures.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supply_Chain_Procedures.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Supply_Chain_Procedures.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'supplier-onboarding-process')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supplier Onboarding Process</h6>
                      <small>Last edited: Yesterday</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'supplier-onboarding-process'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Supplier_Onboarding_Process.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supplier_Onboarding_Process.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Supplier_Onboarding_Process.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'inventory-management-guidelines')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Inventory Management Guidelines</h6>
                      <small>Last edited: 3 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'inventory-management-guidelines'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Inventory_Management_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Inventory_Management_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Inventory_Management_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'logistics-strategy')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Logistics Strategy</h6>
                      <small>Last edited: 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'logistics-strategy'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Logistics_Strategy.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Logistics_Strategy.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Logistics_Strategy.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Create New Tab -->
            <div class="tab-pane fade" id="create-doc-content" role="tabpanel" aria-labelledby="create-doc-tab">
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header">Create from Template</div>
                    <div class="card-body">
                      <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Blank Document</h6>
                              <small>Start with an empty document</small>
                            </div>
                          </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Supplier Agreement</h6>
                              <small>Pre-formatted for supplier contracts</small>
                            </div>
                          </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Inventory Report</h6>
                              <small>Template for inventory documentation</small>
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header">Custom Document</div>
                    <div class="card-body">
                      <form>
                        <div class="mb-3">
                          <label for="doc-name" class="form-label">Document Name</label>
                          <input type="text" class="form-control" id="doc-name" placeholder="Enter name">
                        </div>
                        <div class="mb-3">
                          <label for="doc-folder" class="form-label">Save to Folder</label>
                          <select class="form-select" id="doc-folder">
                            <option selected>My Drive</option>
                            <option>Supplier Contracts</option>
                            <option>Inventory Reports</option>
                            <option>Team Shared Folder</option>
                          </select>
                        </div>
                        <div class="mb-3">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="share-with-team-doc">
                            <label class="form-check-label" for="share-with-team-doc">
                              Share with team
                            </label>
                          </div>
                        </div>
                        <button type="button" class="btn btn-primary">
                          <i class="bi bi-file-earmark-plus me-2"></i>Create Document
                        </button>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Shared with me Tab -->
            <div class="tab-pane fade" id="shared-docs-content" role="tabpanel" aria-labelledby="shared-docs-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="shared-docs-search" class="form-control" placeholder="Search shared documents...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-filter"></i> Filter
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-filter="all">All Documents</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="recent">Recently Shared</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="owned">Owned by me</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="not-owned">Not owned by me</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'supplier-quality-standards')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supplier Quality Standards</h6>
                      <small>Shared by: John Davis - 3 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'supplier-quality-standards'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Supplier_Quality_Standards.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supplier_Quality_Standards.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Supplier_Quality_Standards.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'warehouse-operations-manual')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Warehouse Operations Manual</h6>
                      <small>Shared by: Sarah Wilson - 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'warehouse-operations-manual'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Warehouse_Operations_Manual.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Warehouse_Operations_Manual.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Warehouse_Operations_Manual.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'logistics-partner-guidelines')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Logistics Partner Guidelines</h6>
                      <small>Shared by: David Chen - 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'logistics-partner-guidelines'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Logistics_Partner_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Logistics_Partner_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Logistics_Partner_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://docs.google.com" target="_blank" class="btn btn-primary">Open in Google Docs</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Sheets Modal -->
  <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="sheetsModalLabel"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="sheetsTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="my-sheets-tab" data-bs-toggle="tab" data-bs-target="#my-sheets-content" type="button" role="tab" aria-controls="my-sheets-content" aria-selected="true">My Sheets</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="create-sheet-tab" data-bs-toggle="tab" data-bs-target="#create-sheet-content" type="button" role="tab" aria-controls="create-sheet-content" aria-selected="false">Create New</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="import-sheet-tab" data-bs-toggle="tab" data-bs-target="#import-sheet-content" type="button" role="tab" aria-controls="import-sheet-content" aria-selected="false">Import</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="sheetsTabContent">
            <!-- My Sheets Tab -->
            <div class="tab-pane fade show active" id="my-sheets-content" role="tabpanel" aria-labelledby="my-sheets-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="sheets-search" class="form-control" placeholder="Search sheets...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <button class="btn btn-outline-secondary" id="refresh-sheets-list">
                  <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                </button>
              </div>
              <div class="list-group">
                <a href="#" ondblclick="openGoogleItem('sheets', 'spreadsheet', 'inventory-tracking-2025')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Inventory_Tracking_2025.xlsx</h6>
                      <small>Last edited: 2 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'inventory-tracking-2025'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Inventory_Tracking_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Inventory_Tracking_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Inventory_Tracking_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('sheets', 'spreadsheet', 'supplier-performance-q2-2025')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supplier_Performance_Q2_2025.xlsx</h6>
                      <small>Last edited: 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'supplier-performance-q2-2025'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Supplier_Performance_Q2_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supplier_Performance_Q2_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Supplier_Performance_Q2_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('sheets', 'spreadsheet', 'logistics-cost-analysis')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Logistics_Cost_Analysis.xlsx</h6>
                      <small>Last edited: 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'logistics-cost-analysis'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Logistics_Cost_Analysis.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Logistics_Cost_Analysis.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Logistics_Cost_Analysis.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('sheets', 'spreadsheet', 'supply-chain-metrics')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supply_Chain_Metrics.xlsx</h6>
                      <small>Last edited: 3 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'supply-chain-metrics'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Supply_Chain_Metrics.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supply_Chain_Metrics.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Supply_Chain_Metrics.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Create New Tab -->
            <div class="tab-pane fade" id="create-sheet-content" role="tabpanel" aria-labelledby="create-sheet-tab">
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header">Create from Template</div>
                    <div class="card-body">
                      <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Blank Spreadsheet</h6>
                              <small>Start with an empty spreadsheet</small>
                            </div>
                          </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Inventory Tracking Template</h6>
                              <small>Pre-formatted for inventory management</small>
                            </div>
                          </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Supplier Performance Template</h6>
                              <small>Includes KPI tracking and charts</small>
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header">Custom Spreadsheet</div>
                    <div class="card-body">
                      <form>
                        <div class="mb-3">
                          <label for="sheet-name" class="form-label">Spreadsheet Name</label>
                          <input type="text" class="form-control" id="sheet-name" placeholder="Enter name">
                        </div>
                        <div class="mb-3">
                          <label for="sheet-folder" class="form-label">Save to Folder</label>
                          <select class="form-select" id="sheet-folder">
                            <option selected>My Drive</option>
                            <option>Supplier Contracts</option>
                            <option>Inventory Reports</option>
                            <option>Team Shared Folder</option>
                          </select>
                        </div>
                        <div class="mb-3">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="share-with-team-sheet">
                            <label class="form-check-label" for="share-with-team-sheet">
                              Share with team
                            </label>
                          </div>
                        </div>
                        <button type="button" class="btn btn-primary">
                          <i class="bi bi-file-earmark-plus me-2"></i>Create Spreadsheet
                        </button>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Import Tab -->
            <div class="tab-pane fade" id="import-sheet-content" role="tabpanel" aria-labelledby="import-sheet-tab">
              <div class="row">
                <div class="col-md-7">
                  <div class="upload-area p-5 mb-3 text-center" id="sheet-dropzone" style="border: 2px dashed #ccc; border-radius: 5px; background-color: #f8f9fa;">
                    <i class="bi bi-file-earmark-arrow-up fs-1 text-muted mb-3"></i>
                    <h5>Drag & Drop Spreadsheet Files Here</h5>
                    <p class="text-muted">Supported formats: .xlsx, .xls, .csv, .ods</p>
                    <label for="sheet-file-upload" class="btn btn-primary">
                      <i class="bi bi-folder-plus me-2"></i>Browse Files
                    </label>
                    <input id="sheet-file-upload" type="file" accept=".xlsx,.xls,.csv,.ods" style="display: none;">
                  </div>
                </div>
                <div class="col-md-5">
                  <div class="card">
                    <div class="card-header">Import Options</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="import-sheet-name" class="form-label">New Spreadsheet Name</label>
                        <input type="text" class="form-control" id="import-sheet-name" placeholder="Enter name">
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="convert-to-sheets" checked>
                          <label class="form-check-label" for="convert-to-sheets">
                            Convert to Google Sheets format
                          </label>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="import-first-sheet-only">
                          <label class="form-check-label" for="import-first-sheet-only">
                            Import first sheet only
                          </label>
                        </div>
                      </div>
                      <button type="button" class="btn btn-primary" id="import-sheet-btn" disabled>
                        <i class="bi bi-upload me-2"></i>Import Spreadsheet
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://sheets.google.com" target="_blank" class="btn btn-primary">Open in Google Sheets</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Calendar Modal -->
  <div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="calendarModalLabel"><i class="bi bi-calendar3"></i> Google Calendar</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="calendarTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="events-tab" data-bs-toggle="tab" data-bs-target="#events-content" type="button" role="tab" aria-controls="events-content" aria-selected="true">Events</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="create-event-tab" data-bs-toggle="tab" data-bs-target="#create-event-content" type="button" role="tab" aria-controls="create-event-content" aria-selected="false">Create Event</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="calendar-settings-tab" data-bs-toggle="tab" data-bs-target="#calendar-settings-content" type="button" role="tab" aria-controls="calendar-settings-content" aria-selected="false">Settings</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="calendarTabContent">
            <!-- Events Tab -->
            <div class="tab-pane fade show active" id="events-content" role="tabpanel" aria-labelledby="events-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="btn-group" role="group">
                  <button type="button" class="btn btn-outline-primary active">Day</button>
                  <button type="button" class="btn btn-outline-primary">Week</button>
                  <button type="button" class="btn btn-outline-primary">Month</button>
                </div>
                <button class="btn btn-outline-secondary" id="refresh-events-list">
                  <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                </button>
              </div>
              <div class="calendar-date-header d-flex justify-content-between align-items-center mb-3">
                <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-chevron-left"></i></button>
                <h5 class="mb-0">May 10, 2025</h5>
                <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-chevron-right"></i></button>
              </div>
              <div class="list-group">
                <a href="#" onclick="openGoogleItem('calendar', 'event', 'supplier-meeting-acme')" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Supplier Meeting: Acme Corp</h6>
                    <small class="text-warning">Today</small>
                  </div>
                  <p class="mb-1">2:00 PM - 3:30 PM</p>
                  <small>Quarterly review meeting with Acme Corp suppliers</small>
                </a>
                <a href="#" onclick="openGoogleItem('calendar', 'event', 'inventory-audit')" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Inventory Audit</h6>
                    <small class="text-info">Tomorrow</small>
                  </div>
                  <p class="mb-1">9:00 AM - 12:00 PM</p>
                  <small>Quarterly inventory audit with the warehouse team</small>
                </a>
                <a href="#" onclick="openGoogleItem('calendar', 'event', 'quarterly-supply-chain-review')" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Quarterly Supply Chain Review</h6>
                    <small class="text-secondary">Next Week</small>
                  </div>
                  <p class="mb-1">May 15, 10:00 AM - 12:00 PM</p>
                  <small>Quarterly supply chain performance review with management</small>
                </a>
                <a href="#" onclick="openGoogleItem('calendar', 'event', 'logistics-planning')" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Logistics Planning Meeting</h6>
                    <small class="text-secondary">Next Week</small>
                  </div>
                  <p class="mb-1">May 17, 1:00 PM - 3:00 PM</p>
                  <small>Planning session for next quarter logistics operations</small>
                </a>
              </div>
            </div>

            <!-- Create Event Tab -->
            <div class="tab-pane fade" id="create-event-content" role="tabpanel" aria-labelledby="create-event-tab">
              <form>
                <div class="mb-3">
                  <label for="event-title" class="form-label">Event Title</label>
                  <input type="text" class="form-control" id="event-title" placeholder="Enter event title">
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="event-start-date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="event-start-date">
                  </div>
                  <div class="col-md-6">
                    <label for="event-start-time" class="form-label">Start Time</label>
                    <input type="time" class="form-control" id="event-start-time">
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="event-end-date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="event-end-date">
                  </div>
                  <div class="col-md-6">
                    <label for="event-end-time" class="form-label">End Time</label>
                    <input type="time" class="form-control" id="event-end-time">
                  </div>
                </div>
                <div class="mb-3">
                  <label for="event-location" class="form-label">Location</label>
                  <input type="text" class="form-control" id="event-location" placeholder="Enter location or meeting link">
                </div>
                <div class="mb-3">
                  <label for="event-description" class="form-label">Description</label>
                  <textarea class="form-control" id="event-description" rows="3" placeholder="Enter event description"></textarea>
                </div>
                <div class="mb-3">
                  <label for="event-guests" class="form-label">Guests</label>
                  <input type="text" class="form-control" id="event-guests" placeholder="Enter email addresses (comma separated)">
                </div>
                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="event-all-day">
                    <label class="form-check-label" for="event-all-day">
                      All day event
                    </label>
                  </div>
                </div>
                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="event-notification">
                    <label class="form-check-label" for="event-notification">
                      Send notification to guests
                    </label>
                  </div>
                </div>
                <button type="button" class="btn btn-primary">
                  <i class="bi bi-calendar-plus me-2"></i>Create Event
                </button>
              </form>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="calendar-settings-content" role="tabpanel" aria-labelledby="calendar-settings-tab">
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header">Calendar Display</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="default-view" class="form-label">Default View</label>
                        <select class="form-select" id="default-view">
                          <option selected>Week</option>
                          <option>Month</option>
                          <option>Day</option>
                          <option>Agenda</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="start-day" class="form-label">Week Starts On</label>
                        <select class="form-select" id="start-day">
                          <option selected>Sunday</option>
                          <option>Monday</option>
                          <option>Saturday</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="show-weekends" checked>
                          <label class="form-check-label" for="show-weekends">
                            Show weekends
                          </label>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="show-declined" checked>
                          <label class="form-check-label" for="show-declined">
                            Show declined events
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header">Notifications</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="default-notification" class="form-label">Default Notification Time</label>
                        <select class="form-select" id="default-notification">
                          <option>None</option>
                          <option>At time of event</option>
                          <option selected>10 minutes before</option>
                          <option>30 minutes before</option>
                          <option>1 hour before</option>
                          <option>1 day before</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="email-notifications" checked>
                          <label class="form-check-label" for="email-notifications">
                            Email notifications
                          </label>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="browser-notifications" checked>
                          <label class="form-check-label" for="browser-notifications">
                            Browser notifications
                          </label>
                        </div>
                      </div>
                      <button type="button" class="btn btn-primary">
                        <i class="bi bi-save me-2"></i>Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://calendar.google.com" target="_blank" class="btn btn-primary">Open in Google Calendar</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Maps Modal -->
  <div class="modal fade" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="mapsModalLabel"><i class="bi bi-geo-alt"></i> Google Maps</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="mapsTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="map-view-tab" data-bs-toggle="tab" data-bs-target="#map-view-content" type="button" role="tab" aria-controls="map-view-content" aria-selected="true">Map View</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations-content" type="button" role="tab" aria-controls="locations-content" aria-selected="false">Saved Locations</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="directions-tab" data-bs-toggle="tab" data-bs-target="#directions-content" type="button" role="tab" aria-controls="directions-content" aria-selected="false">Directions</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="mapsTabContent">
            <!-- Map View Tab -->
            <div class="tab-pane fade show active" id="map-view-content" role="tabpanel" aria-labelledby="map-view-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" class="form-control" placeholder="Search locations" aria-label="Search locations">
                  <button class="btn btn-primary" type="button">
                    <i class="bi bi-search"></i>
                  </button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary active">Map</button>
                  <button type="button" class="btn btn-outline-primary">Satellite</button>
                  <button type="button" class="btn btn-outline-primary">Terrain</button>
                </div>
              </div>
              <div class="ratio ratio-16x9">
                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
              </div>
              <div class="d-flex justify-content-end mt-2">
                <button class="btn btn-sm btn-outline-secondary me-2">
                  <i class="bi bi-plus-lg"></i> Zoom In
                </button>
                <button class="btn btn-sm btn-outline-secondary me-2">
                  <i class="bi bi-dash-lg"></i> Zoom Out
                </button>
                <button class="btn btn-sm btn-outline-secondary">
                  <i class="bi bi-pin-map"></i> Save Location
                </button>
              </div>
            </div>

            <!-- Saved Locations Tab -->
            <div class="tab-pane fade" id="locations-content" role="tabpanel" aria-labelledby="locations-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" class="form-control" placeholder="Search saved locations">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <button class="btn btn-primary">
                  <i class="bi bi-pin-map-fill me-2"></i>Add New Location
                </button>
              </div>
              <div class="list-group">
                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                  <div>
                    <i class="bi bi-building me-2"></i>
                    <span>Main Warehouse</span>
                    <small class="text-muted d-block">123 Warehouse Ave, New York, NY 10001</small>
                  </div>
                  <span class="badge bg-primary rounded-pill">HQ</span>
                </a>
                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                  <div>
                    <i class="bi bi-building me-2"></i>
                    <span>Supplier: Acme Corp</span>
                    <small class="text-muted d-block">456 Supplier Street, New York, NY 10002</small>
                  </div>
                  <span class="badge bg-primary rounded-pill">Supplier</span>
                </a>
                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                  <div>
                    <i class="bi bi-building me-2"></i>
                    <span>Distribution Center</span>
                    <small class="text-muted d-block">789 Distribution Blvd, New York, NY 10003</small>
                  </div>
                  <span class="badge bg-secondary rounded-pill">Distribution</span>
                </a>
                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                  <div>
                    <i class="bi bi-building me-2"></i>
                    <span>Regional Storage Facility</span>
                    <small class="text-muted d-block">321 Storage Road, Brooklyn, NY 11201</small>
                  </div>
                  <span class="badge bg-info rounded-pill">Storage</span>
                </a>
              </div>
            </div>

            <!-- Directions Tab -->
            <div class="tab-pane fade" id="directions-content" role="tabpanel" aria-labelledby="directions-tab">
              <div class="row mb-3">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="directions-from" class="form-label">From</label>
                    <input type="text" class="form-control" id="directions-from" placeholder="Starting point or address">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="directions-to" class="form-label">To</label>
                    <input type="text" class="form-control" id="directions-to" placeholder="Destination">
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <div class="btn-group w-100" role="group">
                  <button type="button" class="btn btn-outline-primary active">
                    <i class="bi bi-car-front"></i> Driving
                  </button>
                  <button type="button" class="btn btn-outline-primary">
                    <i class="bi bi-train-front"></i> Transit
                  </button>
                  <button type="button" class="btn btn-outline-primary">
                    <i class="bi bi-person-walking"></i> Walking
                  </button>
                  <button type="button" class="btn btn-outline-primary">
                    <i class="bi bi-bicycle"></i> Cycling
                  </button>
                </div>
              </div>
              <div class="d-grid gap-2 mb-3">
                <button class="btn btn-primary" type="button">
                  <i class="bi bi-signpost-split me-2"></i>Get Directions
                </button>
              </div>
              <div class="card">
                <div class="card-header">
                  <i class="bi bi-clock"></i> Estimated Travel Times
                </div>
                <ul class="list-group list-group-flush">
                  <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-car-front me-2"></i> By Car
                    </div>
                    <span>25 mins</span>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-train-front me-2"></i> By Transit
                    </div>
                    <span>45 mins</span>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-person-walking me-2"></i> Walking
                    </div>
                    <span>2 hours 30 mins</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://maps.google.com" target="_blank" class="btn btn-primary">Open in Google Maps</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Attachments Modal -->
  <div class="modal fade" id="attachmentsModal" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="attachmentsModalLabel"><i class="bi bi-paperclip"></i> Attachments</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="attachmentsTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#attachment-files-content" type="button" role="tab" aria-controls="attachment-files-content" aria-selected="true">Files</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="upload-attachment-tab" data-bs-toggle="tab" data-bs-target="#upload-attachment-content" type="button" role="tab" aria-controls="upload-attachment-content" aria-selected="false">Upload</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="shared-attachments-tab" data-bs-toggle="tab" data-bs-target="#shared-attachments-content" type="button" role="tab" aria-controls="shared-attachments-content" aria-selected="false">Shared with me</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="attachmentsTabContent">
            <!-- Files Tab -->
            <div class="tab-pane fade show active" id="attachment-files-content" role="tabpanel" aria-labelledby="files-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="attachment-search" class="form-control" placeholder="Search attachments...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="dropdown">
                  <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="attachmentFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    Filter By
                  </button>
                  <ul class="dropdown-menu" aria-labelledby="attachmentFilterDropdown">
                    <li><a class="dropdown-item" href="#">All Files</a></li>
                    <li><a class="dropdown-item" href="#">Documents</a></li>
                    <li><a class="dropdown-item" href="#">Images</a></li>
                    <li><a class="dropdown-item" href="#">Spreadsheets</a></li>
                    <li><a class="dropdown-item" href="#">PDFs</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supply_Chain_Analysis_Q2_2025.pdf</h6>
                      <small>2.4 MB - Uploaded: Yesterday</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Supply_Chain_Analysis_Q2_2025.pdf')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Supply_Chain_Analysis_Q2_2025.pdf')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supply_Chain_Analysis_Q2_2025.pdf')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Supply_Chain_Analysis_Q2_2025.pdf')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Inventory_Tracking_2025.xlsx</h6>
                      <small>1.8 MB - Uploaded: 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Inventory_Tracking_2025.xlsx')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Inventory_Tracking_2025.xlsx')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Inventory_Tracking_2025.xlsx')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Inventory_Tracking_2025.xlsx')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-image text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Warehouse_Layout.jpg</h6>
                      <small>3.2 MB - Uploaded: 1 month ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('image', 'Warehouse_Layout.jpg')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Warehouse_Layout.jpg')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Warehouse_Layout.jpg')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Warehouse_Layout.jpg')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supplier_Contract_Template.docx</h6>
                      <small>1.5 MB - Uploaded: 2 months ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Supplier_Contract_Template.docx')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Supplier_Contract_Template.docx')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supplier_Contract_Template.docx')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Supplier_Contract_Template.docx')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <!-- No download selected button needed -->
            </div>

            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload-attachment-content" role="tabpanel" aria-labelledby="upload-attachment-tab">
              <div class="row">
                <div class="col-md-7">
                  <div class="upload-area p-5 mb-3 text-center" id="attachment-dropzone" style="border: 2px dashed #ccc; border-radius: 5px; background-color: #f8f9fa;">
                    <i class="bi bi-cloud-arrow-up fs-1 text-muted mb-3"></i>
                    <h5>Drag & Drop Files Here</h5>
                    <p class="text-muted">or</p>
                    <label for="attachment-file-upload" class="btn btn-primary">
                      <i class="bi bi-folder-plus me-2"></i>Browse Files
                    </label>
                    <input id="attachment-file-upload" type="file" multiple style="display: none;">
                    <p class="text-muted mt-3 small">Maximum file size: 50MB</p>
                  </div>
                  <div class="progress mb-3" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                  </div>
                </div>
                <div class="col-md-5">
                  <div class="card">
                    <div class="card-header">Upload Options</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="attachment-supplier" class="form-label">Attach to Supplier</label>
                        <select class="form-select" id="attachment-supplier">
                          <option selected>Acme Corp</option>
                          <option>Global Logistics</option>
                          <option>Tech Supplies Inc</option>
                          <option>All Suppliers</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="attachment-category" class="form-label">Category</label>
                        <select class="form-select" id="attachment-category">
                          <option selected>Contracts</option>
                          <option>Inventory Reports</option>
                          <option>Logistics Documents</option>
                          <option>Other</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="share-with-team-attachment">
                          <label class="form-check-label" for="share-with-team-attachment">
                            Share with team
                          </label>
                        </div>
                      </div>
                      <button type="button" class="btn btn-primary" id="start-attachment-upload" disabled>
                        <i class="bi bi-upload me-2"></i>Upload Files
                      </button>
                    </div>
                  </div>
                  <div id="attachment-upload-list" class="mt-3">
                    <!-- Files to upload will be listed here -->
                  </div>
                </div>
              </div>
            </div>

            <!-- Shared with me Tab -->
            <div class="tab-pane fade" id="shared-attachments-content" role="tabpanel" aria-labelledby="shared-attachments-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="shared-attachment-search" class="form-control" placeholder="Search shared files...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-filter"></i> Filter
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="other">Other</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Supplier_Evaluation_Criteria.pdf</h6>
                      <small>Shared by: John Davis - 3 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Supplier_Evaluation_Criteria.pdf')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Supplier_Evaluation_Criteria.pdf')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Supplier_Evaluation_Criteria.pdf')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Supplier_Evaluation_Criteria.pdf')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Inventory_Forecast_2025.xlsx</h6>
                      <small>Shared by: Sarah Wilson - 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Inventory_Forecast_2025.xlsx')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Inventory_Forecast_2025.xlsx')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Inventory_Forecast_2025.xlsx')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Inventory_Forecast_2025.xlsx')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Logistics_Strategy_Document.docx</h6>
                      <small>Shared by: David Chen - 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Logistics_Strategy_Document.docx')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Logistics_Strategy_Document.docx')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Logistics_Strategy_Document.docx')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Logistics_Strategy_Document.docx')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <!-- No download selected button needed -->
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // Function to fetch and display tasks from Task Management System
    function fetchTasks() {
      const tasksContainer = document.getElementById('tasks-container');

      // Show loading spinner
      tasksContainer.innerHTML = `
        <div class="d-flex justify-content-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      `;

      // Fetch tasks from the integration endpoint
      fetch('/api/integration/tasks')
        .then(response => response.json())
        .then(data => {
          if (data.status === 'success' && data.data.tasks && data.data.tasks.length > 0) {
            // Create task cards
            let tasksHTML = '<div class="row">';

            data.data.tasks.forEach(task => {
              // Determine status badge class
              let statusClass = '';
              if (task.status === 'Completed') statusClass = 'bg-success';
              else if (task.status === 'In Progress') statusClass = 'bg-warning';
              else if (task.status === 'Pending') statusClass = 'bg-info';
              else statusClass = 'bg-secondary';

              // Determine priority badge class
              let priorityClass = '';
              if (task.priority === 'High') priorityClass = 'bg-danger';
              else if (task.priority === 'Medium') priorityClass = 'bg-warning';
              else priorityClass = 'bg-info';

              // Create task card
              tasksHTML += `
                <div class="col-md-4 mb-3">
                  <div class="card h-100">
                    <div class="card-body">
                      <div class="d-flex justify-content-between align-items-start">
                        <h6 class="card-title">${task.title}</h6>
                        <span class="badge ${statusClass}">${task.status}</span>
                      </div>
                      <p class="card-text small text-muted">${task.description}</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="badge ${priorityClass}">${task.priority} Priority</span>
                        <small class="text-muted">Due: ${task.dueDate}</small>
                      </div>
                    </div>
                    <div class="card-footer bg-transparent">
                      <a href="http://localhost:3009/task/${task.id}" target="_blank" class="btn btn-sm btn-outline-primary">View in TM</a>
                    </div>
                  </div>
                </div>
              `;
            });

            tasksHTML += '</div>';
            tasksContainer.innerHTML = tasksHTML;
          } else {
            // No tasks found
            tasksContainer.innerHTML = `
              <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> No supply chain related tasks found in the Task Management System.
              </div>
            `;
          }
        })
        .catch(error => {
          console.error('Error fetching tasks:', error);
          tasksContainer.innerHTML = `
            <div class="alert alert-danger">
              <i class="bi bi-exclamation-triangle"></i> Failed to fetch tasks from Task Management System.
              Please make sure the Task Management System is running.
            </div>
          `;
        });
    }

    // Add event listener for refresh button
    document.getElementById('refresh-tasks-btn').addEventListener('click', fetchTasks);

    // Gmail functionality
    function openEmail(emailId) {
      // Show the read email tab
      const readEmailTab = document.getElementById('read-email-tab');
      readEmailTab.style.display = 'block';
      readEmailTab.click();

      // Update email content based on the emailId
      let subject, from, avatar, time, body, attachments;

      switch(emailId) {
        case 'supplier-portal-email':
          subject = 'New inventory request submitted';
          from = 'Supplier Portal';
          avatar = 'SP';
          time = '10 minutes ago';
          body = `<p>Hello,</p>
                  <p>A new inventory request has been submitted by Acme Corp through the supplier portal.</p>
                  <p>Request details:</p>
                  <ul>
                    <li>Supplier: Acme Corp</li>
                    <li>Request ID: REQ-2025-0587</li>
                    <li>Items:
                      <ul>
                        <li>Widget A (SKU: W-A-2025) - 500 units</li>
                        <li>Widget B (SKU: W-B-2025) - 300 units</li>
                        <li>Component C (SKU: C-C-2025) - 1,000 units</li>
                      </ul>
                    </li>
                    <li>Requested Delivery Date: June 15, 2025</li>
                    <li>Priority: Medium</li>
                  </ul>
                  <p>Please review and approve this request at your earliest convenience. The supplier has indicated that they need confirmation within 48 hours to ensure timely delivery.</p>
                  <p>Best regards,<br>Supplier Portal System</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                        <div class="list-group">
                          <a href="#" onclick="openGoogleItem('drive', 'pdf', 'inventory-request-form')" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                            <div>
                              <div>inventory_request_form.pdf</div>
                              <small class="text-muted">1.2 MB</small>
                            </div>
                            <div class="btn-group ms-auto">
                              <button class="btn btn-sm btn-outline-primary"><i class="bi bi-download"></i></button>
                              <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-share"></i></button>
                              <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                            </div>
                          </a>
                          <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'inventory-details')" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                            <div>
                              <div>inventory_details.xlsx</div>
                              <small class="text-muted">0.8 MB</small>
                            </div>
                            <div class="btn-group ms-auto">
                              <button class="btn btn-sm btn-outline-primary"><i class="bi bi-download"></i></button>
                              <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-share"></i></button>
                              <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                            </div>
                          </a>
                        </div>`;
          break;
        case 'warehouse-manager-email':
          subject = 'Inventory reconciliation report';
          from = 'Warehouse Manager';
          avatar = 'WM';
          time = '1 hour ago';
          body = `<p>Hello,</p>
                  <p>The inventory reconciliation report for Q2 2025 is now available. We've completed the physical count and reconciled it with our system records.</p>
                  <p>Key findings:</p>
                  <ul>
                    <li>Overall accuracy rate: 97.8%</li>
                    <li>Discrepancies found in 12 SKUs (details in attached spreadsheet)</li>
                    <li>Major variances in the electronics components section</li>
                    <li>Recommended adjustments to inventory valuation: $12,450</li>
                  </ul>
                  <p>Please review the attached report and let me know if you have any questions or need additional information.</p>
                  <p>Regards,<br>Warehouse Manager</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                        <div class="list-group">
                          <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'inventory-reconciliation-q2-2025')" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                            <div>
                              <div>Inventory_Reconciliation_Q2_2025.xlsx</div>
                              <small class="text-muted">1.5 MB</small>
                            </div>
                            <div class="btn-group ms-auto">
                              <button class="btn btn-sm btn-outline-primary"><i class="bi bi-download"></i></button>
                              <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-share"></i></button>
                              <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                            </div>
                          </a>
                          <a href="#" onclick="openGoogleItem('drive', 'pdf', 'inventory-variance-report')" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                            <div>
                              <div>Inventory_Variance_Report.pdf</div>
                              <small class="text-muted">2.3 MB</small>
                            </div>
                            <div class="btn-group ms-auto">
                              <button class="btn btn-sm btn-outline-primary"><i class="bi bi-download"></i></button>
                              <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-share"></i></button>
                              <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                            </div>
                          </a>
                        </div>`;
          break;
        case 'logistics-manager-email':
          subject = 'Shipping delay notification';
          from = 'Logistics Manager';
          avatar = 'LM';
          time = '3 hours ago';
          body = `<p>Dear Supply Chain Team,</p>
                  <p>I'm writing to inform you that there is a shipping delay for order #12345 due to severe weather conditions affecting the eastern shipping routes.</p>
                  <p>Details:</p>
                  <ul>
                    <li>Order #12345 for customer XYZ Corporation</li>
                    <li>Original delivery date: May 10, 2025</li>
                    <li>Revised estimated delivery: May 13, 2025</li>
                    <li>Reason: Severe storms in the Atlantic causing port closures</li>
                  </ul>
                  <p>We've already notified the customer about this delay and they understand the situation. We're monitoring the weather conditions closely and will update you if there are any further changes to the delivery schedule.</p>
                  <p>Best regards,<br>Logistics Manager</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (1)</h6>
                        <div class="list-group">
                          <a href="#" onclick="openGoogleItem('drive', 'pdf', 'shipping-delay-report')" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                            <div>
                              <div>Shipping_Delay_Report.pdf</div>
                              <small class="text-muted">0.9 MB</small>
                            </div>
                            <div class="btn-group ms-auto">
                              <button class="btn btn-sm btn-outline-primary"><i class="bi bi-download"></i></button>
                              <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-share"></i></button>
                              <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                            </div>
                          </a>
                        </div>`;
          break;
        case 'sent-supplier-team':
          subject = 'Quarterly Supplier Review Meeting';
          from = 'You';
          avatar = 'ME';
          time = '1 day ago';
          body = `<p>Dear Supplier Team,</p>
                  <p>Please find attached the agenda for our upcoming quarterly supplier review meeting scheduled for next Monday at 10:00 AM.</p>
                  <p>The agenda includes:</p>
                  <ul>
                    <li>Q2 performance review</li>
                    <li>Supply chain optimization initiatives</li>
                    <li>Quality improvement programs</li>
                    <li>Upcoming product changes and their impact on suppliers</li>
                  </ul>
                  <p>Please review the materials before the meeting and come prepared with any questions or concerns.</p>
                  <p>Best regards,<br>Supply Chain Manager</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                        <div class="list-group">
                          <a href="#" onclick="openGoogleItem('docs', 'document', 'supplier-review-agenda')" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                            <div>
                              <div>Supplier_Review_Agenda.docx</div>
                              <small class="text-muted">0.5 MB</small>
                            </div>
                            <div class="btn-group ms-auto">
                              <button class="btn btn-sm btn-outline-primary"><i class="bi bi-download"></i></button>
                              <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-share"></i></button>
                              <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                            </div>
                          </a>
                          <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'supplier-performance-metrics')" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                            <div>
                              <div>Supplier_Performance_Metrics.xlsx</div>
                              <small class="text-muted">1.2 MB</small>
                            </div>
                            <div class="btn-group ms-auto">
                              <button class="btn btn-sm btn-outline-primary"><i class="bi bi-download"></i></button>
                              <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-share"></i></button>
                              <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                            </div>
                          </a>
                        </div>`;
          break;
        default:
          subject = 'Email Subject';
          from = 'Sender Name';
          avatar = 'SN';
          time = 'Time';
          body = '<p>Email body content...</p>';
          attachments = '';
      }

      // Update the email content
      document.getElementById('read-email-subject').textContent = subject;
      document.getElementById('read-email-from').textContent = from;
      document.getElementById('read-email-avatar').textContent = avatar;
      document.getElementById('read-email-time').textContent = time;
      document.getElementById('read-email-body').innerHTML = body;
      document.getElementById('read-email-attachments').innerHTML = attachments;

      // Store the current email ID for reply functionality
      document.getElementById('read-email-tab').dataset.currentEmail = emailId;
    }

    function replyToEmail() {
      // Show the reply email tab
      const replyEmailTab = document.getElementById('reply-email-tab');
      replyEmailTab.style.display = 'block';
      replyEmailTab.click();

      // Get the current email details
      const subject = document.getElementById('read-email-subject').textContent;
      const from = document.getElementById('read-email-from').textContent;
      const avatar = document.getElementById('read-email-avatar').textContent;
      const time = document.getElementById('read-email-time').textContent;
      const body = document.getElementById('read-email-body').innerHTML;

      // Update the reply form
      document.getElementById('reply-email-subject').textContent = subject;
      document.getElementById('reply-email-from').textContent = from;
      document.getElementById('reply-email-avatar').textContent = avatar;
      document.getElementById('reply-email-time').textContent = time;
      document.getElementById('reply-email-original-body').innerHTML = body;

      // Focus on the reply textarea
      setTimeout(() => {
        document.getElementById('reply-email-body').focus();
      }, 500);
    }

    function sendEmail() {
      // Get the email details
      const to = document.getElementById('email-to').value;
      const subject = document.getElementById('email-subject').value;
      const body = document.getElementById('email-body').value;

      // Validate inputs
      if (!to || !subject || !body) {
        alert('Please fill in all required fields (To, Subject, and Message).');
        return;
      }

      // Simulate sending email
      alert('Email sent successfully!');

      // Clear the form
      document.getElementById('email-to').value = '';
      document.getElementById('email-cc').value = '';
      document.getElementById('email-subject').value = '';
      document.getElementById('email-body').value = '';
      document.getElementById('email-attachments').value = '';
      document.getElementById('attachment-list').innerHTML = '';

      // Return to inbox
      document.getElementById('inbox-tab').click();
    }

    function sendReply() {
      // Get the reply details
      const replyBody = document.getElementById('reply-email-body').value;

      // Validate input
      if (!replyBody) {
        alert('Please enter a reply message.');
        return;
      }

      // Simulate sending reply
      alert('Reply sent successfully!');

      // Clear the form
      document.getElementById('reply-email-body').value = '';
      document.getElementById('reply-email-attachments').value = '';
      document.getElementById('reply-attachment-list').innerHTML = '';

      // Return to inbox
      document.getElementById('inbox-tab').click();

      // Hide the read and reply tabs
      document.getElementById('read-email-tab').style.display = 'none';
      document.getElementById('reply-email-tab').style.display = 'none';
    }

    // Function to open specific Google items
    function openGoogleItem(app, type, itemId) {
      // For general app buttons (without specific item), show the modal
      if (app === 'drive' && !type && !itemId) {
        const driveModal = new bootstrap.Modal(document.getElementById('driveModal'));
        driveModal.show();
        return;
      } else if (app === 'docs' && !type && !itemId) {
        const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
        docsModal.show();
        return;
      } else if (app === 'sheets' && !type && !itemId) {
        const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
        sheetsModal.show();
        return;
      } else if (app === 'attachments' && !type && !itemId) {
        const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
        attachmentsModal.show();
        return;
      }

      // For specific items, directly open the file in a viewer
      try {
        // Create a simulated file viewer
        const fileViewerModal = new bootstrap.Modal(document.getElementById('fileViewerModal') || createFileViewerModal());

        // Set the file information
        const fileTitle = document.getElementById('fileViewerTitle');
        const fileContent = document.getElementById('fileViewerContent');

        // Format the item ID to make it more readable
        const readableId = itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        // Set the title based on the app and type
        fileTitle.innerHTML = `<i class="bi ${getFileIcon(app, type)}"></i> ${readableId}`;

        // Set the content based on the file type
        fileContent.innerHTML = getFileContent(app, type, itemId);

        // Show the modal
        fileViewerModal.show();
      } catch (error) {
        console.error('Error opening file:', error);
        alert('Could not open the file. Please try again later.');
      }
    }

    // Helper function to create a file viewer modal if it doesn't exist
    function createFileViewerModal() {
      const modal = document.createElement('div');
      modal.className = 'modal fade';
      modal.id = 'fileViewerModal';
      modal.tabIndex = '-1';
      modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
      modal.setAttribute('aria-hidden', 'true');

      modal.innerHTML = `
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
              <h5 class="modal-title" id="fileViewerTitle"></h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">Download</button>
              <button type="button" class="btn btn-success" onclick="shareCurrentFile()">Share</button>
              <button type="button" class="btn btn-danger" onclick="deleteCurrentFile()">Delete</button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      return modal;
    }

    // Helper function to get the appropriate icon for the file type
    function getFileIcon(app, type) {
      if (app === 'drive') {
        if (type === 'folder') return 'bi-folder-fill text-primary';
        if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
        if (type === 'image') return 'bi-file-earmark-image text-info';
        return 'bi-file-earmark text-secondary';
      } else if (app === 'docs') {
        return 'bi-file-earmark-text text-primary';
      } else if (app === 'sheets') {
        return 'bi-file-earmark-spreadsheet text-success';
      } else if (app === 'calendar') {
        return 'bi-calendar-event text-primary';
      }
      return 'bi-file-earmark text-secondary';
    }

    // Helper function to generate content for the file viewer
    function getFileContent(app, type, itemId) {
      // Generate simulated content based on file type
      if (app === 'drive') {
        if (type === 'folder') {
          return `<div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> This is a folder view. In a real application, this would show the contents of the folder.
                  </div>
                  <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action">
                      <i class="bi bi-file-earmark-text me-2"></i> Document 1.docx
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                      <i class="bi bi-file-earmark-spreadsheet me-2"></i> Spreadsheet 1.xlsx
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                      <i class="bi bi-file-earmark-pdf me-2"></i> Report.pdf
                    </a>
                  </div>`;
        } else if (type === 'pdf') {
          return `<div class="text-center">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                    <h4 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                    <div class="alert alert-info mt-3">
                      <i class="bi bi-info-circle"></i> This is a PDF viewer. In a real application, the PDF would be displayed here.
                    </div>
                    <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                      <h5>Document Preview</h5>
                      <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                      <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                    </div>
                  </div>`;
        } else if (type === 'image') {
          return `<div class="text-center">
                    <div class="alert alert-info">
                      <i class="bi bi-info-circle"></i> This is an image viewer. In a real application, the image would be displayed here.
                    </div>
                    <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                      <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                      <h5 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h5>
                    </div>
                  </div>`;
        }
      } else if (app === 'docs') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                  <hr>
                  <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                  <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                </div>`;
      } else if (app === 'sheets') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                  <hr>
                  <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                      <thead>
                        <tr>
                          <th>Item</th>
                          <th>Quantity</th>
                          <th>Price</th>
                          <th>Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>Item 1</td>
                          <td>10</td>
                          <td>$10.00</td>
                          <td>$100.00</td>
                        </tr>
                        <tr>
                          <td>Item 2</td>
                          <td>5</td>
                          <td>$20.00</td>
                          <td>$100.00</td>
                        </tr>
                        <tr>
                          <td>Item 3</td>
                          <td>2</td>
                          <td>$30.00</td>
                          <td>$60.00</td>
                        </tr>
                        <tr>
                          <td colspan="3" class="text-end"><strong>Total</strong></td>
                          <td><strong>$260.00</strong></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>`;
      } else if (app === 'calendar') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                  <hr>
                  <div class="row">
                    <div class="col-md-6">
                      <p><strong>Date:</strong> May 15, 2025</p>
                      <p><strong>Time:</strong> 10:00 AM - 11:30 AM</p>
                      <p><strong>Location:</strong> Conference Room A</p>
                    </div>
                    <div class="col-md-6">
                      <p><strong>Organizer:</strong> John Doe</p>
                      <p><strong>Attendees:</strong> 5</p>
                      <p><strong>Status:</strong> Confirmed</p>
                    </div>
                  </div>
                  <div class="mt-3">
                    <h5>Description</h5>
                    <p>This is a calendar event viewer. In a real application, the event details would be displayed here.</p>
                  </div>
                </div>`;
      }

      return `<div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
              </div>`;
    }

    // Function to download the current file
    function downloadCurrentFile() {
      const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();

      // Extract the file name from the title
      let fileName = fileTitle;

      // If the title contains an icon, remove it
      if (fileTitle.includes('<i class="')) {
        fileName = fileTitle.substring(fileTitle.indexOf('</i>') + 4).trim();
      }

      // Add file extension if not present
      if (!fileName.includes('.')) {
        // Determine file extension based on content
        const fileContent = document.getElementById('fileViewerContent');
        if (fileContent.innerHTML.includes('table-bordered')) {
          fileName += '.xlsx';
        } else if (fileContent.innerHTML.includes('document viewer')) {
          fileName += '.docx';
        } else if (fileContent.innerHTML.includes('PDF viewer')) {
          fileName += '.pdf';
        } else if (fileContent.innerHTML.includes('image viewer')) {
          fileName += '.png';
        } else {
          fileName += '.txt';
        }
      }

      alert(`Downloading ${fileName}...`);
      // In a real application, this would trigger a download of the file
    }

    // Function to share the current file
    function shareCurrentFile() {
      const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
      const email = prompt(`Enter email address to share ${fileTitle} with:`);
      if (email) {
        alert(`${fileTitle} has been shared with ${email}.`);
        // In a real application, this would share the file with the specified email
      }
    }

    // Function to delete the current file
    function deleteCurrentFile() {
      const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
      const confirmDelete = confirm(`Are you sure you want to delete ${fileTitle}?`);
      if (confirmDelete) {
        alert(`${fileTitle} has been deleted.`);
        // In a real application, this would delete the file
        // Close the modal after deletion
        const modal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
        if (modal) {
          modal.hide();
        }
      }
    }

    // Initialize sidebar toggle functionality
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');

    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', function(e) {
        e.preventDefault();
        sidebar.classList.toggle('show');
      });
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
      if (window.innerWidth < 768) {
        const isClickInsideSidebar = sidebar.contains(event.target);
        const isClickOnToggle = sidebarToggle && sidebarToggle.contains(event.target);

        if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
          sidebar.classList.remove('show');
        }
      }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      if (window.innerWidth >= 768 && sidebar.classList.contains('show')) {
        sidebar.classList.remove('show');
      }
    });

    // Fetch dashboard data from API
    fetch('/api/dashboard')
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          const dashboardData = data.data;

          // Update metrics
          document.getElementById('total-inventory').textContent = dashboardData.metrics.totalInventory;
          document.getElementById('active-suppliers').textContent = dashboardData.metrics.activeSuppliers;
          document.getElementById('low-stock-items').textContent = dashboardData.metrics.lowStockItems;

          // Populate inventory table
          const inventoryTable = document.getElementById('inventory-table').getElementsByTagName('tbody')[0];
          dashboardData.inventory.forEach(item => {
            const row = inventoryTable.insertRow();

            const nameCell = row.insertCell(0);
            nameCell.textContent = item.name;

            const locationCell = row.insertCell(1);
            locationCell.textContent = item.location;

            const stockCell = row.insertCell(2);
            stockCell.innerHTML = `<strong>${item.currentStock} ${item.unit}</strong>`;

            const statusCell = row.insertCell(3);
            let statusClass = '';
            if (item.status === 'In Stock') statusClass = 'status-in-stock';
            else if (item.status === 'Low Stock') statusClass = 'status-low-stock';
            else statusClass = 'status-out-of-stock';

            statusCell.innerHTML = `<span class="status-badge ${statusClass}">${item.status}</span>`;

            const reorderCell = row.insertCell(4);
            reorderCell.textContent = `${item.reorderPoint} ${item.unit}`;
          });

          // Populate suppliers table
          const suppliersTable = document.getElementById('suppliers-table').getElementsByTagName('tbody')[0];
          dashboardData.suppliers.forEach(supplier => {
            const row = suppliersTable.insertRow();

            const nameCell = row.insertCell(0);
            nameCell.textContent = supplier.name;

            const categoryCell = row.insertCell(1);
            categoryCell.textContent = supplier.category;

            const statusCell = row.insertCell(2);
            let statusClass = '';
            if (supplier.status === 'Active') statusClass = 'status-active';
            else if (supplier.status === 'On Hold') statusClass = 'status-on-hold';
            else statusClass = 'status-inactive';

            statusCell.innerHTML = `<span class="status-badge ${statusClass}">${supplier.status}</span>`;

            const leadTimeCell = row.insertCell(3);
            leadTimeCell.textContent = `${supplier.leadTime} days`;

            const ratingCell = row.insertCell(4);
            ratingCell.innerHTML = `<span class="rating">${supplier.rating} ★</span>`;
          });

          // Create Supply Chain Metrics Chart
          const scmCtx = document.getElementById('supplyChainChart').getContext('2d');
          const scmChart = new Chart(scmCtx, {
            type: 'line',
            data: {
              labels: dashboardData.supplyChainMetrics.labels,
              datasets: [
                {
                  label: 'Order Fulfillment Rate (%)',
                  data: dashboardData.supplyChainMetrics.orderFulfillment,
                  borderColor: '#6a3de8',
                  backgroundColor: 'rgba(106, 61, 232, 0.1)',
                  tension: 0.3,
                  fill: true
                },
                {
                  label: 'Lead Time (days)',
                  data: dashboardData.supplyChainMetrics.leadTime,
                  borderColor: '#e74c3c',
                  backgroundColor: 'rgba(231, 76, 60, 0.1)',
                  tension: 0.3,
                  fill: true
                }
              ]
            },
            options: {
              responsive: true,
              plugins: {
                legend: {
                  position: 'top',
                },
                tooltip: {
                  mode: 'index',
                  intersect: false,
                }
              },
              scales: {
                y: {
                  beginAtZero: false
                }
              }
            }
          });

          // Create Inventory Trends Chart
          const inventoryCtx = document.getElementById('inventoryTrendsChart').getContext('2d');
          const inventoryChart = new Chart(inventoryCtx, {
            type: 'line',
            data: {
              labels: dashboardData.inventoryTrends.labels,
              datasets: [
                {
                  label: 'Raw Materials',
                  data: dashboardData.inventoryTrends.rawMaterials,
                  borderColor: '#3ecf8e',
                  backgroundColor: 'rgba(62, 207, 142, 0.1)',
                  tension: 0.3,
                  fill: true
                },
                {
                  label: 'Components',
                  data: dashboardData.inventoryTrends.components,
                  borderColor: '#f39c12',
                  backgroundColor: 'rgba(243, 156, 18, 0.1)',
                  tension: 0.3,
                  fill: true
                },
                {
                  label: 'Packaging',
                  data: dashboardData.inventoryTrends.packaging,
                  borderColor: '#9b59b6',
                  backgroundColor: 'rgba(155, 89, 182, 0.1)',
                  tension: 0.3,
                  fill: true
                }
              ]
            },
            options: {
              responsive: true,
              plugins: {
                legend: {
                  position: 'top',
                },
                tooltip: {
                  mode: 'index',
                  intersect: false,
                }
              },
              scales: {
                y: {
                  beginAtZero: false
                }
              }
            }
          });
        }
      })
      .catch(error => {
        console.error('Error fetching dashboard data:', error);
      });

    // Function to fetch and display AI-powered supply chain optimization insights
    function fetchAIInsights() {
      const insightsContainer = document.getElementById('ai-insights-container');

      // Show loading spinner
      insightsContainer.innerHTML = `
        <div class="d-flex justify-content-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      `;

      // Fetch insights from the Integration Hub
      fetch('http://localhost:8000/api/ai-analytics/supply-chain-optimization')
        .then(response => response.json())
        .then(data => {
          console.log('AI insights:', data);

          // Store insights data globally for export
          window.aiInsightsData = data;

          // Clear the container
          insightsContainer.innerHTML = '';

          // Add supplier optimization insights
          if (data.insights && data.insights[0] && data.insights[0].suppliers) {
            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'supplier';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
              <h6 class="mb-0">
                <i class="bi bi-building me-2"></i>
                Supplier Optimization
              </h6>
              <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#supplierInsights">
                <i class="bi bi-chevron-down"></i>
              </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'supplierInsights';

            const suppliersRow = document.createElement('div');
            suppliersRow.className = 'row';

            data.insights[0].suppliers.forEach(supplier => {
              const supplierCol = document.createElement('div');
              supplierCol.className = 'col-md-6 mb-3';

              const supplierCard = document.createElement('div');
              supplierCard.className = 'card h-100';

              const cardBody = document.createElement('div');
              cardBody.className = 'card-body';

              cardBody.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h6 class="mb-0">${supplier.name}</h6>
                  <span class="badge bg-success">$${supplier.potentialSavings.toLocaleString()}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span class="text-muted">Current Lead Time:</span>
                  <span class="fw-bold">${supplier.currentLeadTime} days</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span class="text-muted">Optimized Lead Time:</span>
                  <span class="fw-bold text-success">${supplier.optimizedLeadTime} days</span>
                </div>
                <div class="progress mb-2" style="height: 5px;">
                  <div class="progress-bar bg-success" style="width: ${(supplier.optimizedLeadTime / supplier.currentLeadTime) * 100}%"></div>
                </div>
                <p class="small text-muted mb-2">${supplier.recommendedAction}</p>
                <button class="btn btn-sm btn-outline-primary mt-2 view-supplier-details-btn" data-supplier="${supplier.name}">
                  View Details
                </button>
              `;

              supplierCard.appendChild(cardBody);
              supplierCol.appendChild(supplierCard);
              suppliersRow.appendChild(supplierCol);
            });

            sectionBody.appendChild(suppliersRow);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            insightsContainer.appendChild(sectionCol);
          }

          // Add logistics optimization insights
          if (data.insights && data.insights[1] && data.insights[1].recommendations) {
            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'logistics';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
              <h6 class="mb-0">
                <i class="bi bi-truck me-2"></i>
                Logistics Optimization
              </h6>
              <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#logisticsInsights">
                <i class="bi bi-chevron-down"></i>
              </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'logisticsInsights';

            const logisticsRow = document.createElement('div');
            logisticsRow.className = 'row';

            data.insights[1].recommendations.forEach(recommendation => {
              const recommendationCol = document.createElement('div');
              recommendationCol.className = 'col-md-4 mb-3';

              const recommendationCard = document.createElement('div');
              recommendationCard.className = 'card h-100';

              const cardBody = document.createElement('div');
              cardBody.className = 'card-body';

              // Determine difficulty badge class
              let difficultyClass = '';
              if (recommendation.implementationDifficulty === 'Low') difficultyClass = 'bg-success';
              else if (recommendation.implementationDifficulty === 'Medium') difficultyClass = 'bg-warning';
              else difficultyClass = 'bg-danger';

              cardBody.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h6 class="mb-0">${recommendation.title}</h6>
                  <span class="badge ${difficultyClass}">${recommendation.implementationDifficulty}</span>
                </div>
                <p class="small text-muted mb-2">${recommendation.description}</p>
                <div class="d-flex justify-content-between mb-2">
                  <span class="text-muted">Estimated Savings:</span>
                  <span class="fw-bold text-success">$${recommendation.estimatedSavings.toLocaleString()}</span>
                </div>
                <button class="btn btn-sm btn-outline-primary mt-2 implement-recommendation-btn"
                        data-recommendation="${recommendation.title}"
                        data-savings="${recommendation.estimatedSavings}">
                  Implement
                </button>
              `;

              recommendationCard.appendChild(cardBody);
              recommendationCol.appendChild(recommendationCard);
              logisticsRow.appendChild(recommendationCol);
            });

            sectionBody.appendChild(logisticsRow);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            insightsContainer.appendChild(sectionCol);
          }

          // Add inventory optimization insights
          if (data.insights && data.insights[2] && data.insights[2].categories) {
            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'inventory';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
              <h6 class="mb-0">
                <i class="bi bi-box-seam me-2"></i>
                Inventory Optimization
              </h6>
              <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#inventoryInsights">
                <i class="bi bi-chevron-down"></i>
              </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'inventoryInsights';

            const inventoryRow = document.createElement('div');
            inventoryRow.className = 'row';

            data.insights[2].categories.forEach(category => {
              const categoryCol = document.createElement('div');
              categoryCol.className = 'col-md-4 mb-3';

              const categoryCard = document.createElement('div');
              categoryCard.className = 'card h-100';

              const cardBody = document.createElement('div');
              cardBody.className = 'card-body';

              // Determine risk badge class
              let riskClass = '';
              if (category.stockoutRisk === 'Low') riskClass = 'bg-success';
              else if (category.stockoutRisk === 'Medium') riskClass = 'bg-warning';
              else riskClass = 'bg-danger';

              // Calculate optimization percentage
              const optimizationPercentage = ((category.currentInventoryValue - category.optimizedInventoryValue) / category.currentInventoryValue) * 100;

              cardBody.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h6 class="mb-0">${category.name}</h6>
                  <span class="badge ${riskClass}">${category.stockoutRisk} Risk</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span class="text-muted">Current Value:</span>
                  <span class="fw-bold">$${category.currentInventoryValue.toLocaleString()}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span class="text-muted">Optimized Value:</span>
                  <span class="fw-bold text-success">$${category.optimizedInventoryValue.toLocaleString()}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span class="text-muted">Potential Savings:</span>
                  <span class="fw-bold text-success">$${category.potentialSavings.toLocaleString()}</span>
                </div>
                <div class="progress mb-2" style="height: 5px;">
                  <div class="progress-bar bg-success" style="width: ${optimizationPercentage}%"></div>
                </div>
                <button class="btn btn-sm btn-outline-primary mt-2 optimize-inventory-btn"
                        data-category="${category.name}"
                        data-savings="${category.potentialSavings}">
                  Optimize
                </button>
              `;

              categoryCard.appendChild(cardBody);
              categoryCol.appendChild(categoryCard);
              inventoryRow.appendChild(categoryCol);
            });

            sectionBody.appendChild(inventoryRow);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            insightsContainer.appendChild(sectionCol);
          }

          // Add recommendations section
          if (data.recommendations && data.recommendations.length > 0) {
            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'recommendations';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
              <h6 class="mb-0">
                <i class="bi bi-lightbulb me-2"></i>
                AI Recommendations
              </h6>
              <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#recommendationsSection">
                <i class="bi bi-chevron-down"></i>
              </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'recommendationsSection';

            const recommendationsList = document.createElement('ul');
            recommendationsList.className = 'list-group';

            data.recommendations.forEach((recommendation, index) => {
              const item = document.createElement('li');
              item.className = 'list-group-item d-flex align-items-center';

              item.innerHTML = `
                <div class="me-3">
                  <span class="badge bg-primary rounded-circle">
                    ${index + 1}
                  </span>
                </div>
                <div>
                  ${recommendation}
                </div>
                <div class="ms-auto">
                  <button class="btn btn-sm btn-outline-success action-btn" data-recommendation-index="${index}">
                    Take Action
                  </button>
                </div>
              `;

              recommendationsList.appendChild(item);
            });

            sectionBody.appendChild(recommendationsList);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            insightsContainer.appendChild(sectionCol);
          }

          // Add event listeners for interactive elements
          addInteractiveEventListeners(data);
        })
        .catch(error => {
          console.error('Error fetching AI insights:', error);
          insightsContainer.innerHTML = `
            <div class="alert alert-danger">
              <i class="bi bi-exclamation-triangle"></i> Failed to fetch AI insights from the Integration Hub.
              Please make sure the Integration Hub is running.
            </div>
          `;
        });
    }

    // Function to add interactive event listeners to the AI insights
    function addInteractiveEventListeners(data) {
      // Add event listeners for supplier details buttons
      document.querySelectorAll('.view-supplier-details-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const supplierName = this.dataset.supplier;
          const supplier = data.insights[0].suppliers.find(s => s.name === supplierName);

          if (supplier) {
            // Create modal for supplier details
            const modalHTML = `
              <div class="modal fade" id="supplierModal" tabindex="-1" aria-labelledby="supplierModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title" id="supplierModalLabel">Supplier Details: ${supplierName}</h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <div class="card">
                            <div class="card-header">Current Performance</div>
                            <div class="card-body">
                              <div class="d-flex justify-content-between mb-2">
                                <span>Lead Time:</span>
                                <span class="fw-bold">${supplier.currentLeadTime} days</span>
                              </div>
                              <div class="d-flex justify-content-between mb-2">
                                <span>On-Time Delivery:</span>
                                <span class="fw-bold">82%</span>
                              </div>
                              <div class="d-flex justify-content-between mb-2">
                                <span>Quality Rating:</span>
                                <span class="fw-bold">4.2/5.0</span>
                              </div>
                              <div class="d-flex justify-content-between mb-2">
                                <span>Cost Index:</span>
                                <span class="fw-bold">105 (Industry Avg: 100)</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="card">
                            <div class="card-header">Optimization Potential</div>
                            <div class="card-body">
                              <div class="d-flex justify-content-between mb-2">
                                <span>Optimized Lead Time:</span>
                                <span class="fw-bold text-success">${supplier.optimizedLeadTime} days</span>
                              </div>
                              <div class="d-flex justify-content-between mb-2">
                                <span>Potential Savings:</span>
                                <span class="fw-bold text-success">$${supplier.potentialSavings.toLocaleString()}</span>
                              </div>
                              <div class="d-flex justify-content-between mb-2">
                                <span>Implementation Effort:</span>
                                <span class="fw-bold">Medium</span>
                              </div>
                              <div class="d-flex justify-content-between mb-2">
                                <span>ROI Timeline:</span>
                                <span class="fw-bold">3-6 months</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="card">
                        <div class="card-header">Recommended Actions</div>
                        <div class="card-body">
                          <ol class="mb-0">
                            <li class="mb-2">${supplier.recommendedAction}</li>
                            <li class="mb-2">Establish regular performance review meetings</li>
                            <li class="mb-2">Implement shared inventory visibility system</li>
                            <li class="mb-2">Develop contingency plans for supply disruptions</li>
                          </ol>
                        </div>
                      </div>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                      <button type="button" class="btn btn-primary">Implement Optimization</button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Remove any existing modal
            const existingModal = document.getElementById('supplierModal');
            if (existingModal) {
              existingModal.remove();
            }

            // Add modal to the document
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
            modal.show();
          }
        });
      });

      // Add event listeners for implement recommendation buttons
      document.querySelectorAll('.implement-recommendation-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const recommendationTitle = this.dataset.recommendation;
          const savings = this.dataset.savings;

          // Create modal for implementation plan
          const modalHTML = `
            <div class="modal fade" id="implementModal" tabindex="-1" aria-labelledby="implementModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="implementModalLabel">Implementation Plan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <p><strong>Recommendation:</strong> ${recommendationTitle}</p>
                    <p><strong>Estimated Savings:</strong> $${parseInt(savings).toLocaleString()}</p>
                    <form>
                      <div class="mb-3">
                        <label for="assignee" class="form-label">Assign to</label>
                        <select class="form-select" id="assignee">
                          <option>John Doe</option>
                          <option>Jane Smith</option>
                          <option>Mike Johnson</option>
                          <option>Sarah Williams</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="startDate" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="startDate">
                      </div>
                      <div class="mb-3">
                        <label for="dueDate" class="form-label">Due Date</label>
                        <input type="date" class="form-control" id="dueDate">
                      </div>
                      <div class="mb-3">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority">
                          <option>High</option>
                          <option selected>Medium</option>
                          <option>Low</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="notes" class="form-label">Implementation Notes</label>
                        <textarea class="form-control" id="notes" rows="3"></textarea>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Create Implementation Task</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Remove any existing modal
          const existingModal = document.getElementById('implementModal');
          if (existingModal) {
            existingModal.remove();
          }

          // Add modal to the document
          document.body.insertAdjacentHTML('beforeend', modalHTML);

          // Show the modal
          const modal = new bootstrap.Modal(document.getElementById('implementModal'));
          modal.show();
        });
      });

      // Add event listeners for optimize inventory buttons
      document.querySelectorAll('.optimize-inventory-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const category = this.dataset.category;
          const savings = this.dataset.savings;

          // Create modal for optimization plan
          const modalHTML = `
            <div class="modal fade" id="optimizeModal" tabindex="-1" aria-labelledby="optimizeModalLabel" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="optimizeModalLabel">Inventory Optimization Plan: ${category}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <div class="alert alert-info">
                      <i class="bi bi-info-circle"></i> Implementing this optimization plan could save approximately <strong>$${parseInt(savings).toLocaleString()}</strong>.
                    </div>

                    <div class="card mb-3">
                      <div class="card-header">Optimization Steps</div>
                      <div class="card-body">
                        <ol>
                          <li class="mb-2">Review current inventory levels and identify excess stock</li>
                          <li class="mb-2">Adjust reorder points based on historical demand patterns</li>
                          <li class="mb-2">Implement just-in-time delivery for high-volume items</li>
                          <li class="mb-2">Establish safety stock levels for critical items</li>
                          <li class="mb-2">Configure automated alerts for inventory exceptions</li>
                        </ol>
                      </div>
                    </div>

                    <div class="row">
                      <div class="col-md-6">
                        <div class="card">
                          <div class="card-header">Implementation Timeline</div>
                          <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                              <span>Analysis Phase:</span>
                              <span>1-2 weeks</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Planning Phase:</span>
                              <span>2-3 weeks</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Implementation Phase:</span>
                              <span>4-6 weeks</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Evaluation Phase:</span>
                              <span>Ongoing</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="card">
                          <div class="card-header">Resource Requirements</div>
                          <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                              <span>Inventory Manager:</span>
                              <span>10 hours/week</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Warehouse Staff:</span>
                              <span>20 hours/week</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>IT Support:</span>
                              <span>5 hours/week</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Budget:</span>
                              <span>$15,000</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Approve Optimization Plan</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Remove any existing modal
          const existingModal = document.getElementById('optimizeModal');
          if (existingModal) {
            existingModal.remove();
          }

          // Add modal to the document
          document.body.insertAdjacentHTML('beforeend', modalHTML);

          // Show the modal
          const modal = new bootstrap.Modal(document.getElementById('optimizeModal'));
          modal.show();
        });
      });

      // Add event listeners for action buttons
      document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const index = parseInt(this.dataset.recommendationIndex);
          const recommendation = data.recommendations[index];

          // Create modal for action plan
          const modalHTML = `
            <div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="actionModalLabel">Action Plan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <p><strong>Recommendation:</strong> ${recommendation}</p>
                    <form>
                      <div class="mb-3">
                        <label for="actionType" class="form-label">Action Type</label>
                        <select class="form-select" id="actionType">
                          <option>Create Task</option>
                          <option>Schedule Meeting</option>
                          <option>Create Project</option>
                          <option>Assign to Team</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="assignee" class="form-label">Assign to</label>
                        <select class="form-select" id="assignee">
                          <option>John Doe</option>
                          <option>Jane Smith</option>
                          <option>Mike Johnson</option>
                          <option>Sarah Williams</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="dueDate" class="form-label">Due Date</label>
                        <input type="date" class="form-control" id="dueDate">
                      </div>
                      <div class="mb-3">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority">
                          <option>High</option>
                          <option selected>Medium</option>
                          <option>Low</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="3"></textarea>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Create Task</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Remove any existing modal
          const existingModal = document.getElementById('actionModal');
          if (existingModal) {
            existingModal.remove();
          }

          // Add modal to the document
          document.body.insertAdjacentHTML('beforeend', modalHTML);

          // Show the modal
          const modal = new bootstrap.Modal(document.getElementById('actionModal'));
          modal.show();
        });
      });
    }

    // Add event listener for refresh insights button
    document.getElementById('refresh-insights-btn').addEventListener('click', fetchAIInsights);

    // Add event listeners for expand/collapse all insights
    document.getElementById('expand-all-insights').addEventListener('click', function(e) {
      e.preventDefault();
      document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
        const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
        bsCollapse.show();
      });
    });

    document.getElementById('collapse-all-insights').addEventListener('click', function(e) {
      e.preventDefault();
      document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
        const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
        bsCollapse.hide();
      });
    });

    // Add event listener for export insights
    document.getElementById('export-insights').addEventListener('click', function(e) {
      e.preventDefault();
      if (window.aiInsightsData) {
        const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(window.aiInsightsData, null, 2));
        const downloadAnchorNode = document.createElement('a');
        downloadAnchorNode.setAttribute("href", dataStr);
        downloadAnchorNode.setAttribute("download", "supply_chain_insights.json");
        document.body.appendChild(downloadAnchorNode);
        downloadAnchorNode.click();
        downloadAnchorNode.remove();
      }
    });

    // Fetch tasks when the page loads
    fetchTasks();

    // Fetch AI insights when the page loads
    fetchAIInsights();

    // Attachment functions
    function downloadAttachment(fileName) {
      alert(`Downloading ${fileName}...`);
      // In a real application, this would trigger a download
    }

    function viewAttachment(type, fileName) {
      // Create a file viewer modal if it doesn't exist
      const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

      // Set the file information
      const fileTitle = document.getElementById('fileViewerTitle');
      const fileContent = document.getElementById('fileViewerContent');

      // Set the title based on the file type and name
      fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;

      // Set the content based on the file type
      fileContent.innerHTML = getFileContent(type, fileName);

      // Show the modal
      const modal = new bootstrap.Modal(fileViewerModal);
      modal.show();

      console.log(`Viewing ${fileName} directly`);
    }

    // Helper function to create a file viewer modal if it doesn't exist
    function createFileViewerModal() {
      const modal = document.createElement('div');
      modal.className = 'modal fade';
      modal.id = 'fileViewerModal';
      modal.tabIndex = '-1';
      modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
      modal.setAttribute('aria-hidden', 'true');

      modal.innerHTML = `
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
              <h5 class="modal-title" id="fileViewerTitle"></h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">Download</button>
              <button type="button" class="btn btn-success" onclick="shareCurrentFile()">Share</button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      return modal;
    }

    // Helper function to get the appropriate icon for the file type
    function getFileTypeIcon(type) {
      if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
      if (type === 'document') return 'bi-file-earmark-text text-primary';
      if (type === 'spreadsheet') return 'bi-file-earmark-spreadsheet text-success';
      if (type === 'image') return 'bi-file-earmark-image text-info';
      return 'bi-file-earmark text-secondary';
    }

    // Helper function to generate content for the file viewer
    function getFileContent(type, fileName) {
      // Generate simulated content based on file type
      if (type === 'pdf') {
        return `<div class="text-center">
                  <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                  <h4 class="mt-3">${fileName}</h4>
                  <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                    <h5>Document Preview</h5>
                    <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                    <p>The document contains information related to supply chain management.</p>
                  </div>
                </div>`;
      } else if (type === 'document') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${fileName}</h4>
                  <hr>
                  <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                  <p>The document contains information related to supply chain management.</p>
                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                </div>`;
      } else if (type === 'spreadsheet') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${fileName}</h4>
                  <hr>
                  <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                      <thead>
                        <tr>
                          <th>Item</th>
                          <th>Quantity</th>
                          <th>Price</th>
                          <th>Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>Item 1</td>
                          <td>10</td>
                          <td>$10.00</td>
                          <td>$100.00</td>
                        </tr>
                        <tr>
                          <td>Item 2</td>
                          <td>5</td>
                          <td>$20.00</td>
                          <td>$100.00</td>
                        </tr>
                        <tr>
                          <td>Item 3</td>
                          <td>2</td>
                          <td>$30.00</td>
                          <td>$60.00</td>
                        </tr>
                        <tr>
                          <td colspan="3" class="text-end"><strong>Total</strong></td>
                          <td><strong>$260.00</strong></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>`;
      } else if (type === 'image') {
        return `<div class="text-center">
                  <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                    <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                    <h5 class="mt-3">${fileName}</h5>
                    <p>This is an image viewer. In a real application, the image would be displayed here.</p>
                  </div>
                </div>`;
      }

      return `<div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
              </div>`;
    }

    // Function to download the current file
    function downloadCurrentFile() {
      const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
      alert(`Downloading ${fileTitle}...`);
      // In a real application, this would trigger a download
    }

    // Function to share the current file
    function shareCurrentFile() {
      const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
      const email = prompt(`Enter email address to share ${fileTitle} with:`);
      if (email) {
        alert(`${fileTitle} has been shared with ${email}.`);
        // In a real application, this would share the file with the specified email
      }
    }

    function deleteAttachment(fileName) {
      if (confirm(`Are you sure you want to delete ${fileName}?`)) {
        alert(`${fileName} has been deleted.`);
        // In a real application, this would delete the file
      }
    }

    function shareFile(fileName) {
      const email = prompt(`Enter email address to share ${fileName} with:`);
      if (email) {
        alert(`${fileName} has been shared with ${email}.`);
        // In a real application, this would share the file with the specified email
      }
    }

    function downloadFile(fileName) {
      alert(`Downloading ${fileName}...`);
      // In a real application, this would trigger a download of the file

      // Create a temporary link element to simulate download
      const link = document.createElement('a');
      link.href = '#';
      link.download = fileName;

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    function deleteFile(fileName) {
      if (confirm(`Are you sure you want to delete ${fileName}?`)) {
        alert(`${fileName} has been deleted.`);
        // In a real application, this would delete the file
      }
    }

    function downloadSelectedAttachments(source) {
      const selector = source === 'shared' ? '#shared-attachments-content .form-check-input:checked' : '#attachment-files-content .form-check-input:checked';
      const checkboxes = document.querySelectorAll(selector);

      if (checkboxes.length === 0) {
        alert('Please select at least one attachment to download.');
        return;
      }

      alert(`Downloading ${checkboxes.length} selected attachments...`);
      // In a real application, this would download all selected attachments
    }

    // Attachments Component
    const AttachmentsHandler = {
      init() {
        // Dashboard component event listeners
        const fileUploadInput = document.getElementById('attachment-file-upload');
        if (fileUploadInput) {
          fileUploadInput.addEventListener('change', this.handleFileUpload.bind(this));
        }
      },

      handleFileUpload(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        let fileNames = [];
        for (let i = 0; i < files.length; i++) {
          fileNames.push(files[i].name);
        }

        alert(`Files uploaded successfully: ${fileNames.join(', ')}`);

        // Clear the input to allow uploading the same file again
        event.target.value = '';
      }
    };

    // Initialize Google Integration Components
    document.addEventListener('DOMContentLoaded', () => {
      GoogleMapsHandler.init();
      GoogleSheetsHandler.init();
      GoogleCalendarHandler.init();
      GoogleGmailHandler.init();
      GoogleDriveHandler.init();
      GoogleDocsHandler.init();
      AttachmentsHandler.init();
    });
  </script>
</body>
</html>
