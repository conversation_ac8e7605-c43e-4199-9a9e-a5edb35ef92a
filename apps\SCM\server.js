// Supply Chain Management System - Main Entry Point

const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const app = express();
const port = 3008;

// Import shared features if available
try {
  const sharedFeatures = require('../../SharedFeatures');
  const auth = sharedFeatures.auth;
  const logger = sharedFeatures.logger.createLogger('SCM');
} catch (err) {
  console.log('Shared features not available, continuing without them');
}

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Sample data
let inventory = [
  {
    id: 1,
    name: 'Raw Materials',
    location: 'Warehouse A',
    currentStock: 1500,
    minStock: 500,
    maxStock: 2000,
    unit: 'kg',
    status: 'In Stock',
    reorderPoint: 800,
  },
  {
    id: 2,
    name: 'Electronic Components',
    location: 'Warehouse B',
    currentStock: 250,
    minStock: 100,
    maxStock: 500,
    unit: 'pcs',
    status: 'Low Stock',
    reorderPoint: 150,
  },
  {
    id: 3,
    name: 'Packaging Materials',
    location: 'Warehouse C',
    currentStock: 800,
    minStock: 300,
    maxStock: 1000,
    unit: 'pcs',
    status: 'In Stock',
    reorderPoint: 400,
  },
];

let suppliers = [
  {
    id: 1,
    name: 'Global Materials Inc.',
    category: 'Raw Materials',
    location: 'USA',
    status: 'Active',
    leadTime: 5,
    rating: 4.5,
  },
  {
    id: 2,
    name: 'Tech Components Ltd.',
    category: 'Electronic Parts',
    location: 'China',
    status: 'Active',
    leadTime: 7,
    rating: 4.2,
  },
  {
    id: 3,
    name: 'Packaging Solutions',
    category: 'Packaging',
    location: 'Germany',
    status: 'On Hold',
    leadTime: 3,
    rating: 4.8,
  },
];

// Define routes
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// Health check endpoint for integration with the hub
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API endpoint for dashboard data
app.get('/api/dashboard', (req, res) => {
  // Simulate dashboard data
  const dashboardData = {
    metrics: {
      totalInventory: 2550,
      activeSuppliers: 3,
      lowStockItems: 1
    },
    inventory: inventory,
    suppliers: suppliers,
    supplyChainMetrics: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      orderFulfillment: [92, 94, 91, 95, 97, 96],
      leadTime: [6, 5.5, 6.2, 5.8, 5.5, 5.2]
    },
    inventoryTrends: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      rawMaterials: [1200, 1350, 1400, 1450, 1500, 1500],
      components: [300, 280, 260, 240, 270, 250],
      packaging: [750, 780, 800, 820, 790, 800]
    }
  };

  res.json({ status: 'success', data: dashboardData });
});

// API endpoint for inventory data
app.get('/api/inventory', (req, res) => {
  res.json({ status: 'success', data: { inventory } });
});

// API endpoint for supplier data
app.get('/api/suppliers', (req, res) => {
  res.json({ status: 'success', data: { suppliers } });
});

// API endpoint for a specific supplier
app.get('/api/suppliers/:id', (req, res) => {
  const supplierId = parseInt(req.params.id);
  const supplier = suppliers.find(s => s.id === supplierId);

  if (!supplier) {
    return res.status(404).json({ status: 'error', message: 'Supplier not found' });
  }

  res.json({ status: 'success', data: supplier });
});

// API endpoint for cross-application integration with Task Management
app.get('/api/integration/tasks', async (req, res) => {
  try {
    // Fetch tasks from Task Management System
    const response = await fetch('http://localhost:3009/api/tasks').catch(() => null);
    const tasksData = response && response.ok ? await response.json() : null;

    if (!tasksData) {
      return res.status(500).json({ status: 'error', message: 'Failed to fetch tasks from Task Management System' });
    }

    // Filter tasks related to supply chain
    const supplyChainTasks = tasksData.data.tasks.filter(task =>
      task.title.toLowerCase().includes('supply') ||
      task.title.toLowerCase().includes('inventory') ||
      task.title.toLowerCase().includes('order') ||
      task.title.toLowerCase().includes('supplier')
    );

    res.json({ status: 'success', data: { tasks: supplyChainTasks } });
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({ status: 'error', message: 'Failed to fetch tasks' });
  }
});

// API endpoint for legacy HTML page (for backward compatibility)
app.get('/legacy', (req, res) => {
  // Create a simple HTML page for the dashboard
  const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Supply Chain Management</title>
                <style>
                    body { font-family: Arial; margin: 20px; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .dashboard-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }
                    .card {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        padding: 20px;
                        margin-bottom: 20px;
                    }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
                    th { background: #f4f4f4; }
                    .actions { margin: 20px 0; }
                    button { padding: 10px; margin: 5px; cursor: pointer; }
                    .status {
                        padding: 5px 10px;
                        border-radius: 3px;
                        color: white;
                        font-weight: bold;
                    }
                    .in-stock { background: #28a745; }
                    .low-stock { background: #ffc107; }
                    .out-of-stock { background: #dc3545; }
                    .active { background: #28a745; }
                    .on-hold { background: #ffc107; }
                    .inactive { background: #dc3545; }
                    .metrics {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 20px;
                        margin-bottom: 20px;
                    }
                    .metric-card {
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 8px;
                        text-align: center;
                    }
                    .metric-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: #007bff;
                    }
                    .stock-level {
                        font-weight: bold;
                    }
                    .high-stock { color: #28a745; }
                    .medium-stock { color: #ffc107; }
                    .low-stock { color: #dc3545; }
                    .rating {
                        color: #ffc107;
                        font-weight: bold;
                    }
                    .lead-time {
                        font-family: monospace;
                        color: #666;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Supply Chain Management</h1>
                    <div class="actions">
                        <button onclick="window.location.href='http://localhost:8000'">Back to Hub</button>
                    </div>

                    <div class="metrics">
                        <div class="metric-card">
                            <div class="metric-value">2,550</div>
                            <div>Total Inventory</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">3</div>
                            <div>Active Suppliers</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">1</div>
                            <div>Low Stock Items</div>
                        </div>
                    </div>

                    <div class="dashboard-grid">
                        <div class="card">
                            <h2>Inventory Levels</h2>
                            <table>
                                <tr>
                                    <th>Item</th>
                                    <th>Location</th>
                                    <th>Stock Level</th>
                                    <th>Status</th>
                                    <th>Reorder Point</th>
                                </tr>
                                ${inventory
                                  .map(
                                    (item) => `
                                    <tr>
                                        <td>${item.name}</td>
                                        <td>${item.location}</td>
                                        <td>
                                            <span class="stock-level ${item.currentStock > item.maxStock * 0.7 ? 'high-stock' : item.currentStock > item.minStock ? 'medium-stock' : 'low-stock'}">
                                                ${item.currentStock} ${item.unit}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="status ${item.status.toLowerCase().replace(' ', '-')}">
                                                ${item.status}
                                            </span>
                                        </td>
                                        <td>${item.reorderPoint} ${item.unit}</td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>

                        <div class="card">
                            <h2>Supplier Information</h2>
                            <table>
                                <tr>
                                    <th>Supplier</th>
                                    <th>Category</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Lead Time</th>
                                    <th>Rating</th>
                                </tr>
                                ${suppliers
                                  .map(
                                    (supplier) => `
                                    <tr>
                                        <td>${supplier.name}</td>
                                        <td>${supplier.category}</td>
                                        <td>${supplier.location}</td>
                                        <td>
                                            <span class="status ${supplier.status.toLowerCase().replace(' ', '-')}">
                                                ${supplier.status}
                                            </span>
                                        </td>
                                        <td class="lead-time">${supplier.leadTime} days</td>
                                        <td class="rating">${supplier.rating} ★</td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `;
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end(html);
});

// Create an index.html file in the public directory
app.use((req, res) => {
  res.status(404).send('Not found');
});

// Start the server
app.listen(port, () => {
  console.log(`Supply Chain Management running on http://localhost:${port}`);
});
