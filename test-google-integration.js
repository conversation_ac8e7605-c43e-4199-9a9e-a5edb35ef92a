// Test script for Google integration
const path = require('path');
const fs = require('fs');

// Import the Google integration module
const googleIntegration = require('./SharedFeatures/integrations/google');

// Test function
async function testGoogleIntegration() {
  try {
    console.log('Testing Google API initialization...');
    await googleIntegration.initGoogleAPI();
    console.log('✅ Google API initialized successfully!');
    
    // Test Google Drive API
    console.log('\nTesting Google Drive API...');
    const files = await googleIntegration.Drive.listFiles();
    console.log(`✅ Successfully retrieved ${files.length} files from Google Drive`);
    
    // Test Google Calendar API
    console.log('\nTesting Google Calendar API...');
    const now = new Date();
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    const events = await googleIntegration.Calendar.listEvents(now, nextWeek);
    console.log(`✅ Successfully retrieved ${events.length} events from Google Calendar`);
    
    console.log('\nAll tests passed! Google integration is working correctly.');
  } catch (error) {
    console.error('❌ Error testing Google integration:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testGoogleIntegration();
