/**
 * Enhanced Shared Gmail Integration Module for ISA Suite
 *
 * This module provides a standardized implementation of Gmail functionality
 * that can be used across all applications in the ISA Suite.
 *
 * Features:
 * - Consistent modal structure and styling
 * - Email viewing, composing, replying, and forwarding
 * - Advanced search, sort, and filter functionality with highlighting
 * - Enhanced attachment handling with validation and progress
 * - Label management and color coding
 * - Toast notifications and error handling
 * - Responsive design and accessibility
 */

class GmailIntegration {
    constructor(options = {}) {
        // Default configuration
        this.config = {
            appName: 'App',
            appPrefix: '',
            modalId: 'gmailModal',
            primaryColor: '#4285f4', // Default Gmail blue
            secondaryColor: '#ea4335', // Default Gmail red
            debug: false,
            // Enhanced label configuration
            labels: [
                { id: 'clients', name: 'Clients', icon: 'people', count: 0, color: '#28a745' },
                { id: 'urgent', name: 'Urgent', icon: 'exclamation-triangle', count: 0, color: '#dc3545' },
                { id: 'followup', name: 'Follow-up', icon: 'clock', count: 0, color: '#ffc107' },
                { id: 'team', name: 'Team', icon: 'people-fill', count: 0, color: '#17a2b8' }
            ],
            // Enhanced colors based on app type
            colors: {
                primary: '#4285f4',
                secondary: '#6c757d',
                success: '#28a745',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                light: '#f8f9fa',
                dark: '#343a40'
            },
            ...options
        };

        // Set modal ID with app prefix if provided
        if (this.config.appPrefix && !this.config.modalId.startsWith(this.config.appPrefix)) {
            this.config.modalId = `${this.config.appPrefix}-${this.config.modalId}`;
        }

        // Merge colors if app-specific colors provided
        if (options.colors) {
            this.config.colors = { ...this.config.colors, ...options.colors };
        }

        // Use app primary color as Gmail primary if provided
        if (options.colors && options.colors.primary) {
            this.config.primaryColor = options.colors.primary;
        }

        this.initialized = false;
        this.debug = this.config.debug;
        this.currentSort = 'date-desc';
        this.currentFilter = 'all';
        this.searchTerm = '';
    }

    /**
     * Initialize the Gmail integration
     */
    init() {
        if (this.initialized) {
            this.log('Gmail integration already initialized');
            return;
        }

        this.log('Initializing Gmail integration');

        // Create the modal if it doesn't exist
        if (!document.getElementById(this.config.modalId)) {
            this.createModal();
        }

        // Add event listeners
        this.setupEventListeners();

        // Fix any UI issues
        this.fixUIIssues();

        // Make the handler globally accessible
        window.gmailIntegration = this;

        this.initialized = true;
        this.log('Gmail integration initialized successfully');
    }

    /**
     * Create the Gmail modal
     */
    createModal() {
        this.log('Creating Gmail modal');

        const modalHTML = this.getModalHTML();

        // Add the modal to the document
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = modalHTML;
        document.body.appendChild(tempDiv.firstElementChild);

        this.log('Gmail modal created');
    }

    /**
     * Get the HTML for the Gmail modal
     */
    getModalHTML() {
        return `
        <div class="modal fade" id="${this.config.modalId}" tabindex="-1" aria-labelledby="${this.config.modalId}Label" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: ${this.config.primaryColor}; color: white;">
                        <h5 class="modal-title" id="${this.config.modalId}Label"><i class="bi bi-envelope"></i> Gmail</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-3 border-end">
                                <div class="d-grid gap-2 mb-3">
                                    <button class="btn btn-primary" id="${this.config.appPrefix}-compose-new-email" onclick="document.getElementById('${this.config.appPrefix}-compose-tab').click()">
                                        <i class="bi bi-pencil-square me-2"></i>Compose
                                    </button>
                                </div>
                                <div class="list-group">
                                    <a href="#" class="list-group-item list-group-item-action active d-flex justify-content-between align-items-center" id="${this.config.appPrefix}-inbox-link" onclick="document.getElementById('${this.config.appPrefix}-inbox-tab').click()">
                                        <div><i class="bi bi-inbox me-2"></i>Inbox</div>
                                        <span class="badge bg-primary rounded-pill">3</span>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="${this.config.appPrefix}-starred-link">
                                        <div><i class="bi bi-star me-2"></i>Starred</div>
                                        <span class="badge bg-secondary rounded-pill">2</span>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="${this.config.appPrefix}-sent-link" onclick="document.getElementById('${this.config.appPrefix}-sent-tab').click()">
                                        <div><i class="bi bi-send me-2"></i>Sent</div>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="${this.config.appPrefix}-drafts-link">
                                        <div><i class="bi bi-file-earmark me-2"></i>Drafts</div>
                                        <span class="badge bg-secondary rounded-pill">1</span>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="${this.config.appPrefix}-important-link">
                                        <div><i class="bi bi-bookmark me-2"></i>Important</div>
                                    </a>
                                </div>

                                <div class="mt-4">
                                    <h6 class="text-muted mb-2">LABELS</h6>
                                    <div class="list-group">
                                        <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                            <span class="color-dot bg-success me-2"></span>Clients
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                            <span class="color-dot bg-danger me-2"></span>Urgent
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                            <span class="color-dot bg-warning me-2"></span>Follow-up
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                            <span class="color-dot bg-info me-2"></span>Team
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="d-flex justify-content-between mb-3">
                                    <div class="input-group" style="max-width: 300px;">
                                        <input type="text" class="form-control" placeholder="Search emails..." id="${this.config.appPrefix}-email-search">
                                        <button class="btn btn-outline-secondary" type="button" id="${this.config.appPrefix}-search-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                    <div>
                                        <div class="dropdown d-inline-block me-2">
                                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="${this.config.appPrefix}-sort-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-sort-down"></i> Sort
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="${this.config.appPrefix}-sort-dropdown">
                                                <li><a class="dropdown-item sort-option" href="#" data-sort="date-desc">Newest first</a></li>
                                                <li><a class="dropdown-item sort-option" href="#" data-sort="date-asc">Oldest first</a></li>
                                                <li><a class="dropdown-item sort-option" href="#" data-sort="sender-asc">Sender A-Z</a></li>
                                                <li><a class="dropdown-item sort-option" href="#" data-sort="sender-desc">Sender Z-A</a></li>
                                                <li><a class="dropdown-item sort-option" href="#" data-sort="subject-asc">Subject A-Z</a></li>
                                                <li><a class="dropdown-item sort-option" href="#" data-sort="subject-desc">Subject Z-A</a></li>
                                            </ul>
                                        </div>
                                        <div class="dropdown d-inline-block me-2">
                                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="${this.config.appPrefix}-filter-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-funnel"></i> Filter
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="${this.config.appPrefix}-filter-dropdown">
                                                <li><a class="dropdown-item filter-option" href="#" data-filter="all">All emails</a></li>
                                                <li><a class="dropdown-item filter-option" href="#" data-filter="unread">Unread</a></li>
                                                <li><a class="dropdown-item filter-option" href="#" data-filter="read">Read</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><h6 class="dropdown-header">By Label</h6></li>
                                                <li><a class="dropdown-item filter-option" href="#" data-filter="label-clients">
                                                    <span class="badge bg-success">Clients</span>
                                                </a></li>
                                                <li><a class="dropdown-item filter-option" href="#" data-filter="label-urgent">
                                                    <span class="badge bg-danger">Urgent</span>
                                                </a></li>
                                                <li><a class="dropdown-item filter-option" href="#" data-filter="label-followup">
                                                    <span class="badge bg-warning">Follow-up</span>
                                                </a></li>
                                            </ul>
                                        </div>
                                        <button class="btn btn-outline-secondary" id="${this.config.appPrefix}-refresh-gmail">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Gmail Tabs -->
                                <ul class="nav nav-tabs mb-3" id="${this.config.appPrefix}-gmailTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="${this.config.appPrefix}-inbox-tab" data-bs-toggle="tab" data-bs-target="#${this.config.appPrefix}-inbox-content" type="button" role="tab" aria-controls="${this.config.appPrefix}-inbox-content" aria-selected="true">Inbox</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="${this.config.appPrefix}-sent-tab" data-bs-toggle="tab" data-bs-target="#${this.config.appPrefix}-sent-content" type="button" role="tab" aria-controls="${this.config.appPrefix}-sent-content" aria-selected="false">Sent</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="${this.config.appPrefix}-read-tab" data-bs-toggle="tab" data-bs-target="#${this.config.appPrefix}-read-content" type="button" role="tab" aria-controls="${this.config.appPrefix}-read-content" aria-selected="false">Read</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="${this.config.appPrefix}-compose-tab" data-bs-toggle="tab" data-bs-target="#${this.config.appPrefix}-compose-content" type="button" role="tab" aria-controls="${this.config.appPrefix}-compose-content" aria-selected="false">Compose</button>
                                    </li>
                                </ul>

                                <div class="tab-content" id="${this.config.appPrefix}-gmailTabContent">
                                    <!-- Tab content will be populated by the setupTabContent method -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <a href="https://mail.google.com" target="_blank" class="btn btn-primary">Open in Gmail</a>
                    </div>
                </div>
            </div>
        </div>
        `;
    }

    /**
     * Set up event listeners for Gmail UI
     */
    setupEventListeners() {
        this.log('Setting up event listeners');

        // "Open Gmail" button
        const openGmailBtn = document.getElementById(`${this.config.appPrefix}-open-gmail-btn`);
        if (openGmailBtn) {
            this.log('Found Open Gmail button, adding event listener');
            openGmailBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.log('Open Gmail button clicked');
                this.openGmailModal();
            });
        }

        // Search button
        const searchBtn = document.getElementById(`${this.config.appPrefix}-search-btn`);
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                const searchInput = document.getElementById(`${this.config.appPrefix}-email-search`);
                if (searchInput) {
                    this.searchEmails(searchInput.value);
                }
            });
        }

        // Search input (for pressing Enter)
        const searchInput = document.getElementById(`${this.config.appPrefix}-email-search`);
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchEmails(searchInput.value);
                }
            });
        }

        // Sort options
        const sortDropdown = document.getElementById(`${this.config.appPrefix}-sort-dropdown`);
        if (sortDropdown) {
            const sortOptions = document.querySelectorAll(`#${this.config.modalId} .sort-option`);
            sortOptions.forEach(option => {
                option.addEventListener('click', (e) => {
                    e.preventDefault();
                    const sortType = e.target.getAttribute('data-sort');
                    this.sortEmails(sortType);
                });
            });
        }

        // Filter options
        const filterDropdown = document.getElementById(`${this.config.appPrefix}-filter-dropdown`);
        if (filterDropdown) {
            const filterOptions = document.querySelectorAll(`#${this.config.modalId} .filter-option`);
            filterOptions.forEach(option => {
                option.addEventListener('click', (e) => {
                    e.preventDefault();
                    const filterType = e.target.getAttribute('data-filter');
                    this.filterEmails(filterType);
                });
            });
        }

        // Refresh button
        const refreshBtn = document.getElementById(`${this.config.appPrefix}-refresh-gmail`);
        if (refreshBtn) {
            refreshBtn.addEventListener('click', this.refreshGmail.bind(this));
        }

        // Close buttons
        const closeButtons = document.querySelectorAll(`#${this.config.modalId} .btn-close, #${this.config.modalId} [data-bs-dismiss="modal"]`);
        closeButtons.forEach(button => {
            button.addEventListener('click', this.closeGmailModal.bind(this));
        });

        // Email items in inbox
        document.querySelectorAll(`#${this.config.appPrefix}-inbox-content .list-group-item`).forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const sender = item.getAttribute('data-sender');
                const subject = item.getAttribute('data-subject');
                this.openEmail(sender, subject);
            });
        });

        // Compose email buttons
        const composeBtn = document.getElementById(`${this.config.appPrefix}-compose-new-email`);
        if (composeBtn) {
            composeBtn.addEventListener('click', () => {
                const composeTab = document.getElementById(`${this.config.appPrefix}-compose-tab`);
                if (composeTab) {
                    composeTab.click();
                }
            });
        }

        // Send email button
        const sendBtn = document.querySelector(`#${this.config.modalId} .btn-primary[type="button"]`);
        if (sendBtn) {
            sendBtn.addEventListener('click', this.sendEmail.bind(this));
        }

        // Save draft button
        const saveBtn = document.querySelector(`#${this.config.modalId} .btn-outline-primary[type="button"]`);
        if (saveBtn) {
            saveBtn.addEventListener('click', this.saveDraft.bind(this));
        }

        // Discard button
        const discardBtn = document.querySelector(`#${this.config.modalId} .btn-outline-secondary[type="button"]`);
        if (discardBtn) {
            discardBtn.addEventListener('click', this.discardEmail.bind(this));
        }

        // Attach button
        const attachBtn = document.querySelector(`#${this.config.modalId} .btn-outline-secondary[onclick*="alert"]`);
        if (attachBtn) {
            attachBtn.removeAttribute('onclick');
            attachBtn.addEventListener('click', () => {
                // Create a file input element
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.multiple = true;
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);

                // Listen for file selection
                fileInput.addEventListener('change', () => {
                    this.handleAttachments(fileInput);
                    document.body.removeChild(fileInput);
                });

                // Open file dialog
                fileInput.click();
            });
        }

        // Setup tab content
        this.setupTabContent();
    }

    /**
     * Set up tab content
     */
    setupTabContent() {
        this.log('Setting up tab content');

        const tabContent = document.getElementById(`${this.config.appPrefix}-gmailTabContent`);
        if (!tabContent) return;

        // Inbox tab content
        const inboxContent = document.getElementById(`${this.config.appPrefix}-inbox-content`);
        if (!inboxContent || inboxContent.children.length === 0) {
            this.setupInboxTab();
        }

        // Sent tab content
        const sentContent = document.getElementById(`${this.config.appPrefix}-sent-content`);
        if (!sentContent || sentContent.children.length === 0) {
            this.setupSentTab();
        }

        // Read tab content
        const readContent = document.getElementById(`${this.config.appPrefix}-read-content`);
        if (!readContent || readContent.children.length === 0) {
            this.setupReadTab();
        }

        // Compose tab content
        const composeContent = document.getElementById(`${this.config.appPrefix}-compose-content`);
        if (!composeContent || composeContent.children.length === 0) {
            this.setupComposeTab();
        }
    }

    /**
     * Set up inbox tab content
     */
    setupInboxTab() {
        this.log('Setting up inbox tab content');

        const inboxContent = document.getElementById(`${this.config.appPrefix}-inbox-content`);
        if (!inboxContent) return;

        // Create inbox content
        inboxContent.innerHTML = `
            <div id="${this.config.appPrefix}-email-list-section">
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action unread" data-sender="john-davis" data-subject="Meeting follow-up: Product demo feedback">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">JD</div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 fw-bold">John Davis</h6>
                                    <small class="text-muted">10:30 AM</small>
                                </div>
                                <div class="d-flex w-100 justify-content-between">
                                    <p class="mb-1 fw-bold">Meeting follow-up: Product demo feedback</p>
                                    <span class="badge bg-success rounded-pill">Clients</span>
                                </div>
                                <small class="text-muted">Thank you for the product demonstration yesterday. Our team was impressed with the features...</small>
                            </div>
                        </div>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action unread" data-sender="sarah-miller" data-subject="Contract renewal discussion">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #ea4335; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">SM</div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 fw-bold">Sarah Miller</h6>
                                    <small class="text-muted">Yesterday</small>
                                </div>
                                <div class="d-flex w-100 justify-content-between">
                                    <p class="mb-1 fw-bold">Contract renewal discussion</p>
                                    <span class="badge bg-danger rounded-pill">Urgent</span>
                                </div>
                                <small class="text-muted">I'd like to schedule a call to discuss the upcoming contract renewal...</small>
                            </div>
                        </div>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action unread" data-sender="alex-chen" data-subject="New customer inquiry about enterprise plan">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #34a853; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">AC</div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 fw-bold">Alex Chen</h6>
                                    <small class="text-muted">May 10</small>
                                </div>
                                <div class="d-flex w-100 justify-content-between">
                                    <p class="mb-1 fw-bold">New customer inquiry about enterprise plan</p>
                                    <span class="badge bg-warning rounded-pill">Follow-up</span>
                                </div>
                                <small class="text-muted">We're interested in learning more about your enterprise plan options...</small>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        `;

        // Add event listeners to email items
        inboxContent.querySelectorAll('.list-group-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const sender = item.getAttribute('data-sender');
                const subject = item.getAttribute('data-subject');
                this.openEmail(sender, subject);
            });
        });
    }

    /**
     * Set up sent tab content
     */
    setupSentTab() {
        this.log('Setting up sent tab content');

        const sentContent = document.getElementById(`${this.config.appPrefix}-sent-content`);
        if (!sentContent) return;

        // Create sent content
        sentContent.innerHTML = `
            <div class="list-group">
                <a href="#" class="list-group-item list-group-item-action">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">ME</div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">To: John Davis</h6>
                                <small class="text-muted">11:45 AM</small>
                            </div>
                            <p class="mb-1">Re: Meeting follow-up: Product demo feedback</p>
                            <small class="text-muted">Thank you for your feedback. I'd be happy to schedule a follow-up call next week...</small>
                        </div>
                    </div>
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">ME</div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">To: Marketing Team</h6>
                                <small class="text-muted">Yesterday</small>
                            </div>
                            <p class="mb-1">Q2 Marketing Campaign Plan</p>
                            <small class="text-muted">Please find attached the Q2 marketing campaign plan for review...</small>
                        </div>
                    </div>
                </a>
            </div>
        `;
    }

    /**
     * Set up read tab content
     */
    setupReadTab() {
        this.log('Setting up read tab content');

        const readContent = document.getElementById(`${this.config.appPrefix}-read-content`);
        if (!readContent) return;

        // Create read content
        readContent.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i> Select an email from the inbox to view its content.
            </div>
        `;
    }

    /**
     * Set up compose tab content
     */
    setupComposeTab() {
        this.log('Setting up compose tab content');

        const composeContent = document.getElementById(`${this.config.appPrefix}-compose-content`);
        if (!composeContent) return;

        // Create compose content
        composeContent.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <form>
                        <div class="mb-3">
                            <label for="${this.config.appPrefix}-email-to" class="form-label">To</label>
                            <input type="email" class="form-control" id="${this.config.appPrefix}-email-to" placeholder="<EMAIL>" required>
                        </div>
                        <div class="mb-3">
                            <label for="${this.config.appPrefix}-email-cc" class="form-label">Cc/Bcc</label>
                            <input type="text" class="form-control" id="${this.config.appPrefix}-email-cc" placeholder="<EMAIL>">
                        </div>
                        <div class="mb-3">
                            <label for="${this.config.appPrefix}-email-subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="${this.config.appPrefix}-email-subject" placeholder="Email subject">
                        </div>
                        <div class="mb-3">
                            <label for="${this.config.appPrefix}-email-body" class="form-label">Message</label>
                            <textarea class="form-control" id="${this.config.appPrefix}-email-body" rows="10" placeholder="Compose your message here..."></textarea>
                        </div>
                        <div class="mb-3">
                            <button type="button" class="btn btn-outline-secondary" id="${this.config.appPrefix}-attach-btn">
                                <i class="bi bi-paperclip"></i> Attach
                            </button>
                            <input type="file" class="d-none" id="${this.config.appPrefix}-file-input" multiple>
                        </div>
                        <div class="mb-3 d-none" id="${this.config.appPrefix}-attachments-container">
                            <div class="border rounded p-2 bg-light">
                                <h6 class="mb-2">Attachments</h6>
                                <div id="${this.config.appPrefix}-attachments-list">
                                    <!-- Attachments will be listed here -->
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" id="${this.config.appPrefix}-discard-btn">Discard</button>
                            <div>
                                <button type="button" class="btn btn-outline-primary me-2" id="${this.config.appPrefix}-save-draft-btn">Save Draft</button>
                                <button type="button" class="btn btn-primary" id="${this.config.appPrefix}-send-btn">Send</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        `;

        // Add event listeners
        const attachBtn = document.getElementById(`${this.config.appPrefix}-attach-btn`);
        const fileInput = document.getElementById(`${this.config.appPrefix}-file-input`);

        if (attachBtn && fileInput) {
            attachBtn.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', () => {
                this.handleAttachments(fileInput);
            });
        }

        const discardBtn = document.getElementById(`${this.config.appPrefix}-discard-btn`);
        if (discardBtn) {
            discardBtn.addEventListener('click', this.discardEmail.bind(this));
        }

        const saveDraftBtn = document.getElementById(`${this.config.appPrefix}-save-draft-btn`);
        if (saveDraftBtn) {
            saveDraftBtn.addEventListener('click', this.saveDraft.bind(this));
        }

        const sendBtn = document.getElementById(`${this.config.appPrefix}-send-btn`);
        if (sendBtn) {
            sendBtn.addEventListener('click', this.sendEmail.bind(this));
        }
    }
    /**
     * Fix any UI issues with the Gmail modal
     */
    fixUIIssues() {
        this.log('Fixing UI issues');

        // Fix tab content overflow
        const tabContent = document.querySelector(`#${this.config.modalId} .tab-content`);
        if (tabContent) {
            tabContent.style.minHeight = '300px';
            tabContent.style.maxHeight = '60vh';
            tabContent.style.overflowY = 'auto';
        }

        // Fix email list height in modal
        const emailList = document.querySelector(`#${this.config.modalId} .list-group`);
        if (emailList) {
            emailList.style.maxHeight = '50vh';
            emailList.style.overflowY = 'auto';
        }
    }

    /**
     * Open the Gmail modal
     */
    openGmailModal() {
        this.log('Opening Gmail modal');

        // Get the modal element
        const gmailModal = document.getElementById(this.config.modalId);
        if (!gmailModal) {
            console.error('Gmail modal not found in DOM');
            alert('Gmail feature is not available. Please try again later.');
            return false;
        }

        try {
            // Use Bootstrap's Modal class if available
            if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const modal = new bootstrap.Modal(gmailModal);
                modal.show();
            } else {
                // Manual approach as fallback
                this.manuallyOpenModal(gmailModal);
            }

            return true;
        } catch (error) {
            console.error('Error opening Gmail modal:', error);

            // Try manual approach as fallback
            try {
                this.manuallyOpenModal(gmailModal);
                return true;
            } catch (fallbackError) {
                console.error('Fallback modal opening failed:', fallbackError);
                alert('Could not open Gmail. Please try again later.');
                return false;
            }
        }
    }

    /**
     * Manually open the modal without Bootstrap
     */
    manuallyOpenModal(modal) {
        this.log('Manually opening modal');

        // Create backdrop if it doesn't exist
        let backdrop = document.querySelector('.modal-backdrop');
        if (!backdrop) {
            backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
        }

        // Show the modal
        modal.classList.add('show');
        modal.style.display = 'block';
        modal.setAttribute('aria-modal', 'true');
        modal.setAttribute('role', 'dialog');
        modal.removeAttribute('aria-hidden');

        // Add modal-open class to body
        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';
        document.body.style.paddingRight = '17px'; // Account for scrollbar
    }

    /**
     * Close the Gmail modal
     */
    closeGmailModal() {
        this.log('Closing Gmail modal');

        // Get the modal element
        const gmailModal = document.getElementById(this.config.modalId);
        if (!gmailModal) return;

        try {
            // Use Bootstrap's Modal class if available
            if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const modal = bootstrap.Modal.getInstance(gmailModal);
                if (modal) {
                    modal.hide();
                }
            } else {
                // Manual approach as fallback
                this.manuallyCloseModal(gmailModal);
            }
        } catch (error) {
            console.error('Error closing Gmail modal:', error);

            // Try manual approach as fallback
            try {
                this.manuallyCloseModal(gmailModal);
            } catch (fallbackError) {
                console.error('Fallback modal closing failed:', fallbackError);
            }
        }

        // Clean up any lingering backdrops
        this.cleanupModalBackdrops();
    }

    /**
     * Manually close the modal without Bootstrap
     */
    manuallyCloseModal(modal) {
        this.log('Manually closing modal');

        // Hide the modal
        modal.classList.remove('show');
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
        modal.removeAttribute('aria-modal');
        modal.removeAttribute('role');

        // Remove backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }

        // Remove modal-open class from body
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }

    /**
     * Clean up any lingering modal backdrops
     */
    cleanupModalBackdrops() {
        this.log('Cleaning up modal backdrops');

        // Remove all modal backdrops
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });

        // Reset body styles if no modals are open
        if (!document.querySelector('.modal.show')) {
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }
    }

    /**
     * Enhanced search emails with highlighting and better matching
     * @param {string} searchTerm - The term to search for
     */
    searchEmails(searchTerm) {
        this.log('Searching emails for:', searchTerm);
        this.searchTerm = searchTerm;

        if (!searchTerm || searchTerm.trim() === '') {
            // If search term is empty, show all emails and remove highlights
            this.showAllEmails();
            return;
        }

        // Get active tab content
        const activeTab = document.querySelector(`#${this.config.appPrefix}-gmailTabContent .tab-pane.active`);
        if (!activeTab) return;

        const emailItems = activeTab.querySelectorAll('.list-group-item');
        if (!emailItems.length) return;

        // Normalize query
        const normalizedQuery = searchTerm.toLowerCase().trim();
        let visibleCount = 0;

        // Show/hide based on search query
        emailItems.forEach(item => {
            const sender = item.getAttribute('data-sender') || '';
            const subject = item.getAttribute('data-subject') || '';
            const content = item.querySelector('small, .text-muted:last-child')?.textContent.toLowerCase() || '';
            const senderText = item.querySelector('h6, .fw-bold')?.textContent.toLowerCase() || '';

            if (sender.includes(normalizedQuery) || 
                subject.toLowerCase().includes(normalizedQuery) || 
                content.includes(normalizedQuery) ||
                senderText.includes(normalizedQuery)) {
                item.style.display = '';
                visibleCount++;

                // Highlight matching text
                this.highlightText(item, normalizedQuery);
            } else {
                item.style.display = 'none';
            }
        });

        // Show toast with results count
        this.showToast(`Found ${visibleCount} email${visibleCount !== 1 ? 's' : ''} matching "${searchTerm}"`);

        // Show no results message if needed
        this.checkNoResults(activeTab, visibleCount);
    }

    /**
     * Enhanced sort emails with better date handling and visual feedback
     * @param {string} sortType - The type of sort to apply
     */
    sortEmails(sortType) {
        this.log('Sorting emails by:', sortType);
        this.currentSort = sortType;

        // Get the active tab content
        const activeTab = document.querySelector(`#${this.config.appPrefix}-gmailTabContent .tab-pane.active`);
        if (!activeTab) return;

        const emailList = activeTab.querySelector('.list-group');
        if (!emailList) return;

        // Get all email items
        const emailItems = Array.from(emailList.querySelectorAll('.list-group-item'));
        if (!emailItems.length) return;

        // Sort emails based on sort type
        emailItems.sort((a, b) => {
            switch (sortType) {
                case 'date-desc':
                    // Newest first (default)
                    const dateA = a.getAttribute('data-date') || new Date().toISOString();
                    const dateB = b.getAttribute('data-date') || new Date().toISOString();
                    return dateB.localeCompare(dateA);
                case 'date-asc':
                    // Oldest first
                    const dateC = a.getAttribute('data-date') || new Date().toISOString();
                    const dateD = b.getAttribute('data-date') || new Date().toISOString();
                    return dateC.localeCompare(dateD);
                case 'sender-asc':
                    // Sender A-Z
                    const senderA = a.getAttribute('data-sender') || '';
                    const senderB = b.getAttribute('data-sender') || '';
                    return senderA.localeCompare(senderB);
                case 'sender-desc':
                    // Sender Z-A
                    const senderC = a.getAttribute('data-sender') || '';
                    const senderD = b.getAttribute('data-sender') || '';
                    return senderD.localeCompare(senderC);
                case 'subject-asc':
                    // Subject A-Z
                    const subjectA = a.getAttribute('data-subject') || '';
                    const subjectB = b.getAttribute('data-subject') || '';
                    return subjectA.toLowerCase().localeCompare(subjectB.toLowerCase());
                case 'subject-desc':
                    // Subject Z-A
                    const subjectC = a.getAttribute('data-subject') || '';
                    const subjectD = b.getAttribute('data-subject') || '';
                    return subjectD.toLowerCase().localeCompare(subjectC.toLowerCase());
                default:
                    return 0;
            }
        });

        // Reorder the DOM elements
        emailItems.forEach(item => {
            emailList.appendChild(item);
        });

        // Update dropdown button text
        const dropdownButton = document.getElementById(`${this.config.appPrefix}-sort-dropdown`);
        if (dropdownButton) {
            const sortOptions = {
                'date-desc': 'Newest first',
                'date-asc': 'Oldest first',
                'sender-asc': 'Sender A-Z',
                'sender-desc': 'Sender Z-A',
                'subject-asc': 'Subject A-Z',
                'subject-desc': 'Subject Z-A'
            };
            dropdownButton.innerHTML = `<i class="bi bi-sort-down"></i> ${sortOptions[sortType] || 'Sort'}`;
        }

        // Show toast
        this.showToast(`Emails sorted by ${sortType.replace('-', ' ')}`);
    }
            case 'sender-desc': // Sender Z-A
                emailItems.sort((a, b) => {
                    const senderA = a.querySelector('h6').textContent.toLowerCase();
                    const senderB = b.querySelector('h6').textContent.toLowerCase();
                    return senderB.localeCompare(senderA);
                });
                break;
            case 'subject-asc': // Subject A-Z
                emailItems.sort((a, b) => {
                    const subjectA = a.querySelector('p').textContent.toLowerCase();
                    const subjectB = b.querySelector('p').textContent.toLowerCase();
                    return subjectA.localeCompare(subjectB);
                });
                break;
            case 'subject-desc': // Subject Z-A
                emailItems.sort((a, b) => {
                    const subjectA = a.querySelector('p').textContent.toLowerCase();
                    const subjectB = b.querySelector('p').textContent.toLowerCase();
                    return subjectB.localeCompare(subjectA);
                });
                break;
            default:
                return;
        }

        // Re-append sorted items to the list
        emailItems.forEach(item => {
            emailList.appendChild(item);
        });
    }

    /**
     * Helper method to compare dates in email items
     */
    compareDates(dateA, dateB) {
        // Convert relative dates to comparable values
        const getValue = (date) => {
            if (date.includes('AM') || date.includes('PM')) {
                return 4; // Today
            } else if (date.includes('Yesterday')) {
                return 3; // Yesterday
            } else if (date.includes('days ago')) {
                const days = parseInt(date);
                return 2 - (days / 100); // Older but within a week
            } else {
                return 1; // Older than a week
            }
        };

        return getValue(dateB) - getValue(dateA);
    }

    /**
     * Filter emails based on filter type
     * @param {string} filterType - The type of filter to apply
     */
    filterEmails(filterType) {
        this.log('Filtering emails by:', filterType);

        // Get all email items in the active tab
        const activeTab = document.querySelector(`#${this.config.appPrefix}-gmailTabContent .tab-pane.active`);
        if (!activeTab) return;

        const emailItems = activeTab.querySelectorAll('.list-group-item');

        // Apply filter based on filter type
        switch (filterType) {
            case 'all':
                // Show all emails
                emailItems.forEach(item => {
                    item.style.display = '';
                });
                break;
            case 'unread':
                // Show only unread emails
                emailItems.forEach(item => {
                    if (item.classList.contains('unread')) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
                break;
            case 'read':
                // Show only read emails
                emailItems.forEach(item => {
                    if (!item.classList.contains('unread')) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
                break;
            default:
                // Check if it's a label filter
                if (filterType.startsWith('label-')) {
                    const label = filterType.replace('label-', '');

                    // Show only emails with the specified label
                    emailItems.forEach(item => {
                        const badge = item.querySelector('.badge');
                        if (badge && badge.textContent.toLowerCase() === label) {
                            item.style.display = '';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                }
                break;
        }

        // Show a message if no results found
        this.checkNoResults();
    }

    /**
     * Reset all email filters
     */
    resetEmailFilters() {
        this.log('Resetting email filters');

        // Get all email items
        const emailItems = document.querySelectorAll(`#${this.config.appPrefix}-inbox-content .list-group-item, #${this.config.appPrefix}-sent-content .list-group-item`);

        // Show all emails
        emailItems.forEach(item => {
            item.style.display = '';
        });

        // Remove any "no results" messages
        const noResultsMessages = document.querySelectorAll('.no-results-message');
        noResultsMessages.forEach(message => message.remove());
    }

    /**
     * Check if there are no visible results and show a message
     */
    checkNoResults() {
        // Get the active tab
        const activeTab = document.querySelector(`#${this.config.appPrefix}-gmailTabContent .tab-pane.active`);
        if (!activeTab) return;

        // Get all visible email items
        const visibleItems = Array.from(activeTab.querySelectorAll('.list-group-item')).filter(item => item.style.display !== 'none');

        // Remove any existing "no results" messages
        const existingMessage = activeTab.querySelector('.no-results-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // If no visible items, show a message
        if (visibleItems.length === 0) {
            const noResultsMessage = document.createElement('div');
            noResultsMessage.className = 'alert alert-info no-results-message mt-3';
            noResultsMessage.innerHTML = '<i class="bi bi-info-circle me-2"></i> No emails match your search or filter criteria.';

            // Add the message to the active tab
            const emailListSection = activeTab.querySelector('.list-group').parentNode;
            emailListSection.appendChild(noResultsMessage);
        }
    }

    /**
     * Open an email to view its content
     * @param {string} sender - The sender ID
     * @param {string} subject - The email subject
     */
    openEmail(sender, subject) {
        this.log('Opening email from:', sender, 'with subject:', subject);

        // Switch to the read tab
        const readTab = document.getElementById(`${this.config.appPrefix}-read-tab`);
        if (readTab) {
            readTab.click();
        }

        // Get the read content container
        const readContent = document.getElementById(`${this.config.appPrefix}-read-content`);
        if (!readContent) return;

        // Get email content based on sender and subject
        const emailContent = this.getEmailContent(sender, subject);

        // Update the read tab with the email content
        readContent.innerHTML = emailContent;

        // Add event listeners to reply and forward buttons
        const replyBtn = document.getElementById(`${this.config.appPrefix}-reply-btn`);
        if (replyBtn) {
            replyBtn.addEventListener('click', () => this.replyToEmail(sender, subject));
        }

        const forwardBtn = document.getElementById(`${this.config.appPrefix}-forward-btn`);
        if (forwardBtn) {
            forwardBtn.addEventListener('click', () => this.forwardEmail(sender, subject));
        }

        // Mark the email as read
        this.markEmailAsRead(sender, subject);
    }

    /**
     * Get email content based on sender and subject
     * @param {string} sender - The sender ID
     * @param {string} subject - The email subject
     * @returns {string} - HTML content for the email
     */
    getEmailContent(sender, subject) {
        // This is a simplified implementation that returns mock content
        // In a real implementation, this would fetch the actual email content

        let senderName = '';
        let senderEmail = '';
        let senderInitials = '';
        let senderColor = '';
        let date = '';
        let content = '';

        // Set sender details based on sender ID
        switch (sender) {
            case 'john-davis':
                senderName = 'John Davis';
                senderEmail = '<EMAIL>';
                senderInitials = 'JD';
                senderColor = '#4285f4';
                date = 'Today, 10:30 AM';
                content = `
                    <p>Thank you for the product demonstration yesterday. Our team was impressed with the features you showcased.</p>
                    <p>We particularly liked the reporting dashboard and the user management system. The interface is intuitive and aligns well with our workflow.</p>
                    <p>However, we had a few questions about the integration capabilities:</p>
                    <ol>
                        <li>Does the system support API integration with our existing CRM?</li>
                        <li>Can we customize the reporting templates?</li>
                        <li>What is the timeline for implementing the solution?</li>
                    </ol>
                    <p>Looking forward to your response.</p>
                    <p>Best regards,<br>John Davis<br>Product Manager</p>
                `;
                break;
            case 'sarah-miller':
                senderName = 'Sarah Miller';
                senderEmail = '<EMAIL>';
                senderInitials = 'SM';
                senderColor = '#ea4335';
                date = 'Yesterday, 2:45 PM';
                content = `
                    <p>I'd like to schedule a call to discuss the upcoming contract renewal for our enterprise subscription.</p>
                    <p>Our current contract expires at the end of next month, and we're considering upgrading to the premium tier based on our increased usage over the past year.</p>
                    <p>Could you provide some information about:</p>
                    <ul>
                        <li>Pricing options for the premium tier</li>
                        <li>Additional features included in the upgrade</li>
                        <li>Any promotional discounts currently available</li>
                    </ul>
                    <p>Are you available for a call next Tuesday at 2:00 PM?</p>
                    <p>Regards,<br>Sarah Miller<br>Procurement Director</p>
                `;
                break;
            case 'alex-chen':
                senderName = 'Alex Chen';
                senderEmail = '<EMAIL>';
                senderInitials = 'AC';
                senderColor = '#34a853';
                date = 'May 10, 9:15 AM';
                content = `
                    <p>We're interested in learning more about your enterprise plan options for our organization.</p>
                    <p>We currently have about 500 employees and are looking for a solution that can scale with our growth. Your product was recommended to us by one of your existing customers.</p>
                    <p>Key requirements for us include:</p>
                    <ul>
                        <li>Advanced security features</li>
                        <li>Custom branding options</li>
                        <li>Dedicated support</li>
                        <li>Training for our team</li>
                    </ul>
                    <p>Could you send over some information about your enterprise offerings and possibly arrange a demo for our team?</p>
                    <p>Thank you,<br>Alex Chen<br>IT Director</p>
                `;
                break;
            default:
                senderName = 'Unknown Sender';
                senderEmail = '<EMAIL>';
                senderInitials = 'UN';
                senderColor = '#5f6368';
                date = 'Unknown Date';
                content = '<p>Email content not available.</p>';
        }

        // Return HTML for the email content
        return `
            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div style="width: 40px; height: 40px; border-radius: 50%; background-color: ${senderColor}; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">${senderInitials}</div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-0">${subject}</h5>
                            <div class="text-muted">
                                <small>From: ${senderName} &lt;${senderEmail}&gt;</small>
                                <small class="ms-2">To: Me</small>
                                <small class="ms-2">${date}</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="email-content">
                        ${content}
                    </div>

                    <div class="mt-4 pt-3 border-top">
                        <button class="btn btn-primary me-2" id="${this.config.appPrefix}-reply-btn">
                            <i class="bi bi-reply"></i> Reply
                        </button>
                        <button class="btn btn-outline-secondary me-2" id="${this.config.appPrefix}-forward-btn">
                            <i class="bi bi-forward"></i> Forward
                        </button>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="bi bi-printer me-2"></i> Print</a></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-archive me-2"></i> Archive</a></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-trash me-2"></i> Delete</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-exclamation-triangle me-2"></i> Mark as spam</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Mark an email as read
     * @param {string} sender - The sender ID
     * @param {string} subject - The email subject
     */
    markEmailAsRead(sender, subject) {
        this.log('Marking email as read:', subject);

        // Find the email item in the inbox
        const emailItems = document.querySelectorAll(`#${this.config.appPrefix}-inbox-content .list-group-item`);

        emailItems.forEach(item => {
            const itemSender = item.getAttribute('data-sender');
            const itemSubject = item.getAttribute('data-subject');

            if (itemSender === sender && itemSubject === subject) {
                // Remove the unread class
                item.classList.remove('unread');

                // Update the styling
                const senderElement = item.querySelector('h6');
                const subjectElement = item.querySelector('p');

                if (senderElement) senderElement.classList.remove('fw-bold');
                if (subjectElement) subjectElement.classList.remove('fw-bold');
            }
        });
    }

    /**
     * Reply to an email
     * @param {string} sender - The sender ID
     * @param {string} subject - The email subject
     */
    replyToEmail(sender, subject) {
        this.log('Replying to email:', subject);

        // Switch to the compose tab
        const composeTab = document.getElementById(`${this.config.appPrefix}-compose-tab`);
        if (composeTab) {
            composeTab.click();
        }

        // Get the compose form elements
        const toInput = document.getElementById(`${this.config.appPrefix}-email-to`);
        const subjectInput = document.getElementById(`${this.config.appPrefix}-email-subject`);
        const bodyInput = document.getElementById(`${this.config.appPrefix}-email-body`);

        if (!toInput || !subjectInput || !bodyInput) return;

        // Get sender details
        let senderName = '';
        let senderEmail = '';

        switch (sender) {
            case 'john-davis':
                senderName = 'John Davis';
                senderEmail = '<EMAIL>';
                break;
            case 'sarah-miller':
                senderName = 'Sarah Miller';
                senderEmail = '<EMAIL>';
                break;
            case 'alex-chen':
                senderName = 'Alex Chen';
                senderEmail = '<EMAIL>';
                break;
            default:
                senderName = 'Unknown Sender';
                senderEmail = '<EMAIL>';
        }

        // Populate the form
        toInput.value = senderEmail;
        subjectInput.value = `Re: ${subject}`;

        // Create a reply template
        const replyTemplate = `

----- Original Message -----
From: ${senderName} <${senderEmail}>
Subject: ${subject}
Date: ${new Date().toLocaleString()}

`;

        bodyInput.value = replyTemplate;

        // Focus on the body input
        bodyInput.focus();

        // Show a success message
        this.showComposeSuccessMessage('Reply started');
    }

    /**
     * Forward an email
     * @param {string} sender - The sender ID
     * @param {string} subject - The email subject
     */
    forwardEmail(sender, subject) {
        this.log('Forwarding email:', subject);

        // Switch to the compose tab
        const composeTab = document.getElementById(`${this.config.appPrefix}-compose-tab`);
        if (composeTab) {
            composeTab.click();
        }

        // Get the compose form elements
        const toInput = document.getElementById(`${this.config.appPrefix}-email-to`);
        const subjectInput = document.getElementById(`${this.config.appPrefix}-email-subject`);
        const bodyInput = document.getElementById(`${this.config.appPrefix}-email-body`);

        if (!toInput || !subjectInput || !bodyInput) return;

        // Get sender details
        let senderName = '';
        let senderEmail = '';
        let emailContent = '';

        switch (sender) {
            case 'john-davis':
                senderName = 'John Davis';
                senderEmail = '<EMAIL>';
                emailContent = `Thank you for the product demonstration yesterday. Our team was impressed with the features you showcased.

We particularly liked the reporting dashboard and the user management system. The interface is intuitive and aligns well with our workflow.

However, we had a few questions about the integration capabilities:
1. Does the system support API integration with our existing CRM?
2. Can we customize the reporting templates?
3. What is the timeline for implementing the solution?

Looking forward to your response.

Best regards,
John Davis
Product Manager`;
                break;
            case 'sarah-miller':
                senderName = 'Sarah Miller';
                senderEmail = '<EMAIL>';
                emailContent = `I'd like to schedule a call to discuss the upcoming contract renewal for our enterprise subscription.

Our current contract expires at the end of next month, and we're considering upgrading to the premium tier based on our increased usage over the past year.

Could you provide some information about:
- Pricing options for the premium tier
- Additional features included in the upgrade
- Any promotional discounts currently available

Are you available for a call next Tuesday at 2:00 PM?

Regards,
Sarah Miller
Procurement Director`;
                break;
            case 'alex-chen':
                senderName = 'Alex Chen';
                senderEmail = '<EMAIL>';
                emailContent = `We're interested in learning more about your enterprise plan options for our organization.

We currently have about 500 employees and are looking for a solution that can scale with our growth. Your product was recommended to us by one of your existing customers.

Key requirements for us include:
- Advanced security features
- Custom branding options
- Dedicated support
- Training for our team

Could you send over some information about your enterprise offerings and possibly arrange a demo for our team?

Thank you,
Alex Chen
IT Director`;
                break;
            default:
                senderName = 'Unknown Sender';
                senderEmail = '<EMAIL>';
                emailContent = 'Email content not available.';
        }

        // Clear the to field
        toInput.value = '';

        // Populate subject and body
        subjectInput.value = `Fwd: ${subject}`;

        // Create a forward template
        const forwardTemplate = `

----- Forwarded Message -----
From: ${senderName} <${senderEmail}>
Subject: ${subject}
Date: ${new Date().toLocaleString()}

${emailContent}
`;

        bodyInput.value = forwardTemplate;

        // Focus on the to input
        toInput.focus();

        // Show a success message
        this.showComposeSuccessMessage('Forward started');
    }

    /**
     * Send an email
     */
    sendEmail() {
        this.log('Sending email');

        // Get the compose form elements
        const toInput = document.getElementById(`${this.config.appPrefix}-email-to`);
        const subjectInput = document.getElementById(`${this.config.appPrefix}-email-subject`);
        const bodyInput = document.getElementById(`${this.config.appPrefix}-email-body`);

        if (!toInput || !subjectInput || !bodyInput) return;

        // Validate the form
        if (!toInput.value) {
            alert('Please enter a recipient email address.');
            toInput.focus();
            return;
        }

        if (!subjectInput.value) {
            const proceed = confirm('Send this message without a subject?');
            if (!proceed) {
                subjectInput.focus();
                return;
            }
        }

        // Show a loading indicator
        this.showSendingIndicator();

        // Send immediately without delay
        // Hide the loading indicator
        this.hideSendingIndicator();

        // Clear the form
        toInput.value = '';
        subjectInput.value = '';
        bodyInput.value = '';

        // Switch to the sent tab
        const sentTab = document.getElementById(`${this.config.appPrefix}-sent-tab`);
        if (sentTab) {
            sentTab.click();
        }

        // Show a success message
        this.showSentSuccessMessage();

        // Add the sent email to the sent items list (in a real implementation)
        // this.addEmailToSentItems(to, subject, body);
    }

    /**
     * Save an email draft
     */
    saveDraft() {
        this.log('Saving draft');

        // Get the compose form elements
        const toInput = document.getElementById(`${this.config.appPrefix}-email-to`);
        const subjectInput = document.getElementById(`${this.config.appPrefix}-email-subject`);
        const bodyInput = document.getElementById(`${this.config.appPrefix}-email-body`);

        if (!toInput || !subjectInput || !bodyInput) return;

        // Show a loading indicator
        this.showSavingDraftIndicator();

        // Save immediately without delay
        // Hide the loading indicator
        this.hideSavingDraftIndicator();

        // Show a success message
        this.showDraftSavedMessage();

        // In a real implementation, we would save the draft to storage
        // this.storeDraft(to, subject, body);
    }

    /**
     * Discard an email draft
     */
    discardEmail() {
        this.log('Discarding email');

        // Get the compose form elements
        const toInput = document.getElementById(`${this.config.appPrefix}-email-to`);
        const subjectInput = document.getElementById(`${this.config.appPrefix}-email-subject`);
        const bodyInput = document.getElementById(`${this.config.appPrefix}-email-body`);

        if (!toInput || !subjectInput || !bodyInput) return;

        // Check if the form has content
        if (toInput.value || subjectInput.value || bodyInput.value) {
            const confirm = window.confirm('Are you sure you want to discard this draft?');
            if (!confirm) return;
        }

        // Clear the form
        toInput.value = '';
        subjectInput.value = '';
        bodyInput.value = '';

        // Switch to the inbox tab
        const inboxTab = document.getElementById(`${this.config.appPrefix}-inbox-tab`);
        if (inboxTab) {
            inboxTab.click();
        }
    }

    /**
     * Handle file attachments
     * @param {HTMLInputElement} fileInput - The file input element
     */
    handleAttachments(fileInput) {
        this.log('Handling attachments');

        if (!fileInput.files || fileInput.files.length === 0) return;

        // Get the attachments container and list
        const attachmentsContainer = document.getElementById(`${this.config.appPrefix}-attachments-container`);
        const attachmentsList = document.getElementById(`${this.config.appPrefix}-attachments-list`);

        if (!attachmentsContainer || !attachmentsList) return;

        // Show the attachments container
        attachmentsContainer.classList.remove('d-none');

        // Add each file to the attachments list
        Array.from(fileInput.files).forEach(file => {
            // Create attachment item
            const attachmentItem = document.createElement('div');
            attachmentItem.className = 'd-flex justify-content-between align-items-center mb-2';

            // Get file icon based on type
            let fileIcon = 'bi-file-earmark';
            if (file.type.startsWith('image/')) {
                fileIcon = 'bi-file-earmark-image';
            } else if (file.type.startsWith('video/')) {
                fileIcon = 'bi-file-earmark-play';
            } else if (file.type.startsWith('audio/')) {
                fileIcon = 'bi-file-earmark-music';
            } else if (file.type.includes('pdf')) {
                fileIcon = 'bi-file-earmark-pdf';
            } else if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
                fileIcon = 'bi-file-earmark-word';
            } else if (file.type.includes('excel') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) {
                fileIcon = 'bi-file-earmark-excel';
            }

            // Format file size
            const fileSize = this.formatFileSize(file.size);

            // Create attachment HTML
            attachmentItem.innerHTML = `
                <div>
                    <i class="bi ${fileIcon} me-2"></i>
                    <span>${file.name}</span>
                    <small class="text-muted ms-2">${fileSize}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger remove-attachment">
                    <i class="bi bi-x"></i>
                </button>
            `;

            // Add event listener to remove button
            const removeBtn = attachmentItem.querySelector('.remove-attachment');
            if (removeBtn) {
                removeBtn.addEventListener('click', () => {
                    attachmentItem.remove();

                    // Hide the attachments container if no attachments left
                    if (attachmentsList.children.length === 0) {
                        attachmentsContainer.classList.add('d-none');
                    }
                });
            }

            // Add to the attachments list
            attachmentsList.appendChild(attachmentItem);
        });

        // Clear the file input
        fileInput.value = '';
    }

    /**
     * Format file size in human-readable format
     * @param {number} bytes - File size in bytes
     * @returns {string} - Formatted file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Show a success message in the compose tab
     * @param {string} message - The message to show
     */
    showComposeSuccessMessage(message) {
        const composeContent = document.getElementById(`${this.config.appPrefix}-compose-content`);
        if (!composeContent) return;

        // Create success message
        const successMessage = document.createElement('div');
        successMessage.className = 'alert alert-success compose-success-message mb-3';
        successMessage.innerHTML = `<i class="bi bi-check-circle me-2"></i> ${message}`;

        // Add the success message to the compose tab
        composeContent.prepend(successMessage);

        // Remove the success message after a shorter delay
        setTimeout(() => {
            successMessage.remove();
        }, 1000);
    }

    /**
     * Show a sending indicator
     */
    showSendingIndicator() {
        const composeContent = document.getElementById(`${this.config.appPrefix}-compose-content`);
        if (!composeContent) return;

        // Create sending indicator
        const sendingIndicator = document.createElement('div');
        sendingIndicator.className = 'alert alert-info sending-indicator mb-3';
        sendingIndicator.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm text-info me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div>Sending email...</div>
            </div>
        `;

        // Add the sending indicator to the compose tab
        composeContent.prepend(sendingIndicator);
    }

    /**
     * Hide the sending indicator
     */
    hideSendingIndicator() {
        // Remove any sending indicators
        const sendingIndicators = document.querySelectorAll('.sending-indicator');
        sendingIndicators.forEach(indicator => indicator.remove());
    }

    /**
     * Show a success message after sending an email
     */
    showSentSuccessMessage() {
        const sentContent = document.getElementById(`${this.config.appPrefix}-sent-content`);
        if (!sentContent) return;

        // Create success message
        const successMessage = document.createElement('div');
        successMessage.className = 'alert alert-success sent-success-message mb-3';
        successMessage.innerHTML = '<i class="bi bi-check-circle me-2"></i> Email sent successfully.';

        // Add the success message to the sent tab
        sentContent.prepend(successMessage);

        // Remove the success message after a shorter delay
        setTimeout(() => {
            successMessage.remove();
        }, 1000);
    }

    /**
     * Show a saving draft indicator
     */
    showSavingDraftIndicator() {
        const composeContent = document.getElementById(`${this.config.appPrefix}-compose-content`);
        if (!composeContent) return;

        // Create saving indicator
        const savingIndicator = document.createElement('div');
        savingIndicator.className = 'alert alert-info saving-indicator mb-3';
        savingIndicator.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm text-info me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div>Saving draft...</div>
            </div>
        `;

        // Add the saving indicator to the compose tab
        composeContent.prepend(savingIndicator);
    }

    /**
     * Hide the saving draft indicator
     */
    hideSavingDraftIndicator() {
        // Remove any saving indicators
        const savingIndicators = document.querySelectorAll('.saving-indicator');
        savingIndicators.forEach(indicator => indicator.remove());
    }

    /**
     * Show a success message after saving a draft
     */
    showDraftSavedMessage() {
        const composeContent = document.getElementById(`${this.config.appPrefix}-compose-content`);
        if (!composeContent) return;

        // Create success message
        const successMessage = document.createElement('div');
        successMessage.className = 'alert alert-success draft-success-message mb-3';
        successMessage.innerHTML = '<i class="bi bi-check-circle me-2"></i> Draft saved successfully.';

        // Add the success message to the compose tab
        composeContent.prepend(successMessage);

        // Remove the success message after a delay
        setTimeout(() => {
            successMessage.remove();
        }, 3000);
    }

    /**
     * Refresh Gmail content
     */
    refreshGmail() {
        this.log('Refreshing Gmail content');

        // Reset filters and search
        this.resetEmailFilters();

        // Clear search input
        const searchInput = document.getElementById(`${this.config.appPrefix}-email-search`);
        if (searchInput) {
            searchInput.value = '';
        }

        // Show a loading indicator
        this.showLoadingIndicator();

        // Refresh immediately without delay
        this.hideLoadingIndicator();

        // Show a success message
        this.showRefreshSuccessMessage();
    }

    /**
     * Show a loading indicator
     */
    showLoadingIndicator() {
        // Get the active tab
        const activeTab = document.querySelector(`#${this.config.appPrefix}-gmailTabContent .tab-pane.active`);
        if (!activeTab) return;

        // Create loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'text-center my-4 loading-indicator';
        loadingIndicator.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Refreshing emails...</p>
        `;

        // Add the loading indicator to the active tab
        activeTab.appendChild(loadingIndicator);
    }

    /**
     * Hide the loading indicator
     */
    hideLoadingIndicator() {
        // Remove any loading indicators
        const loadingIndicators = document.querySelectorAll('.loading-indicator');
        loadingIndicators.forEach(indicator => indicator.remove());
    }

    /**
     * Show a success message after refreshing
     */
    showRefreshSuccessMessage() {
        // Get the active tab
        const activeTab = document.querySelector(`#${this.config.appPrefix}-gmailTabContent .tab-pane.active`);
        if (!activeTab) return;

        // Create success message
        const successMessage = document.createElement('div');
        successMessage.className = 'alert alert-success refresh-success-message';
        successMessage.innerHTML = '<i class="bi bi-check-circle me-2"></i> Emails refreshed successfully.';

        // Add the success message to the active tab
        activeTab.prepend(successMessage);

        // Remove the success message after a shorter delay
        setTimeout(() => {
            successMessage.remove();
        }, 1000);
    }

    /**
     * Log a message if debug is enabled
     */
    log(message, data = null) {
        if (this.debug) {
            if (data) {
                console.log(`[GmailIntegration] ${message}`, data);
            } else {
                console.log(`[GmailIntegration] ${message}`);
            }
        }
    }
}

// Export the class for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GmailIntegration;
} else {
    window.GmailIntegration = GmailIntegration;
}

/**
 * Initialize Gmail integration for an application
 * @param {Object} config - Configuration options for the Gmail integration
 */
function initializeGmail(config = {}) {
    console.log('Initializing Gmail integration for', config.appName || 'application');

    try {
        // Create and initialize the Gmail integration
        const gmail = new GmailIntegration(config);
        gmail.init();

        console.log('Gmail integration initialized successfully for', config.appName || 'application');
        return gmail;
    } catch (error) {
        console.error('Error initializing Gmail integration:', error);
        return null;
    }
}

// Make initializeGmail globally available
window.initializeGmail = initializeGmail;
