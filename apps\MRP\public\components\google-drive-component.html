<div class="google-integration-component google-drive-component">
    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
        <h5 class="mb-0"><i class="bi bi-folder"></i> Google Drive</h5>
        <div class="component-actions">
            <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
            <button id="upload-file" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-upload"></i></button>
        </div>
    </div>
    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div class="list-group">
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div style="cursor: pointer;" onclick="viewGoogleItem('drive', 'folder', 'Production_Documentation')">
                    <i class="bi bi-folder-fill text-primary me-2"></i>
                    <span>Production Documentation</span>
                    <span class="badge bg-secondary rounded-pill ms-2">12 files</span>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Production_Documentation')"><i class="bi bi-eye"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Production_Documentation')"><i class="bi bi-download"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Production_Documentation')"><i class="bi bi-share"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Production_Documentation')"><i class="bi bi-trash"></i></button>
                </div>
            </div>
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div style="cursor: pointer;" onclick="viewGoogleItem('drive', 'folder', 'Manufacturing_Plans')">
                    <i class="bi bi-folder-fill text-primary me-2"></i>
                    <span>Manufacturing Plans</span>
                    <span class="badge bg-secondary rounded-pill ms-2">25 files</span>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Manufacturing_Plans')"><i class="bi bi-eye"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Manufacturing_Plans')"><i class="bi bi-download"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Manufacturing_Plans')"><i class="bi bi-share"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Manufacturing_Plans')"><i class="bi bi-trash"></i></button>
                </div>
            </div>
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div style="cursor: pointer;" onclick="viewGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')">
                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                    <span>MRP_System_Manual.pdf</span>
                    <span class="badge bg-primary rounded-pill ms-2">Yesterday</span>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')"><i class="bi bi-eye"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')"><i class="bi bi-download"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')"><i class="bi bi-share"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')"><i class="bi bi-trash"></i></button>
                </div>
            </div>
        </div>
        <div class="d-grid gap-2 mt-3">
            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
                <i class="bi bi-folder me-2"></i>Open Drive
            </button>
        </div>
    </div>
</div>
