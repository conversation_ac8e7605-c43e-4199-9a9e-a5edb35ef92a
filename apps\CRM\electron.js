const { app, BrowserWindow, <PERSON><PERSON>, Tray, ipc<PERSON>ain } = require('electron');
const path = require('path');
const url = require('url');
const isDev = process.env.NODE_ENV === 'development';

// Keep a global reference of the window object
let mainWindow;
let tray = null;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
    },
    icon: path.join(__dirname, 'public/icons/icon-512x512.png'),
  });

  // Load the app
  const startUrl = isDev
    ? 'http://localhost:3003' // Development server URL
    : url.format({
        // Production build
        pathname: path.join(__dirname, './build/index.html'),
        protocol: 'file:',
        slashes: true,
      });

  mainWindow.loadURL(startUrl);

  // Open DevTools in development mode
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // Create system tray
  tray = new Tray(path.join(__dirname, 'public/icons/icon-512x512.png'));
  const contextMenu = Menu.buildFromTemplate([
    { label: 'Show CRM', click: () => mainWindow.show() },
    { label: 'Quit', click: () => app.quit() },
  ]);
  tray.setToolTip('ISA Customer Relationship Management');
  tray.setContextMenu(contextMenu);

  // Handle window close event
  mainWindow.on('close', (event) => {
    if (!app.isQuitting) {
      event.preventDefault();
      mainWindow.hide();
      return false;
    }
    return true;
  });

  // Handle window closed event
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Create window when Electron is ready
app.on('ready', createWindow);

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS applications keep their menu bar active until the user quits
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On macOS re-create a window when dock icon is clicked and no other windows open
  if (mainWindow === null) {
    createWindow();
  }
});

// Handle before-quit event
app.on('before-quit', () => {
  app.isQuitting = true;
});

// Create application menu
const template = [
  {
    label: 'File',
    submenu: [{ role: 'quit' }],
  },
  {
    label: 'Edit',
    submenu: [
      { role: 'undo' },
      { role: 'redo' },
      { type: 'separator' },
      { role: 'cut' },
      { role: 'copy' },
      { role: 'paste' },
    ],
  },
  {
    label: 'View',
    submenu: [
      { role: 'reload' },
      { role: 'toggledevtools' },
      { type: 'separator' },
      { role: 'resetzoom' },
      { role: 'zoomin' },
      { role: 'zoomout' },
      { type: 'separator' },
      { role: 'togglefullscreen' },
    ],
  },
  {
    label: 'Help',
    submenu: [
      {
        label: 'About CRM',
        click: () => {
          // Show about dialog
          const aboutWindow = new BrowserWindow({
            width: 400,
            height: 300,
            resizable: false,
            minimizable: false,
            maximizable: false,
            parent: mainWindow,
            modal: true,
            show: false,
            webPreferences: {
              nodeIntegration: false,
              contextIsolation: true,
            },
          });

          aboutWindow.loadURL(
            url.format({
              pathname: path.join(__dirname, 'public/about.html'),
              protocol: 'file:',
              slashes: true,
            }),
          );

          aboutWindow.once('ready-to-show', () => {
            aboutWindow.show();
          });
        },
      },
    ],
  },
];

const menu = Menu.buildFromTemplate(template);
Menu.setApplicationMenu(menu);

// IPC communication
ipcMain.on('app-message', (event, arg) => {
  console.log('Message from renderer process:', arg);
  event.reply('app-reply', 'Message received by main process');
});
