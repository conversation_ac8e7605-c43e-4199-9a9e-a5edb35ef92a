// Help System Module

// Mock implementation for demonstration purposes
// In a real app, this would use a database or content management system

const logger = require('../logger').createLogger('HelpSystem');

// Mock tutorials data
const tutorials = {
  bms: [
    {
      id: 'bms-intro',
      title: 'Introduction to BMS',
      description: 'Learn the basics of the Business Management System',
      duration: '5 minutes',
      steps: [
        { title: 'Step 1: Login', content: 'Enter your username and password to log in.' },
        { title: 'Step 2: Dashboard', content: 'Navigate the dashboard to see key metrics.' },
        { title: 'Step 3: Navigation', content: 'Use the sidebar to access different modules.' },
      ],
    },
    {
      id: 'bms-financial',
      title: 'Financial Management',
      description: 'Learn how to manage finances in BMS',
      duration: '10 minutes',
      steps: [
        { title: 'Step 1: Chart of Accounts', content: 'View and manage your chart of accounts.' },
        { title: 'Step 2: Invoicing', content: 'Create and manage invoices.' },
        { title: 'Step 3: Reporting', content: 'Generate financial reports.' },
      ],
    },
  ],
  crm: [
    {
      id: 'crm-intro',
      title: 'Introduction to CRM',
      description: 'Learn the basics of the Customer Relationship Management system',
      duration: '5 minutes',
      steps: [
        { title: 'Step 1: Login', content: 'Enter your username and password to log in.' },
        { title: 'Step 2: Dashboard', content: 'Navigate the dashboard to see key metrics.' },
        { title: 'Step 3: Navigation', content: 'Use the sidebar to access different modules.' },
      ],
    },
    {
      id: 'crm-contacts',
      title: 'Managing Contacts',
      description: 'Learn how to manage contacts in CRM',
      duration: '8 minutes',
      steps: [
        { title: 'Step 1: Adding Contacts', content: 'Add new contacts to the system.' },
        { title: 'Step 2: Organizing Contacts', content: 'Organize contacts into groups.' },
        {
          title: 'Step 3: Communication',
          content: 'Communicate with contacts through the system.',
        },
      ],
    },
  ],
  hub: [
    {
      id: 'hub-intro',
      title: 'Introduction to Integration Hub',
      description: 'Learn the basics of the Integration Hub',
      duration: '7 minutes',
      steps: [
        { title: 'Step 1: Login', content: 'Enter your username and password to log in.' },
        { title: 'Step 2: Dashboard', content: 'Navigate the dashboard to see system status.' },
        { title: 'Step 3: Configuration', content: 'Configure integrations between systems.' },
      ],
    },
  ],
};

// Mock user manuals data
const userManuals = {
  bms: {
    title: 'Business Management System User Manual',
    version: '1.0.0',
    sections: [
      {
        title: 'Introduction',
        content:
          'The Business Management System (BMS) is a comprehensive solution for managing your business operations.',
      },
      {
        title: 'Getting Started',
        content:
          'To get started with BMS, log in using your credentials and navigate to the dashboard.',
      },
      {
        title: 'Modules',
        subsections: [
          {
            title: 'Financial Management',
            content:
              'The Financial Management module allows you to manage your finances, including invoicing, expenses, and reporting.',
          },
          {
            title: 'User Management',
            content:
              'The User Management module allows you to manage users, roles, and permissions.',
          },
        ],
      },
    ],
  },
  crm: {
    title: 'Customer Relationship Management User Manual',
    version: '1.0.0',
    sections: [
      {
        title: 'Introduction',
        content:
          'The Customer Relationship Management (CRM) system is a comprehensive solution for managing your customer relationships.',
      },
      {
        title: 'Getting Started',
        content:
          'To get started with CRM, log in using your credentials and navigate to the dashboard.',
      },
      {
        title: 'Modules',
        subsections: [
          {
            title: 'Contact Management',
            content:
              'The Contact Management module allows you to manage your contacts, including customers, leads, and prospects.',
          },
          {
            title: 'Sales Pipeline',
            content:
              'The Sales Pipeline module allows you to track sales opportunities through the sales process.',
          },
        ],
      },
    ],
  },
  hub: {
    title: 'Integration Hub User Manual',
    version: '1.0.0',
    sections: [
      {
        title: 'Introduction',
        content:
          'The Integration Hub is a central system for connecting and integrating all other systems.',
      },
      {
        title: 'Getting Started',
        content:
          'To get started with the Integration Hub, log in using your credentials and navigate to the dashboard.',
      },
      {
        title: 'Modules',
        subsections: [
          {
            title: 'System Status',
            content:
              'The System Status module allows you to monitor the status of all connected systems.',
          },
          {
            title: 'Integration Configuration',
            content:
              'The Integration Configuration module allows you to configure integrations between systems.',
          },
        ],
      },
    ],
  },
};

// Mock diagnosis tools data
const diagnosisTools = {
  bms: [
    {
      id: 'bms-connectivity',
      title: 'Connectivity Diagnosis',
      description: 'Diagnose connectivity issues with the BMS',
      steps: [
        { title: 'Step 1: Check Network', action: 'checkNetwork' },
        { title: 'Step 2: Check Server', action: 'checkServer' },
        { title: 'Step 3: Check Database', action: 'checkDatabase' },
      ],
    },
    {
      id: 'bms-performance',
      title: 'Performance Diagnosis',
      description: 'Diagnose performance issues with the BMS',
      steps: [
        { title: 'Step 1: Check CPU Usage', action: 'checkCPU' },
        { title: 'Step 2: Check Memory Usage', action: 'checkMemory' },
        { title: 'Step 3: Check Database Performance', action: 'checkDatabasePerformance' },
      ],
    },
  ],
  crm: [
    {
      id: 'crm-connectivity',
      title: 'Connectivity Diagnosis',
      description: 'Diagnose connectivity issues with the CRM',
      steps: [
        { title: 'Step 1: Check Network', action: 'checkNetwork' },
        { title: 'Step 2: Check Server', action: 'checkServer' },
        { title: 'Step 3: Check Database', action: 'checkDatabase' },
      ],
    },
  ],
  hub: [
    {
      id: 'hub-connectivity',
      title: 'Connectivity Diagnosis',
      description: 'Diagnose connectivity issues with the Integration Hub',
      steps: [
        { title: 'Step 1: Check Network', action: 'checkNetwork' },
        { title: 'Step 2: Check Server', action: 'checkServer' },
        { title: 'Step 3: Check Connected Systems', action: 'checkConnectedSystems' },
      ],
    },
  ],
};

/**
 * Get tutorials for a specific application
 */
function getTutorials(appCode) {
  logger.info('Getting tutorials', { appCode });
  return tutorials[appCode.toLowerCase()] || [];
}

/**
 * Get tutorial by ID
 */
function getTutorialById(id) {
  logger.info('Getting tutorial by ID', { id });

  // Search for tutorial in all applications
  for (const appTutorials of Object.values(tutorials)) {
    const tutorial = appTutorials.find((t) => t.id === id);
    if (tutorial) {
      return tutorial;
    }
  }

  return null;
}

/**
 * Get user manual for a specific application
 */
function getUserManual(appCode) {
  logger.info('Getting user manual', { appCode });
  return userManuals[appCode.toLowerCase()] || null;
}

/**
 * Get diagnosis tools for a specific application
 */
function getDiagnosisTools(appCode) {
  logger.info('Getting diagnosis tools', { appCode });
  return diagnosisTools[appCode.toLowerCase()] || [];
}

/**
 * Get diagnosis tool by ID
 */
function getDiagnosisToolById(id) {
  logger.info('Getting diagnosis tool by ID', { id });

  // Search for diagnosis tool in all applications
  for (const appTools of Object.values(diagnosisTools)) {
    const tool = appTools.find((t) => t.id === id);
    if (tool) {
      return tool;
    }
  }

  return null;
}

module.exports = {
  getTutorials,
  getTutorialById,
  getUserManual,
  getDiagnosisTools,
  getDiagnosisToolById,
};
