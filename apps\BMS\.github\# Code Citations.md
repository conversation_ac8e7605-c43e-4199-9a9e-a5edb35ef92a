# Code Citations

## License: unknown

https://github.com/barrage-studios/SeasonShift/tree/4f2cd0c5be2d4736e70e192714b06c0dca6fe71c/.github/PULL_REQUEST_TEMPLATE.md

```
# Description
Please include a summary of the change and which issue is fixed. Please also include relevant motivation and context.

Fixes # (issue)

## Type of change
Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue
```

## License: MIT

https://github.com/dhezhen/YKTN/tree/234b415dde2eca18fbf74877de648f81add9b836/assets/TableFilter/PULL_REQUEST_TEMPLATE.md

```
the change and which issue is fixed. Please also include relevant motivation and context.

Fixes # (issue)

## Type of change
Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (
```

## License: MIT

https://github.com/koalyptus/TableFilter/tree/7afe45f1e6d46ef97ea20ac47bc4ee961b5d231f/PULL_REQUEST_TEMPLATE.md

```
. Please also include relevant motivation and context.

Fixes # (issue)

## Type of change
Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality
```

## License: MIT

https://github.com/JacerOmri/curl-axel/tree/8f37c19769e63c3d0dda733ca1537fd73462bc52/PULL_REQUEST_TEMPLATE.md

```
issue)

## Type of change
Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would
```

## License: unknown

https://github.com/dsully/perl-crypt-openssl-pkcs12/tree/ec29c975c388667ce35ba5c27da17e3a65a2352a/.github/PULL_REQUEST_TEMPLATE.md

```
change
Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work
```
