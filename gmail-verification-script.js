// Gmail Modal Fix Verification for BMS
// Copy and paste this into the browser console on the BMS page

function verifyGmailFix() {
    console.clear();
    console.log('🔍 BMS Gmail Modal Fix Verification');
    console.log('=====================================');
    
    let passed = 0;
    let total = 0;
    
    // Test 1: Enhanced Gmail Functions
    total++;
    console.log('\n📋 Test 1: Enhanced Gmail Functions');
    if (typeof createEnhancedGmailModal === 'function' && typeof openEnhancedGmailModal === 'function') {
        console.log('✅ PASS: Enhanced Gmail functions exist');
        passed++;
    } else {
        console.log('❌ FAIL: Enhanced Gmail functions missing');
        console.log('   createEnhancedGmailModal:', typeof createEnhancedGmailModal);
        console.log('   openEnhancedGmailModal:', typeof openEnhancedGmailModal);
    }
    
    // Test 2: Gmail Modal Structure
    total++;
    console.log('\n📋 Test 2: Gmail Modal Structure');
    const gmailModal = document.getElementById('gmailModal');
    if (gmailModal) {
        const hasLabels = !!gmailModal.querySelector('.list-group');
        const hasTabs = !!gmailModal.querySelector('#enhancedGmailTab');
        const hasEnhancedContent = !!gmailModal.querySelector('#enhanced-inbox-content');
        
        if (hasLabels && hasTabs && hasEnhancedContent) {
            console.log('✅ PASS: Enhanced Gmail modal structure detected');
            console.log('   - Labels sidebar: ✓');
            console.log('   - Tab navigation: ✓');
            console.log('   - Enhanced content: ✓');
            passed++;
        } else {
            console.log('❌ FAIL: Gmail modal missing enhanced features');
            console.log('   - Labels sidebar:', hasLabels ? '✓' : '✗');
            console.log('   - Tab navigation:', hasTabs ? '✓' : '✗');
            console.log('   - Enhanced content:', hasEnhancedContent ? '✓' : '✗');
        }
    } else {
        console.log('❌ FAIL: Gmail modal not found');
    }
    
    // Test 3: View All Emails Link
    total++;
    console.log('\n📋 Test 3: View All Emails Link');
    const viewAllLink = document.querySelector('a[href="#"]');
    const allLinks = Array.from(document.querySelectorAll('a')).filter(a => 
        a.textContent.includes('View All Emails') || 
        a.getAttribute('data-bs-target') === '#gmailModal'
    );
    
    if (allLinks.length > 0) {
        console.log('✅ PASS: View All Emails link found');
        allLinks.forEach((link, index) => {
            console.log(`   Link ${index + 1}:`, link.textContent.trim());
            console.log(`   Target:`, link.getAttribute('data-bs-target'));
        });
        passed++;
    } else {
        console.log('❌ FAIL: View All Emails link not found');
    }
    
    // Test 4: No Conflicting Modals
    total++;
    console.log('\n📋 Test 4: No Conflicting Modals');
    const oldModal = document.getElementById('enhancedGmailModal');
    if (!oldModal) {
        console.log('✅ PASS: No conflicting enhanced modal found');
        passed++;
    } else {
        console.log('❌ FAIL: Old enhanced modal still exists (conflict detected)');
    }
    
    // Test 5: Script Loading
    total++;
    console.log('\n📋 Test 5: Script Loading');
    const scripts = Array.from(document.getElementsByTagName('script'));
    const enhancedScript = scripts.find(s => s.src && s.src.includes('enhanced-gmail-fix.js'));
    const simpleScript = scripts.find(s => s.src && s.src.includes('bms-gmail-simple-fix.js'));
    
    if (enhancedScript && !simpleScript) {
        console.log('✅ PASS: Enhanced Gmail script loaded, simple script disabled');
        passed++;
    } else {
        console.log('❌ FAIL: Script loading issue');
        console.log('   Enhanced script:', !!enhancedScript ? '✓' : '✗');
        console.log('   Simple script (should be disabled):', !!simpleScript ? '✗ (CONFLICT)' : '✓');
    }
    
    // Summary
    console.log('\n📊 SUMMARY');
    console.log('===========');
    console.log(`Tests passed: ${passed}/${total}`);
    
    if (passed === total) {
        console.log('🎉 ALL TESTS PASSED! Gmail modal fix is working correctly.');
        console.log('\n🧪 Manual Test: Run testGmailModal() to open the enhanced modal');
    } else {
        console.log('⚠️  Some tests failed. Please check the issues above.');
    }
    
    return { passed, total, success: passed === total };
}

// Helper function to manually test the modal
function testGmailModal() {
    console.log('🧪 Testing Gmail Modal...');
    
    if (typeof openEnhancedGmailModal === 'function') {
        try {
            openEnhancedGmailModal();
            console.log('✅ Enhanced Gmail modal opened successfully');
            
            // Check if modal is actually visible
            setTimeout(() => {
                const modal = document.getElementById('gmailModal');
                const isVisible = modal && (modal.classList.contains('show') || modal.style.display !== 'none');
                
                if (isVisible) {
                    console.log('✅ Modal is visible on screen');
                } else {
                    console.log('⚠️  Modal opened but may not be visible');
                }
            }, 500);
            
        } catch (error) {
            console.log('❌ Error opening modal:', error);
        }
    } else {
        console.log('❌ openEnhancedGmailModal function not available');
    }
}

// Auto-run verification
verifyGmailFix();

console.log('\n🔧 Available Commands:');
console.log('- verifyGmailFix() - Run all tests again');
console.log('- testGmailModal() - Open the enhanced Gmail modal');
