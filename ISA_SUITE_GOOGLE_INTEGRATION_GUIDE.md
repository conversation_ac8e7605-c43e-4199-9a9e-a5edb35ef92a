# ISA Suite Google Integration Implementation Guide

This document provides a comprehensive guide for implementing consistent Google integrations across all ISA Suite applications. The current implementations in APS, TM, SCM, APM, and PMS serve as the reference model for the remaining applications (CRM, BMS, MRP, and WMS).

## Table of Contents

1. [Overview](#overview)
2. [Google Integration Components](#google-integration-components)
3. [Layout Structure](#layout-structure)
4. [File Viewing Functionality](#file-viewing-functionality)
5. [Action Buttons Implementation](#action-buttons-implementation)
6. [Modal Implementation](#modal-implementation)
7. [JavaScript Functions](#javascript-functions)
8. [Implementation Checklist](#implementation-checklist)
9. [Application-Specific Notes](#application-specific-notes)

## Overview

The ISA Suite applications feature integrated Google services (Drive, Docs, Sheets, Calendar, Gmail) with consistent functionality across all applications. Key features include:

- Direct file viewing without opening Google applications
- Consistent action buttons (view, download, share, delete)
- Unified file viewer modal
- Consistent layout and styling
- Cross-application workflow integration

## Google Integration Components

Each application includes the following Google integration components:

1. **Google Drive**: File storage and management
2. **Google Docs**: Document creation and editing
3. **Google Sheets**: Spreadsheet creation and management
4. **Google Calendar**: Event scheduling and management
5. **Google Gmail**: Email communication

Additionally, some applications have specific integrations:
- **Google Maps**: Location services (in CRM sidebar only)
- **Google Translate**: Translation services (in BMS only)

## Layout Structure

### Main Dashboard Layout

The Google integration components are displayed in the main dashboard area with a consistent layout:

```html
<div class="row mb-4">
  <!-- Google Drive Component -->
  <div class="col-md-6 col-lg-4 mb-4">
    <div class="card h-100 border-0 shadow-sm">
      <div class="card-header bg-white border-bottom-0 d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-folder text-primary me-2"></i> Google Drive</h5>
        <button id="refresh-drive" class="btn btn-sm btn-outline-secondary">
          <i class="bi bi-arrow-clockwise"></i>
        </button>
      </div>
      <div class="card-body google-drive-component component-body">
        <!-- Google Drive content -->
      </div>
      <div class="card-footer bg-white border-top-0">
        <div class="d-grid">
          <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
            <i class="bi bi-folder me-2"></i>View All Files
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Other Google components follow the same pattern -->
</div>
```

### Sidebar Layout

The attachments function is included in the sidebar with a consistent layout:

```html
<div class="sidebar-section">
  <h6 class="sidebar-heading d-flex justify-content-between align-items-center">
    <span>Attachments</span>
    <a class="link-secondary" href="#" data-bs-toggle="modal" data-bs-target="#attachmentsModal">
      <i class="bi bi-paperclip"></i>
    </a>
  </h6>
  <div class="sidebar-section-body">
    <!-- Attachments content -->
  </div>
</div>
```

## File Viewing Functionality

All files (Google Drive, Docs, Sheets, and Attachments) implement direct file viewing with the following features:

1. Files open directly in a modal viewer when clicked
2. Action buttons (view, download, share, delete) are available for each file
3. The file viewer modal displays content based on file type

### File Viewer Implementation

The file viewer is implemented using a modal that displays file content based on file type:

```html
<div class="modal fade" id="fileViewerModal" tabindex="-1" aria-labelledby="fileViewerModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
        <h5 class="modal-title" id="fileViewerTitle"></h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="fileViewerContent"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-success" onclick="downloadCurrentFile()"><i class="bi bi-download"></i> Download</button>
        <button type="button" class="btn btn-outline-info" onclick="shareCurrentFile()"><i class="bi bi-share"></i> Share</button>
        <button type="button" class="btn btn-outline-danger" onclick="deleteCurrentFile()"><i class="bi bi-trash"></i> Delete</button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
```

## Action Buttons Implementation

Each file in the Google integration components and attachments section includes a consistent set of action buttons:

```html
<div class="btn-group">
  <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Document_Name.pdf')"><i class="bi bi-eye"></i></button>
  <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Document_Name.pdf')"><i class="bi bi-download"></i></button>
  <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Document_Name.pdf')"><i class="bi bi-share"></i></button>
  <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Document_Name.pdf')"><i class="bi bi-trash"></i></button>
</div>
```

## Modal Implementation

Each Google integration component has a corresponding modal for viewing all items:

1. **Google Drive Modal**: For viewing all files and folders
2. **Google Docs Modal**: For viewing all documents
3. **Google Sheets Modal**: For viewing all spreadsheets
4. **Google Calendar Modal**: For viewing all events
5. **Google Gmail Modal**: For viewing all emails
6. **Attachments Modal**: For viewing all attachments

Each modal follows a consistent structure with tabs for different functions:

```html
<div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
        <h5 class="modal-title" id="docsModalLabel"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <ul class="nav nav-tabs" id="docsTab" role="tablist">
          <!-- Tab navigation items -->
        </ul>
        <div class="tab-content mt-3" id="docsTabContent">
          <!-- Tab content panes -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
```

## JavaScript Functions

The following JavaScript functions are essential for implementing the Google integration functionality:

### 1. File Viewing Functions

```javascript
// Function to view an attachment
function viewAttachment(type, fileName) {
  // Create a file viewer modal if it doesn't exist
  const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

  // Set the file information
  const fileTitle = document.getElementById('fileViewerTitle');
  const fileContent = document.getElementById('fileViewerContent');

  // Set the title based on the file type and name
  fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;

  // Set the content based on the file type
  fileContent.innerHTML = getAttachmentContent(type, fileName);

  // Show the modal
  const modal = new bootstrap.Modal(fileViewerModal);
  modal.show();
}

// Function to create the file viewer modal
function createFileViewerModal() {
  // Create the modal element
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = 'fileViewerModal';
  modal.tabIndex = '-1';
  modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
  modal.setAttribute('aria-hidden', 'true');
  
  // Set the modal content
  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="fileViewerTitle"></h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="fileViewerContent"></div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-success" onclick="downloadCurrentFile()"><i class="bi bi-download"></i> Download</button>
          <button type="button" class="btn btn-outline-info" onclick="shareCurrentFile()"><i class="bi bi-share"></i> Share</button>
          <button type="button" class="btn btn-outline-danger" onclick="deleteCurrentFile()"><i class="bi bi-trash"></i> Delete</button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  `;
  
  // Append the modal to the body
  document.body.appendChild(modal);
  
  return modal;
}
```

### 2. Helper Functions

```javascript
// Helper function to get the appropriate icon for the file type
function getFileTypeIcon(type) {
  if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
  if (type === 'document') return 'bi-file-earmark-text text-primary';
  if (type === 'spreadsheet') return 'bi-file-earmark-spreadsheet text-success';
  if (type === 'image') return 'bi-file-earmark-image text-info';
  if (type === 'presentation') return 'bi-file-earmark-slides text-warning';
  if (type === 'archive') return 'bi-file-earmark-zip text-warning';
  return 'bi-file-earmark text-secondary';
}

// Helper function to generate content for the attachment viewer
function getAttachmentContent(type, fileName) {
  // Generate simulated content based on file type
  // Implementation varies based on file type
}
```

### 3. Action Button Functions

```javascript
// Function to download a file
function downloadFile(fileName) {
  alert(`Downloading ${fileName}...`);
  // In a real application, this would trigger a download
}

// Function to share a file
function shareFile(fileName) {
  const email = prompt(`Enter email address to share ${fileName} with:`);
  if (email) {
    alert(`${fileName} has been shared with ${email}.`);
    // In a real application, this would share the file with the specified email
  }
}

// Function to delete an attachment
function deleteAttachment(fileName) {
  if (confirm(`Are you sure you want to delete ${fileName}?`)) {
    alert(`${fileName} has been deleted.`);
    // In a real application, this would delete the file
  }
}
```

## Implementation Checklist

To implement the Google integrations in the remaining applications (CRM, BMS, MRP, and WMS), follow this checklist:

1. **Dashboard Components**
   - [ ] Add Google Drive component
   - [ ] Add Google Docs component
   - [ ] Add Google Sheets component
   - [ ] Add Google Calendar component
   - [ ] Add Google Gmail component
   - [ ] Add Google Maps (CRM sidebar only)
   - [ ] Add Google Translate (BMS only)

2. **Sidebar Components**
   - [ ] Add Attachments section to sidebar

3. **Modals**
   - [ ] Implement Google Drive modal
   - [ ] Implement Google Docs modal
   - [ ] Implement Google Sheets modal
   - [ ] Implement Google Calendar modal
   - [ ] Implement Google Gmail modal
   - [ ] Implement Attachments modal
   - [ ] Implement File Viewer modal

4. **JavaScript Functions**
   - [ ] Add file viewing functions
   - [ ] Add helper functions
   - [ ] Add action button functions

5. **Styling**
   - [ ] Ensure consistent styling with app color scheme
   - [ ] Verify responsive design

## Application-Specific Notes

### CRM Application
- Include Google Maps in the sidebar only
- Ensure customer data integration with Google services

### BMS Application
- Include Google Translate along with other Google integrations
- Ensure financial data integration with Google services

### MRP Application
- Ensure manufacturing data integration with Google services
- Implement material planning integration with Google Sheets

### WMS Application
- Ensure inventory data integration with Google services
- Implement warehouse layout integration with Google Drive

## Color Schemes

Each application has its own color scheme that should be applied to the Google integration components:

- **PMS**: Pink color scheme (not too bright, not too soft)
- **APM**: Blue color scheme
- **TM**: Green color scheme
- **SCM**: Orange color scheme
- **APS**: Purple color scheme
- **CRM**: Red color scheme
- **BMS**: Yellow color scheme (to be updated)
- **MRP**: Teal color scheme
- **WMS**: Brown color scheme

The color scheme is applied using CSS variables:

```css
:root {
  --app-primary-color: #your-color-here;
  --app-secondary-color: #your-secondary-color-here;
}
```

## Conclusion

By following this guide, you can implement consistent Google integrations across all ISA Suite applications. The key is to maintain consistency in layout, functionality, and user experience while adapting to each application's specific needs and color scheme.
