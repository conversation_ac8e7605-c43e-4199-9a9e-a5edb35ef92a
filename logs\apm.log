
> apm@1.0.0 start C:\ISASUITE\apps\APM
> node index.js production

APM running at http://localhost:3006
Connected to IntegrationHub
[WARN] [GoogleIntegration] Google API token has expired, attempting to refresh {}
Notifications database initialized
[ERROR] [GoogleIntegration] Google API authorization failed {
  error: 'Failed to parse or use token: Failed to refresh token: invalid_grant. Please run the authentication flow again.'
}
[ERROR] [GoogleIntegration] Failed to initialize Google API {
  error: 'Failed to parse or use token: Failed to refresh token: invalid_grant. Please run the authentication flow again.'
}
[WARN] [GoogleIntegration] Token issue detected. You may need to run the authentication flow again. {}
[WARN] [GoogleIntegration] Run: node SharedFeatures/integrations/google.js {}
[ERROR] [APM] Failed to initialize Google API {
  error: 'Failed to parse or use token: Failed to refresh token: invalid_grant. Please run the authentication flow again.'
}
