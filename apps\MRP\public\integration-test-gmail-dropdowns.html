<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Dropdown Integration Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-results {
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Gmail Dropdown Integration Test</h1>
        <p class="text-muted">This test validates the Gmail dropdown functionality in the MRP application.</p>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Controls</h3>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="runFullTest()">Run Full Integration Test</button>
                    <button class="btn btn-secondary" onclick="testDropdownInitialization()">Test Dropdown Initialization</button>
                    <button class="btn btn-secondary" onclick="testBootstrapDropdowns()">Test Bootstrap Dropdowns</button>
                    <button class="btn btn-secondary" onclick="testManualDropdowns()">Test Manual Dropdowns</button>
                    <button class="btn btn-secondary" onclick="testEventListeners()">Test Event Listeners</button>
                    <button class="btn btn-info" onclick="openMRPApplication()">Open MRP Application</button>
                    <button class="btn btn-warning" onclick="clearResults()">Clear Results</button>
                </div>
                
                <h4 class="mt-4">Quick Test Dropdowns</h4>
                <div class="d-flex gap-2 mb-3">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="test-sort-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-sort-down"></i> Sort
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="test-sort-dropdown">
                            <li><a class="dropdown-item sort-option" href="#" data-sort="date-desc">Newest first</a></li>
                            <li><a class="dropdown-item sort-option" href="#" data-sort="date-asc">Oldest first</a></li>
                            <li><a class="dropdown-item sort-option" href="#" data-sort="sender-asc">Sender A-Z</a></li>
                        </ul>
                    </div>
                    
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="test-filter-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-funnel"></i> Filter
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="test-filter-dropdown">
                            <li><a class="dropdown-item filter-option" href="#" data-filter="all">All emails</a></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="unread">Unread</a></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="read">Read</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Test Results</h3>
                <div id="test-results" class="border p-3 test-results bg-light"></div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Instructions</h3>
            <ol>
                <li>Click "Run Full Integration Test" to validate all functionality</li>
                <li>Test the quick dropdowns above to verify Bootstrap is working</li>
                <li>Click "Open MRP Application" to test in the actual application</li>
                <li>In the MRP app, open the Gmail modal and test the dropdowns</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/mrp-gmail-utils.js"></script>
    <script src="js/mrp-gmail-dropdowns.js"></script>
    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push({
                timestamp,
                message,
                type
            });
            updateResults();
        }
        
        function updateResults() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="${result.type}"><strong>[${result.timestamp}]</strong> ${result.message}</div>`
            ).join('');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            testResults = [];
            updateResults();
        }
        
        function runFullTest() {
            clearResults();
            log('Starting full integration test...', 'info');
            
            // Test 1: Check script loading
            log('Test 1: Checking script loading...', 'info');
            if (typeof window.MRPGmailDropdowns !== 'undefined') {
                log('✓ MRP Gmail Dropdowns script loaded', 'success');
            } else {
                log('✗ MRP Gmail Dropdowns script NOT loaded', 'error');
            }
            
            if (typeof window.MRPGmailUtils !== 'undefined') {
                log('✓ MRP Gmail Utils script loaded', 'success');
            } else {
                log('✗ MRP Gmail Utils script NOT loaded', 'error');
            }
            
            // Test 2: Bootstrap availability
            log('Test 2: Checking Bootstrap availability...', 'info');
            if (typeof bootstrap !== 'undefined') {
                log(`✓ Bootstrap ${bootstrap.Tooltip?.VERSION || 'unknown version'} loaded`, 'success');
            } else {
                log('✗ Bootstrap NOT available', 'error');
            }
            
            // Test 3: Test dropdown elements
            setTimeout(() => testDropdownInitialization(), 100);
            setTimeout(() => testBootstrapDropdowns(), 200);
            setTimeout(() => testManualDropdowns(), 300);
            setTimeout(() => testEventListeners(), 400);
            setTimeout(() => {
                log('Full integration test completed!', 'success');
            }, 500);
        }
        
        function testDropdownInitialization() {
            log('Test 3: Testing dropdown initialization...', 'info');
            
            const sortDropdown = document.getElementById('test-sort-dropdown');
            const filterDropdown = document.getElementById('test-filter-dropdown');
            
            if (sortDropdown) {
                log('✓ Sort dropdown element found', 'success');
                const hasBootstrapAttrs = sortDropdown.hasAttribute('data-bs-toggle');
                log(hasBootstrapAttrs ? '✓ Sort dropdown has Bootstrap attributes' : '✗ Sort dropdown missing Bootstrap attributes', hasBootstrapAttrs ? 'success' : 'warning');
            } else {
                log('✗ Sort dropdown element NOT found', 'error');
            }
            
            if (filterDropdown) {
                log('✓ Filter dropdown element found', 'success');
                const hasBootstrapAttrs = filterDropdown.hasAttribute('data-bs-toggle');
                log(hasBootstrapAttrs ? '✓ Filter dropdown has Bootstrap attributes' : '✗ Filter dropdown missing Bootstrap attributes', hasBootstrapAttrs ? 'success' : 'warning');
            } else {
                log('✗ Filter dropdown element NOT found', 'error');
            }
        }
        
        function testBootstrapDropdowns() {
            log('Test 4: Testing Bootstrap dropdown functionality...', 'info');
            
            if (typeof bootstrap === 'undefined') {
                log('⚠ Skipping Bootstrap test - Bootstrap not available', 'warning');
                return;
            }
            
            const sortDropdown = document.getElementById('test-sort-dropdown');
            if (sortDropdown) {
                try {
                    const dropdownInstance = new bootstrap.Dropdown(sortDropdown);
                    log('✓ Bootstrap dropdown instance created for sort dropdown', 'success');
                    
                    // Test toggle
                    dropdownInstance.toggle();
                    setTimeout(() => {
                        const menu = sortDropdown.nextElementSibling;
                        const isOpen = menu && menu.classList.contains('show');
                        log(isOpen ? '✓ Bootstrap sort dropdown opens correctly' : '✗ Bootstrap sort dropdown failed to open', isOpen ? 'success' : 'error');
                        
                        if (isOpen) {
                            dropdownInstance.hide();
                            setTimeout(() => {
                                const isClosed = !menu.classList.contains('show');
                                log(isClosed ? '✓ Bootstrap sort dropdown closes correctly' : '✗ Bootstrap sort dropdown failed to close', isClosed ? 'success' : 'error');
                            }, 100);
                        }
                    }, 100);
                } catch (error) {
                    log(`✗ Bootstrap dropdown creation failed: ${error.message}`, 'error');
                }
            }
        }
        
        function testManualDropdowns() {
            log('Test 5: Testing manual dropdown functionality...', 'info');
            
            const sortDropdown = document.getElementById('test-sort-dropdown');
            if (sortDropdown) {
                const menu = sortDropdown.nextElementSibling;
                if (menu) {
                    // Test manual toggle
                    menu.classList.add('show');
                    setTimeout(() => {
                        const isOpen = menu.classList.contains('show');
                        log(isOpen ? '✓ Manual dropdown opens correctly' : '✗ Manual dropdown failed to open', isOpen ? 'success' : 'error');
                        
                        menu.classList.remove('show');
                        setTimeout(() => {
                            const isClosed = !menu.classList.contains('show');
                            log(isClosed ? '✓ Manual dropdown closes correctly' : '✗ Manual dropdown failed to close', isClosed ? 'success' : 'error');
                        }, 100);
                    }, 100);
                } else {
                    log('✗ Dropdown menu element not found', 'error');
                }
            }
        }
        
        function testEventListeners() {
            log('Test 6: Testing event listeners...', 'info');
            
            // Test click events on dropdown options
            const sortOptions = document.querySelectorAll('#test-sort-dropdown + .dropdown-menu .sort-option');
            const filterOptions = document.querySelectorAll('#test-filter-dropdown + .dropdown-menu .filter-option');
            
            log(`Found ${sortOptions.length} sort options`, 'info');
            log(`Found ${filterOptions.length} filter options`, 'info');
            
            if (sortOptions.length > 0) {
                log('✓ Sort options found and ready for event binding', 'success');
            } else {
                log('✗ No sort options found', 'error');
            }
            
            if (filterOptions.length > 0) {
                log('✓ Filter options found and ready for event binding', 'success');
            } else {
                log('✗ No filter options found', 'error');
            }
        }
        
        function openMRPApplication() {
            log('Opening MRP application in new tab...', 'info');
            window.open('http://localhost:3002', '_blank');
        }
        
        // Initialize quick test dropdowns
        document.addEventListener('DOMContentLoaded', function() {
            log('Integration test page loaded', 'success');
            log('Ready to run tests!', 'info');
            
            // Test if our custom dropdowns work on this page
            const testSortDropdown = document.getElementById('test-sort-dropdown');
            const testFilterDropdown = document.getElementById('test-filter-dropdown');
            
            if (testSortDropdown && testFilterDropdown) {
                log('Quick test dropdowns available for manual testing', 'info');
            }
        });
        
        // Add click handlers for dropdown options to test event handling
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('sort-option')) {
                e.preventDefault();
                const sortType = e.target.getAttribute('data-sort');
                log(`Sort option clicked: ${sortType}`, 'success');
            }
            
            if (e.target.classList.contains('filter-option')) {
                e.preventDefault();
                const filterType = e.target.getAttribute('data-filter');
                log(`Filter option clicked: ${filterType}`, 'success');
            }
        });
    </script>
</body>
</html>
