# Use Node.js v22.15.0
FROM node:22.15.0-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install pnpm
RUN npm install -g pnpm@8.15.4

# Ensure production environment for pnpm install and build
ENV NODE_ENV=production

# Install dependencies
RUN pnpm install --frozen-lockfile

# Set correct permissions for pnpm store (for non-root containers, optional)
RUN pnpm store path

# Copy source code
COPY --chown=node:node . .

# Build the application
RUN pnpm build

# Switch to node user for runtime (after build)
USER node

# Expose port
EXPOSE 3000

# Start the application
CMD ["pnpm", "start"]