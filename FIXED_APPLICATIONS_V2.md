# ISA Suite - Fixed Applications V2

This document explains the latest fixes that have been applied to the ISA Suite applications.

## Fixed Issues

The following issues have been fixed:

1. **Port Configuration Fixes**:
   - **Project Management System (PMS)**: Fixed port configuration from 3008 to 3007
   - **Warehouse Management System (WMS)**: Fixed port configuration from 3001 to 3004

2. **Application Startup Improvements**:
   - Modified the single-window-launcher.bat script to directly execute server.js files for WMS, PMS, TM, and SCM applications
   - Added automatic installation of node-fetch for SCM and TM applications
   - Created logs directory if it doesn't exist
   - All application output is logged to individual log files in the logs directory

3. **UI Enhancements**:
   - Enhanced WMS and PMS with dashboards, sidebars, top banners, and colors
   - Added interactive elements to match other applications
   - Ensured consistent styling across all applications

## How to Use

### Starting Applications
1. Run `single-window-launcher.bat`
2. Select the desired mode (Production, Sandbox/Training, or Demo)
3. All applications will start in the background without opening individual terminal windows
4. The Integration Hub will open in your browser automatically

### Checking Application Status
1. Run `check-status.bat`
2. The script will display the status of all applications (online or offline)

### Opening Applications in Browser
1. Run `open-apps.bat`
2. All applications will open in your default browser

### Stopping Applications
1. Press any key in the launcher window to stop all applications
   OR
2. Run `stop-apps.bat` to stop all applications

## Application URLs

Once the applications are running, you can access them through the Integration Hub or directly using the following URLs:

- Integration Hub: http://localhost:8000
- Business Management System: http://localhost:3001
- Materials Requirements Planning: http://localhost:3002
- Customer Relationship Management: http://localhost:3003
- Warehouse Management System: http://localhost:3004
- Advanced Planning and Scheduling: http://localhost:3005
- Asset Performance Management: http://localhost:3006
- Project Management System: http://localhost:3007
- Supply Chain Management: http://localhost:3008
- Task Management System: http://localhost:3009

## Troubleshooting

If you encounter issues:

1. Check the log files in the `logs` directory
2. Run `check-status.bat` to see which applications are running
3. Make sure no other applications are using the same ports (8000, 3001-3009)
4. Try stopping all applications using `stop-apps.bat` and then start them again

### Common Issues and Solutions

#### SCM or TM Not Running
- Check if node-fetch is installed correctly
- Look at the log files in the logs directory for specific error messages
- Try running the applications manually:
  ```
  cd C:\ISASUITE\apps\SCM
  node server.js
  ```

#### WMS or PMS UI Issues
- Clear your browser cache
- Try accessing the application in a different browser
- Check the browser console for any JavaScript errors

## Notes

- The Integration Hub links will open applications in separate browser tabs/windows
- All application output is logged to individual log files in the logs directory
- Running multiple launchers simultaneously may cause port conflicts
