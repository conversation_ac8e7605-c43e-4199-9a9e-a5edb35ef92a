@echo off
echo Creating desktop shortcuts for ISA Suite...

REM Set installation directory
set "INSTALL_DIR=C:\ISASUITE"
set "DESKTOP_DIR=%USERPROFILE%\Desktop"

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click on this file and select "Run as administrator".
    pause
    exit /b 1
)

REM Check if installation directory exists
if not exist "%INSTALL_DIR%" (
    echo Error: ISA Suite installation directory not found at %INSTALL_DIR%
    echo Please make sure ISA Suite is installed correctly.
    pause
    exit /b 1
)

REM Create shortcut for Service Manager
echo Creating shortcut for ISA Suite Service Manager...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_DIR%\ISA Suite Service Manager.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\service-manager.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'ISA Suite Service Manager'; $Shortcut.Save()"

REM Create shortcut for Single Window Launcher
echo Creating shortcut for ISA Suite Single Window Launcher...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_DIR%\ISA Suite Single Window.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\single-window-launcher.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'ISA Suite Single Window Launcher'; $Shortcut.Save()"

REM Create shortcut for Integration Hub
echo Creating shortcut for ISA Suite Integration Hub...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_DIR%\ISA Suite Integration Hub.lnk'); $Shortcut.TargetPath = 'http://localhost:8000'; $Shortcut.Description = 'ISA Suite Integration Hub'; $Shortcut.Save()"

echo.
echo Desktop shortcuts created successfully:
echo - ISA Suite Service Manager
echo - ISA Suite Single Window Launcher
echo - ISA Suite Integration Hub
echo.
echo You can now start ISA Suite by double-clicking on any of these shortcuts.
echo.
pause
