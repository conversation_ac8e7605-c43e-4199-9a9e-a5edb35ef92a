/**
 * CRM Data Functions
 * 
 * This file contains functions specific to the CRM application for handling data,
 * including search, sort, filter, and refresh functionality.
 */

/**
 * CRMDataFunctions - Namespace for CRM-specific data functions
 */
const CRMDataFunctions = {
    /**
     * Initialize all data functionality for the CRM application
     */
    init: function() {
        // Initialize customers table data
        this.initCustomersTable();
        
        // Initialize deals table data
        this.initDealsTable();
        
        // Initialize search, sort, and filter for customers table
        this.initCustomersTableControls();
        
        // Initialize search, sort, and filter for deals table
        this.initDealsTableControls();
        
        // Initialize refresh functionality
        this.initRefreshControls();
        
        // Fix navigation links
        this.fixNavigationLinks();
        
        console.log('CRM Data Functions initialized');
    },
    
    /**
     * Initialize the customers table with data
     */
    initCustomersTable: function() {
        const customersData = [
            { id: 'C-001', name: 'Acme Corporation', contact: '<PERSON>', email: '<EMAIL>', phone: '(*************', status: 'Active', lastContact: '2023-10-15' },
            { id: 'C-002', name: 'TechNova Inc.', contact: '<PERSON>', email: '<EMAIL>', phone: '(*************', status: 'Active', lastContact: '2023-10-12' },
            { id: 'C-003', name: 'Global Solutions', contact: 'Michael Brown', email: '<EMAIL>', phone: '(*************', status: 'Inactive', lastContact: '2023-09-28' },
            { id: 'C-004', name: 'Innovative Systems', contact: 'Emily Davis', email: '<EMAIL>', phone: '(*************', status: 'Active', lastContact: '2023-10-10' },
            { id: 'C-005', name: 'Summit Enterprises', contact: 'David Wilson', email: '<EMAIL>', phone: '(*************', status: 'Lead', lastContact: '2023-10-05' },
            { id: 'C-006', name: 'Pinnacle Group', contact: 'Jennifer Lee', email: '<EMAIL>', phone: '(*************', status: 'Active', lastContact: '2023-10-14' },
            { id: 'C-007', name: 'Horizon Industries', contact: 'Robert Taylor', email: '<EMAIL>', phone: '(*************', status: 'Lead', lastContact: '2023-10-08' },
            { id: 'C-008', name: 'Elite Services', contact: 'Lisa Anderson', email: '<EMAIL>', phone: '(*************', status: 'Inactive', lastContact: '2023-09-20' },
            { id: 'C-009', name: 'Prime Solutions', contact: 'James Martin', email: '<EMAIL>', phone: '(*************', status: 'Active', lastContact: '2023-10-11' },
            { id: 'C-010', name: 'Vertex Technologies', contact: 'Michelle Garcia', email: '<EMAIL>', phone: '(*************', status: 'Lead', lastContact: '2023-10-07' }
        ];
        
        const tableBody = document.getElementById('customersTableBody');
        if (!tableBody) {
            console.log('Creating customers table');
            this.createCustomersTable(customersData);
            return;
        }
        
        // Clear existing rows
        tableBody.innerHTML = '';
        
        // Add data rows
        customersData.forEach(customer => {
            const row = document.createElement('tr');
            row.setAttribute('data-id', customer.id);
            
            // Determine status class
            let statusClass = '';
            switch (customer.status) {
                case 'Active':
                    statusClass = 'bg-success';
                    break;
                case 'Inactive':
                    statusClass = 'bg-danger';
                    break;
                case 'Lead':
                    statusClass = 'bg-warning';
                    break;
            }
            
            // Format date
            const lastContactDate = new Date(customer.lastContact);
            const formattedDate = lastContactDate.toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric' 
            });
            
            row.innerHTML = `
                <td>${customer.id}</td>
                <td>${customer.name}</td>
                <td>${customer.contact}</td>
                <td><a href="mailto:${customer.email}">${customer.email}</a></td>
                <td><a href="tel:${customer.phone}">${customer.phone}</a></td>
                <td><span class="badge ${statusClass}">${customer.status}</span></td>
                <td>${formattedDate}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary view-customer" data-id="${customer.id}">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary edit-customer" data-id="${customer.id}">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger delete-customer" data-id="${customer.id}">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // Add event listeners for action buttons
        this.initCustomersTableActions();
        
        console.log('Customers table initialized with data');
    },
    
    /**
     * Create customers table if it doesn't exist
     */
    createCustomersTable: function(customersData) {
        // Find the main content area
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) return;
        
        // Create customers section
        const customersSection = document.createElement('div');
        customersSection.id = 'customers-section';
        customersSection.className = 'mt-4';
        customersSection.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Customers</h2>
                <button class="btn btn-primary" id="addCustomerBtn">
                    <i class="bi bi-plus-circle"></i> Add Customer
                </button>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="customersSearchInput" placeholder="Search customers...">
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="d-flex justify-content-end">
                        <div class="me-2">
                            <select class="form-select" id="customerStatusFilter">
                                <option value="all">All Statuses</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="lead">Lead</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" id="refreshCustomersBtn">
                            <i class="bi bi-arrow-repeat"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive table-container">
                <table class="table table-striped table-hover" id="customersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Company</th>
                            <th>Contact</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Status</th>
                            <th>Last Contact</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="customersTableBody">
                        <!-- Table rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        `;
        
        // Add to main content
        mainContent.appendChild(customersSection);
        
        // Now initialize the table with data
        this.initCustomersTable();
    },
    
    /**
     * Initialize action buttons in the customers table
     */
    initCustomersTableActions: function() {
        // View customer buttons
        document.querySelectorAll('.view-customer').forEach(button => {
            button.addEventListener('click', function() {
                const customerId = this.getAttribute('data-id');
                alert(`Viewing customer ${customerId}`);
                // In a real application, this would open a modal or navigate to a detail page
            });
        });
        
        // Edit customer buttons
        document.querySelectorAll('.edit-customer').forEach(button => {
            button.addEventListener('click', function() {
                const customerId = this.getAttribute('data-id');
                alert(`Editing customer ${customerId}`);
                // In a real application, this would open an edit form
            });
        });
        
        // Delete customer buttons
        document.querySelectorAll('.delete-customer').forEach(button => {
            button.addEventListener('click', function() {
                const customerId = this.getAttribute('data-id');
                if (confirm(`Are you sure you want to delete customer ${customerId}?`)) {
                    alert(`Customer ${customerId} deleted`);
                    // In a real application, this would send a delete request to the server
                    // and remove the row from the table
                }
            });
        });
        
        // Add customer button
        const addCustomerBtn = document.getElementById('addCustomerBtn');
        if (addCustomerBtn) {
            addCustomerBtn.addEventListener('click', function() {
                alert('Add new customer');
                // In a real application, this would open a form to add a new customer
            });
        }
    },
    
    /**
     * Initialize the deals table with data
     */
    initDealsTable: function() {
        const dealsData = [
            { id: 'D-001', name: 'Enterprise Software Package', customer: 'Acme Corporation', value: 75000, stage: 'Proposal', probability: 60, closingDate: '2023-11-15' },
            { id: 'D-002', name: 'Cloud Migration Services', customer: 'TechNova Inc.', value: 120000, stage: 'Negotiation', probability: 80, closingDate: '2023-11-30' },
            { id: 'D-003', name: 'IT Infrastructure Upgrade', customer: 'Global Solutions', value: 95000, stage: 'Closed Won', probability: 100, closingDate: '2023-10-20' },
            { id: 'D-004', name: 'Custom Software Development', customer: 'Innovative Systems', value: 150000, stage: 'Discovery', probability: 40, closingDate: '2023-12-15' },
            { id: 'D-005', name: 'Annual Maintenance Contract', customer: 'Summit Enterprises', value: 45000, stage: 'Proposal', probability: 70, closingDate: '2023-11-10' },
            { id: 'D-006', name: 'Security Audit Services', customer: 'Pinnacle Group', value: 35000, stage: 'Closed Lost', probability: 0, closingDate: '2023-10-05' },
            { id: 'D-007', name: 'Data Analytics Platform', customer: 'Horizon Industries', value: 85000, stage: 'Negotiation', probability: 75, closingDate: '2023-12-05' },
            { id: 'D-008', name: 'Mobile App Development', customer: 'Elite Services', value: 65000, stage: 'Discovery', probability: 30, closingDate: '2024-01-15' }
        ];
        
        const tableBody = document.getElementById('dealsTableBody');
        if (!tableBody) {
            console.log('Creating deals table');
            this.createDealsTable(dealsData);
            return;
        }
        
        // Clear existing rows
        tableBody.innerHTML = '';
        
        // Add data rows
        dealsData.forEach(deal => {
            const row = document.createElement('tr');
            row.setAttribute('data-id', deal.id);
            
            // Determine stage class
            let stageClass = '';
            switch (deal.stage) {
                case 'Closed Won':
                    stageClass = 'bg-success';
                    break;
                case 'Closed Lost':
                    stageClass = 'bg-danger';
                    break;
                case 'Negotiation':
                    stageClass = 'bg-warning';
                    break;
                case 'Proposal':
                    stageClass = 'bg-info';
                    break;
                case 'Discovery':
                    stageClass = 'bg-secondary';
                    break;
            }
            
            // Format date
            const closingDate = new Date(deal.closingDate);
            const formattedDate = closingDate.toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric' 
            });
            
            // Format value
            const formattedValue = new Intl.NumberFormat('en-US', { 
                style: 'currency', 
                currency: 'USD',
                maximumFractionDigits: 0
            }).format(deal.value);
            
            row.innerHTML = `
                <td>${deal.id}</td>
                <td>${deal.name}</td>
                <td>${deal.customer}</td>
                <td>${formattedValue}</td>
                <td><span class="badge ${stageClass}">${deal.stage}</span></td>
                <td>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: ${deal.probability}%;" 
                            aria-valuenow="${deal.probability}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <small>${deal.probability}%</small>
                </td>
                <td>${formattedDate}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary view-deal" data-id="${deal.id}">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary edit-deal" data-id="${deal.id}">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger delete-deal" data-id="${deal.id}">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // Add event listeners for action buttons
        this.initDealsTableActions();
        
        console.log('Deals table initialized with data');
    },
    
    /**
     * Create deals table if it doesn't exist
     */
    createDealsTable: function(dealsData) {
        // Find the main content area
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) return;
        
        // Create deals section
        const dealsSection = document.createElement('div');
        dealsSection.id = 'deals-section';
        dealsSection.className = 'mt-5';
        dealsSection.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Deals</h2>
                <button class="btn btn-primary" id="addDealBtn">
                    <i class="bi bi-plus-circle"></i> Add Deal
                </button>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="dealsSearchInput" placeholder="Search deals...">
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="d-flex justify-content-end">
                        <div class="me-2">
                            <select class="form-select" id="dealStageFilter">
                                <option value="all">All Stages</option>
                                <option value="discovery">Discovery</option>
                                <option value="proposal">Proposal</option>
                                <option value="negotiation">Negotiation</option>
                                <option value="closed won">Closed Won</option>
                                <option value="closed lost">Closed Lost</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" id="refreshDealsBtn">
                            <i class="bi bi-arrow-repeat"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive table-container">
                <table class="table table-striped table-hover" id="dealsTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Deal Name</th>
                            <th>Customer</th>
                            <th>Value</th>
                            <th>Stage</th>
                            <th>Probability</th>
                            <th>Closing Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="dealsTableBody">
                        <!-- Table rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        `;
        
        // Add to main content
        mainContent.appendChild(dealsSection);
        
        // Now initialize the table with data
        this.initDealsTable();
    },
    
    /**
     * Initialize action buttons in the deals table
     */
    initDealsTableActions: function() {
        // View deal buttons
        document.querySelectorAll('.view-deal').forEach(button => {
            button.addEventListener('click', function() {
                const dealId = this.getAttribute('data-id');
                alert(`Viewing deal ${dealId}`);
                // In a real application, this would open a modal or navigate to a detail page
            });
        });
        
        // Edit deal buttons
        document.querySelectorAll('.edit-deal').forEach(button => {
            button.addEventListener('click', function() {
                const dealId = this.getAttribute('data-id');
                alert(`Editing deal ${dealId}`);
                // In a real application, this would open an edit form
            });
        });
        
        // Delete deal buttons
        document.querySelectorAll('.delete-deal').forEach(button => {
            button.addEventListener('click', function() {
                const dealId = this.getAttribute('data-id');
                if (confirm(`Are you sure you want to delete deal ${dealId}?`)) {
                    alert(`Deal ${dealId} deleted`);
                    // In a real application, this would send a delete request to the server
                    // and remove the row from the table
                }
            });
        });
        
        // Add deal button
        const addDealBtn = document.getElementById('addDealBtn');
        if (addDealBtn) {
            addDealBtn.addEventListener('click', function() {
                alert('Add new deal');
                // In a real application, this would open a form to add a new deal
            });
        }
    },
    
    /**
     * Initialize search, sort, and filter controls for the customers table
     */
    initCustomersTableControls: function() {
        // Initialize search
        if (document.getElementById('customersSearchInput') && document.getElementById('customersTable')) {
            ISADataUtils.initTableSearch('customersSearchInput', 'customersTable', [0, 1, 2, 3, 4]); // Search ID, Company, Contact, Email, Phone columns
        }
        
        // Initialize sorting
        if (document.getElementById('customersTable')) {
            ISADataUtils.initTableSort('customersTable', {
                excludeColumns: [7], // Exclude Actions column
                dateColumns: [6] // Last Contact is a date
            });
        }
        
        // Initialize filtering
        if (document.getElementById('customerStatusFilter') && document.getElementById('customersTable')) {
            ISADataUtils.initTableFilter('customersTable', [
                {
                    filterId: 'customerStatusFilter',
                    columnIndex: 5, // Status column
                    filterFn: (cell, filterValue, row) => {
                        if (filterValue === 'all') return true;
                        const cellText = cell.textContent.toLowerCase();
                        return cellText.includes(filterValue);
                    }
                }
            ]);
        }
    },
    
    /**
     * Initialize search, sort, and filter controls for the deals table
     */
    initDealsTableControls: function() {
        // Initialize search
        if (document.getElementById('dealsSearchInput') && document.getElementById('dealsTable')) {
            ISADataUtils.initTableSearch('dealsSearchInput', 'dealsTable', [0, 1, 2]); // Search ID, Deal Name, Customer columns
        }
        
        // Initialize sorting
        if (document.getElementById('dealsTable')) {
            ISADataUtils.initTableSort('dealsTable', {
                excludeColumns: [7], // Exclude Actions column
                numericColumns: [3, 5], // Value and Probability are numeric
                dateColumns: [6] // Closing Date is a date
            });
        }
        
        // Initialize filtering
        if (document.getElementById('dealStageFilter') && document.getElementById('dealsTable')) {
            ISADataUtils.initTableFilter('dealsTable', [
                {
                    filterId: 'dealStageFilter',
                    columnIndex: 4, // Stage column
                    filterFn: (cell, filterValue, row) => {
                        if (filterValue === 'all') return true;
                        const cellText = cell.textContent.toLowerCase();
                        return cellText.includes(filterValue);
                    }
                }
            ]);
        }
    },
    
    /**
     * Initialize refresh controls
     */
    initRefreshControls: function() {
        // Refresh customers table
        if (document.getElementById('refreshCustomersBtn') && document.getElementById('customersTableBody')) {
            ISADataUtils.initRefresh('refreshCustomersBtn', 'customersTableBody', () => {
                return new Promise((resolve, reject) => {
                    // Simulate network delay
                    setTimeout(() => {
                        try {
                            this.initCustomersTable();
                            resolve();
                        } catch (error) {
                            reject(error);
                        }
                    }, 1000);
                });
            });
        }
        
        // Refresh deals table
        if (document.getElementById('refreshDealsBtn') && document.getElementById('dealsTableBody')) {
            ISADataUtils.initRefresh('refreshDealsBtn', 'dealsTableBody', () => {
                return new Promise((resolve, reject) => {
                    // Simulate network delay
                    setTimeout(() => {
                        try {
                            this.initDealsTable();
                            resolve();
                        } catch (error) {
                            reject(error);
                        }
                    }, 1000);
                });
            });
        }
    },
    
    /**
     * Fix navigation links to ensure they work properly
     */
    fixNavigationLinks: function() {
        // Fix sidebar navigation links
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            const href = link.getAttribute('href');
            
            // Skip links that are already properly configured
            if (href && href.startsWith('/') && !href.includes('#') && !link.hasAttribute('data-bs-toggle')) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all links
                    document.querySelectorAll('.sidebar .nav-link').forEach(l => {
                        l.classList.remove('active');
                    });
                    
                    // Add active class to clicked link
                    this.classList.add('active');
                    
                    // Update page title
                    const linkText = this.textContent.trim();
                    document.querySelector('.main-content h1').textContent = linkText + ' Dashboard';
                    
                    // In a real application, this would load the appropriate content
                    if (linkText === 'Customers') {
                        // Show customers section
                        const customersSection = document.getElementById('customers-section');
                        if (!customersSection) {
                            CRMDataFunctions.createCustomersTable([]);
                        }
                    } else if (linkText === 'Deals') {
                        // Show deals section
                        const dealsSection = document.getElementById('deals-section');
                        if (!dealsSection) {
                            CRMDataFunctions.createDealsTable([]);
                        }
                    } else {
                        alert(`Navigating to ${linkText}`);
                    }
                });
            }
        });
    }
};

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    CRMDataFunctions.init();
});
