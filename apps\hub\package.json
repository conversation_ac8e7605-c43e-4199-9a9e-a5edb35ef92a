{"name": "@isasuite/hub", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next", "test": "jest", "test:watch": "jest --watch", "monitor": "ts-node scripts/monitor.ts", "start:all": "concurrently \"pnpm dev\" \"pnpm monitor\""}, "dependencies": {"next": "14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "http-proxy": "^1.18.1", "body-parser": "^1.20.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "mongoose": "^8.1.0", "redis": "^4.6.13", "winston": "^3.11.0", "dotenv": "^16.4.1", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "14.1.0", "jest": "^29.7.0", "@testing-library/react": "^14.2.1", "@types/jest": "^29.5.12", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/compression": "^1.7.5", "ts-jest": "^29.1.2", "jest-environment-jsdom": "^29.7.0", "concurrently": "^8.2.2", "ts-node": "^10.9.2"}, "engines": {"node": ">=18.0.0"}}