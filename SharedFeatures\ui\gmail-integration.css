/**
 * Shared Gmail Integration Styles for ISA Suite
 * 
 * This stylesheet provides consistent styling for Gmail functionality
 * across all applications in the ISA Suite.
 */

/* Gmail Modal */
.modal-header {
    padding: 0.75rem 1rem;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 500;
}

/* Email List */
.list-group-item.unread h6,
.list-group-item.unread p {
    font-weight: bold !important;
}

.list-group-item {
    transition: background-color 0.2s ease;
}

.list-group-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Color dots for labels */
.color-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

/* Email content */
.email-content {
    line-height: 1.6;
}

.email-content p {
    margin-bottom: 1rem;
}

.email-content ul,
.email-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

/* Compose form */
.compose-form label {
    font-weight: 500;
}

/* Attachments */
.attachment-item {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
}

/* Loading indicators */
.loading-indicator,
.sending-indicator,
.saving-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Success messages */
.compose-success-message,
.sent-success-message,
.draft-success-message,
.refresh-success-message {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: none;
    }
    
    .col-md-3.border-end {
        border-right: none !important;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
    }
    
    .list-group {
        max-height: none !important;
    }
}

/* Custom scrollbar for email lists */
.list-group::-webkit-scrollbar {
    width: 6px;
}

.list-group::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.list-group::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.list-group::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Tab content scrollbar */
.tab-content::-webkit-scrollbar {
    width: 6px;
}

.tab-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.tab-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Email action buttons */
.email-actions {
    display: flex;
    gap: 0.5rem;
}

.email-actions .btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Attachment button in compose */
#attachments-container {
    max-height: 200px;
    overflow-y: auto;
}

/* No results message */
.no-results-message {
    text-align: center;
    padding: 2rem;
    margin: 1rem 0;
}

/* Email sender avatar */
.sender-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}
