@echo off
setlocal enabledelayedexpansion

REM Get the current directory (USB drive)
set "USB_DIR=%~dp0"
set "USB_DIR=%USB_DIR:~0,-1%"

REM Set path to local Node.js installation
set "NODE_PATH=%USB_DIR%\Node\node-v22.15.0-win-x64"
set "PATH=%NODE_PATH%;%PATH%"

:menu
cls
echo ===================================================
echo        ICE SYSTEMS AUSTRALASIA APPLICATIONS
echo ===================================================
echo.
echo Running from: %USB_DIR%
echo.
echo Web Applications:
echo  1. Business Management System (BMS)
echo  2. Materials Requirements Planning (MRP)
echo  3. Customer Relationship Management (CRM)
echo  4. Warehouse Management System (WMS)
echo  5. Advanced Planning and Scheduling (APS)
echo  6. Asset Performance Management (APM)
echo  7. Integration Hub
echo.
echo Desktop Applications:
echo  8. BMS Desktop
echo  9. MRP Desktop
echo 10. CRM Desktop
echo 11. WMS Desktop
echo 12. APS Desktop
echo 13. APM Desktop
echo.
echo Standalone Applications (without Integration Hub):
echo 21. BMS Web (Standalone)
echo 22. MRP Web (Standalone)
echo 23. CRM Web (Standalone)
echo 24. WMS Web (Standalone)
echo 25. APS Web (Standalone)
echo 26. APM Web (Standalone)
echo.
echo Utilities:
echo 14. Mobile Access Portal
echo 15. Create desktop shortcuts
echo 16. Create standalone shortcuts
echo.
echo  0. Exit
echo.
set /p choice=Enter your choice (0-26):

if "%choice%"=="0" goto end

REM Check if Integration Hub is running for web applications
if "%choice%"=="1" goto check_hub
if "%choice%"=="2" goto check_hub
if "%choice%"=="3" goto check_hub
if "%choice%"=="4" goto check_hub
if "%choice%"=="5" goto check_hub
if "%choice%"=="6" goto check_hub

REM Launch Integration Hub directly
if "%choice%"=="7" (
    echo Starting Integration Hub...
    start cmd /k "cd /d "%USB_DIR%\Apps\hub" && "%NODE_PATH%\npm.cmd" start production"
    goto menu
)

REM Launch desktop applications
if "%choice%"=="8" (
    echo Starting BMS Desktop...
    start cmd /k "cd /d "%USB_DIR%\Apps\BMS" && "%NODE_PATH%\npm.cmd" run electron-dev"
    goto menu
)
if "%choice%"=="9" (
    echo Starting MRP Desktop...
    start cmd /k "cd /d "%USB_DIR%\Apps\MRP" && "%NODE_PATH%\npm.cmd" run electron-dev"
    goto menu
)
if "%choice%"=="10" (
    echo Starting CRM Desktop...
    start cmd /k "cd /d "%USB_DIR%\Apps\CRM" && "%NODE_PATH%\npm.cmd" run electron-dev"
    goto menu
)
if "%choice%"=="11" (
    echo Starting WMS Desktop...
    start cmd /k "cd /d "%USB_DIR%\Apps\WMS" && "%NODE_PATH%\npm.cmd" run electron-dev"
    goto menu
)
if "%choice%"=="12" (
    echo Starting APS Desktop...
    start cmd /k "cd /d "%USB_DIR%\Apps\APS" && "%NODE_PATH%\npm.cmd" run electron-dev"
    goto menu
)
if "%choice%"=="13" (
    echo Starting APM Desktop...
    start cmd /k "cd /d "%USB_DIR%\Apps\APM" && "%NODE_PATH%\npm.cmd" run electron-dev"
    goto menu
)

REM Launch mobile access portal
if "%choice%"=="14" (
    echo Starting Mobile Access Portal...

    REM Check if portal-server.js exists
    if exist "%USB_DIR%\portal-server.js" (
        REM Check if QRCode module is installed
        if not exist "%USB_DIR%\node_modules\qrcode" (
            echo Installing required dependencies...
            cd /d "%USB_DIR%"
            "%NODE_PATH%\npm.cmd" install express cors qrcode
            echo.
        )

        start cmd /k "cd /d "%USB_DIR%" && "%NODE_PATH%\node.exe" portal-server.js"
        start "" http://localhost:8080
    ) else (
        echo Portal server not found. Starting Integration Hub instead...
        start cmd /k "cd /d "%USB_DIR%\Apps\hub" && "%NODE_PATH%\npm.cmd" start production"
        start "" http://localhost:8000
    )

    goto menu
)

REM Create desktop shortcuts
if "%choice%"=="15" (
    echo Creating desktop shortcuts...

    REM Create shortcuts directory if it doesn't exist
    if not exist "%USB_DIR%\shortcuts" mkdir "%USB_DIR%\shortcuts"

    REM Create launcher shortcut
    echo Set oWS = WScript.CreateObject("WScript.Shell") > "%USB_DIR%\shortcuts\create_launcher_shortcut.vbs"
    echo sLinkFile = "%USERPROFILE%\Desktop\ISA Applications (USB).lnk" >> "%USB_DIR%\shortcuts\create_launcher_shortcut.vbs"
    echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%USB_DIR%\shortcuts\create_launcher_shortcut.vbs"
    echo oLink.TargetPath = "%USB_DIR%\run-from-usb.bat" >> "%USB_DIR%\shortcuts\create_launcher_shortcut.vbs"
    echo oLink.WorkingDirectory = "%USB_DIR%" >> "%USB_DIR%\shortcuts\create_launcher_shortcut.vbs"
    echo oLink.IconLocation = "%USB_DIR%\Apps\hub\public\icons\icon-512x512.ico, 0" >> "%USB_DIR%\shortcuts\create_launcher_shortcut.vbs"
    echo oLink.Description = "ISA Applications Launcher (USB)" >> "%USB_DIR%\shortcuts\create_launcher_shortcut.vbs"
    echo oLink.Save >> "%USB_DIR%\shortcuts\create_launcher_shortcut.vbs"
    cscript //nologo "%USB_DIR%\shortcuts\create_launcher_shortcut.vbs"

    echo Shortcuts created on your desktop!
    pause
    goto menu
)

REM Create standalone shortcuts
if "%choice%"=="16" (
    call "%USB_DIR%\create-standalone-shortcuts.bat"
    goto menu
)

REM Launch standalone applications
if "%choice%"=="21" (
    echo Starting BMS in standalone mode...
    start cmd /k "cd /d "%USB_DIR%\Apps\BMS" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3001
    goto menu
)
if "%choice%"=="22" (
    echo Starting MRP in standalone mode...
    start cmd /k "cd /d "%USB_DIR%\Apps\MRP" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3002
    goto menu
)
if "%choice%"=="23" (
    echo Starting CRM in standalone mode...
    start cmd /k "cd /d "%USB_DIR%\Apps\CRM" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3003
    goto menu
)
if "%choice%"=="24" (
    echo Starting WMS in standalone mode...
    start cmd /k "cd /d "%USB_DIR%\Apps\WMS" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3004
    goto menu
)
if "%choice%"=="25" (
    echo Starting APS in standalone mode...
    start cmd /k "cd /d "%USB_DIR%\Apps\APS" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3005
    goto menu
)
if "%choice%"=="26" (
    echo Starting APM in standalone mode...
    start cmd /k "cd /d "%USB_DIR%\Apps\APM" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3006
    goto menu
)

goto menu

:check_hub
REM Check if Integration Hub is running
set HUB_RUNNING=0
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8000"') do set HUB_RUNNING=1

if %HUB_RUNNING%==0 (
    echo Integration Hub is not running. Starting it now...
    start cmd /k "cd /d "%USB_DIR%\Apps\hub" && "%NODE_PATH%\npm.cmd" start production"
    timeout /t 10 /nobreak > nul
)

REM Launch the selected web application
if "%choice%"=="1" (
    echo Starting Business Management System...
    start cmd /k "cd /d "%USB_DIR%\Apps\BMS" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3001
)
if "%choice%"=="2" (
    echo Starting Materials Requirements Planning...
    start cmd /k "cd /d "%USB_DIR%\Apps\MRP" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3002
)
if "%choice%"=="3" (
    echo Starting Customer Relationship Management...
    start cmd /k "cd /d "%USB_DIR%\Apps\CRM" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3003
)
if "%choice%"=="4" (
    echo Starting Warehouse Management System...
    start cmd /k "cd /d "%USB_DIR%\Apps\WMS" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3004
)
if "%choice%"=="5" (
    echo Starting Advanced Planning and Scheduling...
    start cmd /k "cd /d "%USB_DIR%\Apps\APS" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3005
)
if "%choice%"=="6" (
    echo Starting Asset Performance Management...
    start cmd /k "cd /d "%USB_DIR%\Apps\APM" && "%NODE_PATH%\npm.cmd" start production"
    start "" http://localhost:3006
)

goto menu

:end
echo Thank you for using ISA Applications.
exit /b 0
