<div class="modal fade isa-modal" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mapsModalLabel"><i class="bi bi-geo-alt"></i> Google Maps</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="mapsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations-content" type="button" role="tab" aria-controls="locations-content" aria-selected="true">Locations</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="directions-tab" data-bs-toggle="tab" data-bs-target="#directions-content" type="button" role="tab" aria-controls="directions-content" aria-selected="false">Directions</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="customer-locations-tab" data-bs-toggle="tab" data-bs-target="#customer-locations-content" type="button" role="tab" aria-controls="customer-locations-content" aria-selected="false">Customer Locations</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="saved-tab" data-bs-toggle="tab" data-bs-target="#saved-content" type="button" role="tab" aria-controls="saved-content" aria-selected="false">Saved Places</button>
                    </li>
                </ul>
                <div class="tab-content" id="mapsTabContent">
                    <!-- Tab content will be similar to existing Maps modal -->
                    <!-- ...existing tab content... -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="https://maps.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Google Maps</a>
            </div>
        </div>
    </div>
</div>
