/**
 * Test Gmail Modal Dropdowns
 * This script tests the Gmail dropdown functionality in the browser console
 */

// Test script to be run in browser console
const testCode = `
// Test Gmail Modal Opening and Dropdown Functionality
(function() {
    console.log('🧪 Testing Gmail Modal and Dropdowns...');
    
    // Step 1: Find and click Gmail button
    const gmailBtn = document.getElementById('mrp-open-gmail-btn') || 
                    document.querySelector('[data-bs-target="#mrp-gmailModal"]') ||
                    document.querySelector('a[href*="gmail"]');
    
    if (!gmailBtn) {
        console.log('❌ Gmail button not found');
        return;
    }
    
    console.log('✅ Found Gmail button:', gmailBtn);
    
    // Step 2: Click the Gmail button to open modal
    gmailBtn.click();
    
    // Step 3: Wait a moment for modal to open, then test dropdowns
    setTimeout(() => {
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal || !modal.classList.contains('show')) {
            console.log('❌ Gmail modal not open');
            return;
        }
        
        console.log('✅ Gmail modal is open');
        
        // Step 4: Test sort dropdown
        const sortBtn = document.getElementById('sort-dropdown');
        const filterBtn = document.getElementById('filter-dropdown');
        
        if (!sortBtn || !filterBtn) {
            console.log('❌ Dropdown buttons not found');
            return;
        }
        
        console.log('✅ Found dropdown buttons');
        
        // Step 5: Test Bootstrap dropdown instances
        const sortInstance = bootstrap.Dropdown.getInstance(sortBtn);
        const filterInstance = bootstrap.Dropdown.getInstance(filterBtn);
        
        console.log('Sort dropdown instance:', !!sortInstance);
        console.log('Filter dropdown instance:', !!filterInstance);
        
        // Step 6: Test clicking the dropdowns
        console.log('🧪 Testing sort dropdown click...');
        sortBtn.click();
        
        setTimeout(() => {
            console.log('🧪 Testing filter dropdown click...');
            filterBtn.click();
            
            // Step 7: Report results
            setTimeout(() => {
                const sortMenu = sortBtn.nextElementSibling;
                const filterMenu = filterBtn.nextElementSibling;
                
                const sortVisible = sortMenu && sortMenu.classList.contains('show');
                const filterVisible = filterMenu && filterMenu.classList.contains('show');
                
                console.log('📊 RESULTS:');
                console.log('Sort dropdown working:', sortVisible);
                console.log('Filter dropdown working:', filterVisible);
                
                if (sortVisible && filterVisible) {
                    console.log('🎉 SUCCESS: Both dropdowns are working!');
                } else {
                    console.log('⚠️ Some dropdowns may not be working properly');
                }
            }, 500);
        }, 500);
        
    }, 1000);
})();
`;

console.log('Copy this code and run it in the browser console to test Gmail dropdowns:');
console.log(testCode);
