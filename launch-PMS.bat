@echo off
echo Starting Project Management System (PMS)...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Change to the PMS directory
cd /d C:\ISASUITE\apps\PMS\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call C:\ISASUITE\PortableNodeJS\pnpm install
)

REM Close any potentially running instance on the same port
taskkill /F /FI "WINDOWTITLE eq Project Management System*" /T > nul 2>&1
taskkill /F /FI "WINDOWTITLE eq http://localhost:3007*" /T > nul 2>&1

REM Start the application server
echo Starting PMS server...
start "Project Management System" cmd /c "C:\ISASUITE\PortableNodeJS\node.exe server.js"

REM Wait for the server to initialize
timeout /t 2 > nul

REM Open the browser
echo PMS is now running
echo To access PMS, go to: http://localhost:3007
start "" http://localhost:3007

echo.
echo PMS application launched. Close this window to stop the application.