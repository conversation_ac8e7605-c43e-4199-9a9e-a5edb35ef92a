<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Task Management System</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <style>
    :root {
      --primary-color: #6c5ce7;
      --secondary-color: #00b894;
      --warning-color: #fdcb6e;
      --danger-color: #e17055;
      --light-bg: #f8f9fa;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f7fa;
    }

    .sidebar {
      background-color: #6c5ce7;
      color: white;
      height: 100vh;
      position: fixed;
      padding-top: 20px;
      overflow-y: auto; /* Add scrollbar when content overflows */
    }

    /* Custom scrollbar for sidebar */
    .sidebar::-webkit-scrollbar {
      width: 6px;
    }

    .sidebar::-webkit-scrollbar-track {
      background: #5d4ecc; /* Darker shade of sidebar color */
    }

    .sidebar::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }

    .sidebar::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.5);
    }

    .sidebar .nav-link {
      color: #f8f9fa;
      padding: 0.75rem 1rem;
      font-weight: 500;
    }

    .sidebar .nav-link:hover {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar .nav-link.active {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.2);
    }

    .sidebar .nav-link i {
      margin-right: 10px;
    }

    @media (max-width: 767.98px) {
      .sidebar {
        position: fixed;
        top: 56px;
        left: -220px;
        height: calc(100vh - 56px);
        width: 220px;
        transition: left 0.3s;
        z-index: 1030;
      }

      .sidebar.show {
        left: 0;
      }

      .main-content {
        margin-left: 0;
        padding: 10px;
        padding-top: 70px;
      }

      .metric-card {
        margin-bottom: 15px;
      }

      .table-responsive {
        overflow-x: auto;
      }

      .d-flex.justify-content-between {
        flex-direction: column;
      }

      .d-flex.justify-content-between > div {
        margin-top: 10px;
      }

      .task-meta {
        flex-direction: column;
      }

      .task-meta > span {
        margin-bottom: 5px;
      }
    }

    @media (min-width: 768px) {
      .main-content {
        margin-left: 220px;
        padding: 20px;
        padding-top: 70px;
      }

      .sidebar {
        width: 220px;
      }
    }

    .card {
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border: none;
    }

    .card-header {
      background-color: white;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      font-weight: 600;
    }

    .metric-card {
      text-align: center;
      padding: 15px;
    }

    .metric-value {
      font-size: 28px;
      font-weight: 700;
      margin: 10px 0;
    }

    .metric-label {
      font-size: 14px;
      color: #6c757d;
    }

    .status-badge {
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
    }

    .status-planned {
      background-color: rgba(108, 92, 231, 0.2);
      color: #6c5ce7;
    }

    .status-in-progress {
      background-color: rgba(253, 203, 110, 0.2);
      color: #fdcb6e;
    }

    .status-completed {
      background-color: rgba(0, 184, 148, 0.2);
      color: #00b894;
    }

    .priority-badge {
      padding: 3px 8px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
    }

    .priority-high {
      background-color: rgba(225, 112, 85, 0.2);
      color: #e17055;
    }

    .priority-medium {
      background-color: rgba(253, 203, 110, 0.2);
      color: #fdcb6e;
    }

    .priority-low {
      background-color: rgba(0, 184, 148, 0.2);
      color: #00b894;
    }

    .progress-container {
      width: 100%;
      background-color: #f0f0f0;
      border-radius: 10px;
      height: 8px;
      margin-top: 5px;
    }

    .progress-bar {
      height: 100%;
      border-radius: 10px;
      background-color: var(--primary-color);
    }

    .table th {
      font-weight: 600;
      color: #495057;
    }

    .header-action-button {
      float: right;
      font-size: 14px;
      padding: 5px 10px;
    }

    .task-card {
      border-left: 4px solid #6c5ce7;
      margin-bottom: 10px;
      transition: transform 0.2s;
    }

    .task-card:hover {
      transform: translateY(-2px);
    }

    .task-card.high {
      border-left-color: #e17055;
    }

    .task-card.medium {
      border-left-color: #fdcb6e;
    }

    .task-card.low {
      border-left-color: #00b894;
    }

    .task-title {
      font-weight: 600;
      margin-bottom: 5px;
    }

    .task-meta {
      font-size: 12px;
      color: #6c757d;
      display: flex;
      justify-content: space-between;
    }

    .task-progress {
      margin-top: 10px;
    }

    .dashboard-card {
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .card-icon {
      font-size: 2rem;
      margin-bottom: 10px;
    }
  </style>
</head>
<body>
  <!-- Top Navbar -->
  <nav class="navbar navbar-dark fixed-top" style="background-color: #6c5ce7;">
    <div class="container-fluid">
      <div class="d-flex align-items-center">
        <button id="sidebarToggle" class="d-md-none me-2" style="background: transparent; border: none; color: white;">
          <i class="bi bi-list fs-4"></i>
        </button>
        <a class="navbar-brand" href="/">Task Manager</a>
      </div>
      <div class="d-flex">
        <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3" style="background: transparent; border: 1px solid white; color: white; padding: 5px 10px;">
          <i class="bi bi-box-arrow-left me-1"></i> Back to Hub
        </a>
        <button class="btn position-relative me-2" style="background: transparent; border: none; color: white;">
          <i class="bi bi-bell fs-5"></i>
          <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
            6
          </span>
        </button>
        <button class="btn" style="background: transparent; border: none; color: white;">
          <i class="bi bi-person-circle fs-5"></i>
        </button>
      </div>
    </div>
  </nav>

  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div class="col-md-2 sidebar" id="sidebar">
        <h4 class="text-center mb-4">Task Manager</h4>
        <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link active" href="#"><i class="bi bi-speedometer2"></i> Dashboard</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-list-task"></i> Tasks</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-people"></i> Team</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-calendar-event"></i> Calendar</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-graph-up"></i> Reports</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-gear"></i> Settings</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#attachments" data-bs-toggle="modal" data-bs-target="#attachmentsModal">
              <i class="bi bi-paperclip"></i> Attachments
            </a>
          </li>

          <li class="nav-item">
            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
              <span>Google Integrations</span>
            </h6>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal">
              <i class="bi bi-calendar3"></i> Google Calendar
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal">
              <i class="bi bi-folder"></i> Google Drive
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal">
              <i class="bi bi-file-earmark-text"></i> Google Docs
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#sheetsModal">
              <i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-gmail" data-bs-toggle="modal" data-bs-target="#gmailModal">
              <i class="bi bi-envelope"></i> Gmail
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#google-maps" data-bs-toggle="modal" data-bs-target="#mapsModal">
              <i class="bi bi-geo-alt"></i> Google Maps
            </a>
          </li>

          <li class="nav-item mt-4">
            <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();"><i class="bi bi-box-arrow-left"></i> Back to Hub</a>
          </li>
        </ul>
      </div>

      <!-- Main Content -->
      <div class="col-md-10 main-content">
        <div class="container-fluid px-4">
          <div class="d-flex justify-content-between align-items-center mb-4 mt-2">
            <h2>Task Management Dashboard</h2>
            <div>
              <button class="btn btn-outline-secondary me-2"><i class="bi bi-filter"></i> Filter</button>
              <button class="btn btn-primary"><i class="bi bi-plus"></i> New Task</button>
            </div>
          </div>

        <!-- Metrics Row -->
        <div class="row mb-4">
          <div class="col-md-4">
            <div class="card dashboard-card bg-primary text-white">
              <div class="card-body text-center">
                <i class="bi bi-list-task card-icon"></i>
                <h5 class="card-title">Active Tasks</h5>
                <h2 class="card-text" id="active-tasks">3</h2>
                <p class="card-text">Requiring attention</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card dashboard-card bg-success text-white">
              <div class="card-body text-center">
                <i class="bi bi-graph-up card-icon"></i>
                <h5 class="card-title">Overall Progress</h5>
                <h2 class="card-text" id="overall-progress">53%</h2>
                <div class="progress mt-2" style="height: 10px;">
                  <div class="progress-bar bg-light" id="progress-bar" role="progressbar" style="width: 53%" aria-valuenow="53" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card dashboard-card bg-info text-white">
              <div class="card-body text-center">
                <i class="bi bi-people card-icon"></i>
                <h5 class="card-title">Team Members</h5>
                <h2 class="card-text" id="team-members">3</h2>
                <p class="card-text">Active contributors</p>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Insights Card -->
        <div class="card mb-4" style="border-left: 4px solid #6c5ce7;">
          <div class="card-header d-flex justify-content-between align-items-center">
            <div>
              <i class="bi bi-robot"></i> AI-Powered Task Optimization
              <span class="badge bg-primary ms-2">AI Insights</span>
            </div>
            <div>
              <button class="btn btn-sm btn-outline-primary" id="refresh-insights-btn">
                <i class="bi bi-arrow-clockwise"></i> Refresh
              </button>
              <div class="dropdown d-inline-block ms-2">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="insightsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="bi bi-gear"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="insightsDropdown">
                  <li><a class="dropdown-item" href="#" id="expand-all-insights">Expand All</a></li>
                  <li><a class="dropdown-item" href="#" id="collapse-all-insights">Collapse All</a></li>
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="#" id="export-insights">Export Insights</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div id="ai-insights-container">
              <div class="d-flex justify-content-center">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                Tasks by Status
                <button class="btn btn-sm btn-outline-secondary header-action-button">
                  <i class="bi bi-three-dots"></i>
                </button>
              </div>
              <div class="card-body">
                <canvas id="tasksByStatusChart" height="250"></canvas>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                Tasks by Priority
                <button class="btn btn-sm btn-outline-secondary header-action-button">
                  <i class="bi bi-three-dots"></i>
                </button>
              </div>
              <div class="card-body">
                <canvas id="tasksByPriorityChart" height="250"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Google Integrations -->
        <div class="row mt-4">
          <div class="col-12">
            <h4 class="mb-3">Google Integrations</h4>
          </div>

          <!-- Google Gmail -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-gmail-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: rgba(108, 92, 231, 0.1); border-radius: 10px 10px 0 0;">
                <h5 class="mb-0" style="color: #6c5ce7;"><i class="bi bi-envelope"></i> Gmail</h5>
                <div class="component-actions">
                  <button id="refresh-gmail" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="compose-email" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#gmailModal"><i class="bi bi-pencil"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">JD</div>
                        <div>
                          <div class="fw-bold">John Doe</div>
                          <div class="small text-truncate" style="max-width: 200px;">Task update: Website redesign completed</div>
                        </div>
                      </div>
                    </div>
                    <span class="badge bg-primary rounded-pill">10m</span>
                  </a>
                  <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">SM</div>
                        <div>
                          <div class="fw-bold">Sarah Miller</div>
                          <div class="small text-truncate" style="max-width: 200px;">Project timeline discussion</div>
                        </div>
                      </div>
                    </div>
                    <span class="badge bg-primary rounded-pill">1h</span>
                  </a>
                  <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">TJ</div>
                        <div>
                          <div class="fw-bold">Team Lead</div>
                          <div class="small text-truncate" style="max-width: 200px;">Weekly status meeting reminder</div>
                        </div>
                      </div>
                    </div>
                    <span class="badge bg-primary rounded-pill">3h</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#gmailModal">
                    <i class="bi bi-envelope me-2"></i>Open Gmail
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Google Drive -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-drive-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: rgba(108, 92, 231, 0.1); border-radius: 10px 10px 0 0;">
                <h5 class="mb-0" style="color: #6c5ce7;"><i class="bi bi-folder"></i> Google Drive</h5>
                <div class="component-actions">
                  <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="upload-file" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-upload"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" onclick="openGoogleItem('drive', 'folder', 'project-documentation')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-folder-fill text-primary me-2"></i>
                      <span>Project Documentation</span>
                    </div>
                    <span class="badge bg-secondary rounded-pill">15 files</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('drive', 'folder', 'task-templates')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-folder-fill text-primary me-2"></i>
                      <span>Task Templates</span>
                    </div>
                    <span class="badge bg-secondary rounded-pill">8 files</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('drive', 'pdf', 'project-plan-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                      <span>Project_Plan_Q2_2025.pdf</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">Yesterday</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
                    <i class="bi bi-folder me-2"></i>Open Drive
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Google Docs -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-docs-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: rgba(108, 92, 231, 0.1); border-radius: 10px 10px 0 0;">
                <h5 class="mb-0" style="color: #6c5ce7;"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                <div class="component-actions">
                  <button id="refresh-docs" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="create-new-doc" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#docsModal"><i class="bi bi-plus"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" onclick="openGoogleItem('docs', 'document', 'task-management-procedures')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-text text-primary me-2"></i>
                      <span>Task Management Procedures</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">Today</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('docs', 'document', 'project-requirements-document')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-text text-primary me-2"></i>
                      <span>Project Requirements Document</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">Yesterday</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('docs', 'document', 'meeting-minutes-template')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-text text-primary me-2"></i>
                      <span>Meeting Minutes Template</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">3 days ago</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#docsModal">
                    <i class="bi bi-file-earmark-text me-2"></i>View All Documents
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Google Sheets -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-sheets-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: rgba(108, 92, 231, 0.1); border-radius: 10px 10px 0 0;">
                <h5 class="mb-0" style="color: #6c5ce7;"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                <div class="component-actions">
                  <button id="refresh-sheets" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="create-new-sheet" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#sheetsModal"><i class="bi bi-plus"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'task-tracking-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                      <span>Task_Tracking_2025.xlsx</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">2 days ago</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'resource-allocation-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                      <span>Resource_Allocation_Q2_2025.xlsx</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">1 week ago</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'project-timeline-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                      <span>Project_Timeline_2025.xlsx</span>
                    </div>
                    <span class="badge bg-primary rounded-pill">2 weeks ago</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                    <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Google Calendar -->
          <div class="col-md-6 mb-4">
            <div class="google-integration-component google-calendar-component">
              <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: rgba(108, 92, 231, 0.1); border-radius: 10px 10px 0 0;">
                <h5 class="mb-0" style="color: #6c5ce7;"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                <div class="component-actions">
                  <button id="refresh-calendar" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                  <button id="create-new-event" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#calendarModal"><i class="bi bi-plus"></i></button>
                </div>
              </div>
              <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div class="list-group">
                  <a href="#" onclick="openGoogleItem('calendar', 'event', 'team-status-meeting')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <div class="fw-bold">Team Status Meeting</div>
                      <div class="small text-muted">
                        <i class="bi bi-clock me-1"></i>Today, 10:00 AM - 11:00 AM
                      </div>
                    </div>
                    <span class="badge bg-warning rounded-pill">Today</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('calendar', 'event', 'project-planning-session')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <div class="fw-bold">Project Planning Session</div>
                      <div class="small text-muted">
                        <i class="bi bi-clock me-1"></i>Tomorrow, 2:00 PM - 3:30 PM
                      </div>
                    </div>
                    <span class="badge bg-info rounded-pill">Tomorrow</span>
                  </a>
                  <a href="#" onclick="openGoogleItem('calendar', 'event', 'sprint-review')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                      <div class="fw-bold">Sprint Review</div>
                      <div class="small text-muted">
                        <i class="bi bi-clock me-1"></i>May 15, 9:00 AM - 10:30 AM
                      </div>
                    </div>
                    <span class="badge bg-secondary rounded-pill">Next Week</span>
                  </a>
                </div>
                <div class="d-grid gap-2 mt-3">
                  <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#calendarModal">
                    <i class="bi bi-calendar3 me-2"></i>View Full Calendar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tasks and Team Row -->
        <div class="row">
          <div class="col-md-8">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                Recent Tasks
                <button class="btn btn-sm btn-outline-primary header-action-button">
                  View All
                </button>
              </div>
              <div class="card-body">
                <div id="tasks-container">
                  <!-- Will be populated by JavaScript -->
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                Team Members
                <button class="btn btn-sm btn-outline-primary header-action-button">
                  View All
                </button>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-hover" id="team-table">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Tasks</th>
                        <th>Completed</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- Will be populated by JavaScript -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <div class="card mt-4">
              <div class="card-header">
                Progress Trend
              </div>
              <div class="card-body">
                <canvas id="progressTrendChart" height="200"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Integration Modals -->
  <!-- Gmail Modal -->
  <div class="modal fade" id="gmailModal" tabindex="-1" aria-labelledby="gmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="gmailModalLabel"><i class="bi bi-envelope"></i> Gmail</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="gmailTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox-content" type="button" role="tab" aria-controls="inbox-content" aria-selected="true">Inbox</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose-content" type="button" role="tab" aria-controls="compose-content" aria-selected="false">Compose</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent-content" type="button" role="tab" aria-controls="sent-content" aria-selected="false">Sent</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="read-email-tab" data-bs-toggle="tab" data-bs-target="#read-email-content" type="button" role="tab" aria-controls="read-email-content" aria-selected="false" style="display: none;">Read Email</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="reply-email-tab" data-bs-toggle="tab" data-bs-target="#reply-email-content" type="button" role="tab" aria-controls="reply-email-content" aria-selected="false" style="display: none;">Reply</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="gmailTabContent">
            <!-- Inbox Tab -->
            <div class="tab-pane fade show active" id="inbox-content" role="tabpanel" aria-labelledby="inbox-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="email-search" class="form-control" placeholder="Search emails...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button class="btn btn-primary" id="compose-new-email" onclick="document.getElementById('compose-tab').click();">
                    <i class="bi bi-pencil-square me-2"></i>Compose
                  </button>
                  <button class="btn btn-outline-secondary ms-2" id="refresh-gmail-modal">
                    <i class="bi bi-arrow-clockwise"></i>
                  </button>
                </div>
              </div>
              <div class="list-group">
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('john-doe-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1 fw-bold">John Doe</h6>
                    <small class="text-muted">10 minutes ago</small>
                  </div>
                  <p class="mb-1 fw-bold">Task update: Website redesign completed</p>
                  <small class="text-muted">I've completed the website redesign task. Please review and provide feedback...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sarah-miller-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Sarah Miller</h6>
                    <small class="text-muted">1 hour ago</small>
                  </div>
                  <p class="mb-1">Project timeline discussion</p>
                  <small class="text-muted">We need to discuss the project timeline for the upcoming release...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('team-lead-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Team Lead</h6>
                    <small class="text-muted">3 hours ago</small>
                  </div>
                  <p class="mb-1">Weekly status meeting reminder</p>
                  <small class="text-muted">This is a reminder about our weekly status meeting scheduled for tomorrow...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('project-manager-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Project Manager</h6>
                    <small class="text-muted">Yesterday</small>
                  </div>
                  <p class="mb-1">Project status report</p>
                  <small class="text-muted">Please find attached the latest project status report for review...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('hr-department-email')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">HR Department</h6>
                    <small class="text-muted">2 days ago</small>
                  </div>
                  <p class="mb-1">Employee training schedule</p>
                  <small class="text-muted">The updated employee training schedule for next month is now available...</small>
                </a>
              </div>
            </div>

            <!-- Compose Tab -->
            <div class="tab-pane fade" id="compose-content" role="tabpanel" aria-labelledby="compose-tab">
              <form>
                <div class="mb-3">
                  <label for="email-to" class="form-label">To</label>
                  <input type="email" class="form-control" id="email-to" placeholder="Enter recipient email">
                </div>
                <div class="mb-3">
                  <label for="email-cc" class="form-label">Cc</label>
                  <input type="email" class="form-control" id="email-cc" placeholder="Enter cc email addresses">
                </div>
                <div class="mb-3">
                  <label for="email-subject" class="form-label">Subject</label>
                  <input type="text" class="form-control" id="email-subject" placeholder="Enter subject">
                </div>
                <div class="mb-3">
                  <label for="email-body" class="form-label">Message</label>
                  <textarea class="form-control" id="email-body" rows="10" placeholder="Type your message here"></textarea>
                </div>
                <div class="mb-3">
                  <label for="email-attachments" class="form-label">Attachments</label>
                  <input class="form-control" type="file" id="email-attachments" multiple>
                  <div id="attachment-list" class="mt-2">
                    <!-- Attachments will be listed here -->
                  </div>
                </div>
                <div class="d-flex justify-content-between">
                  <button type="button" class="btn btn-primary" id="send-email-btn" onclick="sendEmail()">Send</button>
                  <button type="button" class="btn btn-outline-secondary" id="save-draft-btn">Save as Draft</button>
                </div>
              </form>
            </div>

            <!-- Sent Tab -->
            <div class="tab-pane fade" id="sent-content" role="tabpanel" aria-labelledby="sent-tab">
              <div class="list-group">
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sent-executive-team')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">To: Executive Team</h6>
                    <small class="text-muted">1 day ago</small>
                  </div>
                  <p class="mb-1">Quarterly Business Review Agenda</p>
                  <small class="text-muted">Please find attached the agenda for our upcoming quarterly business review meeting...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sent-john-davis')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">To: John Davis</h6>
                    <small class="text-muted">3 days ago</small>
                  </div>
                  <p class="mb-1">Re: Budget Approval</p>
                  <small class="text-muted">I've reviewed the budget proposal and have a few questions before I can approve it...</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sent-finance-department')">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">To: Finance Department</h6>
                    <small class="text-muted">1 week ago</small>
                  </div>
                  <p class="mb-1">Financial Report Feedback</p>
                  <small class="text-muted">Thank you for preparing the Q1 financial report. I have some feedback and suggestions for the next report...</small>
                </a>
              </div>
            </div>

            <!-- Read Email Tab -->
            <div class="tab-pane fade" id="read-email-content" role="tabpanel" aria-labelledby="read-email-tab">
              <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h5 class="mb-0" id="read-email-subject">Task update: Website redesign completed</h5>
                  <div>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="replyToEmail()"><i class="bi bi-reply"></i> Reply</button>
                    <button class="btn btn-sm btn-outline-primary me-1"><i class="bi bi-reply-all"></i> Reply All</button>
                    <button class="btn btn-sm btn-outline-primary"><i class="bi bi-forward"></i> Forward</button>
                  </div>
                </div>
                <div class="card-body">
                  <div class="d-flex justify-content-between mb-3">
                    <div>
                      <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;" id="read-email-avatar">JD</div>
                        <div>
                          <div class="fw-bold" id="read-email-from">John Doe</div>
                          <div class="small text-muted">To: <span id="read-email-to">me</span></div>
                        </div>
                      </div>
                    </div>
                    <div class="text-muted" id="read-email-time">10 minutes ago</div>
                  </div>
                  <div class="email-body mb-4" id="read-email-body">
                    <p>Hi,</p>
                    <p>I've completed the website redesign task as per the requirements. The new design is now live on the staging server.</p>
                    <p>Key changes include:</p>
                    <ul>
                      <li>Updated homepage layout</li>
                      <li>New responsive design for mobile devices</li>
                      <li>Improved navigation menu</li>
                      <li>Updated color scheme as per brand guidelines</li>
                    </ul>
                    <p>Please review and provide your feedback. I'm available for any changes or adjustments needed.</p>
                    <p>Best regards,<br>John</p>
                  </div>
                  <div class="email-attachments" id="read-email-attachments">
                    <h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                    <div class="list-group">
                      <a href="#" onclick="openGoogleItem('drive', 'image', 'website-redesign-preview')" class="list-group-item list-group-item-action d-flex align-items-center">
                        <i class="bi bi-file-earmark-image text-primary me-2"></i>
                        <div>
                          <div>website_redesign_preview.jpg</div>
                          <small class="text-muted">1.2 MB</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                      </a>
                      <a href="#" onclick="openGoogleItem('drive', 'pdf', 'redesign-documentation')" class="list-group-item list-group-item-action d-flex align-items-center">
                        <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                        <div>
                          <div>redesign_documentation.pdf</div>
                          <small class="text-muted">2.4 MB</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-flex justify-content-between mt-3">
                <button class="btn btn-outline-secondary" onclick="document.getElementById('inbox-tab').click();"><i class="bi bi-arrow-left"></i> Back to Inbox</button>
                <div>
                  <button class="btn btn-outline-danger me-2"><i class="bi bi-trash"></i> Delete</button>
                  <button class="btn btn-outline-secondary"><i class="bi bi-archive"></i> Archive</button>
                </div>
              </div>
            </div>

            <!-- Reply Email Tab -->
            <div class="tab-pane fade" id="reply-email-content" role="tabpanel" aria-labelledby="reply-email-tab">
              <div class="card mb-3">
                <div class="card-header bg-light">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <span class="fw-bold">Re: </span>
                      <span id="reply-email-subject">Task update: Website redesign completed</span>
                    </div>
                  </div>
                </div>
                <div class="card-body bg-light">
                  <div class="d-flex">
                    <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;" id="reply-email-avatar">JD</div>
                    <div>
                      <div class="fw-bold" id="reply-email-from">John Doe</div>
                      <div class="small text-muted" id="reply-email-time">10 minutes ago</div>
                      <div class="mt-2" id="reply-email-original-body">
                        <p>Hi,</p>
                        <p>I've completed the website redesign task as per the requirements. The new design is now live on the staging server.</p>
                        <p>Key changes include:</p>
                        <ul>
                          <li>Updated homepage layout</li>
                          <li>New responsive design for mobile devices</li>
                          <li>Improved navigation menu</li>
                          <li>Updated color scheme as per brand guidelines</li>
                        </ul>
                        <p>Please review and provide your feedback. I'm available for any changes or adjustments needed.</p>
                        <p>Best regards,<br>John</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <label for="reply-email-body" class="form-label">Your Reply</label>
                <textarea class="form-control" id="reply-email-body" rows="6" placeholder="Type your reply here..."></textarea>
              </div>
              <div class="mb-3">
                <label for="reply-email-attachments" class="form-label">Attachments</label>
                <input class="form-control" type="file" id="reply-email-attachments" multiple>
                <div id="reply-attachment-list" class="mt-2">
                  <!-- Attachments will be listed here -->
                </div>
              </div>
              <div class="d-flex justify-content-between">
                <button class="btn btn-outline-secondary" onclick="document.getElementById('read-email-tab').click();"><i class="bi bi-arrow-left"></i> Back</button>
                <div>
                  <button class="btn btn-outline-secondary me-2" id="save-reply-draft-btn">Save Draft</button>
                  <button class="btn btn-primary" id="send-reply-btn" onclick="sendReply()">Send Reply</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://mail.google.com" target="_blank" class="btn btn-primary">Open in Gmail</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Drive Modal -->
  <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="driveModalLabel"><i class="bi bi-folder"></i> Google Drive</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="driveTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content" type="button" role="tab" aria-controls="files-content" aria-selected="true">Files</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button" role="tab" aria-controls="upload-content" aria-selected="false">Upload</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="shared-tab" data-bs-toggle="tab" data-bs-target="#shared-content" type="button" role="tab" aria-controls="shared-content" aria-selected="false">Shared with me</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="driveTabContent">
            <!-- Files Tab -->
            <div class="tab-pane fade show active" id="files-content" role="tabpanel" aria-labelledby="files-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="file-search" class="form-control" placeholder="Search files...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-sort-down"></i> Sort by
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="date">Date</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="size">Size</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="type">Type</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <a href="#" onclick="openGoogleItem('drive', 'folder', 'project-documentation')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project Documentation</h6>
                      <small>15 files - Last updated: Yesterday</small>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'project-documentation'); event.stopPropagation();">
                      <i class="bi bi-folder-symlink"></i> Open
                    </button>
                  </div>
                </a>
                <a href="#" onclick="openGoogleItem('drive', 'folder', 'task-templates')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Task Templates</h6>
                      <small>8 files - Last updated: 3 days ago</small>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'task-templates'); event.stopPropagation();">
                      <i class="bi bi-folder-symlink"></i> Open
                    </button>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('drive', 'pdf', 'project-plan-q2-2025')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project_Plan_Q2_2025.pdf</h6>
                      <small>2.4 MB - Last updated: Yesterday</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'project-plan-q2-2025'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Plan_Q2_2025.pdf'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Project_Plan_Q2_2025.pdf'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Project_Plan_Q2_2025.pdf'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('drive', 'document', 'task-management-guide')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Task_Management_Guide.docx</h6>
                      <small>1.2 MB - Last updated: 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'task-management-guide'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Task_Management_Guide.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Task_Management_Guide.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Task_Management_Guide.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload-content" role="tabpanel" aria-labelledby="upload-tab">
              <div class="row">
                <div class="col-md-7">
                  <div class="upload-area p-5 mb-3 text-center" id="dropzone" style="border: 2px dashed #ccc; border-radius: 5px; background-color: #f8f9fa;">
                    <i class="bi bi-cloud-arrow-up fs-1 text-muted mb-3"></i>
                    <h5>Drag & Drop Files Here</h5>
                    <p class="text-muted">or</p>
                    <label for="file-upload" class="btn btn-primary">
                      <i class="bi bi-folder-plus me-2"></i>Browse Files
                    </label>
                    <input id="file-upload" type="file" multiple style="display: none;">
                    <p class="text-muted mt-3 small">Maximum file size: 50MB</p>
                  </div>
                  <div class="progress mb-3" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                  </div>
                </div>
                <div class="col-md-5">
                  <div class="card">
                    <div class="card-header">Upload Options</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="upload-folder" class="form-label">Destination folder</label>
                        <select class="form-select" id="upload-folder">
                          <option selected>My Drive</option>
                          <option>Project Documentation</option>
                          <option>Task Templates</option>
                          <option>Team Shared Folder</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="convert-to-google-format">
                          <label class="form-check-label" for="convert-to-google-format">
                            Convert to Google format
                          </label>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="share-with-team">
                          <label class="form-check-label" for="share-with-team">
                            Share with team
                          </label>
                        </div>
                      </div>
                      <button type="button" class="btn btn-primary" id="start-upload" disabled>
                        <i class="bi bi-upload me-2"></i>Start Upload
                      </button>
                    </div>
                  </div>
                  <div id="upload-list" class="mt-3">
                    <!-- Files to upload will be listed here -->
                  </div>
                </div>
              </div>
            </div>

            <!-- Shared with me Tab -->
            <div class="tab-pane fade" id="shared-content" role="tabpanel" aria-labelledby="shared-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="shared-search" class="form-control" placeholder="Search shared files...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-filter"></i> Filter
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="other">Other</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <a href="#" ondblclick="openGoogleItem('drive', 'pdf', 'task-management-guidelines')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Task_Management_Guidelines.pdf</h6>
                      <small>Shared by: John Davis - 3 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'task-management-guidelines'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Task_Management_Guidelines.pdf'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Task_Management_Guidelines.pdf'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Task_Management_Guidelines.pdf'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('drive', 'spreadsheet', 'team-performance-metrics')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Team_Performance_Metrics.xlsx</h6>
                      <small>Shared by: Sarah Wilson - 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'spreadsheet', 'team-performance-metrics'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Team_Performance_Metrics.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Team_Performance_Metrics.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Team_Performance_Metrics.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('drive', 'document', 'project-scope-document')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project_Scope_Document.docx</h6>
                      <small>Shared by: David Chen - 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'project-scope-document'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Scope_Document.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Project_Scope_Document.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Project_Scope_Document.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://drive.google.com" target="_blank" class="btn btn-primary">Open in Google Drive</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Docs Modal -->
  <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="docsModalLabel"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="docsTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="my-docs-tab" data-bs-toggle="tab" data-bs-target="#my-docs-content" type="button" role="tab" aria-controls="my-docs-content" aria-selected="true">My Docs</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="create-doc-tab" data-bs-toggle="tab" data-bs-target="#create-doc-content" type="button" role="tab" aria-controls="create-doc-content" aria-selected="false">Create New</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="shared-docs-tab" data-bs-toggle="tab" data-bs-target="#shared-docs-content" type="button" role="tab" aria-controls="shared-docs-content" aria-selected="false">Shared with me</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="docsTabContent">
            <!-- My Docs Tab -->
            <div class="tab-pane fade show active" id="my-docs-content" role="tabpanel" aria-labelledby="my-docs-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="docs-search" class="form-control" placeholder="Search documents...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <button class="btn btn-outline-secondary" id="refresh-docs-list">
                  <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                </button>
              </div>
              <div class="list-group">
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'task-management-procedures')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Task Management Procedures</h6>
                      <small>Last edited: Today</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'task-management-procedures'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Task_Management_Procedures.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Task_Management_Procedures.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Task_Management_Procedures.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'project-requirements-document')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project Requirements Document</h6>
                      <small>Last edited: Yesterday</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'project-requirements-document'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Requirements_Document.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Project_Requirements_Document.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Project_Requirements_Document.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'meeting-minutes-template')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Meeting Minutes Template</h6>
                      <small>Last edited: 3 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'meeting-minutes-template'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Meeting_Minutes_Template.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Meeting_Minutes_Template.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Meeting_Minutes_Template.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'project-status-report')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project Status Report</h6>
                      <small>Last edited: 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'project-status-report'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Status_Report.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Project_Status_Report.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Project_Status_Report.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Create New Tab -->
            <div class="tab-pane fade" id="create-doc-content" role="tabpanel" aria-labelledby="create-doc-tab">
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header">Create from Template</div>
                    <div class="card-body">
                      <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Blank Document</h6>
                              <small>Start with an empty document</small>
                            </div>
                          </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Meeting Minutes</h6>
                              <small>Pre-formatted for meeting notes</small>
                            </div>
                          </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Project Brief</h6>
                              <small>Template for project documentation</small>
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header">Custom Document</div>
                    <div class="card-body">
                      <form>
                        <div class="mb-3">
                          <label for="doc-name" class="form-label">Document Name</label>
                          <input type="text" class="form-control" id="doc-name" placeholder="Enter name">
                        </div>
                        <div class="mb-3">
                          <label for="doc-folder" class="form-label">Save to Folder</label>
                          <select class="form-select" id="doc-folder">
                            <option selected>My Drive</option>
                            <option>Project Documentation</option>
                            <option>Task Templates</option>
                            <option>Team Shared Folder</option>
                          </select>
                        </div>
                        <div class="mb-3">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="share-with-team-doc">
                            <label class="form-check-label" for="share-with-team-doc">
                              Share with team
                            </label>
                          </div>
                        </div>
                        <button type="button" class="btn btn-primary">
                          <i class="bi bi-file-earmark-plus me-2"></i>Create Document
                        </button>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Shared with me Tab -->
            <div class="tab-pane fade" id="shared-docs-content" role="tabpanel" aria-labelledby="shared-docs-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="shared-docs-search" class="form-control" placeholder="Search shared documents...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-filter"></i> Filter
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-filter="all">All Documents</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="recent">Recently Shared</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="owned">Owned by me</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="not-owned">Not owned by me</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'team-collaboration-guidelines')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Team Collaboration Guidelines</h6>
                      <small>Shared by: John Davis - 3 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'team-collaboration-guidelines'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Team_Collaboration_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Team_Collaboration_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Team_Collaboration_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'project-kickoff-notes')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project Kickoff Notes</h6>
                      <small>Shared by: Sarah Wilson - 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'project-kickoff-notes'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Kickoff_Notes.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Project_Kickoff_Notes.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Project_Kickoff_Notes.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('docs', 'document', 'task-assignment-guidelines')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Task Assignment Guidelines</h6>
                      <small>Shared by: David Chen - 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'task-assignment-guidelines'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Task_Assignment_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Task_Assignment_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Task_Assignment_Guidelines.docx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://docs.google.com" target="_blank" class="btn btn-primary">Open in Google Docs</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Sheets Modal -->
  <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="sheetsModalLabel"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="sheetsTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="my-sheets-tab" data-bs-toggle="tab" data-bs-target="#my-sheets-content" type="button" role="tab" aria-controls="my-sheets-content" aria-selected="true">My Sheets</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="create-sheet-tab" data-bs-toggle="tab" data-bs-target="#create-sheet-content" type="button" role="tab" aria-controls="create-sheet-content" aria-selected="false">Create New</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="import-sheet-tab" data-bs-toggle="tab" data-bs-target="#import-sheet-content" type="button" role="tab" aria-controls="import-sheet-content" aria-selected="false">Import</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="sheetsTabContent">
            <!-- My Sheets Tab -->
            <div class="tab-pane fade show active" id="my-sheets-content" role="tabpanel" aria-labelledby="my-sheets-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="sheets-search" class="form-control" placeholder="Search sheets...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <button class="btn btn-outline-secondary" id="refresh-sheets-list">
                  <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                </button>
              </div>
              <div class="list-group">
                <a href="#" ondblclick="openGoogleItem('sheets', 'spreadsheet', 'task-tracking-2025')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Task_Tracking_2025.xlsx</h6>
                      <small>Last edited: 2 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'task-tracking-2025'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Task_Tracking_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Task_Tracking_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Task_Tracking_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('sheets', 'spreadsheet', 'resource-allocation-q2-2025')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Resource_Allocation_Q2_2025.xlsx</h6>
                      <small>Last edited: 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'resource-allocation-q2-2025'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Resource_Allocation_Q2_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Resource_Allocation_Q2_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Resource_Allocation_Q2_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('sheets', 'spreadsheet', 'project-timeline-2025')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project_Timeline_2025.xlsx</h6>
                      <small>Last edited: 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'project-timeline-2025'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Timeline_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Project_Timeline_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Project_Timeline_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
                <a href="#" ondblclick="openGoogleItem('sheets', 'spreadsheet', 'team-budget-2025')" class="list-group-item list-group-item-action">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Team_Budget_2025.xlsx</h6>
                      <small>Last edited: 3 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'team-budget-2025'); event.stopPropagation();">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Team_Budget_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Team_Budget_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('Team_Budget_2025.xlsx'); event.stopPropagation();">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>

            <!-- Create New Tab -->
            <div class="tab-pane fade" id="create-sheet-content" role="tabpanel" aria-labelledby="create-sheet-tab">
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header">Create from Template</div>
                    <div class="card-body">
                      <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Blank Spreadsheet</h6>
                              <small>Start with an empty spreadsheet</small>
                            </div>
                          </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Task Tracker Template</h6>
                              <small>Pre-formatted for tracking tasks</small>
                            </div>
                          </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                            <div>
                              <h6 class="mb-1">Project Timeline Template</h6>
                              <small>Includes Gantt chart and milestones</small>
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header">Custom Spreadsheet</div>
                    <div class="card-body">
                      <form>
                        <div class="mb-3">
                          <label for="sheet-name" class="form-label">Spreadsheet Name</label>
                          <input type="text" class="form-control" id="sheet-name" placeholder="Enter name">
                        </div>
                        <div class="mb-3">
                          <label for="sheet-folder" class="form-label">Save to Folder</label>
                          <select class="form-select" id="sheet-folder">
                            <option selected>My Drive</option>
                            <option>Project Documentation</option>
                            <option>Task Templates</option>
                            <option>Team Shared Folder</option>
                          </select>
                        </div>
                        <div class="mb-3">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="share-with-team-sheet">
                            <label class="form-check-label" for="share-with-team-sheet">
                              Share with team
                            </label>
                          </div>
                        </div>
                        <button type="button" class="btn btn-primary">
                          <i class="bi bi-file-earmark-plus me-2"></i>Create Spreadsheet
                        </button>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Import Tab -->
            <div class="tab-pane fade" id="import-sheet-content" role="tabpanel" aria-labelledby="import-sheet-tab">
              <div class="row">
                <div class="col-md-7">
                  <div class="upload-area p-5 mb-3 text-center" id="sheet-dropzone" style="border: 2px dashed #ccc; border-radius: 5px; background-color: #f8f9fa;">
                    <i class="bi bi-file-earmark-arrow-up fs-1 text-muted mb-3"></i>
                    <h5>Drag & Drop Spreadsheet Files Here</h5>
                    <p class="text-muted">Supported formats: .xlsx, .xls, .csv, .ods</p>
                    <label for="sheet-file-upload" class="btn btn-primary">
                      <i class="bi bi-folder-plus me-2"></i>Browse Files
                    </label>
                    <input id="sheet-file-upload" type="file" accept=".xlsx,.xls,.csv,.ods" style="display: none;">
                  </div>
                </div>
                <div class="col-md-5">
                  <div class="card">
                    <div class="card-header">Import Options</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="import-sheet-name" class="form-label">New Spreadsheet Name</label>
                        <input type="text" class="form-control" id="import-sheet-name" placeholder="Enter name">
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="convert-to-sheets" checked>
                          <label class="form-check-label" for="convert-to-sheets">
                            Convert to Google Sheets format
                          </label>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="import-first-sheet-only">
                          <label class="form-check-label" for="import-first-sheet-only">
                            Import first sheet only
                          </label>
                        </div>
                      </div>
                      <button type="button" class="btn btn-primary" id="import-sheet-btn" disabled>
                        <i class="bi bi-upload me-2"></i>Import Spreadsheet
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://sheets.google.com" target="_blank" class="btn btn-primary">Open in Google Sheets</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Calendar Modal -->
  <div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="calendarModalLabel"><i class="bi bi-calendar3"></i> Google Calendar</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="calendarTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="events-tab" data-bs-toggle="tab" data-bs-target="#events-content" type="button" role="tab" aria-controls="events-content" aria-selected="true">Events</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="create-event-tab" data-bs-toggle="tab" data-bs-target="#create-event-content" type="button" role="tab" aria-controls="create-event-content" aria-selected="false">Create Event</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="calendar-settings-tab" data-bs-toggle="tab" data-bs-target="#calendar-settings-content" type="button" role="tab" aria-controls="calendar-settings-content" aria-selected="false">Settings</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="calendarTabContent">
            <!-- Events Tab -->
            <div class="tab-pane fade show active" id="events-content" role="tabpanel" aria-labelledby="events-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="btn-group" role="group">
                  <button type="button" class="btn btn-outline-primary active">Day</button>
                  <button type="button" class="btn btn-outline-primary">Week</button>
                  <button type="button" class="btn btn-outline-primary">Month</button>
                </div>
                <button class="btn btn-outline-secondary" id="refresh-events-list">
                  <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                </button>
              </div>
              <div class="calendar-date-header d-flex justify-content-between align-items-center mb-3">
                <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-chevron-left"></i></button>
                <h5 class="mb-0">May 10, 2025</h5>
                <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-chevron-right"></i></button>
              </div>
              <div class="list-group">
                <a href="#" onclick="openGoogleItem('calendar', 'event', 'team-status-meeting')" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Team Status Meeting</h6>
                    <small class="text-warning">Today</small>
                  </div>
                  <p class="mb-1">10:00 AM - 11:00 AM</p>
                  <small>Weekly team status meeting to discuss project progress</small>
                </a>
                <a href="#" onclick="openGoogleItem('calendar', 'event', 'project-planning-session')" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Project Planning Session</h6>
                    <small class="text-info">Tomorrow</small>
                  </div>
                  <p class="mb-1">2:00 PM - 3:30 PM</p>
                  <small>Planning session for the next project phase</small>
                </a>
                <a href="#" onclick="openGoogleItem('calendar', 'event', 'sprint-review')" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Sprint Review</h6>
                    <small class="text-secondary">Next Week</small>
                  </div>
                  <p class="mb-1">May 15, 9:00 AM - 10:30 AM</p>
                  <small>End of sprint review meeting with stakeholders</small>
                </a>
                <a href="#" onclick="openGoogleItem('calendar', 'event', 'team-building')" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Team Building Activity</h6>
                    <small class="text-secondary">Next Week</small>
                  </div>
                  <p class="mb-1">May 17, 2:00 PM - 5:00 PM</p>
                  <small>Quarterly team building activity</small>
                </a>
              </div>
            </div>

            <!-- Create Event Tab -->
            <div class="tab-pane fade" id="create-event-content" role="tabpanel" aria-labelledby="create-event-tab">
              <form>
                <div class="mb-3">
                  <label for="event-title" class="form-label">Event Title</label>
                  <input type="text" class="form-control" id="event-title" placeholder="Enter event title">
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="event-start-date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="event-start-date">
                  </div>
                  <div class="col-md-6">
                    <label for="event-start-time" class="form-label">Start Time</label>
                    <input type="time" class="form-control" id="event-start-time">
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="event-end-date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="event-end-date">
                  </div>
                  <div class="col-md-6">
                    <label for="event-end-time" class="form-label">End Time</label>
                    <input type="time" class="form-control" id="event-end-time">
                  </div>
                </div>
                <div class="mb-3">
                  <label for="event-location" class="form-label">Location</label>
                  <input type="text" class="form-control" id="event-location" placeholder="Enter location or meeting link">
                </div>
                <div class="mb-3">
                  <label for="event-description" class="form-label">Description</label>
                  <textarea class="form-control" id="event-description" rows="3" placeholder="Enter event description"></textarea>
                </div>
                <div class="mb-3">
                  <label for="event-guests" class="form-label">Guests</label>
                  <input type="text" class="form-control" id="event-guests" placeholder="Enter email addresses (comma separated)">
                </div>
                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="event-all-day">
                    <label class="form-check-label" for="event-all-day">
                      All day event
                    </label>
                  </div>
                </div>
                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="event-notification">
                    <label class="form-check-label" for="event-notification">
                      Send notification to guests
                    </label>
                  </div>
                </div>
                <button type="button" class="btn btn-primary">
                  <i class="bi bi-calendar-plus me-2"></i>Create Event
                </button>
              </form>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="calendar-settings-content" role="tabpanel" aria-labelledby="calendar-settings-tab">
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header">Calendar Display</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="default-view" class="form-label">Default View</label>
                        <select class="form-select" id="default-view">
                          <option selected>Week</option>
                          <option>Month</option>
                          <option>Day</option>
                          <option>Agenda</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="start-day" class="form-label">Week Starts On</label>
                        <select class="form-select" id="start-day">
                          <option selected>Sunday</option>
                          <option>Monday</option>
                          <option>Saturday</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="show-weekends" checked>
                          <label class="form-check-label" for="show-weekends">
                            Show weekends
                          </label>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="show-declined" checked>
                          <label class="form-check-label" for="show-declined">
                            Show declined events
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header">Notifications</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="default-notification" class="form-label">Default Notification Time</label>
                        <select class="form-select" id="default-notification">
                          <option>None</option>
                          <option>At time of event</option>
                          <option selected>10 minutes before</option>
                          <option>30 minutes before</option>
                          <option>1 hour before</option>
                          <option>1 day before</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="email-notifications" checked>
                          <label class="form-check-label" for="email-notifications">
                            Email notifications
                          </label>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="browser-notifications" checked>
                          <label class="form-check-label" for="browser-notifications">
                            Browser notifications
                          </label>
                        </div>
                      </div>
                      <button type="button" class="btn btn-primary">
                        <i class="bi bi-save me-2"></i>Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://calendar.google.com" target="_blank" class="btn btn-primary">Open in Google Calendar</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Google Maps Modal -->
  <div class="modal fade" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="mapsModalLabel"><i class="bi bi-geo-alt"></i> Google Maps</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="mapsTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="map-view-tab" data-bs-toggle="tab" data-bs-target="#map-view-content" type="button" role="tab" aria-controls="map-view-content" aria-selected="true">Map View</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations-content" type="button" role="tab" aria-controls="locations-content" aria-selected="false">Saved Locations</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="directions-tab" data-bs-toggle="tab" data-bs-target="#directions-content" type="button" role="tab" aria-controls="directions-content" aria-selected="false">Directions</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="mapsTabContent">
            <!-- Map View Tab -->
            <div class="tab-pane fade show active" id="map-view-content" role="tabpanel" aria-labelledby="map-view-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" class="form-control" placeholder="Search locations" aria-label="Search locations">
                  <button class="btn btn-primary" type="button">
                    <i class="bi bi-search"></i>
                  </button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary active">Map</button>
                  <button type="button" class="btn btn-outline-primary">Satellite</button>
                  <button type="button" class="btn btn-outline-primary">Terrain</button>
                </div>
              </div>
              <div class="ratio ratio-16x9">
                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
              </div>
              <div class="d-flex justify-content-end mt-2">
                <button class="btn btn-sm btn-outline-secondary me-2">
                  <i class="bi bi-plus-lg"></i> Zoom In
                </button>
                <button class="btn btn-sm btn-outline-secondary me-2">
                  <i class="bi bi-dash-lg"></i> Zoom Out
                </button>
                <button class="btn btn-sm btn-outline-secondary">
                  <i class="bi bi-pin-map"></i> Save Location
                </button>
              </div>
            </div>

            <!-- Saved Locations Tab -->
            <div class="tab-pane fade" id="locations-content" role="tabpanel" aria-labelledby="locations-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" class="form-control" placeholder="Search saved locations">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <button class="btn btn-primary">
                  <i class="bi bi-pin-map-fill me-2"></i>Add New Location
                </button>
              </div>
              <div class="list-group">
                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                  <div>
                    <i class="bi bi-building me-2"></i>
                    <span>Main Office</span>
                    <small class="text-muted d-block">123 Business Ave, New York, NY 10001</small>
                  </div>
                  <span class="badge bg-primary rounded-pill">HQ</span>
                </a>
                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                  <div>
                    <i class="bi bi-building me-2"></i>
                    <span>Client Site A</span>
                    <small class="text-muted d-block">456 Client Street, New York, NY 10002</small>
                  </div>
                  <span class="badge bg-primary rounded-pill">Client</span>
                </a>
                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                  <div>
                    <i class="bi bi-building me-2"></i>
                    <span>Conference Center</span>
                    <small class="text-muted d-block">789 Event Blvd, New York, NY 10003</small>
                  </div>
                  <span class="badge bg-secondary rounded-pill">Event</span>
                </a>
                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                  <div>
                    <i class="bi bi-building me-2"></i>
                    <span>Branch Office</span>
                    <small class="text-muted d-block">321 Branch Road, Brooklyn, NY 11201</small>
                  </div>
                  <span class="badge bg-info rounded-pill">Branch</span>
                </a>
              </div>
            </div>

            <!-- Directions Tab -->
            <div class="tab-pane fade" id="directions-content" role="tabpanel" aria-labelledby="directions-tab">
              <div class="row mb-3">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="directions-from" class="form-label">From</label>
                    <input type="text" class="form-control" id="directions-from" placeholder="Starting point or address">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="directions-to" class="form-label">To</label>
                    <input type="text" class="form-control" id="directions-to" placeholder="Destination">
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <div class="btn-group w-100" role="group">
                  <button type="button" class="btn btn-outline-primary active">
                    <i class="bi bi-car-front"></i> Driving
                  </button>
                  <button type="button" class="btn btn-outline-primary">
                    <i class="bi bi-train-front"></i> Transit
                  </button>
                  <button type="button" class="btn btn-outline-primary">
                    <i class="bi bi-person-walking"></i> Walking
                  </button>
                  <button type="button" class="btn btn-outline-primary">
                    <i class="bi bi-bicycle"></i> Cycling
                  </button>
                </div>
              </div>
              <div class="d-grid gap-2 mb-3">
                <button class="btn btn-primary" type="button">
                  <i class="bi bi-signpost-split me-2"></i>Get Directions
                </button>
              </div>
              <div class="card">
                <div class="card-header">
                  <i class="bi bi-clock"></i> Estimated Travel Times
                </div>
                <ul class="list-group list-group-flush">
                  <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-car-front me-2"></i> By Car
                    </div>
                    <span>25 mins</span>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-train-front me-2"></i> By Transit
                    </div>
                    <span>45 mins</span>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                      <i class="bi bi-person-walking me-2"></i> Walking
                    </div>
                    <span>2 hours 30 mins</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="https://maps.google.com" target="_blank" class="btn btn-primary">Open in Google Maps</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Attachments Modal -->
  <div class="modal fade" id="attachmentsModal" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="attachmentsModalLabel"><i class="bi bi-paperclip"></i> Attachments</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="attachmentsTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#attachment-files-content" type="button" role="tab" aria-controls="attachment-files-content" aria-selected="true">Files</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="upload-attachment-tab" data-bs-toggle="tab" data-bs-target="#upload-attachment-content" type="button" role="tab" aria-controls="upload-attachment-content" aria-selected="false">Upload</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="shared-attachments-tab" data-bs-toggle="tab" data-bs-target="#shared-attachments-content" type="button" role="tab" aria-controls="shared-attachments-content" aria-selected="false">Shared with me</button>
            </li>
          </ul>
          <div class="tab-content mt-3" id="attachmentsTabContent">
            <!-- Files Tab -->
            <div class="tab-pane fade show active" id="attachment-files-content" role="tabpanel" aria-labelledby="files-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="attachment-search" class="form-control" placeholder="Search attachments...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="dropdown">
                  <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="attachmentFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    Filter By
                  </button>
                  <ul class="dropdown-menu" aria-labelledby="attachmentFilterDropdown">
                    <li><a class="dropdown-item" href="#">All Files</a></li>
                    <li><a class="dropdown-item" href="#">Documents</a></li>
                    <li><a class="dropdown-item" href="#">Images</a></li>
                    <li><a class="dropdown-item" href="#">Spreadsheets</a></li>
                    <li><a class="dropdown-item" href="#">PDFs</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project_Requirements.pdf</h6>
                      <small>2.4 MB - Uploaded: Yesterday</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Project_Requirements.pdf')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Project_Requirements.pdf')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Project_Requirements.pdf')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Requirements.pdf')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Task_Assignments.xlsx</h6>
                      <small>1.8 MB - Uploaded: 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Task_Assignments.xlsx')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Task_Assignments.xlsx')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Task_Assignments.xlsx')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Task_Assignments.xlsx')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-image text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project_Diagram.jpg</h6>
                      <small>3.2 MB - Uploaded: 1 month ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('image', 'Project_Diagram.jpg')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Project_Diagram.jpg')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Project_Diagram.jpg')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Diagram.jpg')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Meeting_Notes.docx</h6>
                      <small>1.5 MB - Uploaded: 2 months ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Meeting_Notes.docx')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Meeting_Notes.docx')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Meeting_Notes.docx')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Meeting_Notes.docx')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

            </div>

            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload-attachment-content" role="tabpanel" aria-labelledby="upload-attachment-tab">
              <div class="row">
                <div class="col-md-7">
                  <div class="upload-area p-5 mb-3 text-center" id="attachment-dropzone" style="border: 2px dashed #ccc; border-radius: 5px; background-color: #f8f9fa;">
                    <i class="bi bi-cloud-arrow-up fs-1 text-muted mb-3"></i>
                    <h5>Drag & Drop Files Here</h5>
                    <p class="text-muted">or</p>
                    <label for="attachment-file-upload" class="btn btn-primary">
                      <i class="bi bi-folder-plus me-2"></i>Browse Files
                    </label>
                    <input id="attachment-file-upload" type="file" multiple style="display: none;">
                    <p class="text-muted mt-3 small">Maximum file size: 50MB</p>
                  </div>
                  <div class="progress mb-3" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                  </div>
                </div>
                <div class="col-md-5">
                  <div class="card">
                    <div class="card-header">Upload Options</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label for="attachment-project" class="form-label">Attach to Project</label>
                        <select class="form-select" id="attachment-project">
                          <option selected>Website Redesign</option>
                          <option>Mobile App Development</option>
                          <option>Database Migration</option>
                          <option>Cloud Infrastructure</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="attachment-task" class="form-label">Attach to Task</label>
                        <select class="form-select" id="attachment-task">
                          <option selected>All Tasks</option>
                          <option>Frontend Development</option>
                          <option>Backend API Integration</option>
                          <option>UI/UX Design</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="share-with-team-attachment">
                          <label class="form-check-label" for="share-with-team-attachment">
                            Share with team
                          </label>
                        </div>
                      </div>
                      <button type="button" class="btn btn-primary" id="start-attachment-upload" disabled>
                        <i class="bi bi-upload me-2"></i>Upload Files
                      </button>
                    </div>
                  </div>
                  <div id="attachment-upload-list" class="mt-3">
                    <!-- Files to upload will be listed here -->
                  </div>
                </div>
              </div>
            </div>

            <!-- Shared with me Tab -->
            <div class="tab-pane fade" id="shared-attachments-content" role="tabpanel" aria-labelledby="shared-attachments-tab">
              <div class="d-flex justify-content-between mb-3">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" id="shared-attachment-search" class="form-control" placeholder="Search shared files...">
                  <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                </div>
                <div class="btn-group">
                  <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-filter"></i> Filter
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                    <li><a class="dropdown-item" href="#" data-filter="other">Other</a></li>
                  </ul>
                </div>
              </div>
              <div class="list-group">
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Project_Proposal.pdf</h6>
                      <small>Shared by: John Davis - 3 days ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Project_Proposal.pdf')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Project_Proposal.pdf')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Project_Proposal.pdf')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Proposal.pdf')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Team_Performance_Metrics.xlsx</h6>
                      <small>Shared by: Sarah Wilson - 1 week ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Team_Performance_Metrics.xlsx')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Team_Performance_Metrics.xlsx')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Team_Performance_Metrics.xlsx')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Team_Performance_Metrics.xlsx')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="list-group-item">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1">Task_Assignment_Guidelines.docx</h6>
                      <small>Shared by: David Chen - 2 weeks ago</small>
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Task_Assignment_Guidelines.docx')">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Task_Assignment_Guidelines.docx')">
                        <i class="bi bi-download"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Task_Assignment_Guidelines.docx')">
                        <i class="bi bi-share"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Task_Assignment_Guidelines.docx')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <!-- No download selected button needed -->
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // Function to fetch and display AI-powered task optimization insights
    function fetchAIInsights() {
      const insightsContainer = document.getElementById('ai-insights-container');

      // Show loading spinner
      insightsContainer.innerHTML = `
        <div class="d-flex justify-content-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      `;

      // Fetch insights from the Integration Hub
      fetch('http://localhost:8000/api/ai-analytics/task-optimization')
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.json();
        })
        .then(data => {
          console.log('AI insights:', data);

          // Store insights data globally for export
          window.aiInsightsData = data;

          // Clear the container
          insightsContainer.innerHTML = '';

          // Create insights sections
          createTaskOptimizationSection(data, insightsContainer);
          createResourceAllocationSection(data, insightsContainer);
          createWorkflowImprovementSection(data, insightsContainer);
          createRecommendationsSection(data, insightsContainer);

          // Add event listeners for interactive elements
          addInteractiveEventListeners(data);
        })
        .catch(error => {
          console.error('Error fetching AI insights:', error);
          insightsContainer.innerHTML = `
            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle"></i>
              Unable to connect to AI analytics engine. Please make sure the Integration Hub is running.
              <div class="mt-2">
                <button class="btn btn-sm btn-outline-primary" id="generate-sample-insights">
                  Generate Sample Insights
                </button>
              </div>
            </div>
          `;

          // Add event listener for the sample insights button
          document.getElementById('generate-sample-insights').addEventListener('click', generateSampleInsights);
        });
    }

    // Initialize sidebar toggle functionality
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');

    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', function(e) {
        e.preventDefault();
        sidebar.classList.toggle('show');
      });
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
      if (window.innerWidth < 768) {
        const isClickInsideSidebar = sidebar.contains(event.target);
        const isClickOnToggle = sidebarToggle && sidebarToggle.contains(event.target);

        if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
          sidebar.classList.remove('show');
        }
      }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      if (window.innerWidth >= 768 && sidebar.classList.contains('show')) {
        sidebar.classList.remove('show');
      }
    });

    // Fetch dashboard data from API
    fetch('/api/dashboard')
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          const dashboardData = data.data;

          // Update metrics
          document.getElementById('active-tasks').textContent = dashboardData.metrics.activeTasks;
          document.getElementById('overall-progress').textContent = dashboardData.metrics.overallProgress + '%';
          document.getElementById('progress-bar').style.width = dashboardData.metrics.overallProgress + '%';
          document.getElementById('team-members').textContent = dashboardData.metrics.teamMembers;

          // Populate tasks container
          const tasksContainer = document.getElementById('tasks-container');
          dashboardData.tasks.forEach(task => {
            const taskCard = document.createElement('div');
            taskCard.className = `card task-card ${task.priority.toLowerCase()}`;

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body';

            const taskTitle = document.createElement('div');
            taskTitle.className = 'task-title';
            taskTitle.textContent = task.title;

            const taskMeta = document.createElement('div');
            taskMeta.className = 'task-meta';

            const assignee = document.createElement('span');
            assignee.innerHTML = `<i class="bi bi-person"></i> ${task.assignee}`;

            const dueDate = document.createElement('span');
            dueDate.innerHTML = `<i class="bi bi-calendar"></i> ${task.dueDate}`;

            const statusBadge = document.createElement('span');
            let statusClass = '';
            if (task.status === 'Planned') statusClass = 'status-planned';
            else if (task.status === 'In Progress') statusClass = 'status-in-progress';
            else statusClass = 'status-completed';

            statusBadge.className = `status-badge ${statusClass}`;
            statusBadge.textContent = task.status;

            const priorityBadge = document.createElement('span');
            let priorityClass = '';
            if (task.priority === 'High') priorityClass = 'priority-high';
            else if (task.priority === 'Medium') priorityClass = 'priority-medium';
            else priorityClass = 'priority-low';

            priorityBadge.className = `priority-badge ${priorityClass}`;
            priorityBadge.textContent = task.priority;

            const taskProgress = document.createElement('div');
            taskProgress.className = 'task-progress';

            const progressText = document.createElement('small');
            progressText.textContent = `Progress: ${task.progress}%`;

            const progressContainer = document.createElement('div');
            progressContainer.className = 'progress-container';

            const progressBar = document.createElement('div');
            progressBar.className = 'progress-bar';
            progressBar.style.width = `${task.progress}%`;

            taskMeta.appendChild(assignee);
            taskMeta.appendChild(dueDate);
            taskMeta.appendChild(statusBadge);
            taskMeta.appendChild(priorityBadge);

            progressContainer.appendChild(progressBar);
            taskProgress.appendChild(progressText);
            taskProgress.appendChild(progressContainer);

            cardBody.appendChild(taskTitle);
            cardBody.appendChild(taskMeta);
            cardBody.appendChild(taskProgress);

            taskCard.appendChild(cardBody);
            tasksContainer.appendChild(taskCard);
          });

          // Populate team table
          const teamTable = document.getElementById('team-table').getElementsByTagName('tbody')[0];
          dashboardData.teamMembers.forEach(member => {
            const row = teamTable.insertRow();

            const nameCell = row.insertCell(0);
            nameCell.textContent = member.name;

            const tasksCell = row.insertCell(1);
            tasksCell.textContent = member.tasks;

            const completedCell = row.insertCell(2);
            completedCell.textContent = member.completed;
          });

          // Create Tasks by Status Chart
          const statusCtx = document.getElementById('tasksByStatusChart').getContext('2d');
          const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
              labels: dashboardData.tasksByStatus.labels,
              datasets: [
                {
                  data: dashboardData.tasksByStatus.data,
                  backgroundColor: [
                    'rgba(108, 92, 231, 0.7)',
                    'rgba(253, 203, 110, 0.7)',
                    'rgba(0, 184, 148, 0.7)'
                  ],
                  borderColor: [
                    'rgba(108, 92, 231, 1)',
                    'rgba(253, 203, 110, 1)',
                    'rgba(0, 184, 148, 1)'
                  ],
                  borderWidth: 1
                }
              ]
            },
            options: {
              responsive: true,
              plugins: {
                legend: {
                  position: 'bottom',
                }
              }
            }
          });

          // Create Tasks by Priority Chart
          const priorityCtx = document.getElementById('tasksByPriorityChart').getContext('2d');
          const priorityChart = new Chart(priorityCtx, {
            type: 'doughnut',
            data: {
              labels: dashboardData.tasksByPriority.labels,
              datasets: [
                {
                  data: dashboardData.tasksByPriority.data,
                  backgroundColor: [
                    'rgba(225, 112, 85, 0.7)',
                    'rgba(253, 203, 110, 0.7)',
                    'rgba(0, 184, 148, 0.7)'
                  ],
                  borderColor: [
                    'rgba(225, 112, 85, 1)',
                    'rgba(253, 203, 110, 1)',
                    'rgba(0, 184, 148, 1)'
                  ],
                  borderWidth: 1
                }
              ]
            },
            options: {
              responsive: true,
              plugins: {
                legend: {
                  position: 'bottom',
                }
              }
            }
          });

          // Create Progress Trend Chart
          const progressCtx = document.getElementById('progressTrendChart').getContext('2d');
          const progressChart = new Chart(progressCtx, {
            type: 'line',
            data: {
              labels: dashboardData.progressTrend.labels,
              datasets: [
                {
                  label: 'Progress (%)',
                  data: dashboardData.progressTrend.data,
                  borderColor: '#6c5ce7',
                  backgroundColor: 'rgba(108, 92, 231, 0.1)',
                  tension: 0.3,
                  fill: true
                }
              ]
            },
            options: {
              responsive: true,
              plugins: {
                legend: {
                  display: false
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  max: 100
                }
              }
            }
          });
        }
      })
      .catch(error => {
        console.error('Error fetching dashboard data:', error);
      });

    // Function to create the Task Optimization section
    function createTaskOptimizationSection(data, container) {
      if (!data.taskOptimization || !data.taskOptimization.insights) {
        return;
      }

      const sectionCol = document.createElement('div');
      sectionCol.className = 'col-12 mb-3';

      const sectionCard = document.createElement('div');
      sectionCard.className = 'card';
      sectionCard.dataset.insightType = 'taskOptimization';

      const sectionHeader = document.createElement('div');
      sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
      sectionHeader.innerHTML = `
        <h6 class="mb-0">
          <i class="bi bi-calendar-check me-2"></i>
          Task Optimization
        </h6>
        <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#taskOptimizationInsights">
          <i class="bi bi-chevron-down"></i>
        </button>
      `;

      const sectionBody = document.createElement('div');
      sectionBody.className = 'card-body collapse show';
      sectionBody.id = 'taskOptimizationInsights';

      const insightsRow = document.createElement('div');
      insightsRow.className = 'row';

      data.taskOptimization.insights.forEach(insight => {
        const insightCol = document.createElement('div');
        insightCol.className = 'col-md-4 mb-3';

        const insightCard = document.createElement('div');
        insightCard.className = 'card h-100';

        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';

        // Determine impact badge class
        let impactClass = '';
        if (insight.impact === 'High') impactClass = 'bg-success';
        else if (insight.impact === 'Medium') impactClass = 'bg-warning';
        else impactClass = 'bg-danger';

        cardBody.innerHTML = `
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">${insight.title}</h6>
            <span class="badge ${impactClass}">${insight.impact} Impact</span>
          </div>
          <p class="small text-muted mb-2">${insight.description}</p>
          <div class="d-flex justify-content-between mb-2">
            <span class="text-muted">Time Savings:</span>
            <span class="fw-bold text-success">${insight.timeSavings} hours/week</span>
          </div>
          <button class="btn btn-sm btn-outline-primary mt-2 optimize-task-btn"
                  data-insight-id="${insight.id}"
                  data-insight-title="${insight.title}">
            Apply Optimization
          </button>
        `;

        insightCard.appendChild(cardBody);
        insightCol.appendChild(insightCard);
        insightsRow.appendChild(insightCol);
      });

      sectionBody.appendChild(insightsRow);
      sectionCard.appendChild(sectionHeader);
      sectionCard.appendChild(sectionBody);
      sectionCol.appendChild(sectionCard);
      container.appendChild(sectionCol);
    }

    // Function to create the Resource Allocation section
    function createResourceAllocationSection(data, container) {
      if (!data.resourceAllocation || !data.resourceAllocation.team) {
        return;
      }

      const sectionCol = document.createElement('div');
      sectionCol.className = 'col-12 mb-3';

      const sectionCard = document.createElement('div');
      sectionCard.className = 'card';
      sectionCard.dataset.insightType = 'resourceAllocation';

      const sectionHeader = document.createElement('div');
      sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
      sectionHeader.innerHTML = `
        <h6 class="mb-0">
          <i class="bi bi-people me-2"></i>
          Resource Allocation
        </h6>
        <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#resourceAllocationInsights">
          <i class="bi bi-chevron-down"></i>
        </button>
      `;

      const sectionBody = document.createElement('div');
      sectionBody.className = 'card-body collapse show';
      sectionBody.id = 'resourceAllocationInsights';

      const teamRow = document.createElement('div');
      teamRow.className = 'row';

      data.resourceAllocation.team.forEach(member => {
        const memberCol = document.createElement('div');
        memberCol.className = 'col-md-4 mb-3';

        const memberCard = document.createElement('div');
        memberCard.className = 'card h-100';

        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';

        // Determine workload badge class
        let workloadClass = '';
        if (member.workloadStatus === 'Underutilized') workloadClass = 'bg-info';
        else if (member.workloadStatus === 'Optimal') workloadClass = 'bg-success';
        else workloadClass = 'bg-danger';

        cardBody.innerHTML = `
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">${member.name}</h6>
            <span class="badge ${workloadClass}">${member.workloadStatus}</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span class="text-muted">Current Tasks:</span>
            <span class="fw-bold">${member.currentTasks}</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span class="text-muted">Optimal Tasks:</span>
            <span class="fw-bold text-success">${member.optimalTasks}</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span class="text-muted">Skill Match:</span>
            <span class="fw-bold">${member.skillMatch}%</span>
          </div>
          <div class="progress mb-2" style="height: 5px;">
            <div class="progress-bar ${workloadClass === 'bg-success' ? 'bg-success' : (workloadClass === 'bg-info' ? 'bg-info' : 'bg-danger')}"
                 style="width: ${member.workloadPercentage}%"></div>
          </div>
          <button class="btn btn-sm btn-outline-primary mt-2 optimize-resource-btn"
                  data-member-id="${member.id}"
                  data-member-name="${member.name}">
            Optimize Workload
          </button>
        `;

        memberCard.appendChild(cardBody);
        memberCol.appendChild(memberCard);
        teamRow.appendChild(memberCol);
      });

      sectionBody.appendChild(teamRow);
      sectionCard.appendChild(sectionHeader);
      sectionCard.appendChild(sectionBody);
      sectionCol.appendChild(sectionCard);
      container.appendChild(sectionCol);
    }

    // Function to create the Workflow Improvement section
    function createWorkflowImprovementSection(data, container) {
      if (!data.workflowImprovements || !data.workflowImprovements.workflows) {
        return;
      }

      const sectionCol = document.createElement('div');
      sectionCol.className = 'col-12 mb-3';

      const sectionCard = document.createElement('div');
      sectionCard.className = 'card';
      sectionCard.dataset.insightType = 'workflowImprovement';

      const sectionHeader = document.createElement('div');
      sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
      sectionHeader.innerHTML = `
        <h6 class="mb-0">
          <i class="bi bi-diagram-3 me-2"></i>
          Workflow Improvements
        </h6>
        <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#workflowImprovementInsights">
          <i class="bi bi-chevron-down"></i>
        </button>
      `;

      const sectionBody = document.createElement('div');
      sectionBody.className = 'card-body collapse show';
      sectionBody.id = 'workflowImprovementInsights';

      const workflowsRow = document.createElement('div');
      workflowsRow.className = 'row';

      data.workflowImprovements.workflows.forEach(workflow => {
        const workflowCol = document.createElement('div');
        workflowCol.className = 'col-md-6 mb-3';

        const workflowCard = document.createElement('div');
        workflowCard.className = 'card h-100';

        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';

        // Determine complexity badge class
        let complexityClass = '';
        if (workflow.implementationComplexity === 'Low') complexityClass = 'bg-success';
        else if (workflow.implementationComplexity === 'Medium') complexityClass = 'bg-warning';
        else complexityClass = 'bg-danger';

        cardBody.innerHTML = `
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">${workflow.name}</h6>
            <span class="badge ${complexityClass}">${workflow.implementationComplexity} Complexity</span>
          </div>
          <p class="small text-muted mb-2">${workflow.description}</p>
          <div class="d-flex justify-content-between mb-2">
            <span class="text-muted">Current Efficiency:</span>
            <span class="fw-bold">${workflow.currentEfficiency}%</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span class="text-muted">Potential Efficiency:</span>
            <span class="fw-bold text-success">${workflow.potentialEfficiency}%</span>
          </div>
          <div class="progress mb-2" style="height: 5px;">
            <div class="progress-bar bg-success" style="width: ${workflow.potentialEfficiency}%"></div>
          </div>
          <button class="btn btn-sm btn-outline-primary mt-2 improve-workflow-btn"
                  data-workflow-id="${workflow.id}"
                  data-workflow-name="${workflow.name}">
            View Improvement Plan
          </button>
        `;

        workflowCard.appendChild(cardBody);
        workflowCol.appendChild(workflowCard);
        workflowsRow.appendChild(workflowCol);
      });

      sectionBody.appendChild(workflowsRow);
      sectionCard.appendChild(sectionHeader);
      sectionCard.appendChild(sectionBody);
      sectionCol.appendChild(sectionCard);
      container.appendChild(sectionCol);
    }

    // Function to create the Recommendations section
    function createRecommendationsSection(data, container) {
      if (!data.recommendations || !data.recommendations.length) {
        return;
      }

      const sectionCol = document.createElement('div');
      sectionCol.className = 'col-12 mb-3';

      const sectionCard = document.createElement('div');
      sectionCard.className = 'card';
      sectionCard.dataset.insightType = 'recommendations';

      const sectionHeader = document.createElement('div');
      sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
      sectionHeader.innerHTML = `
        <h6 class="mb-0">
          <i class="bi bi-lightbulb me-2"></i>
          AI Recommendations
        </h6>
        <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#recommendationsSection">
          <i class="bi bi-chevron-down"></i>
        </button>
      `;

      const sectionBody = document.createElement('div');
      sectionBody.className = 'card-body collapse show';
      sectionBody.id = 'recommendationsSection';

      const recommendationsList = document.createElement('ul');
      recommendationsList.className = 'list-group';

      data.recommendations.forEach((recommendation, index) => {
        const item = document.createElement('li');
        item.className = 'list-group-item d-flex align-items-center';

        item.innerHTML = `
          <div class="me-3">
            <span class="badge bg-primary rounded-circle">
              ${index + 1}
            </span>
          </div>
          <div>
            ${recommendation}
          </div>
          <div class="ms-auto">
            <button class="btn btn-sm btn-outline-success action-btn" data-recommendation-index="${index}">
              Take Action
            </button>
          </div>
        `;

        recommendationsList.appendChild(item);
      });

      sectionBody.appendChild(recommendationsList);
      sectionCard.appendChild(sectionHeader);
      sectionCard.appendChild(sectionBody);
      sectionCol.appendChild(sectionCard);
      container.appendChild(sectionCol);
    }

    // Function to generate sample insights when the API is not available
    function generateSampleInsights() {
      const sampleData = {
        taskOptimization: {
          insights: [
            {
              id: 1,
              title: "Automate Status Updates",
              description: "Implement automated status updates based on task progress to reduce manual reporting time.",
              impact: "High",
              timeSavings: 5.5
            },
            {
              id: 2,
              title: "Batch Similar Tasks",
              description: "Group similar tasks together to reduce context switching and improve focus.",
              impact: "Medium",
              timeSavings: 3.2
            },
            {
              id: 3,
              title: "Implement Task Templates",
              description: "Create templates for recurring tasks to standardize processes and reduce setup time.",
              impact: "High",
              timeSavings: 4.8
            }
          ]
        },
        resourceAllocation: {
          team: [
            {
              id: 1,
              name: "John Doe",
              workloadStatus: "Overallocated",
              currentTasks: 12,
              optimalTasks: 8,
              skillMatch: 85,
              workloadPercentage: 150
            },
            {
              id: 2,
              name: "Jane Smith",
              workloadStatus: "Optimal",
              currentTasks: 7,
              optimalTasks: 7,
              skillMatch: 92,
              workloadPercentage: 100
            },
            {
              id: 3,
              name: "Mike Johnson",
              workloadStatus: "Underutilized",
              currentTasks: 4,
              optimalTasks: 6,
              skillMatch: 78,
              workloadPercentage: 67
            }
          ]
        },
        workflowImprovements: {
          workflows: [
            {
              id: 1,
              name: "Task Approval Process",
              description: "Streamline the approval process by reducing unnecessary review steps.",
              currentEfficiency: 65,
              potentialEfficiency: 90,
              implementationComplexity: "Low"
            },
            {
              id: 2,
              name: "Bug Tracking Workflow",
              description: "Improve bug tracking by adding automated categorization and priority assignment.",
              currentEfficiency: 72,
              potentialEfficiency: 88,
              implementationComplexity: "Medium"
            }
          ]
        },
        recommendations: [
          "Implement a daily standup meeting to improve team coordination and reduce blockers",
          "Set up automated reminders for approaching deadlines to prevent last-minute rushes",
          "Create a knowledge base for common issues and solutions to reduce repetitive problem-solving",
          "Establish clear escalation paths for different types of issues to improve response times"
        ]
      };

      const insightsContainer = document.getElementById('ai-insights-container');
      insightsContainer.innerHTML = '';

      // Store insights data globally for export
      window.aiInsightsData = sampleData;

      // Create insights sections
      createTaskOptimizationSection(sampleData, insightsContainer);
      createResourceAllocationSection(sampleData, insightsContainer);
      createWorkflowImprovementSection(sampleData, insightsContainer);
      createRecommendationsSection(sampleData, insightsContainer);

      // Add event listeners for interactive elements
      addInteractiveEventListeners(sampleData);
    }

    // Add event listener for refresh insights button
    document.getElementById('refresh-insights-btn').addEventListener('click', fetchAIInsights);

    // Function to add interactive event listeners to the AI insights
    function addInteractiveEventListeners(data) {
      // Add event listeners for task optimization buttons
      document.querySelectorAll('.optimize-task-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const insightId = this.dataset.insightId;
          const insightTitle = this.dataset.insightTitle;

          // Find the insight in the data
          let insight;
          if (data.taskOptimization && data.taskOptimization.insights) {
            insight = data.taskOptimization.insights.find(i => i.id == insightId);
          }

          // Create modal for task optimization
          const modalHTML = `
            <div class="modal fade" id="taskOptimizationModal" tabindex="-1" aria-labelledby="taskOptimizationModalLabel" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="taskOptimizationModalLabel">Apply Optimization: ${insightTitle}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <div class="alert alert-info">
                      <i class="bi bi-info-circle"></i>
                      Implementing this optimization could save approximately <strong>${insight ? insight.timeSavings : '4-6'} hours per week</strong>.
                    </div>

                    <div class="card mb-3">
                      <div class="card-header">Implementation Steps</div>
                      <div class="card-body">
                        <ol>
                          <li class="mb-2">Analyze current task workflow and identify bottlenecks</li>
                          <li class="mb-2">Design optimized workflow based on AI recommendations</li>
                          <li class="mb-2">Create documentation for the new process</li>
                          <li class="mb-2">Train team members on the new workflow</li>
                          <li class="mb-2">Implement changes in the task management system</li>
                          <li class="mb-2">Monitor results and make adjustments as needed</li>
                        </ol>
                      </div>
                    </div>

                    <div class="row">
                      <div class="col-md-6">
                        <div class="card">
                          <div class="card-header">Resource Requirements</div>
                          <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                              <span>Project Manager:</span>
                              <span>4 hours</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Team Lead:</span>
                              <span>6 hours</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Team Members:</span>
                              <span>2 hours each</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>IT Support:</span>
                              <span>3 hours</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="card">
                          <div class="card-header">Timeline</div>
                          <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                              <span>Planning:</span>
                              <span>1-2 days</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Implementation:</span>
                              <span>2-3 days</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Training:</span>
                              <span>1 day</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Evaluation:</span>
                              <span>1 week</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="mt-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="notifyTeam">
                        <label class="form-check-label" for="notifyTeam">
                          Notify team members about this change
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="scheduleTraining">
                        <label class="form-check-label" for="scheduleTraining">
                          Schedule training session
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="createDocumentation">
                        <label class="form-check-label" for="createDocumentation">
                          Create documentation for the new process
                        </label>
                      </div>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Implement Optimization</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Remove any existing modal
          const existingModal = document.getElementById('taskOptimizationModal');
          if (existingModal) {
            existingModal.remove();
          }

          // Add modal to the document
          document.body.insertAdjacentHTML('beforeend', modalHTML);

          // Show the modal
          const modal = new bootstrap.Modal(document.getElementById('taskOptimizationModal'));
          modal.show();
        });
      });

      // Add event listeners for resource optimization buttons
      document.querySelectorAll('.optimize-resource-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const memberId = this.dataset.memberId;
          const memberName = this.dataset.memberName;

          // Find the team member in the data
          let member;
          if (data.resourceAllocation && data.resourceAllocation.team) {
            member = data.resourceAllocation.team.find(m => m.id == memberId);
          }

          // Create modal for resource optimization
          const modalHTML = `
            <div class="modal fade" id="resourceOptimizationModal" tabindex="-1" aria-labelledby="resourceOptimizationModalLabel" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="resourceOptimizationModalLabel">Optimize Workload: ${memberName}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <div class="row mb-3">
                      <div class="col-md-6">
                        <div class="card">
                          <div class="card-header">Current Workload</div>
                          <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                              <span>Assigned Tasks:</span>
                              <span class="fw-bold">${member ? member.currentTasks : '0'}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Workload Status:</span>
                              <span class="fw-bold">${member ? member.workloadStatus : 'Unknown'}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Skill Match:</span>
                              <span class="fw-bold">${member ? member.skillMatch : '0'}%</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="card">
                          <div class="card-header">Recommended Changes</div>
                          <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                              <span>Optimal Tasks:</span>
                              <span class="fw-bold text-success">${member ? member.optimalTasks : '0'}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Tasks to Reassign:</span>
                              <span class="fw-bold text-${member && member.workloadStatus === 'Overallocated' ? 'danger' : 'success'}">${member && member.workloadStatus === 'Overallocated' ? (member.currentTasks - member.optimalTasks) : (member.optimalTasks - member.currentTasks)}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                              <span>Action Required:</span>
                              <span class="fw-bold">${member && member.workloadStatus === 'Overallocated' ? 'Reduce Workload' : (member.workloadStatus === 'Underutilized' ? 'Increase Workload' : 'Maintain Workload')}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="card mb-3">
                      <div class="card-header">Task Reassignment</div>
                      <div class="card-body">
                        ${member && member.workloadStatus === 'Overallocated' ? `
                          <p>Select tasks to reassign from ${memberName}:</p>
                          <div class="list-group">
                            <label class="list-group-item">
                              <input class="form-check-input me-1" type="checkbox">
                              Update project documentation
                            </label>
                            <label class="list-group-item">
                              <input class="form-check-input me-1" type="checkbox">
                              Review pull requests
                            </label>
                            <label class="list-group-item">
                              <input class="form-check-input me-1" type="checkbox">
                              Prepare weekly status report
                            </label>
                            <label class="list-group-item">
                              <input class="form-check-input me-1" type="checkbox">
                              Attend client meetings
                            </label>
                          </div>
                          <div class="mt-3">
                            <label class="form-label">Reassign selected tasks to:</label>
                            <select class="form-select">
                              <option>Mike Johnson</option>
                              <option>Sarah Williams</option>
                              <option>David Chen</option>
                            </select>
                          </div>
                        ` : `
                          <p>Select tasks to assign to ${memberName}:</p>
                          <div class="list-group">
                            <label class="list-group-item">
                              <input class="form-check-input me-1" type="checkbox">
                              Create test cases for new features
                            </label>
                            <label class="list-group-item">
                              <input class="form-check-input me-1" type="checkbox">
                              Implement UI improvements
                            </label>
                            <label class="list-group-item">
                              <input class="form-check-input me-1" type="checkbox">
                              Optimize database queries
                            </label>
                          </div>
                          <div class="mt-3">
                            <label class="form-label">Assign selected tasks from:</label>
                            <select class="form-select">
                              <option>John Doe</option>
                              <option>Jane Smith</option>
                              <option>Alex Johnson</option>
                            </select>
                          </div>
                        `}
                      </div>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Apply Changes</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Remove any existing modal
          const existingModal = document.getElementById('resourceOptimizationModal');
          if (existingModal) {
            existingModal.remove();
          }

          // Add modal to the document
          document.body.insertAdjacentHTML('beforeend', modalHTML);

          // Show the modal
          const modal = new bootstrap.Modal(document.getElementById('resourceOptimizationModal'));
          modal.show();
        });
      });

      // Add event listeners for workflow improvement buttons
      document.querySelectorAll('.improve-workflow-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const workflowId = this.dataset.workflowId;
          const workflowName = this.dataset.workflowName;

          // Find the workflow in the data
          let workflow;
          if (data.workflowImprovements && data.workflowImprovements.workflows) {
            workflow = data.workflowImprovements.workflows.find(w => w.id == workflowId);
          }

          // Create modal for workflow improvement
          const modalHTML = `
            <div class="modal fade" id="workflowImprovementModal" tabindex="-1" aria-labelledby="workflowImprovementModalLabel" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="workflowImprovementModalLabel">Workflow Improvement: ${workflowName}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <div class="alert alert-info">
                      <i class="bi bi-info-circle"></i>
                      Implementing this improvement could increase efficiency from <strong>${workflow ? workflow.currentEfficiency : '0'}%</strong> to <strong>${workflow ? workflow.potentialEfficiency : '0'}%</strong>.
                    </div>

                    <div class="row mb-3">
                      <div class="col-md-6">
                        <div class="card">
                          <div class="card-header">Current Workflow</div>
                          <div class="card-body">
                            <ol>
                              <li class="mb-2">Task creation (manual)</li>
                              <li class="mb-2">Assignment to team member (manual)</li>
                              <li class="mb-2">Initial review by team lead</li>
                              <li class="mb-2">Work in progress</li>
                              <li class="mb-2">Quality assurance review</li>
                              <li class="mb-2">Final approval</li>
                              <li class="mb-2">Task completion</li>
                            </ol>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="card">
                          <div class="card-header">Improved Workflow</div>
                          <div class="card-body">
                            <ol>
                              <li class="mb-2">Task creation (automated templates)</li>
                              <li class="mb-2">Auto-assignment based on skills and workload</li>
                              <li class="mb-2 text-decoration-line-through text-muted">Initial review by team lead</li>
                              <li class="mb-2">Work in progress with automated status updates</li>
                              <li class="mb-2">Automated quality checks + manual review</li>
                              <li class="mb-2">Final approval</li>
                              <li class="mb-2">Task completion with automated reporting</li>
                            </ol>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="card mb-3">
                      <div class="card-header">Implementation Plan</div>
                      <div class="card-body">
                        <div class="row">
                          <div class="col-md-6">
                            <h6>Phase 1: Setup (1-2 weeks)</h6>
                            <ul>
                              <li>Create task templates</li>
                              <li>Configure auto-assignment rules</li>
                              <li>Set up automated status updates</li>
                            </ul>
                          </div>
                          <div class="col-md-6">
                            <h6>Phase 2: Rollout (2-3 weeks)</h6>
                            <ul>
                              <li>Train team on new workflow</li>
                              <li>Implement automated quality checks</li>
                              <li>Set up reporting automation</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="pilotImplementation">
                      <label class="form-check-label" for="pilotImplementation">Start with pilot implementation</label>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Implement Improvement</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Remove any existing modal
          const existingModal = document.getElementById('workflowImprovementModal');
          if (existingModal) {
            existingModal.remove();
          }

          // Add modal to the document
          document.body.insertAdjacentHTML('beforeend', modalHTML);

          // Show the modal
          const modal = new bootstrap.Modal(document.getElementById('workflowImprovementModal'));
          modal.show();
        });
      });

      // Add event listeners for action buttons
      document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const index = parseInt(this.dataset.recommendationIndex);
          const recommendation = data.recommendations[index];

          // Create modal for action plan
          const modalHTML = `
            <div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="actionModalLabel">Action Plan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <p><strong>Recommendation:</strong> ${recommendation}</p>
                    <form>
                      <div class="mb-3">
                        <label for="actionType" class="form-label">Action Type</label>
                        <select class="form-select" id="actionType">
                          <option>Create Task</option>
                          <option>Schedule Meeting</option>
                          <option>Create Project</option>
                          <option>Assign to Team</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="assignee" class="form-label">Assign to</label>
                        <select class="form-select" id="assignee">
                          <option>John Doe</option>
                          <option>Jane Smith</option>
                          <option>Mike Johnson</option>
                          <option>Sarah Williams</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="dueDate" class="form-label">Due Date</label>
                        <input type="date" class="form-control" id="dueDate">
                      </div>
                      <div class="mb-3">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority">
                          <option>High</option>
                          <option selected>Medium</option>
                          <option>Low</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="3"></textarea>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Create Task</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Remove any existing modal
          const existingModal = document.getElementById('actionModal');
          if (existingModal) {
            existingModal.remove();
          }

          // Add modal to the document
          document.body.insertAdjacentHTML('beforeend', modalHTML);

          // Show the modal
          const modal = new bootstrap.Modal(document.getElementById('actionModal'));
          modal.show();
        });
      });
    }

    // Add event listeners for expand/collapse all insights
    document.getElementById('expand-all-insights').addEventListener('click', function(e) {
      e.preventDefault();
      document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
        const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
        bsCollapse.show();
      });
    });

    document.getElementById('collapse-all-insights').addEventListener('click', function(e) {
      e.preventDefault();
      document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
        const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
        bsCollapse.hide();
      });
    });

    // Add event listener for export insights
    document.getElementById('export-insights').addEventListener('click', function(e) {
      e.preventDefault();
      if (window.aiInsightsData) {
        const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(window.aiInsightsData, null, 2));
        const downloadAnchorNode = document.createElement('a');
        downloadAnchorNode.setAttribute("href", dataStr);
        downloadAnchorNode.setAttribute("download", "task_optimization_insights.json");
        document.body.appendChild(downloadAnchorNode);
        downloadAnchorNode.click();
        downloadAnchorNode.remove();
      }
    });

    // Fetch AI insights when the page loads
    fetchAIInsights();

    // Attachments Component
    const AttachmentsHandler = {
      init() {
        // Dashboard component event listeners
        const fileUploadInput = document.getElementById('attachment-file-upload');
        if (fileUploadInput) {
          fileUploadInput.addEventListener('change', this.handleFileUpload.bind(this));
        }
      },

      handleFileUpload(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        let fileNames = [];
        for (let i = 0; i < files.length; i++) {
          fileNames.push(files[i].name);
        }

        alert(`Files uploaded successfully: ${fileNames.join(', ')}`);

        // Clear the input to allow uploading the same file again
        event.target.value = '';
      }
    };

    // Function to view an attachment
    function viewAttachment(type, fileName) {
      alert(`Viewing ${fileName}...`);
      // In a real application, this would open the file for viewing
    }

    // Function to download an attachment
    function downloadAttachment(fileName) {
      alert(`Downloading ${fileName}...`);
      // In a real application, this would trigger a download
    }

    // Function to download a file
    function downloadFile(fileName) {
      alert(`Downloading ${fileName}...`);
      // In a real application, this would trigger a download
    }

    // Function to delete an attachment
    function deleteAttachment(fileName) {
      if (confirm(`Are you sure you want to delete ${fileName}?`)) {
        alert(`${fileName} has been deleted.`);
        // In a real application, this would delete the file
      }
    }

    function shareFile(fileName) {
      const email = prompt(`Enter email address to share ${fileName} with:`);
      if (email) {
        alert(`${fileName} has been shared with ${email}.`);
        // In a real application, this would share the file with the specified email
      }
    }

    // Function to download selected attachments
    function downloadSelectedAttachments(source) {
      const selector = source === 'shared' ? '#shared-attachments-content .form-check-input:checked' : '#attachment-files-content .form-check-input:checked';
      const checkboxes = document.querySelectorAll(selector);

      if (checkboxes.length === 0) {
        alert('Please select at least one attachment to download.');
        return;
      }

      alert(`Downloading ${checkboxes.length} selected attachments...`);
      // In a real application, this would download all selected attachments
    }

    // Initialize Google Integration Components
    document.addEventListener('DOMContentLoaded', () => {
      GoogleMapsHandler.init();
      GoogleSheetsHandler.init();
      GoogleCalendarHandler.init();
      GoogleGmailHandler.init();
      GoogleDriveHandler.init();
      GoogleDocsHandler.init();
      AttachmentsHandler.init();
    });

    // Gmail functionality
    function openEmail(emailId) {
      // Show the read email tab
      const readEmailTab = document.getElementById('read-email-tab');
      readEmailTab.style.display = 'block';
      readEmailTab.click();

      // Update email content based on the emailId
      let subject, from, avatar, time, body, attachments;

      switch(emailId) {
        case 'john-doe-email':
          subject = 'Task update: Website redesign completed';
          from = 'John Doe';
          avatar = 'JD';
          time = '10 minutes ago';
          body = `<p>Hi,</p>
                  <p>I've completed the website redesign task as per the requirements. The new design is now live on the staging server.</p>
                  <p>Key changes include:</p>
                  <ul>
                    <li>Updated homepage layout</li>
                    <li>New responsive design for mobile devices</li>
                    <li>Improved navigation menu</li>
                    <li>Updated color scheme as per brand guidelines</li>
                  </ul>
                  <p>Please review and provide your feedback. I'm available for any changes or adjustments needed.</p>
                  <p>Best regards,<br>John</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                        <div class="list-group">
                          <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-image text-primary me-2"></i>
                            <div>
                              <div>website_redesign_preview.jpg</div>
                              <small class="text-muted">1.2 MB</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                          </a>
                          <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                            <div>
                              <div>redesign_documentation.pdf</div>
                              <small class="text-muted">2.4 MB</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                          </a>
                        </div>`;
          break;
        case 'sarah-miller-email':
          subject = 'Project timeline discussion';
          from = 'Sarah Miller';
          avatar = 'SM';
          time = '1 hour ago';
          body = `<p>Hello,</p>
                  <p>We need to discuss the project timeline for the upcoming release. There are a few concerns about the current schedule that I'd like to address.</p>
                  <p>Key points for discussion:</p>
                  <ul>
                    <li>Current milestone deadlines</li>
                    <li>Resource allocation for critical tasks</li>
                    <li>Potential risks and mitigation strategies</li>
                    <li>Adjustments needed to meet the release date</li>
                  </ul>
                  <p>Could we schedule a meeting this week to go over these items? I'm available most afternoons.</p>
                  <p>Thanks,<br>Sarah</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (1)</h6>
                        <div class="list-group">
                          <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                            <div>
                              <div>project_timeline_v2.xlsx</div>
                              <small class="text-muted">1.8 MB</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                          </a>
                        </div>`;
          break;
        case 'team-lead-email':
          subject = 'Weekly status meeting reminder';
          from = 'Team Lead';
          avatar = 'TL';
          time = '3 hours ago';
          body = `<p>Team,</p>
                  <p>This is a reminder about our weekly status meeting scheduled for tomorrow at 10:00 AM in the main conference room.</p>
                  <p>Please come prepared to discuss:</p>
                  <ul>
                    <li>Progress on your assigned tasks</li>
                    <li>Any blockers or issues you're facing</li>
                    <li>Plans for the upcoming week</li>
                  </ul>
                  <p>If you can't attend in person, please join via the video conference link below.</p>
                  <p>Regards,<br>Team Lead</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (1)</h6>
                        <div class="list-group">
                          <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                            <div>
                              <div>meeting_agenda.docx</div>
                              <small class="text-muted">0.5 MB</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                          </a>
                        </div>`;
          break;
        case 'project-manager-email':
          subject = 'Project status report';
          from = 'Project Manager';
          avatar = 'PM';
          time = 'Yesterday';
          body = `<p>Hello,</p>
                  <p>Please find attached the latest project status report for review. We've made significant progress in several areas, but there are a few concerns that need attention.</p>
                  <p>Highlights:</p>
                  <ul>
                    <li>Frontend development is ahead of schedule</li>
                    <li>Database optimization is complete</li>
                    <li>API integration is facing some challenges</li>
                    <li>QA team has identified several critical bugs</li>
                  </ul>
                  <p>Let's discuss these items in our next meeting.</p>
                  <p>Best regards,<br>Project Manager</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                        <div class="list-group">
                          <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                            <div>
                              <div>project_status_report.pdf</div>
                              <small class="text-muted">2.1 MB</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                          </a>
                          <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                            <div>
                              <div>issue_tracker.xlsx</div>
                              <small class="text-muted">1.5 MB</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                          </a>
                        </div>`;
          break;
        case 'hr-department-email':
          subject = 'Employee training schedule';
          from = 'HR Department';
          avatar = 'HR';
          time = '2 days ago';
          body = `<p>Dear Team Members,</p>
                  <p>The updated employee training schedule for next month is now available. Please review the attached document for details on upcoming training sessions.</p>
                  <p>Important notes:</p>
                  <ul>
                    <li>All team members are required to complete the security awareness training</li>
                    <li>Technical training sessions are optional but recommended</li>
                    <li>Please register for your preferred sessions by the end of this week</li>
                    <li>Contact HR if you have any questions or conflicts</li>
                  </ul>
                  <p>Thank you for your cooperation.</p>
                  <p>Best regards,<br>HR Department</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (1)</h6>
                        <div class="list-group">
                          <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                            <div>
                              <div>training_schedule.pdf</div>
                              <small class="text-muted">1.3 MB</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                          </a>
                        </div>`;
          break;
        case 'sent-executive-team':
          subject = 'Quarterly Business Review Agenda';
          from = 'You';
          avatar = 'ME';
          time = '1 day ago';
          body = `<p>Dear Executive Team,</p>
                  <p>Please find attached the agenda for our upcoming quarterly business review meeting scheduled for next Monday at 9:00 AM.</p>
                  <p>The agenda includes:</p>
                  <ul>
                    <li>Q2 financial performance review</li>
                    <li>Project status updates</li>
                    <li>Resource allocation for Q3</li>
                    <li>Strategic initiatives discussion</li>
                  </ul>
                  <p>Please review the materials before the meeting and come prepared with any questions or concerns.</p>
                  <p>Best regards,<br>Your Name</p>`;
          attachments = `<h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                        <div class="list-group">
                          <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                            <div>
                              <div>qbr_agenda.pdf</div>
                              <small class="text-muted">0.8 MB</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                          </a>
                          <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                            <div>
                              <div>q2_performance_summary.xlsx</div>
                              <small class="text-muted">2.2 MB</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-auto"><i class="bi bi-download"></i></button>
                          </a>
                        </div>`;
          break;
        default:
          subject = 'Email Subject';
          from = 'Sender Name';
          avatar = 'SN';
          time = 'Time';
          body = '<p>Email body content...</p>';
          attachments = '';
      }

      // Update the email content
      document.getElementById('read-email-subject').textContent = subject;
      document.getElementById('read-email-from').textContent = from;
      document.getElementById('read-email-avatar').textContent = avatar;
      document.getElementById('read-email-time').textContent = time;
      document.getElementById('read-email-body').innerHTML = body;
      document.getElementById('read-email-attachments').innerHTML = attachments;

      // Store the current email ID for reply functionality
      document.getElementById('read-email-tab').dataset.currentEmail = emailId;
    }

    function replyToEmail() {
      // Show the reply email tab
      const replyEmailTab = document.getElementById('reply-email-tab');
      replyEmailTab.style.display = 'block';
      replyEmailTab.click();

      // Get the current email details
      const subject = document.getElementById('read-email-subject').textContent;
      const from = document.getElementById('read-email-from').textContent;
      const avatar = document.getElementById('read-email-avatar').textContent;
      const time = document.getElementById('read-email-time').textContent;
      const body = document.getElementById('read-email-body').innerHTML;

      // Update the reply form
      document.getElementById('reply-email-subject').textContent = subject;
      document.getElementById('reply-email-from').textContent = from;
      document.getElementById('reply-email-avatar').textContent = avatar;
      document.getElementById('reply-email-time').textContent = time;
      document.getElementById('reply-email-original-body').innerHTML = body;

      // Focus on the reply textarea
      setTimeout(() => {
        document.getElementById('reply-email-body').focus();
      }, 500);
    }

    function sendEmail() {
      // Get the email details
      const to = document.getElementById('email-to').value;
      const subject = document.getElementById('email-subject').value;
      const body = document.getElementById('email-body').value;

      // Validate inputs
      if (!to || !subject || !body) {
        alert('Please fill in all required fields (To, Subject, and Message).');
        return;
      }

      // Simulate sending email
      alert('Email sent successfully!');

      // Clear the form
      document.getElementById('email-to').value = '';
      document.getElementById('email-cc').value = '';
      document.getElementById('email-subject').value = '';
      document.getElementById('email-body').value = '';
      document.getElementById('email-attachments').value = '';
      document.getElementById('attachment-list').innerHTML = '';

      // Return to inbox
      document.getElementById('inbox-tab').click();
    }

    function sendReply() {
      // Get the reply details
      const replyBody = document.getElementById('reply-email-body').value;

      // Validate input
      if (!replyBody) {
        alert('Please enter a reply message.');
        return;
      }

      // Simulate sending reply
      alert('Reply sent successfully!');

      // Clear the form
      document.getElementById('reply-email-body').value = '';
      document.getElementById('reply-email-attachments').value = '';
      document.getElementById('reply-attachment-list').innerHTML = '';

      // Return to inbox
      document.getElementById('inbox-tab').click();

      // Hide the read and reply tabs
      document.getElementById('read-email-tab').style.display = 'none';
      document.getElementById('reply-email-tab').style.display = 'none';
    }

    // Google Drive functionality
    document.getElementById('upload-new-file').addEventListener('click', function() {
      // Create a file input element
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.multiple = true;

      // Trigger click on the file input
      fileInput.click();

      // Handle file selection
      fileInput.addEventListener('change', function() {
        if (fileInput.files.length > 0) {
          // Simulate file upload
          setTimeout(() => {
            alert(`${fileInput.files.length} file(s) uploaded successfully!`);
          }, 1000);
        }
      });
    });

    // Google Docs functionality
    document.getElementById('create-new-document').addEventListener('click', function() {
      // Simulate creating a new document
      const docName = prompt('Enter a name for the new document:');
      if (docName) {
        alert(`New document "${docName}" created successfully!`);
      }
    });

    // Google Sheets functionality
    document.getElementById('create-new-spreadsheet').addEventListener('click', function() {
      // Simulate creating a new spreadsheet
      const sheetName = prompt('Enter a name for the new spreadsheet:');
      if (sheetName) {
        alert(`New spreadsheet "${sheetName}" created successfully!`);
      }
    });

    // Google Calendar functionality
    document.getElementById('create-new-event-modal').addEventListener('click', function() {
      // Simulate creating a new calendar event
      const eventDetails = {
        title: prompt('Event title:'),
        date: prompt('Event date (YYYY-MM-DD):'),
        startTime: prompt('Start time (HH:MM):'),
        endTime: prompt('End time (HH:MM):'),
        description: prompt('Event description:')
      };

      if (eventDetails.title && eventDetails.date) {
        alert(`New event "${eventDetails.title}" created successfully!`);
      }
    });

    // Function to open specific Google items
    function openGoogleItem(app, type, itemId) {
      // For general app buttons (without specific item), show the modal
      if (app === 'drive' && !type && !itemId) {
        const driveModal = new bootstrap.Modal(document.getElementById('driveModal'));
        driveModal.show();
        return;
      } else if (app === 'docs' && !type && !itemId) {
        const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
        docsModal.show();
        return;
      } else if (app === 'sheets' && !type && !itemId) {
        const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
        sheetsModal.show();
        return;
      } else if (app === 'attachments' && !type && !itemId) {
        const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
        attachmentsModal.show();
        return;
      }

      // For specific items, directly open the file in a viewer
      try {
        // Create a simulated file viewer
        const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

        // Set the file information
        const fileTitle = document.getElementById('fileViewerTitle');
        const fileContent = document.getElementById('fileViewerContent');

        // Format the item ID to make it more readable
        const readableId = itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        // Set the title based on the app and type
        fileTitle.innerHTML = `<i class="bi ${getFileIcon(app, type)}"></i> ${readableId}`;

        // Set the content based on the file type
        fileContent.innerHTML = getFileContent(app, type, itemId);

        // Show the modal
        const modal = new bootstrap.Modal(fileViewerModal);
        modal.show();
      } catch (error) {
        console.error('Error opening file:', error);
        alert('Could not open the file. Please try again later.');
      }
    }

    // Helper function to create a file viewer modal if it doesn't exist
    function createFileViewerModal() {
      const modal = document.createElement('div');
      modal.className = 'modal fade';
      modal.id = 'fileViewerModal';
      modal.tabIndex = '-1';
      modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
      modal.setAttribute('aria-hidden', 'true');

      modal.innerHTML = `
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header" style="background-color: var(--primary-color); color: white;">
              <h5 class="modal-title" id="fileViewerTitle"></h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">Download</button>
              <button type="button" class="btn btn-success" onclick="shareCurrentFile()">Share</button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      return modal;
    }

    // Helper function to get the appropriate icon for the file type
    function getFileIcon(app, type) {
      if (app === 'drive') {
        if (type === 'folder') return 'bi-folder-fill text-primary';
        if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
        if (type === 'image') return 'bi-file-earmark-image text-info';
        if (type === 'document') return 'bi-file-earmark-text text-primary';
        return 'bi-file-earmark text-secondary';
      } else if (app === 'docs') {
        return 'bi-file-earmark-text text-primary';
      } else if (app === 'sheets') {
        return 'bi-file-earmark-spreadsheet text-success';
      } else if (app === 'calendar') {
        return 'bi-calendar-event text-primary';
      }
      return 'bi-file-earmark text-secondary';
    }

    // Helper function to generate content for the file viewer
    function getFileContent(app, type, itemId) {
      // Generate simulated content based on file type
      if (app === 'drive') {
        if (type === 'folder') {
          return `<div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> This is a folder view. In a real application, this would show the contents of the folder.
                  </div>
                  <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action">
                      <i class="bi bi-file-earmark-text me-2"></i> Document 1.docx
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                      <i class="bi bi-file-earmark-spreadsheet me-2"></i> Spreadsheet 1.xlsx
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                      <i class="bi bi-file-earmark-pdf me-2"></i> Report.pdf
                    </a>
                  </div>`;
        } else if (type === 'pdf') {
          return `<div class="text-center">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                    <h4 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                    <div class="alert alert-info mt-3">
                      <i class="bi bi-info-circle"></i> This is a PDF viewer. In a real application, the PDF would be displayed here.
                    </div>
                    <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                      <h5>Document Preview</h5>
                      <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                      <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                    </div>
                  </div>`;
        } else if (type === 'document') {
          return `<div class="border p-3" style="background-color: #f8f9fa;">
                    <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                    <hr>
                    <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                    <p>The document contains information about task management procedures and guidelines.</p>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                  </div>`;
        }
      } else if (app === 'docs') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                  <hr>
                  <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                  <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                </div>`;
      } else if (app === 'sheets') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                  <hr>
                  <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                      <thead>
                        <tr>
                          <th>Task</th>
                          <th>Assignee</th>
                          <th>Due Date</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>Design UI mockups</td>
                          <td>John Smith</td>
                          <td>2025-06-15</td>
                          <td>In Progress</td>
                        </tr>
                        <tr>
                          <td>Implement login functionality</td>
                          <td>Sarah Johnson</td>
                          <td>2025-06-20</td>
                          <td>Planned</td>
                        </tr>
                        <tr>
                          <td>Database schema design</td>
                          <td>Michael Brown</td>
                          <td>2025-06-10</td>
                          <td>Completed</td>
                        </tr>
                        <tr>
                          <td>API documentation</td>
                          <td>Emily Davis</td>
                          <td>2025-06-25</td>
                          <td>Planned</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>`;
      } else if (app === 'calendar') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                  <hr>
                  <div class="row">
                    <div class="col-md-6">
                      <p><strong>Date:</strong> June 15, 2025</p>
                      <p><strong>Time:</strong> 10:00 AM - 11:30 AM</p>
                      <p><strong>Location:</strong> Conference Room A</p>
                    </div>
                    <div class="col-md-6">
                      <p><strong>Organizer:</strong> John Doe</p>
                      <p><strong>Attendees:</strong> 5</p>
                      <p><strong>Status:</strong> Confirmed</p>
                    </div>
                  </div>
                  <div class="mt-3">
                    <h5>Description</h5>
                    <p>This is a calendar event viewer. In a real application, the event details would be displayed here.</p>
                  </div>
                </div>`;
      }

      return `<div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
              </div>`;
    }

    // Function to download the current file
    function downloadCurrentFile() {
      const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
      alert(`Downloading ${fileTitle}...`);
      // In a real application, this would trigger a download
    }

    // Function to share the current file
    function shareCurrentFile() {
      const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
      const email = prompt(`Enter email address to share ${fileTitle} with:`);
      if (email) {
        alert(`${fileTitle} has been shared with ${email}.`);
        // In a real application, this would share the file with the specified email
      }
    }

    // Function to view attachments
    function viewAttachment(type, fileName) {
      // Create a file viewer modal if it doesn't exist
      const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

      // Set the file information
      const fileTitle = document.getElementById('fileViewerTitle');
      const fileContent = document.getElementById('fileViewerContent');

      // Set the title based on the file type and name
      fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;

      // Set the content based on the file type
      fileContent.innerHTML = getAttachmentContent(type, fileName);

      // Show the modal
      const modal = new bootstrap.Modal(fileViewerModal);
      modal.show();

      console.log(`Viewing ${fileName} directly`);
    }

    // Helper function to get the appropriate icon for the file type
    function getFileTypeIcon(type) {
      if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
      if (type === 'document') return 'bi-file-earmark-text text-primary';
      if (type === 'spreadsheet') return 'bi-file-earmark-spreadsheet text-success';
      if (type === 'image') return 'bi-file-earmark-image text-info';
      return 'bi-file-earmark text-secondary';
    }

    // Helper function to generate content for the attachment viewer
    function getAttachmentContent(type, fileName) {
      // Generate simulated content based on file type
      if (type === 'pdf') {
        return `<div class="text-center">
                  <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                  <h4 class="mt-3">${fileName}</h4>
                  <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                    <h5>Document Preview</h5>
                    <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                    <p>The document contains information related to task management.</p>
                  </div>
                </div>`;
      } else if (type === 'document') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${fileName}</h4>
                  <hr>
                  <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                  <p>The document contains information related to task management.</p>
                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                </div>`;
      } else if (type === 'spreadsheet') {
        return `<div class="border p-3" style="background-color: #f8f9fa;">
                  <h4>${fileName}</h4>
                  <hr>
                  <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                      <thead>
                        <tr>
                          <th>Task</th>
                          <th>Assignee</th>
                          <th>Due Date</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>Design UI mockups</td>
                          <td>John Smith</td>
                          <td>2025-06-15</td>
                          <td>In Progress</td>
                        </tr>
                        <tr>
                          <td>Implement login functionality</td>
                          <td>Sarah Johnson</td>
                          <td>2025-06-20</td>
                          <td>Planned</td>
                        </tr>
                        <tr>
                          <td>Database schema design</td>
                          <td>Michael Brown</td>
                          <td>2025-06-10</td>
                          <td>Completed</td>
                        </tr>
                        <tr>
                          <td>API documentation</td>
                          <td>Emily Davis</td>
                          <td>2025-06-25</td>
                          <td>Planned</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>`;
      } else if (type === 'image') {
        return `<div class="text-center">
                  <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                    <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                    <h5 class="mt-3">${fileName}</h5>
                    <p>This is an image viewer. In a real application, the image would be displayed here.</p>
                  </div>
                </div>`;
      }

      return `<div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
              </div>`;
    }

    // Function for downloading attachments
    function downloadAttachment(fileName) {
      alert(`Downloading ${fileName}...`);
      // In a real application, this would trigger a download
    }

    // Function for deleting attachments
    function deleteAttachment(fileName) {
      if (confirm(`Are you sure you want to delete ${fileName}?`)) {
        alert(`${fileName} has been deleted.`);
        // In a real application, this would delete the file
      }
    }

    // Add event listeners for create/upload buttons
    document.addEventListener('DOMContentLoaded', function() {
      // Google Drive upload button
      const uploadFileBtn = document.getElementById('upload-file');
      if (uploadFileBtn) {
        uploadFileBtn.addEventListener('click', function() {
          // This will open the Drive modal, which is already handled by Bootstrap
        });
      }

      // Google Docs create button
      const createNewDocBtn = document.getElementById('create-new-doc');
      if (createNewDocBtn) {
        createNewDocBtn.addEventListener('click', function() {
          // This will open the Docs modal, which is already handled by Bootstrap
        });
      }

      // Google Sheets create button
      const createNewSheetBtn = document.getElementById('create-new-sheet');
      if (createNewSheetBtn) {
        createNewSheetBtn.addEventListener('click', function() {
          // This will open the Sheets modal, which is already handled by Bootstrap
        });
      }

      // Google Calendar create button
      const createNewEventBtn = document.getElementById('create-new-event');
      if (createNewEventBtn) {
        createNewEventBtn.addEventListener('click', function() {
          // This will open the Calendar modal, which is already handled by Bootstrap
        });
      }
    });
  </script>
</body>
</html>
