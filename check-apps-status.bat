@echo off
setlocal enabledelayedexpansion

REM Set colors for console output
set "GREEN=0A"
set "YELLOW=0E"
set "RED=0C"
set "WHITE=0F"
set "CYAN=0B"

echo ===================================
color %CYAN%
echo    ISA Suite - Application Status
color %WHITE%
echo ===================================
echo.
echo Checking status of all ISA Suite applications...
echo.

REM Define application information - name|port
set "apps[1]=Integration Hub|8000"
set "apps[2]=Business Management System|3001"
set "apps[3]=Materials Requirements Planning|3002"
set "apps[4]=Customer Relationship Management|3003"
set "apps[5]=Warehouse Management System|3004"
set "apps[6]=Advanced Planning and Scheduling|3005"
set "apps[7]=Asset Performance Management|3006"
set "apps[8]=Project Management System|3007"
set "apps[9]=Supply Chain Management|3008"
set "apps[10]=Task Management System|3009"

REM Check status of each application
for /L %%i in (1,1,10) do (
    for /F "tokens=1-2 delims=|" %%a in ("!apps[%%i]!") do (
        REM Check if the port is in use (application is running)
        netstat -ano | findstr ":%%b" > nul
        if !errorlevel! equ 0 (
            color %GREEN%
            echo [ONLINE]  %%a (http://localhost:%%b)
        ) else (
            color %RED%
            echo [OFFLINE] %%a (http://localhost:%%b)
        )
    )
)

color %WHITE%
echo.
echo ===================================
echo.
echo To start all applications, run single-window-launcher.bat
echo To stop all applications, run stop-apps.bat
echo.
pause
