import { NextPage } from 'next';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import Head from 'next/head';

interface AppCard {
  id: string;
  name: string;
  description: string;
  url: string;
  icon: string;
  status: 'online' | 'offline' | 'loading';
}

const Home: NextPage = () => {
  const [apps, setApps] = useState<AppCard[]>([
    {
      id: 'bms',
      name: 'Business Management System',
      description: 'Business operations and management',
      url: 'http://localhost:3001',
      icon: '📊',
      status: 'loading'
    },
    {
      id: 'mrp',
      name: 'Materials Requirements Planning',
      description: 'Inventory and materials management',
      url: 'http://localhost:3002',
      icon: '📦',
      status: 'loading'
    },
    {
      id: 'crm',
      name: 'Customer Relationship Management',
      description: 'Customer data and interactions',
      url: 'http://localhost:3003',
      icon: '👥',
      status: 'loading'
    },
    {
      id: 'wms',
      name: 'Warehouse Management System',
      description: 'Warehouse operations and logistics',
      url: 'http://localhost:3004',
      icon: '🏭',
      status: 'loading'
    },
    {
      id: 'aps',
      name: 'Advanced Planning and Scheduling',
      description: 'Production planning and scheduling',
      url: 'http://localhost:3005',
      icon: '📅',
      status: 'loading'
    },
    {
      id: 'apm',
      name: 'Asset Performance Management',
      description: 'Equipment maintenance and reliability',
      url: 'http://localhost:3006',
      icon: '⚙️',
      status: 'loading'
    },
    {
      id: 'pms',
      name: 'Project Management System',
      description: 'Project planning and execution',
      url: 'http://localhost:3007',
      icon: '📝',
      status: 'loading'
    },
    {
      id: 'scm',
      name: 'Supply Chain Management',
      description: 'Supply chain visibility and optimization',
      url: 'http://localhost:3008',
      icon: '🔄',
      status: 'loading'
    },
    {
      id: 'tm',
      name: 'Task Management System',
      description: 'Task assignment and tracking',
      url: 'http://localhost:3009',
      icon: '✓',
      status: 'loading'
    }
  ]);

  // Check application status
  useEffect(() => {
    const checkAppStatus = async () => {
      const updatedApps = [...apps];
      
      for (let i = 0; i < updatedApps.length; i++) {
        try {
          const response = await fetch(`${updatedApps[i].url}/api/health`, { 
            method: 'GET',
            mode: 'no-cors',
            cache: 'no-cache',
            headers: {
              'Content-Type': 'application/json'
            },
          });
          updatedApps[i].status = 'online';
        } catch (error) {
          updatedApps[i].status = 'offline';
        }
      }
      
      setApps(updatedApps);
    };
    
    checkAppStatus();
    const interval = setInterval(checkAppStatus, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <Head>
        <title>ISA Suite Hub - Dashboard</title>
        <meta name="description" content="ISA Suite Integration Hub Dashboard" />
      </Head>
      
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center',
        marginBottom: '30px'
      }}>
        <h1 style={{ fontSize: '2.5rem', color: '#333', marginBottom: '10px' }}>ISA Suite Hub</h1>
        <p style={{ fontSize: '1.2rem', color: '#666', maxWidth: '800px', textAlign: 'center' }}>
          Welcome to the Integrated Systems Administration Suite - Your central dashboard for manufacturing operations management
        </p>
      </div>
      
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', 
        gap: '20px',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {apps.map((app) => (
          <a 
            href={app.url} 
            target="_blank" 
            rel="noopener noreferrer"
            key={app.id}
            style={{ 
              textDecoration: 'none', 
              color: 'inherit' 
            }}
          >
            <div style={{ 
              border: '1px solid #e0e0e0', 
              borderRadius: '8px',
              padding: '20px',
              transition: 'transform 0.2s, box-shadow 0.2s',
              height: '100%',
              boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
              display: 'flex',
              flexDirection: 'column',
              backgroundColor: '#fff'
            }}>
              <div style={{ marginBottom: '5px', fontSize: '3rem', textAlign: 'center' }}>
                {app.icon}
              </div>
              <h2 style={{ fontSize: '1.3rem', margin: '10px 0', color: '#333', textAlign: 'center' }}>
                {app.name}
              </h2>
              <p style={{ margin: '0 0 15px 0', color: '#666', flexGrow: 1, fontSize: '0.9rem', textAlign: 'center' }}>
                {app.description}
              </p>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: 'auto'
              }}>
                <span style={{ 
                  display: 'inline-block',
                  width: '10px',
                  height: '10px',
                  borderRadius: '50%',
                  backgroundColor: app.status === 'online' ? '#28a745' : 
                                  app.status === 'offline' ? '#dc3545' : '#ffc107',
                  marginRight: '5px'
                }}></span>
                <span style={{ 
                  fontSize: '0.8rem',
                  color: app.status === 'online' ? '#28a745' : 
                         app.status === 'offline' ? '#dc3545' : '#ffc107'
                }}>
                  {app.status === 'online' ? 'Online' : 
                   app.status === 'offline' ? 'Offline' : 'Loading'}
                </span>
              </div>
            </div>
          </a>
        ))}
      </div>
      
      <footer style={{ 
        marginTop: '50px', 
        textAlign: 'center',
        color: '#666',
        fontSize: '0.8rem',
        padding: '20px'
      }}>
        <p>ISA Suite v1.0.0 &copy; 2025</p>
      </footer>
    </div>
  );
};

export default Home;
