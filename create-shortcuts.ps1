# Create Desktop Shortcuts for ISA Suite

# Function to create a desktop shortcut
function Create-Shortcut {
    param (
        [string]$Name,
        [string]$Target,
        [string]$Arguments,
        [string]$Description,
        [string]$IconLocation
    )
    
    $WshShell = New-Object -ComObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\$Name.lnk")
    $Shortcut.TargetPath = $Target
    $Shortcut.Arguments = $Arguments
    $Shortcut.Description = $Description
    if ($IconLocation) {
        $Shortcut.IconLocation = $IconLocation
    }
    $Shortcut.Save()
    
    Write-Host "Created shortcut: $Name" -ForegroundColor Green
}

# Create shortcut for all-in-one script
Create-Shortcut -Name "ISA Suite - Start and Open" `
    -Target "D:\ISASUITE\start-and-open.bat" `
    -Description "Start ISA Suite applications and open them in browser"

# Create shortcut for setup and run script
Create-Shortcut -Name "ISA Suite - Setup and Run" `
    -Target "powershell.exe" `
    -Arguments "-ExecutionPolicy Bypass -File `"D:\ISASUITE\setup-and-run.ps1`"" `
    -Description "Set up and run ISA Suite applications"

# Create shortcut for Integration Hub
Create-Shortcut -Name "ISA Suite - Integration Hub" `
    -Target "http://localhost:8000" `
    -Description "Open Integration Hub in default browser"

# Create shortcut for BMS
Create-Shortcut -Name "ISA Suite - Business Management System" `
    -Target "http://localhost:3001" `
    -Description "Open Business Management System in default browser"

# Create shortcut for CRM
Create-Shortcut -Name "ISA Suite - Customer Relationship Management" `
    -Target "http://localhost:3003" `
    -Description "Open Customer Relationship Management in default browser"

Write-Host "Desktop shortcuts created successfully!" -ForegroundColor Green
