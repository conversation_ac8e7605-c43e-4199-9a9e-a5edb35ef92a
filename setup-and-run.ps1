# Setup and Run Script for ISA Suite

# Function to check if a command exists
function Test-Command {
    param (
        [string]$Command
    )
    
    $exists = $null -ne (Get-Command $Command -ErrorAction SilentlyContinue)
    return $exists
}

# Verify portable Node.js exists
$portableNodePath = "C:\ISASUITE\PortableNodeJS"
if (-not (Test-Command "node")) {
    if (-not (Test-Path $portableNodePath)) {
        Write-Host "Error: Node.js is not installed and portable Node.js not found at $portableNodePath" -ForegroundColor Red
        exit 1
    }
    $env:PATH = "$portableNodePath;" + $env:PATH
}

# Use portable pnpm from local drive to install dependencies
Write-Host "Using portable pnpm for package management..." -ForegroundColor Yellow
Set-Location "C:\ISASUITE"  # Set correct working directory
& "C:\ISASUITE\PortableNodeJS\pnpm.CMD" install

# Store process IDs for cleanup
$script:processIds = @()

# Add logging setup
$logFile = "C:\ISASUITE\logs\$(Get-Date -Format 'yyyy-MM-dd')-setup.log"
$null = New-Item -ItemType Directory -Force -Path "C:\ISASUITE\logs"

function Write-Log {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timestamp - $Message" | Tee-Object -FilePath $logFile -Append
}

# Add configuration
$script:config = @{
    MaxRetries = 3
    HealthCheckTimeout = 30
    StartupDelay = 2
    Dependencies = @("express", "cors", "socket.io", "axios")
}

# Function to test app health
function Test-AppHealth {
    param (
        [string]$AppName,
        [string]$Url,
        [int]$Timeout = $script:config.HealthCheckTimeout
    )
    
    $elapsed = 0
    Write-Host "Checking $AppName health at $Url (timeout: ${Timeout}s)..." -ForegroundColor Yellow
    
    while ($elapsed -lt $Timeout) {
        try {
            $response = Invoke-WebRequest -Uri "$Url/health" -Method GET -TimeoutSec 1
            $health = $response.Content | ConvertFrom-Json
            
            if ($response.StatusCode -eq 200 -and $health.status -eq "ok") {
                Write-Host "$AppName is healthy!" -ForegroundColor Green
                return $true
            }
        } catch {
            if ($elapsed % 5 -eq 0) {
                Write-Log "Health check attempt failed: $_"
            }
            Start-Sleep -Seconds 1
            $elapsed++
        }
    }
    
    Write-Host "Error: $AppName health check failed" -ForegroundColor Red
    return $false
}

# Updated Install-AppDependencies with error handling and critical dependency verification
function Install-AppDependencies {
    param (
        [string]$AppName,
        [string]$AppPath
    )
    
    Write-Log "Installing dependencies for $AppName..."
    if (-not (Test-Path $AppPath)) {
        throw "App directory not found: $AppPath"
    }
    
    Set-Location $AppPath
    
    # Verify package.json exists
    if (-not (Test-Path "package.json")) {
        throw "package.json not found in $AppPath"
    }
    
    # Install dependencies
    $result = & "C:\ISASUITE\PortableNodeJS\pnpm.CMD" install 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Log "Error installing dependencies for $AppName"
        throw "Failed to install dependencies: $result"
    }
    
    # Only check for critical dependencies in app folders, not shared modules
    $appsWithCriticalDeps = @("BMS", "CRM", "MRP", "WMS", "APS", "APM", "PMS", "SCM", "TM", "hub")
    if ($appsWithCriticalDeps -contains $AppName) {
        $deps = @("express", "cors", "socket.io")
        foreach ($dep in $deps) {
            if (-not (Test-Path "node_modules/$dep")) {
                throw "Critical dependency not found: $dep"
            }
        }
        if (-not (Test-Path "node_modules")) {
            throw "node_modules directory not created for $AppName"
        }
    }
}

# Updated Start-App with retries
function Start-App {
    param (
        [string]$AppName,
        [string]$AppPath,
        [string]$Mode = "development",
        [int]$RetryCount = 3
    )
    
    for ($i = 1; $i -le $RetryCount; $i++) {
        Write-Log "Starting $AppName (Attempt $i of $RetryCount)..."
        $process = Start-Process -FilePath "cmd.exe" -ArgumentList "/c cd $AppPath && node index.js $Mode" -PassThru
        $script:processIds += $process.Id
        
        # Wait briefly to check if process is still running
        Start-Sleep -Seconds 2
        if (Get-Process -Id $process.Id -ErrorAction SilentlyContinue) {
            return $process
        }
        
        Write-Log "Failed to start $AppName, retrying..."
        Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 2
    }
    
    throw "Failed to start $AppName after $RetryCount attempts"
}

# Closing brace added to fix the issue

# Closing brace added to fix the issue

# Updated Cleanup function
function Cleanup {
    Write-Log "Cleaning up processes..."
    foreach ($pid in $script:processIds) {
        try {
            $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
            if ($process) {
                $process.CloseMainWindow() | Out-Null
                Start-Sleep -Seconds 2
                if (!$process.HasExited) {
                    $process | Stop-Process -Force
                }
            }
        } catch {
            Write-Log ("Error cleaning up process " + $pid + ": " + $_.Exception.Message)
        }
    }
}

# Register cleanup on script exit
$null = Register-EngineEvent PowerShell.Exiting -Action { Cleanup } -SupportEvent

# Updated Initialize-ISASuite with try-catch
function Initialize-ISASuite {
    param (
        [string]$Mode = "development"
    )
    
    try {
        # Verify required directories exist
        $requiredDirs = @(
            "C:\ISASUITE\SharedFeatures",
            "C:\ISASUITE\apps\hub",
            "C:\ISASUITE\apps\BMS",
            "C:\ISASUITE\apps\CRM"
        )
        
        foreach ($dir in $requiredDirs) {
            if (-not (Test-Path $dir)) {
                Write-Host "Error: Required directory not found: $dir" -ForegroundColor Red
                exit 1
            }
        }
        
        # Set working directory to ISA Suite root
        Set-Location "C:\ISASUITE"
        
        foreach ($dir in $requiredDirs) {
            Install-AppDependencies -AppName (Split-Path $dir -Leaf) -AppPath $dir
        }
        
        Start-App -AppName "Integration Hub" -AppPath "C:\ISASUITE\apps\hub" -Mode $Mode
        Write-Log "Integration Hub started successfully."
        
        # Wait for Integration Hub with timeout
        if (-not (Test-AppHealth -AppName "Integration Hub" -Url "http://localhost:8000" -Timeout 30)) {
            Write-Host "Error: Integration Hub failed to start within 30 seconds" -ForegroundColor Red
            Cleanup
            exit 1
        }
        
        # Start other apps with error handling
        try {
            Start-App -AppName "Business Management System" -AppPath "C:\ISASUITE\apps\BMS" -Mode $Mode
            Start-App -AppName "Customer Relationship Management" -AppPath "C:\ISASUITE\apps\CRM" -Mode $Mode
        } catch {
            Write-Log "Error starting applications: $_"
            Cleanup
            exit 1
        }
        
        # Return to ISA Suite root
        Set-Location "C:\ISASUITE"
        
        Write-Host "ISA Suite is now running!" -ForegroundColor Green
        Write-Host "Integration Hub: http://localhost:8000" -ForegroundColor Cyan
        Write-Host "Business Management System: http://localhost:3001" -ForegroundColor Cyan
        Write-Host "Customer Relationship Management: http://localhost:3003" -ForegroundColor Cyan
        
    } catch {
        Write-Log "Fatal error: $_"
        Cleanup
        exit 1
    }
}

# Validate application mode
function Test-ValidMode {
    param (
        [string]$Mode
    )
    
    $validModes = @("development", "production", "sandbox", "demo")
    return $Mode -in $validModes
}

# Ask user which mode to run in
$modeOptions = @("development", "production", "sandbox", "demo")
$modeIndex = 0

Write-Host "Select application mode:" -ForegroundColor Yellow
for ($i = 0; $i -lt $modeOptions.Length; $i++) {
    if ($i -eq $modeIndex) {
        Write-Host "[$i] $($modeOptions[$i]) (default)" -ForegroundColor Green
    } else {
        Write-Host "[$i] $($modeOptions[$i])"
    }
}

$selectedMode = $null
do {
    $userInput = Read-Host "Enter mode number (default: $modeIndex)"
    if ([string]::IsNullOrEmpty($userInput)) {
        $selectedMode = $modeIndex
        break
    }
    elseif ($userInput -match '^\d+$' -and [int]$userInput -ge 0 -and [int]$userInput -lt $modeOptions.Length) {
        $selectedMode = [int]$userInput
        break
    }
    else {
        Write-Host "Invalid input. Please enter a number between 0 and $($modeOptions.Length - 1)." -ForegroundColor Red
    }
} while ($true)

$selectedMode = $modeOptions[$selectedMode]
if (-not (Test-ValidMode $selectedMode)) {
    Write-Log "Invalid mode selected: $selectedMode"
    Write-Host "Invalid mode selected. Defaulting to development." -ForegroundColor Yellow
    $selectedMode = "development"
}

$env:NODE_ENV = $selectedMode
Write-Host "Running in $selectedMode mode" -ForegroundColor Green

# Run setup
Initialize-ISASuite -Mode $selectedMode
