// Quick Gmail Dropdown Validation - Copy/Paste into Browser Console
// Run this at http://localhost:3002 to test Gmail modal dropdown functionality

console.clear();
console.log('🚀 QUICK GMAIL DROPDOWN TEST');
console.log('============================');

// Step 1: Test modal opening
console.log('\n1️⃣ Testing Gmail Modal...');
try {
    const gmailModal = document.getElementById('mrp-gmailModal');
    if (gmailModal) {
        console.log('✅ Gmail modal found');
        
        // Check if Bootstrap modal is available
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modalInstance = new bootstrap.Modal(gmailModal);
            modalInstance.show();
            console.log('✅ Modal opened successfully');
            
            // Wait a moment then test dropdowns
            setTimeout(() => {
                console.log('\n2️⃣ Testing Dropdowns...');
                
                // Test sort dropdown
                const sortBtn = document.getElementById('sort-dropdown');
                if (sortBtn) {
                    console.log('✅ Sort dropdown button found');
                    sortBtn.click();
                    console.log('📝 Sort dropdown clicked');
                } else {
                    console.log('❌ Sort dropdown button not found');
                }
                
                // Test filter dropdown
                const filterBtn = document.getElementById('filter-dropdown');
                if (filterBtn) {
                    console.log('✅ Filter dropdown button found');
                    filterBtn.click();
                    console.log('📝 Filter dropdown clicked');
                } else {
                    console.log('❌ Filter dropdown button not found');
                }
                
                // Test dropdown options
                setTimeout(() => {
                    const sortOptions = document.querySelectorAll('.sort-option');
                    const filterOptions = document.querySelectorAll('.filter-option');
                    
                    console.log(`✅ Found ${sortOptions.length} sort options`);
                    console.log(`✅ Found ${filterOptions.length} filter options`);
                    
                    if (sortOptions.length > 0) {
                        sortOptions[0].click();
                        console.log('📝 First sort option clicked');
                    }
                    
                    if (filterOptions.length > 0) {
                        filterOptions[0].click();
                        console.log('📝 First filter option clicked');
                    }
                    
                    console.log('\n3️⃣ Testing Debug Object...');
                    if (window.mrpGmailDropdowns) {
                        console.log('✅ Debug object available:', window.mrpGmailDropdowns);
                        console.log('📊 Debug stats:', window.mrpGmailDropdowns.getStats());
                    } else {
                        console.log('❌ Debug object not found');
                    }
                    
                    console.log('\n🎉 TEST COMPLETE!');
                    console.log('Try manually clicking the dropdown buttons to verify functionality.');
                    
                }, 1000);
            }, 1000);
            
        } else {
            console.log('❌ Bootstrap Modal not available');
        }
    } else {
        console.log('❌ Gmail modal not found');
    }
} catch (error) {
    console.error('❌ Error during test:', error);
}
