[{"C:\\ISASUITE\\apps\\hub\\pages\\api\\apps\\index.ts": "1", "C:\\ISASUITE\\apps\\hub\\pages\\api\\auth\\[...nextauth].ts": "2", "C:\\ISASUITE\\apps\\hub\\pages\\api\\health\\index.ts": "3", "C:\\ISASUITE\\apps\\hub\\pages\\api\\integration\\index.ts": "4", "C:\\ISASUITE\\apps\\hub\\pages\\api\\metrics\\index.ts": "5", "C:\\ISASUITE\\apps\\hub\\pages\\dashboard\\index.tsx": "6", "C:\\ISASUITE\\apps\\hub\\pages\\index.tsx": "7", "C:\\ISASUITE\\apps\\hub\\components\\dashboard\\AppGrid.tsx": "8", "C:\\ISASUITE\\apps\\hub\\components\\dashboard\\SystemMetrics.tsx": "9", "C:\\ISASUITE\\apps\\hub\\components\\layouts\\DashboardLayout.tsx": "10", "C:\\ISASUITE\\apps\\hub\\lib\\mongodb.ts": "11", "C:\\ISASUITE\\apps\\hub\\lib\\monitor.ts": "12", "C:\\ISASUITE\\apps\\hub\\lib\\Untitled-1.ts": "13"}, {"size": 1746, "mtime": 1746397743402, "results": "14", "hashOfConfig": "15"}, {"size": 1592, "mtime": 1746397743418, "results": "16", "hashOfConfig": "15"}, {"size": 1510, "mtime": 1746397743418, "results": "17", "hashOfConfig": "15"}, {"size": 720, "mtime": 1746397743434, "results": "18", "hashOfConfig": "15"}, {"size": 2055, "mtime": 1746397743454, "results": "19", "hashOfConfig": "15"}, {"size": 1241, "mtime": 1746397743454, "results": "20", "hashOfConfig": "15"}, {"size": 6516, "mtime": 1746484273230, "results": "21", "hashOfConfig": "15"}, {"size": 1725, "mtime": 1746397743292, "results": "22", "hashOfConfig": "15"}, {"size": 1963, "mtime": 1746397743307, "results": "23", "hashOfConfig": "15"}, {"size": 1471, "mtime": 1746397743323, "results": "24", "hashOfConfig": "15"}, {"size": 5844, "mtime": 1746397743357, "results": "25", "hashOfConfig": "15"}, {"size": 377, "mtime": 1746397743361, "results": "26", "hashOfConfig": "15"}, {"size": 3074, "mtime": 1746397743377, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1dkueuh", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\ISASUITE\\apps\\hub\\pages\\api\\apps\\index.ts", ["67"], [], "C:\\ISASUITE\\apps\\hub\\pages\\api\\auth\\[...nextauth].ts", [], [], "C:\\ISASUITE\\apps\\hub\\pages\\api\\health\\index.ts", ["68"], [], "C:\\ISASUITE\\apps\\hub\\pages\\api\\integration\\index.ts", ["69"], [], "C:\\ISASUITE\\apps\\hub\\pages\\api\\metrics\\index.ts", ["70"], [], "C:\\ISASUITE\\apps\\hub\\pages\\dashboard\\index.tsx", ["71"], [], "C:\\ISASUITE\\apps\\hub\\pages\\index.tsx", ["72"], [], "C:\\ISASUITE\\apps\\hub\\components\\dashboard\\AppGrid.tsx", ["73"], [], "C:\\ISASUITE\\apps\\hub\\components\\dashboard\\SystemMetrics.tsx", ["74"], [], "C:\\ISASUITE\\apps\\hub\\components\\layouts\\DashboardLayout.tsx", ["75"], [], "C:\\ISASUITE\\apps\\hub\\lib\\mongodb.ts", ["76"], [], "C:\\ISASUITE\\apps\\hub\\lib\\monitor.ts", [], [], "C:\\ISASUITE\\apps\\hub\\lib\\Untitled-1.ts", ["77"], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "78", "line": 38, "column": 42, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "78", "line": 7, "column": 42, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "78", "line": 16, "column": 36, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "78", "line": 7, "column": 42, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "79", "line": 21, "column": 12, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "80", "line": 6, "column": 1, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "80", "line": 4, "column": 1, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "80", "line": 3, "column": 1, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "80", "line": 5, "column": 1, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "80", "line": 12, "column": 1, "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "80", "line": 9, "column": 1, "nodeType": null}, "Parsing error: Unexpected token :", "Parsing error: Unexpected token <", "Parsing error: The keyword 'interface' is reserved"]