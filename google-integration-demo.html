<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Google Integration Demo</title>
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
  <style>
    body {
      padding: 20px;
      background-color: #f8f9fa;
    }
    .header {
      margin-bottom: 30px;
      text-align: center;
    }
    .google-integration-component {
      border: 1px solid #ddd;
      border-radius: 8px;
      margin-bottom: 20px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .component-header {
      padding: 15px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .component-header h3 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
    .component-body {
      padding: 15px;
      min-height: 100px;
    }
    .component-footer {
      padding: 10px 15px;
      border-top: 1px solid #eee;
      text-align: right;
    }
    .loading {
      text-align: center;
      color: #666;
      padding: 20px;
    }
    .placeholder {
      text-align: center;
      color: #999;
      padding: 20px;
    }
    .files-container, .events-container, .docs-container {
      max-height: 300px;
      overflow-y: auto;
    }
    .error {
      color: #dc3545;
      text-align: center;
      padding: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Google Integration Demo</h1>
      <p class="lead">This page demonstrates the Google integration UI components</p>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <!-- Google Drive Component -->
        <div class="google-integration-component google-drive-component">
          <div class="component-header">
            <h3><i class="fas fa-folder"></i> Google Drive</h3>
            <div class="component-actions">
              <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="fas fa-sync"></i></button>
              <button id="upload-drive" class="btn btn-sm btn-outline-success"><i class="fas fa-upload"></i></button>
            </div>
          </div>
          <div class="component-body">
            <div class="input-group mb-3">
              <input type="text" id="drive-search" class="form-control" placeholder="Search files...">
              <div class="input-group-append">
                <button id="drive-search-btn" class="btn btn-outline-secondary"><i class="fas fa-search"></i></button>
              </div>
            </div>
            <div id="drive-files-container" class="files-container">
              <div class="loading">Loading files...</div>
            </div>
          </div>
          <div class="component-footer">
            <a href="https://drive.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Drive</a>
          </div>
        </div>
        
        <!-- Google Calendar Component -->
        <div class="google-integration-component google-calendar-component">
          <div class="component-header">
            <h3><i class="fas fa-calendar-alt"></i> Google Calendar</h3>
            <div class="component-actions">
              <button id="refresh-calendar" class="btn btn-sm btn-outline-primary"><i class="fas fa-sync"></i></button>
              <button id="add-event" class="btn btn-sm btn-outline-success"><i class="fas fa-plus"></i></button>
            </div>
          </div>
          <div class="component-body">
            <div class="date-selector mb-3">
              <button id="prev-week" class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-left"></i></button>
              <span id="date-range">This Week</span>
              <button id="next-week" class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-right"></i></button>
            </div>
            <div id="calendar-events-container" class="events-container">
              <div class="placeholder">No events found</div>
            </div>
          </div>
          <div class="component-footer">
            <a href="https://calendar.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Calendar</a>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <!-- Google Gmail Component -->
        <div class="google-integration-component google-gmail-component">
          <div class="component-header">
            <h3><i class="fas fa-envelope"></i> Gmail</h3>
            <div class="component-actions">
              <button id="compose-email" class="btn btn-sm btn-outline-success"><i class="fas fa-pen"></i> Compose</button>
            </div>
          </div>
          <div class="component-body">
            <div id="email-compose-form" class="email-compose-form">
              <div class="form-group">
                <input type="text" id="email-to" class="form-control" placeholder="To">
              </div>
              <div class="form-group">
                <input type="text" id="email-subject" class="form-control" placeholder="Subject">
              </div>
              <div class="form-group">
                <textarea id="email-body" class="form-control" rows="5" placeholder="Message"></textarea>
              </div>
              <div class="form-group">
                <button id="send-email" class="btn btn-primary">Send</button>
                <button id="cancel-email" class="btn btn-secondary">Cancel</button>
              </div>
            </div>
          </div>
          <div class="component-footer">
            <a href="https://mail.google.com" target="_blank" class="btn btn-link btn-sm">Open in Gmail</a>
          </div>
        </div>
        
        <!-- Google Maps Component -->
        <div class="google-integration-component google-maps-component">
          <div class="component-header">
            <h3><i class="fas fa-map-marker-alt"></i> Google Maps</h3>
          </div>
          <div class="component-body">
            <div class="input-group mb-3">
              <input type="text" id="location-search" class="form-control" placeholder="Search location...">
              <div class="input-group-append">
                <button id="search-location" class="btn btn-outline-secondary"><i class="fas fa-search"></i></button>
              </div>
            </div>
            <div id="map-container" class="map-container" style="height: 300px;">
              <div class="placeholder">Search for a location to display the map</div>
            </div>
          </div>
          <div class="component-footer">
            <a href="https://maps.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Maps</a>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-12">
        <!-- Google Translate Component -->
        <div class="google-integration-component google-translate-component">
          <div class="component-header">
            <h3><i class="fas fa-language"></i> Google Translate</h3>
          </div>
          <div class="component-body">
            <div class="row">
              <div class="col-md-5">
                <select id="source-language" class="form-control mb-2">
                  <option value="">Detect language</option>
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="it">Italian</option>
                  <option value="ja">Japanese</option>
                  <option value="ko">Korean</option>
                  <option value="zh">Chinese</option>
                </select>
                <textarea id="source-text" class="form-control" rows="5" placeholder="Enter text to translate"></textarea>
              </div>
              <div class="col-md-2 text-center my-auto">
                <button id="translate-btn" class="btn btn-primary"><i class="fas fa-exchange-alt"></i></button>
              </div>
              <div class="col-md-5">
                <select id="target-language" class="form-control mb-2">
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="it">Italian</option>
                  <option value="ja">Japanese</option>
                  <option value="ko">Korean</option>
                  <option value="zh">Chinese</option>
                </select>
                <textarea id="target-text" class="form-control" rows="5" placeholder="Translation will appear here" readonly></textarea>
              </div>
            </div>
          </div>
          <div class="component-footer">
            <a href="https://translate.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Translate</a>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  <script>
    // Mock data for demo purposes
    const mockDriveFiles = [
      { id: '1', name: 'Project Proposal.docx', mimeType: 'application/vnd.google-apps.document', webViewLink: 'https://docs.google.com' },
      { id: '2', name: 'Budget Spreadsheet.xlsx', mimeType: 'application/vnd.google-apps.spreadsheet', webViewLink: 'https://sheets.google.com' },
      { id: '3', name: 'Project Images', mimeType: 'application/vnd.google-apps.folder', webViewLink: 'https://drive.google.com' },
      { id: '4', name: 'Presentation.pptx', mimeType: 'application/vnd.google-apps.presentation', webViewLink: 'https://slides.google.com' }
    ];
    
    // Initialize components
    document.addEventListener('DOMContentLoaded', function() {
      // Mock Drive files
      setTimeout(() => {
        const container = document.getElementById('drive-files-container');
        if (container) {
          const filesList = document.createElement('ul');
          filesList.className = 'list-group';
          
          mockDriveFiles.forEach(file => {
            const item = document.createElement('li');
            item.className = 'list-group-item d-flex justify-content-between align-items-center';
            
            const icon = file.mimeType.includes('folder') ? 'folder' : 'file';
            
            item.innerHTML = `
              <div>
                <i class="fas fa-${icon} mr-2"></i>
                <a href="${file.webViewLink}" target="_blank">${file.name}</a>
              </div>
              <div>
                <button class="btn btn-sm btn-outline-secondary file-action" data-action="share" data-id="${file.id}">
                  <i class="fas fa-share-alt"></i>
                </button>
              </div>
            `;
            
            filesList.appendChild(item);
          });
          
          container.innerHTML = '';
          container.appendChild(filesList);
        }
      }, 1000);
      
      // Set up event listeners
      document.getElementById('compose-email').addEventListener('click', function() {
        document.getElementById('email-compose-form').style.display = 'block';
      });
      
      document.getElementById('cancel-email').addEventListener('click', function() {
        document.getElementById('email-compose-form').style.display = 'none';
      });
      
      document.getElementById('translate-btn').addEventListener('click', function() {
        const sourceText = document.getElementById('source-text').value;
        const targetLanguage = document.getElementById('target-language').value;
        
        // Mock translation
        document.getElementById('target-text').value = `[Translated to ${targetLanguage}] ${sourceText}`;
      });
    });
  </script>
</body>
</html>
