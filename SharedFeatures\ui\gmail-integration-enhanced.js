/**
 * Enhanced Shared Gmail Integration Module for ISA Suite
 *
 * This module provides a standardized implementation of Gmail functionality
 * that can be used across all applications in the ISA Suite.
 *
 * Features:
 * - Consistent modal structure and styling
 * - Email viewing, composing, replying, and forwarding
 * - Advanced search, sort, and filter functionality with highlighting
 * - Enhanced attachment handling with validation and progress
 * - Label management and color coding
 * - Toast notifications and error handling
 * - Responsive design and accessibility
 */

class GmailIntegration {
    constructor(options = {}) {
        // Default configuration
        this.config = {
            appName: 'App',
            appPrefix: '',
            modalId: 'gmailModal',
            primaryColor: '#4285f4', // Default Gmail blue
            secondaryColor: '#ea4335', // Default Gmail red
            debug: false,
            // Enhanced label configuration
            labels: [
                { id: 'clients', name: 'Clients', icon: 'people', count: 0, color: '#28a745' },
                { id: 'urgent', name: 'Urgent', icon: 'exclamation-triangle', count: 0, color: '#dc3545' },
                { id: 'followup', name: 'Follow-up', icon: 'clock', count: 0, color: '#ffc107' },
                { id: 'team', name: 'Team', icon: 'people-fill', count: 0, color: '#17a2b8' }
            ],
            // Enhanced colors based on app type
            colors: {
                primary: '#4285f4',
                secondary: '#6c757d',
                success: '#28a745',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                light: '#f8f9fa',
                dark: '#343a40'
            },
            ...options
        };

        // Set modal ID with app prefix if provided
        if (this.config.appPrefix && !this.config.modalId.startsWith(this.config.appPrefix)) {
            this.config.modalId = `${this.config.appPrefix}-${this.config.modalId}`;
        }

        // Merge colors if app-specific colors provided
        if (options.colors) {
            this.config.colors = { ...this.config.colors, ...options.colors };
        }

        // Use app primary color as Gmail primary if provided
        if (options.colors && options.colors.primary) {
            this.config.primaryColor = options.colors.primary;
        }

        this.initialized = false;
        this.debug = this.config.debug;
        this.currentSort = 'date-desc';
        this.currentFilter = 'all';
        this.searchTerm = '';
    }

    /**
     * Initialize the Gmail integration
     */
    init() {
        if (this.initialized) {
            this.log('Gmail integration already initialized');
            return;
        }

        this.log('Initializing Gmail integration');

        // Create the modal if it doesn't exist
        if (!document.getElementById(this.config.modalId)) {
            this.createModal();
        }

        // Enhance existing emails with data attributes
        this.enhanceEmailData();

        // Add event listeners
        this.setupEventListeners();

        // Fix any UI issues
        this.fixUIIssues();

        // Make the handler globally accessible
        window.gmailIntegration = this;

        this.initialized = true;
        this.log('Gmail integration initialized successfully');
    }

    /**
     * Enhance existing email items with data attributes for better functionality
     */
    enhanceEmailData() {
        this.log('Enhancing email data attributes');

        // Find all email items across all tabs
        const emailItems = document.querySelectorAll(`#${this.config.modalId} .list-group-item`);
        
        emailItems.forEach((item, index) => {
            // Add data attributes if they don't exist
            if (!item.hasAttribute('data-sender')) {
                const sender = item.querySelector('h6, .fw-bold')?.textContent.trim() || `sender-${index + 1}`;
                item.setAttribute('data-sender', sender.toLowerCase().replace(/\s+/g, '-'));
            }

            if (!item.hasAttribute('data-subject')) {
                const subject = item.querySelector('p, .mb-1')?.textContent.trim() || `Subject ${index + 1}`;
                item.setAttribute('data-subject', subject);
            }

            if (!item.hasAttribute('data-date')) {
                const date = new Date();
                date.setDate(date.getDate() - index); // Simulate different dates
                item.setAttribute('data-date', date.toISOString());
            }

            if (!item.hasAttribute('data-label')) {
                // Assign labels based on existing badges or randomly
                const existingBadge = item.querySelector('.badge');
                let label = 'clients'; // default
                
                if (existingBadge) {
                    const badgeText = existingBadge.textContent.toLowerCase();
                    if (badgeText.includes('urgent')) label = 'urgent';
                    else if (badgeText.includes('follow')) label = 'followup';
                    else if (badgeText.includes('team')) label = 'team';
                    else if (badgeText.includes('client')) label = 'clients';
                } else {
                    // Assign random labels
                    const labels = this.config.labels;
                    label = labels[Math.floor(Math.random() * labels.length)].id;
                }
                
                item.setAttribute('data-label', label);

                // Add label badge if it doesn't exist
                if (!existingBadge) {
                    const labelObj = this.config.labels.find(l => l.id === label);
                    const labelBadge = document.createElement('span');
                    labelBadge.className = 'badge ms-2';
                    labelBadge.style.backgroundColor = labelObj.color;
                    labelBadge.textContent = labelObj.name;

                    const titleElement = item.querySelector('h6, .fw-bold');
                    if (titleElement) {
                        titleElement.appendChild(labelBadge);
                    }
                }
            }

            // Add unread class to some emails randomly
            if (Math.random() > 0.6) {
                item.classList.add('unread');
                item.style.backgroundColor = `rgba(${this.hexToRgb(this.config.primaryColor)}, 0.05)`;
                
                // Make sender and subject bold for unread emails
                const senderElement = item.querySelector('h6');
                const subjectElement = item.querySelector('p');
                if (senderElement) senderElement.classList.add('fw-bold');
                if (subjectElement) subjectElement.classList.add('fw-bold');
            }
        });
    }

    /**
     * Convert hex color to RGB values
     */
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? 
            `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : 
            '66, 133, 244'; // Default Gmail blue RGB
    }

    /**
     * Enhanced search emails with highlighting and better matching
     */
    searchEmails(searchTerm) {
        this.log('Searching emails for:', searchTerm);
        this.searchTerm = searchTerm;

        if (!searchTerm || searchTerm.trim() === '') {
            this.showAllEmails();
            return;
        }

        // Get active tab content
        const activeTab = document.querySelector(`#${this.config.modalId} .tab-pane.active`);
        if (!activeTab) return;

        const emailItems = activeTab.querySelectorAll('.list-group-item');
        if (!emailItems.length) return;

        // Normalize query
        const normalizedQuery = searchTerm.toLowerCase().trim();
        let visibleCount = 0;

        // Show/hide based on search query
        emailItems.forEach(item => {
            const sender = item.getAttribute('data-sender') || '';
            const subject = item.getAttribute('data-subject') || '';
            const content = item.querySelector('small, .text-muted:last-child')?.textContent.toLowerCase() || '';
            const senderText = item.querySelector('h6, .fw-bold')?.textContent.toLowerCase() || '';

            if (sender.includes(normalizedQuery) || 
                subject.toLowerCase().includes(normalizedQuery) || 
                content.includes(normalizedQuery) ||
                senderText.includes(normalizedQuery)) {
                item.style.display = '';
                visibleCount++;

                // Highlight matching text
                this.highlightText(item, normalizedQuery);
            } else {
                item.style.display = 'none';
            }
        });

        // Show toast with results count
        this.showToast(`Found ${visibleCount} email${visibleCount !== 1 ? 's' : ''} matching "${searchTerm}"`);

        // Show no results message if needed
        this.checkNoResults(activeTab, visibleCount);
    }

    /**
     * Enhanced sort emails with better date handling and visual feedback
     */
    sortEmails(sortType) {
        this.log('Sorting emails by:', sortType);
        this.currentSort = sortType;

        // Get the active tab content
        const activeTab = document.querySelector(`#${this.config.modalId} .tab-pane.active`);
        if (!activeTab) return;

        const emailList = activeTab.querySelector('.list-group');
        if (!emailList) return;

        // Get all email items
        const emailItems = Array.from(emailList.querySelectorAll('.list-group-item'));
        if (!emailItems.length) return;

        // Sort emails based on sort type
        emailItems.sort((a, b) => {
            switch (sortType) {
                case 'date-desc':
                    const dateA = a.getAttribute('data-date') || new Date().toISOString();
                    const dateB = b.getAttribute('data-date') || new Date().toISOString();
                    return dateB.localeCompare(dateA);
                case 'date-asc':
                    const dateC = a.getAttribute('data-date') || new Date().toISOString();
                    const dateD = b.getAttribute('data-date') || new Date().toISOString();
                    return dateC.localeCompare(dateD);
                case 'sender-asc':
                    const senderA = a.getAttribute('data-sender') || '';
                    const senderB = b.getAttribute('data-sender') || '';
                    return senderA.localeCompare(senderB);
                case 'sender-desc':
                    const senderC = a.getAttribute('data-sender') || '';
                    const senderD = b.getAttribute('data-sender') || '';
                    return senderD.localeCompare(senderC);
                case 'subject-asc':
                    const subjectA = a.getAttribute('data-subject') || '';
                    const subjectB = b.getAttribute('data-subject') || '';
                    return subjectA.toLowerCase().localeCompare(subjectB.toLowerCase());
                case 'subject-desc':
                    const subjectC = a.getAttribute('data-subject') || '';
                    const subjectD = b.getAttribute('data-subject') || '';
                    return subjectD.toLowerCase().localeCompare(subjectC.toLowerCase());
                default:
                    return 0;
            }
        });

        // Reorder the DOM elements
        emailItems.forEach(item => {
            emailList.appendChild(item);
        });

        // Update dropdown button text
        const dropdownButton = document.getElementById(`${this.config.appPrefix}-sort-dropdown`);
        if (dropdownButton) {
            const sortOptions = {
                'date-desc': 'Newest first',
                'date-asc': 'Oldest first',
                'sender-asc': 'Sender A-Z',
                'sender-desc': 'Sender Z-A',
                'subject-asc': 'Subject A-Z',
                'subject-desc': 'Subject Z-A'
            };
            dropdownButton.innerHTML = `<i class="bi bi-sort-down"></i> ${sortOptions[sortType] || 'Sort'}`;
        }

        // Show toast
        this.showToast(`Emails sorted by ${sortType.replace('-', ' ')}`);
    }

    /**
     * Enhanced filter emails with label support
     */
    filterEmails(filterType) {
        this.log('Filtering emails by:', filterType);
        this.currentFilter = filterType;

        // Get the active tab content
        const activeTab = document.querySelector(`#${this.config.modalId} .tab-pane.active`);
        if (!activeTab) return;

        // Get all email items
        const emailItems = activeTab.querySelectorAll('.list-group-item');
        if (!emailItems.length) return;

        let visibleCount = 0;

        // Show/hide based on filter
        emailItems.forEach(item => {
            let shouldShow = false;

            if (filterType === 'all') {
                shouldShow = true;
            } else if (filterType === 'unread') {
                shouldShow = item.classList.contains('unread');
            } else if (filterType === 'read') {
                shouldShow = !item.classList.contains('unread');
            } else if (filterType.startsWith('label-')) {
                const label = filterType.replace('label-', '');
                const itemLabel = item.getAttribute('data-label');
                shouldShow = itemLabel === label;
            }

            if (shouldShow) {
                item.style.display = '';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        // Update dropdown button text
        const dropdownButton = document.getElementById(`${this.config.appPrefix}-filter-dropdown`);
        if (dropdownButton) {
            const filterOptions = {
                'all': 'All emails',
                'unread': 'Unread',
                'read': 'Read',
                'label-clients': 'Clients',
                'label-urgent': 'Urgent',
                'label-followup': 'Follow-up',
                'label-team': 'Team'
            };
            dropdownButton.innerHTML = `<i class="bi bi-funnel"></i> ${filterOptions[filterType] || 'Filter'}`;
        }

        // Show toast
        this.showToast(`Showing ${visibleCount} email${visibleCount !== 1 ? 's' : ''} with filter: ${filterType}`);

        // Show no results message if needed
        this.checkNoResults(activeTab, visibleCount);
    }

    /**
     * Highlight matching text in email items
     */
    highlightText(item, query) {
        // Remove existing highlights
        item.querySelectorAll('.highlight').forEach(el => {
            const parent = el.parentNode;
            parent.replaceChild(document.createTextNode(el.textContent), el);
            parent.normalize();
        });

        // Highlight text in subject
        const subjectEl = item.querySelector('p, .mb-1');
        if (subjectEl) {
            this.highlightInElement(subjectEl, query);
        }

        // Highlight text in content
        const contentEl = item.querySelector('small, .text-muted:last-child');
        if (contentEl) {
            this.highlightInElement(contentEl, query);
        }

        // Highlight text in sender
        const senderEl = item.querySelector('h6, .fw-bold');
        if (senderEl) {
            this.highlightInElement(senderEl, query);
        }
    }

    /**
     * Highlight text in a specific element
     */
    highlightInElement(element, query) {
        const text = element.textContent;
        const normalizedText = text.toLowerCase();
        const index = normalizedText.indexOf(query);

        if (index >= 0) {
            const before = text.substring(0, index);
            const match = text.substring(index, index + query.length);
            const after = text.substring(index + query.length);

            element.innerHTML = before + '<span class="highlight" style="background-color: yellow; padding: 1px 2px; border-radius: 2px;">' + match + '</span>' + after;
        }
    }

    /**
     * Show all emails and remove filters/highlights
     */
    showAllEmails() {
        const activeTab = document.querySelector(`#${this.config.modalId} .tab-pane.active`);
        if (!activeTab) return;

        // Get all email items
        const emailItems = activeTab.querySelectorAll('.list-group-item');
        if (!emailItems.length) return;

        // Show all emails
        emailItems.forEach(item => {
            item.style.display = '';

            // Remove highlights
            item.querySelectorAll('.highlight').forEach(el => {
                const parent = el.parentNode;
                parent.replaceChild(document.createTextNode(el.textContent), el);
                parent.normalize();
            });
        });

        // Remove no results message
        this.removeNoResultsMessage(activeTab);
    }

    /**
     * Enhanced refresh Gmail functionality
     */
    refreshGmail() {
        this.log('Refreshing Gmail content');

        // Reset search term and filters
        this.searchTerm = '';
        this.currentFilter = 'all';
        this.currentSort = 'date-desc';

        // Reset search input
        const searchInput = document.getElementById(`${this.config.appPrefix}-email-search`);
        if (searchInput) {
            searchInput.value = '';
        }

        // Reset sort dropdown
        const sortDropdown = document.getElementById(`${this.config.appPrefix}-sort-dropdown`);
        if (sortDropdown) {
            sortDropdown.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
        }

        // Reset filter dropdown
        const filterDropdown = document.getElementById(`${this.config.appPrefix}-filter-dropdown`);
        if (filterDropdown) {
            filterDropdown.innerHTML = '<i class="bi bi-funnel"></i> Filter';
        }

        // Show all emails
        this.showAllEmails();

        // Show success message
        this.showToast('Emails refreshed successfully');
    }

    /**
     * Check and show/hide no results message
     */
    checkNoResults(container, visibleCount = null) {
        if (!container) return;

        if (visibleCount === null) {
            const visibleItems = container.querySelectorAll('.list-group-item:not([style*="display: none"])');
            visibleCount = visibleItems.length;
        }

        if (visibleCount === 0) {
            this.showNoResultsMessage(container);
        } else {
            this.removeNoResultsMessage(container);
        }
    }

    /**
     * Show no results message
     */
    showNoResultsMessage(container) {
        // Remove existing message
        this.removeNoResultsMessage(container);

        const message = document.createElement('div');
        message.className = 'no-results-message text-center p-4';
        message.innerHTML = `
            <div class="text-muted">
                <i class="bi bi-inbox display-4 d-block mb-3"></i>
                <h5>No emails found</h5>
                <p>Try adjusting your search terms or filters.</p>
            </div>
        `;

        // Add the message to the container
        const emailList = container.querySelector('.list-group');
        if (emailList) {
            emailList.parentNode.insertBefore(message, emailList.nextSibling);
        }
    }

    /**
     * Remove no results message
     */
    removeNoResultsMessage(container) {
        const message = container.querySelector('.no-results-message');
        if (message) {
            message.remove();
        }
    }

    /**
     * Enhanced toast notification system
     */
    showToast(message, type = 'success') {
        // Use the ISADataUtils.showToast function if available
        if (window.ISADataUtils && typeof window.ISADataUtils.showToast === 'function') {
            window.ISADataUtils.showToast(message, type);
            return;
        }

        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        // Create a unique ID for this toast
        const toastId = 'toast-' + Date.now();

        // Determine icon and title based on type
        let icon, title, bgColor;
        switch (type) {
            case 'success':
                icon = 'check-circle-fill text-success';
                title = 'Success';
                bgColor = 'bg-light';
                break;
            case 'danger':
            case 'error':
                icon = 'exclamation-circle-fill text-danger';
                title = 'Error';
                bgColor = 'bg-light';
                break;
            case 'warning':
                icon = 'exclamation-triangle-fill text-warning';
                title = 'Warning';
                bgColor = 'bg-light';
                break;
            case 'info':
                icon = 'info-circle-fill text-info';
                title = 'Information';
                bgColor = 'bg-light';
                break;
            default:
                icon = 'bell-fill';
                title = 'Notification';
                bgColor = 'bg-light';
                break;
        }

        // Create toast element
        const toastHtml = `
            <div id="${toastId}" class="toast ${bgColor}" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi bi-${icon} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <small>Just now</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        // Add toast to container
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Initialize and show the toast
        const toastElement = document.getElementById(toastId);
        if (window.bootstrap && window.bootstrap.Toast) {
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // Remove toast from DOM after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function () {
                toastElement.remove();
            });
        }
    }

    /**
     * Setup event listeners with enhanced functionality
     */
    setupEventListeners() {
        this.log('Setting up enhanced event listeners');

        // Search functionality
        const searchInput = document.getElementById(`${this.config.appPrefix}-email-search`);
        const searchBtn = document.getElementById(`${this.config.appPrefix}-search-btn`);

        if (searchInput && searchBtn) {
            // Search on button click
            searchBtn.addEventListener('click', () => {
                this.searchEmails(searchInput.value);
            });

            // Search on Enter key
            searchInput.addEventListener('keyup', (e) => {
                if (e.key === 'Enter') {
                    this.searchEmails(searchInput.value);
                }
            });
        }

        // Sort functionality
        document.querySelectorAll(`#${this.config.modalId} .sort-option`).forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                const sortType = e.target.getAttribute('data-sort');
                this.sortEmails(sortType);
            });
        });

        // Filter functionality
        document.querySelectorAll(`#${this.config.modalId} .filter-option`).forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                const filterType = e.target.getAttribute('data-filter');
                this.filterEmails(filterType);
            });
        });

        // Refresh button
        const refreshBtn = document.getElementById(`${this.config.appPrefix}-refresh-gmail`);
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshGmail();
            });
        }

        // Open Gmail button
        const openGmailBtn = document.getElementById(`${this.config.appPrefix}-open-gmail-btn`);
        if (openGmailBtn) {
            openGmailBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.openGmailModal();
            });
        }
    }

    /**
     * Open Gmail modal
     */
    openGmailModal() {
        this.log('Opening Gmail modal');

        const gmailModal = document.getElementById(this.config.modalId);
        if (!gmailModal) {
            console.error('Gmail modal not found');
            return;
        }

        // Use Bootstrap modal if available
        if (window.bootstrap && window.bootstrap.Modal) {
            const modal = new bootstrap.Modal(gmailModal);
            modal.show();
        } else {
            // Fallback to manual modal display
            gmailModal.style.display = 'block';
            gmailModal.classList.add('show');
        }
    }

    /**
     * Create modal (stub - will use existing modal structure)
     */
    createModal() {
        this.log('Enhanced Gmail integration will work with existing modal structure');
    }

    /**
     * Fix UI issues
     */
    fixUIIssues() {
        this.log('Fixing UI issues for enhanced Gmail integration');
        
        // Add any necessary CSS fixes
        const style = document.createElement('style');
        style.textContent = `
            .highlight {
                background-color: yellow !important;
                padding: 1px 2px;
                border-radius: 2px;
            }
            
            .no-results-message {
                margin: 20px 0;
            }
            
            .toast-container {
                z-index: 9999;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Log messages
     */
    log(message, data = null) {
        if (this.debug) {
            if (data) {
                console.log(`[Enhanced GmailIntegration] ${message}`, data);
            } else {
                console.log(`[Enhanced GmailIntegration] ${message}`);
            }
        }
    }
}

// Export the class for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GmailIntegration;
} else {
    window.GmailIntegration = GmailIntegration;
}
