const express = require('express');
const cors = require('cors');
const http = require('http');
const { Server } = require('socket.io');
const path = require('path');

// Create Express app
const app = express();
const server = http.createServer(app);
const io = new Server(server);

// Initialize in-memory notifications array
const notifications = [];

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Create public directory if it doesn't exist
const publicDir = path.join(__dirname, 'public');
if (!require('fs').existsSync(publicDir)) {
  require('fs').mkdirSync(publicDir, { recursive: true });
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Root endpoint
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API endpoints for application integration
app.get('/api/apps', async (req, res) => {
  const apps = [
    { id: 'hub', name: 'Integration Hub', url: 'http://localhost:8000', status: 'online' },
    { id: 'bms', name: 'Business Management System', url: 'http://localhost:3001', status: 'unknown' },
    { id: 'crm', name: 'Customer Relationship Management', url: 'http://localhost:3003', status: 'unknown' },
    { id: 'mrp', name: 'Materials Requirements Planning', url: 'http://localhost:3002', status: 'unknown' },
    { id: 'wms', name: 'Warehouse Management System', url: 'http://localhost:3004', status: 'unknown' },
    { id: 'aps', name: 'Advanced Planning and Scheduling', url: 'http://localhost:3005', status: 'unknown' },
    { id: 'apm', name: 'Asset Performance Management', url: 'http://localhost:3006', status: 'unknown' },
    { id: 'pms', name: 'Project Management System', url: 'http://localhost:3007', status: 'unknown' },
    { id: 'scm', name: 'Supply Chain Management', url: 'http://localhost:3008', status: 'unknown' },
    { id: 'tm', name: 'Task Management System', url: 'http://localhost:3009', status: 'unknown' }
  ];

  // Check status of each app
  for (const app of apps) {
    try {
      const response = await fetch(`${app.url}/health`, { timeout: 2000 }).catch(() => null);
      app.status = response && response.ok ? 'online' : 'offline';
    } catch (error) {
      app.status = 'offline';
    }
  }

  res.json(apps);
});

// API endpoint for cross-application data
app.get('/api/cross-app-data', async (req, res) => {
  try {
    // Collect data from multiple applications
    const bmsResponse = await fetch('http://localhost:3001/api/business-data').catch(() => null);
    const crmResponse = await fetch('http://localhost:3003/api/customers').catch(() => null);
    const mrpResponse = await fetch('http://localhost:3002/api/inventory').catch(() => null);
    const wmsResponse = await fetch('http://localhost:3004/api/warehouses').catch(() => null);
    const apsResponse = await fetch('http://localhost:3005/api/schedules').catch(() => null);
    const apmResponse = await fetch('http://localhost:3006/api/assets').catch(() => null);
    const pmsResponse = await fetch('http://localhost:3007/api/projects').catch(() => null);

    // Combine data
    const crossAppData = {
      businessData: bmsResponse && bmsResponse.ok ? await bmsResponse.json() : null,
      customerData: crmResponse && crmResponse.ok ? await crmResponse.json() : null,
      inventoryData: mrpResponse && mrpResponse.ok ? await mrpResponse.json() : null,
      warehouseData: wmsResponse && wmsResponse.ok ? await wmsResponse.json() : null,
      scheduleData: apsResponse && apsResponse.ok ? await apsResponse.json() : null,
      assetData: apmResponse && apmResponse.ok ? await apmResponse.json() : null,
      projectData: pmsResponse && pmsResponse.ok ? await pmsResponse.json() : null,
      timestamp: new Date().toISOString()
    };

    res.json(crossAppData);
  } catch (error) {
    console.error('Error fetching cross-application data:', error);
    res.status(500).json({ error: 'Failed to fetch cross-application data' });
  }
});

// API endpoint for unified reporting
app.get('/api/unified-reports/:reportType', async (req, res) => {
  try {
    const { reportType } = req.params;
    let reportData = {};

    switch (reportType) {
      case 'inventory-status':
        // Combine data from MRP and WMS
        const mrpResponse = await fetch('http://localhost:3002/api/inventory').catch(() => null);
        const wmsResponse = await fetch('http://localhost:3004/api/warehouses').catch(() => null);

        reportData = {
          inventory: mrpResponse && mrpResponse.ok ? await mrpResponse.json() : null,
          warehouses: wmsResponse && wmsResponse.ok ? await wmsResponse.json() : null,
          timestamp: new Date().toISOString()
        };
        break;

      case 'production-planning':
        // Combine data from MRP, APS, and APM
        const mrpProdResponse = await fetch('http://localhost:3002/api/inventory').catch(() => null);
        const apsResponse = await fetch('http://localhost:3005/api/schedules').catch(() => null);
        const apmResponse = await fetch('http://localhost:3006/api/assets').catch(() => null);

        reportData = {
          inventory: mrpProdResponse && mrpProdResponse.ok ? await mrpProdResponse.json() : null,
          schedules: apsResponse && apsResponse.ok ? await apsResponse.json() : null,
          assets: apmResponse && apmResponse.ok ? await apmResponse.json() : null,
          timestamp: new Date().toISOString()
        };
        break;

      case 'project-status':
        // Combine data from PMS, CRM, and BMS
        const pmsResponse = await fetch('http://localhost:3007/api/projects').catch(() => null);
        const crmResponse = await fetch('http://localhost:3003/api/customers').catch(() => null);
        const bmsResponse = await fetch('http://localhost:3001/api/business-data').catch(() => null);

        reportData = {
          projects: pmsResponse && pmsResponse.ok ? await pmsResponse.json() : null,
          customers: crmResponse && crmResponse.ok ? await crmResponse.json() : null,
          business: bmsResponse && bmsResponse.ok ? await bmsResponse.json() : null,
          timestamp: new Date().toISOString()
        };
        break;

      default:
        return res.status(400).json({ error: 'Invalid report type' });
    }

    res.json(reportData);
  } catch (error) {
    console.error('Error generating unified report:', error);
    res.status(500).json({ error: 'Failed to generate unified report' });
  }
});

// API endpoint for system metrics
app.get('/api/metrics', (req, res) => {
  const metrics = {
    cpu: {
      usage: Math.floor(Math.random() * 30),
      cores: require('os').cpus().length
    },
    memory: {
      total: require('os').totalmem(),
      free: require('os').freemem(),
      used: require('os').totalmem() - require('os').freemem()
    },
    uptime: require('os').uptime(),
    timestamp: new Date().toISOString()
  };

  res.json(metrics);
});

// Socket.io connection
io.on('connection', (socket) => {
  console.log('Client connected');

  // Send initial data to the client
  socket.emit('metrics-update', {
    cpu: {
      usage: Math.floor(Math.random() * 30),
      cores: require('os').cpus().length
    },
    memory: {
      total: require('os').totalmem(),
      free: require('os').freemem(),
      used: require('os').totalmem() - require('os').freemem()
    },
    uptime: require('os').uptime(),
    timestamp: new Date().toISOString()
  });

  // Handle app status updates from clients
  socket.on('app-status-update', (data) => {
    console.log('App status update received:', data);
    // Broadcast the status update to all clients
    io.emit('app-status-change', data);
  });

  // Handle client messages
  socket.on('message', (data) => {
    console.log('Message received:', data);
    // Broadcast the message to all clients
    io.emit('message', data);
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

// Set up periodic metrics updates
setInterval(() => {
  const metrics = {
    cpu: {
      usage: Math.floor(Math.random() * 30),
      cores: require('os').cpus().length
    },
    memory: {
      total: require('os').totalmem(),
      free: require('os').freemem(),
      used: require('os').totalmem() - require('os').freemem()
    },
    uptime: require('os').uptime(),
    timestamp: new Date().toISOString()
  };

  io.emit('metrics-update', metrics);
}, 5000); // Update every 5 seconds

// Load shared features
let sharedFeatures;
try {
  sharedFeatures = require('../../SharedFeatures');
  console.log('Shared features loaded successfully');
} catch (error) {
  console.log('Shared features not available, continuing without them');
  sharedFeatures = null;
}

// API endpoint for notifications
app.post('/api/notifications', async (req, res) => {
  const notification = req.body;

  // Validate notification
  if (!notification.source || !notification.type || !notification.message) {
    return res.status(400).json({ error: 'Invalid notification format' });
  }

  // Add timestamp if not provided
  if (!notification.timestamp) {
    notification.timestamp = new Date().toISOString();
  }

  // Add unique ID if not provided
  if (!notification.id) {
    notification.id = `notif-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  }

  // Broadcast notification to all clients
  io.emit('notification', notification);

  // Store notification in memory for backward compatibility
  notifications.push(notification);
  if (notifications.length > 100) {
    notifications.shift(); // Keep only the last 100 notifications in memory
  }

  // Store notification in persistent storage if available
  if (sharedFeatures && sharedFeatures.notifications) {
    try {
      await sharedFeatures.notifications.sendNotification(notification);
      console.log(`Notification ${notification.id} stored in persistent storage`);
    } catch (error) {
      console.error('Error storing notification in persistent storage:', error);
      // Continue even if persistent storage fails
    }
  }

  res.status(201).json({ status: 'success', notification });
});

// API endpoint for retrieving notifications
app.get('/api/notifications', async (req, res) => {
  // Get query parameters
  const limit = parseInt(req.query.limit) || 20;
  const offset = parseInt(req.query.offset) || 0;
  const source = req.query.source;
  const type = req.query.type;
  const includeRead = req.query.includeRead === 'true';
  const includeDeleted = req.query.includeDeleted === 'true';

  // Use persistent storage if available
  if (sharedFeatures && sharedFeatures.notifications) {
    try {
      const options = {
        limit,
        offset,
        source,
        type,
        includeRead,
        includeDeleted
      };

      const storedNotifications = await sharedFeatures.notifications.getNotifications(options);
      const count = await sharedFeatures.notifications.getNotificationCount(options);

      res.json({
        status: 'success',
        count: storedNotifications.length,
        total: count,
        data: storedNotifications
      });
      return;
    } catch (error) {
      console.error('Error retrieving notifications from persistent storage:', error);
      // Fall back to in-memory notifications
    }
  }

  // Fall back to in-memory notifications
  let filteredNotifications = [...notifications];

  if (source) {
    filteredNotifications = filteredNotifications.filter(n => n.source === source);
  }

  if (type) {
    filteredNotifications = filteredNotifications.filter(n => n.type === type);
  }

  // Sort by timestamp (newest first)
  filteredNotifications.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  // Apply pagination
  filteredNotifications = filteredNotifications.slice(offset, offset + limit);

  res.json({
    status: 'success',
    count: filteredNotifications.length,
    total: notifications.length,
    data: filteredNotifications
  });
});

// API endpoint for marking a notification as read
app.put('/api/notifications/:id/read', async (req, res) => {
  const id = req.params.id;

  // Use persistent storage if available
  if (sharedFeatures && sharedFeatures.notifications) {
    try {
      const success = await sharedFeatures.notifications.markNotificationAsRead(id);

      if (success) {
        res.json({ status: 'success', message: `Notification ${id} marked as read` });
      } else {
        res.status(404).json({ status: 'error', message: `Notification ${id} not found` });
      }
      return;
    } catch (error) {
      console.error('Error marking notification as read in persistent storage:', error);
      // Fall back to in-memory notifications
    }
  }

  // Fall back to in-memory notifications
  const notification = notifications.find(n => n.id === id);

  if (notification) {
    notification.read = true;
    res.json({ status: 'success', message: `Notification ${id} marked as read` });
  } else {
    res.status(404).json({ status: 'error', message: `Notification ${id} not found` });
  }
});

// API endpoint for marking a notification as deleted
app.delete('/api/notifications/:id', async (req, res) => {
  const id = req.params.id;

  // Use persistent storage if available
  if (sharedFeatures && sharedFeatures.notifications) {
    try {
      const success = await sharedFeatures.notifications.markNotificationAsDeleted(id);

      if (success) {
        res.json({ status: 'success', message: `Notification ${id} marked as deleted` });
      } else {
        res.status(404).json({ status: 'error', message: `Notification ${id} not found` });
      }
      return;
    } catch (error) {
      console.error('Error marking notification as deleted in persistent storage:', error);
      // Fall back to in-memory notifications
    }
  }

  // Fall back to in-memory notifications
  const index = notifications.findIndex(n => n.id === id);

  if (index !== -1) {
    notifications.splice(index, 1);
    res.json({ status: 'success', message: `Notification ${id} deleted` });
  } else {
    res.status(404).json({ status: 'error', message: `Notification ${id} not found` });
  }
});

// AI-powered analytics endpoints
app.get('/api/ai-analytics/inventory-optimization', async (req, res) => {
  try {
    // Fetch inventory data from MRP
    const mrpResponse = await fetch('http://localhost:3002/api/inventory').catch(() => null);
    const inventoryData = mrpResponse && mrpResponse.ok ? await mrpResponse.json() : null;

    if (!inventoryData) {
      return res.status(500).json({ error: 'Failed to fetch inventory data' });
    }

    // Simulate AI analysis (in a real implementation, this would use a machine learning model)
    const analysis = {
      insights: [
        {
          type: 'excess_inventory',
          items: [
            { id: 2, name: 'Component B', currentLevel: 350, recommendedLevel: 200, potentialSavings: 15000 },
            { id: 10, name: 'Raw Material J', currentLevel: 250, recommendedLevel: 150, potentialSavings: 10000 }
          ],
          totalSavings: 25000
        },
        {
          type: 'stockout_risk',
          items: [
            { id: 4, name: 'Raw Material D', currentLevel: 80, recommendedLevel: 150, riskLevel: 'high' },
            { id: 5, name: 'Component E', currentLevel: 30, recommendedLevel: 75, riskLevel: 'medium' }
          ]
        },
        {
          type: 'demand_forecast',
          forecast: [
            { month: 'June', predictedDemand: 1200, confidenceInterval: [1000, 1400] },
            { month: 'July', predictedDemand: 1350, confidenceInterval: [1100, 1600] },
            { month: 'August', predictedDemand: 1500, confidenceInterval: [1200, 1800] }
          ]
        }
      ],
      recommendations: [
        'Reduce stock levels of Component B by 150 units to save approximately $15,000',
        'Increase Raw Material D inventory by 70 units to avoid potential stockouts',
        'Consider adjusting production schedule to account for increasing demand trend'
      ],
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating AI inventory analysis:', error);
    res.status(500).json({ error: 'Failed to generate AI inventory analysis' });
  }
});

app.get('/api/ai-analytics/maintenance-prediction', async (req, res) => {
  try {
    // Fetch asset data from APM
    const apmResponse = await fetch('http://localhost:3006/api/assets').catch(() => null);
    const assetData = apmResponse && apmResponse.ok ? await apmResponse.json() : null;

    if (!assetData) {
      return res.status(500).json({ error: 'Failed to fetch asset data' });
    }

    // Simulate AI analysis (in a real implementation, this would use a machine learning model)
    const analysis = {
      insights: [
        {
          type: 'predictive_maintenance',
          assets: [
            {
              id: 1,
              name: 'Production Machine A',
              failureProbability: 0.15,
              predictedFailureDate: '2025-06-10',
              maintenanceRecommendation: 'Schedule preventive maintenance within 2 weeks',
              estimatedDowntime: '4 hours',
              estimatedCost: 2500
            },
            {
              id: 3,
              name: 'Conveyor System C',
              failureProbability: 0.35,
              predictedFailureDate: '2025-05-15',
              maintenanceRecommendation: 'Immediate inspection required, signs of belt wear detected',
              estimatedDowntime: '8 hours',
              estimatedCost: 5000
            }
          ]
        },
        {
          type: 'performance_optimization',
          assets: [
            {
              id: 5,
              name: 'Robotic Arm E',
              currentEfficiency: 82,
              potentialEfficiency: 95,
              optimizationRecommendation: 'Recalibrate motion parameters and update firmware',
              estimatedProductivityGain: '15%'
            }
          ]
        }
      ],
      recommendations: [
        'Schedule maintenance for Conveyor System C before May 15 to avoid unplanned downtime',
        'Implement regular vibration analysis for Production Machine A',
        'Optimize Robotic Arm E parameters to improve production efficiency by up to 15%'
      ],
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating AI maintenance prediction:', error);
    res.status(500).json({ error: 'Failed to generate AI maintenance prediction' });
  }
});

// New AI-powered analytics endpoint for sales forecasting
app.get('/api/ai-analytics/sales-forecast', async (req, res) => {
  try {
    // Fetch sales data from CRM and BMS
    const crmResponse = await fetch('http://localhost:3003/api/customers').catch(() => null);
    const bmsResponse = await fetch('http://localhost:3001/api/business-data').catch(() => null);

    const customerData = crmResponse && crmResponse.ok ? await crmResponse.json() : null;
    const businessData = bmsResponse && bmsResponse.ok ? await bmsResponse.json() : null;

    if (!customerData && !businessData) {
      return res.status(500).json({ error: 'Failed to fetch required data for sales forecasting' });
    }

    // Simulate AI analysis for sales forecasting
    const analysis = {
      insights: [
        {
          type: 'sales_forecast',
          forecast: [
            { quarter: 'Q3 2025', predictedSales: 1250000, confidenceInterval: [1100000, 1400000] },
            { quarter: 'Q4 2025', predictedSales: 1450000, confidenceInterval: [1300000, 1600000] },
            { quarter: 'Q1 2026', predictedSales: 1350000, confidenceInterval: [1200000, 1500000] }
          ]
        },
        {
          type: 'customer_segments',
          segments: [
            {
              name: 'Enterprise',
              growthRate: 15,
              churnRisk: 'low',
              recommendedActions: [
                'Implement account-based marketing for top 10 accounts',
                'Schedule quarterly executive reviews'
              ]
            },
            {
              name: 'Mid-Market',
              growthRate: 8,
              churnRisk: 'medium',
              recommendedActions: [
                'Increase customer success touchpoints',
                'Develop targeted upsell campaigns'
              ]
            },
            {
              name: 'Small Business',
              growthRate: 22,
              churnRisk: 'high',
              recommendedActions: [
                'Simplify onboarding process',
                'Implement automated check-ins'
              ]
            }
          ]
        },
        {
          type: 'product_performance',
          products: [
            { name: 'Product A', salesTrend: 'increasing', margin: 42, recommendedAction: 'Increase marketing spend' },
            { name: 'Product B', salesTrend: 'stable', margin: 38, recommendedAction: 'Maintain current strategy' },
            { name: 'Product C', salesTrend: 'decreasing', margin: 45, recommendedAction: 'Review pricing strategy' }
          ]
        }
      ],
      recommendations: [
        'Focus marketing efforts on Small Business segment which shows highest growth potential',
        'Develop retention program for Mid-Market customers to reduce churn risk',
        'Review Product C pricing and positioning to address declining sales'
      ],
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating AI sales forecast:', error);
    res.status(500).json({ error: 'Failed to generate AI sales forecast' });
  }
});

// AI-powered analytics endpoint for manufacturing optimization
app.get('/api/ai-analytics/manufacturing-optimization', async (req, res) => {
  try {
    // Simulate AI analysis for manufacturing optimization
    const analysis = {
      insights: [
        {
          type: 'production_efficiency',
          metrics: [
            {
              name: 'Overall Equipment Effectiveness (OEE)',
              current: 68,
              target: 85,
              industry_benchmark: 72,
              components: {
                availability: 82,
                performance: 75,
                quality: 92
              }
            },
            {
              name: 'Production Cycle Time',
              current: 45,
              target: 35,
              industry_benchmark: 40,
              unit: 'minutes'
            },
            {
              name: 'First Pass Yield',
              current: 92,
              target: 98,
              industry_benchmark: 94,
              unit: '%'
            }
          ]
        },
        {
          type: 'bottleneck_analysis',
          bottlenecks: [
            {
              process: 'Assembly Line A',
              impact: 'High',
              throughput_reduction: 22,
              root_causes: [
                'Equipment downtime due to maintenance issues',
                'Operator skill gaps',
                'Material flow constraints'
              ],
              recommended_actions: [
                'Implement predictive maintenance program',
                'Enhance operator training for specific tasks',
                'Redesign material delivery system'
              ]
            },
            {
              process: 'Quality Control Station',
              impact: 'Medium',
              throughput_reduction: 15,
              root_causes: [
                'Manual inspection processes',
                'Inconsistent quality criteria application',
                'Inspection equipment limitations'
              ],
              recommended_actions: [
                'Implement automated vision inspection system',
                'Standardize quality criteria with clear documentation',
                'Upgrade inspection equipment'
              ]
            }
          ]
        },
        {
          type: 'resource_optimization',
          resources: [
            {
              type: 'Labor',
              current_utilization: 78,
              optimal_utilization: 90,
              potential_savings: 125000,
              recommendations: [
                'Implement skills-based routing of tasks',
                'Cross-train operators for multiple stations',
                'Adjust staffing levels based on production demand'
              ]
            },
            {
              type: 'Energy',
              current_utilization: 65,
              optimal_utilization: 85,
              potential_savings: 75000,
              recommendations: [
                'Install energy monitoring systems',
                'Optimize equipment startup/shutdown sequences',
                'Upgrade to energy-efficient lighting and HVAC'
              ]
            },
            {
              type: 'Materials',
              current_utilization: 88,
              optimal_utilization: 95,
              potential_savings: 180000,
              recommendations: [
                'Implement just-in-time material delivery',
                'Optimize cutting patterns to reduce waste',
                'Establish material recovery processes'
              ]
            }
          ]
        }
      ],
      recommendations: [
        'Implement predictive maintenance on Assembly Line A to reduce downtime by 35%',
        'Automate quality inspection processes to improve throughput and consistency',
        'Optimize labor scheduling based on production demand patterns',
        'Implement energy management system to reduce consumption during non-peak hours',
        'Redesign material flow to eliminate bottlenecks and reduce waste'
      ],
      potential_impact: {
        productivity_increase: '15-20%',
        cost_reduction: '$380,000 annually',
        quality_improvement: '5-7% reduction in defects',
        lead_time_reduction: '25-30%'
      },
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating manufacturing optimization analysis:', error);
    res.status(500).json({ error: 'Failed to generate manufacturing optimization analysis' });
  }
});

// AI-powered analytics endpoint for supply chain optimization
app.get('/api/ai-analytics/supply-chain-optimization', async (req, res) => {
  try {
    // Fetch data from SCM and TM
    const scmResponse = await fetch('http://localhost:3008/api/suppliers').catch(() => null);
    const tmResponse = await fetch('http://localhost:3009/api/tasks?category=Supply%20Chain').catch(() => null);

    const supplierData = scmResponse && scmResponse.ok ? await scmResponse.json() : null;
    const tasksData = tmResponse && tmResponse.ok ? await tmResponse.json() : null;

    if (!supplierData && !tasksData) {
      return res.status(500).json({ error: 'Failed to fetch required data for supply chain optimization' });
    }

    // Simulate AI analysis for supply chain optimization
    const analysis = {
      insights: [
        {
          type: 'supplier_optimization',
          suppliers: [
            {
              name: 'Global Materials Inc.',
              currentLeadTime: 5,
              optimizedLeadTime: 3.5,
              potentialSavings: 12500,
              recommendedAction: 'Negotiate volume-based discounts and implement JIT delivery'
            },
            {
              name: 'Tech Components Ltd.',
              currentLeadTime: 7,
              optimizedLeadTime: 5,
              potentialSavings: 8700,
              recommendedAction: 'Establish local warehouse for frequently ordered components'
            }
          ]
        },
        {
          type: 'logistics_optimization',
          recommendations: [
            {
              title: 'Route Optimization',
              description: 'Optimize delivery routes to reduce transportation costs by 15%',
              estimatedSavings: 35000,
              implementationDifficulty: 'Medium'
            },
            {
              title: 'Warehouse Layout Redesign',
              description: 'Redesign warehouse layout to improve picking efficiency by 22%',
              estimatedSavings: 28000,
              implementationDifficulty: 'High'
            },
            {
              title: 'Cross-Docking Implementation',
              description: 'Implement cross-docking for high-volume items to reduce storage costs',
              estimatedSavings: 42000,
              implementationDifficulty: 'High'
            }
          ]
        },
        {
          type: 'inventory_optimization',
          categories: [
            {
              name: 'Raw Materials',
              currentInventoryValue: 450000,
              optimizedInventoryValue: 380000,
              potentialSavings: 70000,
              stockoutRisk: 'Low'
            },
            {
              name: 'Electronic Components',
              currentInventoryValue: 320000,
              optimizedInventoryValue: 275000,
              potentialSavings: 45000,
              stockoutRisk: 'Medium'
            },
            {
              name: 'Packaging Materials',
              currentInventoryValue: 180000,
              optimizedInventoryValue: 150000,
              potentialSavings: 30000,
              stockoutRisk: 'Low'
            }
          ]
        }
      ],
      recommendations: [
        'Implement JIT delivery with Global Materials Inc. to reduce lead time and inventory costs',
        'Redesign warehouse layout to improve picking efficiency and reduce labor costs',
        'Reduce safety stock levels for raw materials with consistent supply patterns',
        'Implement cross-docking for high-volume items to reduce storage costs'
      ],
      relatedTasks: tasksData ? tasksData.data.tasks.map(task => ({
        id: task.id,
        title: task.title,
        status: task.status,
        priority: task.priority,
        dueDate: task.dueDate
      })) : [],
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating supply chain optimization analysis:', error);
    res.status(500).json({ error: 'Failed to generate supply chain optimization analysis' });
  }
});

// AI-powered analytics endpoint for schedule optimization
app.get('/api/ai-analytics/schedule-optimization', async (req, res) => {
  try {
    // Simulate AI analysis for schedule optimization
    const analysis = {
      scheduleOptimization: {
        productionLines: [
          {
            id: 1,
            name: "Production Line A",
            currentEfficiency: 85,
            potentialEfficiency: 92,
            bottleneck: "Setup Time",
            recommendation: "Reduce setup time by implementing quick-change tooling"
          },
          {
            id: 2,
            name: "Production Line B",
            currentEfficiency: 78,
            potentialEfficiency: 88,
            bottleneck: "Material Handling",
            recommendation: "Optimize material flow to reduce waiting time"
          },
          {
            id: 3,
            name: "Production Line C",
            currentEfficiency: 82,
            potentialEfficiency: 90,
            bottleneck: "Quality Inspection",
            recommendation: "Implement inline quality inspection to reduce delays"
          }
        ]
      },
      resourceAllocation: {
        resources: [
          {
            id: 1,
            name: "Machine Operators",
            currentUtilization: 75,
            optimalUtilization: 85,
            recommendation: "Cross-train operators to improve flexibility"
          },
          {
            id: 2,
            name: "CNC Machines",
            currentUtilization: 65,
            optimalUtilization: 90,
            recommendation: "Implement predictive maintenance to reduce downtime"
          },
          {
            id: 3,
            name: "Assembly Stations",
            currentUtilization: 80,
            optimalUtilization: 95,
            recommendation: "Reorganize workstations to improve workflow"
          }
        ]
      },
      sequenceOptimization: {
        sequences: [
          {
            id: 1,
            productGroup: "Product Family A",
            currentSetupTime: 45,
            optimizedSetupTime: 25,
            timeSavings: 20,
            recommendation: "Group similar products to minimize changeover time"
          },
          {
            id: 2,
            productGroup: "Product Family B",
            currentSetupTime: 60,
            optimizedSetupTime: 35,
            timeSavings: 25,
            recommendation: "Optimize sequence to minimize color and material changes"
          },
          {
            id: 3,
            productGroup: "Product Family C",
            currentSetupTime: 30,
            optimizedSetupTime: 15,
            timeSavings: 15,
            recommendation: "Implement SMED techniques for faster changeovers"
          }
        ]
      },
      recommendations: [
        "Implement advanced scheduling algorithm to optimize production sequence",
        "Develop cross-training program to improve resource flexibility",
        "Invest in quick-change tooling to reduce setup times",
        "Implement real-time monitoring to identify bottlenecks faster",
        "Develop contingency plans for critical resource failures"
      ],
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating schedule optimization analysis:', error);
    res.status(500).json({ error: 'Failed to generate schedule optimization analysis' });
  }
});

// AI-powered analytics endpoint for warehouse optimization
app.get('/api/ai-analytics/warehouse-optimization', async (req, res) => {
  try {
    // Simulate AI analysis for warehouse optimization
    const analysis = {
      spaceOptimization: {
        warehouses: [
          {
            id: 1,
            name: "Main Warehouse",
            currentUtilization: 75,
            optimalUtilization: 85,
            potentialSavings: 12000,
            recommendation: "Reorganize storage layout to increase capacity by 10%"
          },
          {
            id: 2,
            name: "West Coast Warehouse",
            currentUtilization: 60,
            optimalUtilization: 80,
            potentialSavings: 18000,
            recommendation: "Implement vertical storage solutions to increase capacity by 20%"
          },
          {
            id: 3,
            name: "East Coast Warehouse",
            currentUtilization: 80,
            optimalUtilization: 90,
            potentialSavings: 8000,
            recommendation: "Optimize aisle width and implement narrow aisle equipment to increase capacity by 10%"
          }
        ]
      },
      pickingOptimization: {
        strategies: [
          {
            id: 1,
            name: "Zone Picking",
            currentEfficiency: 65,
            potentialEfficiency: 85,
            timeReduction: 25,
            implementation: "Medium",
            recommendation: "Divide warehouse into picking zones based on item velocity"
          },
          {
            id: 2,
            name: "Batch Picking",
            currentEfficiency: 70,
            potentialEfficiency: 90,
            timeReduction: 30,
            implementation: "Low",
            recommendation: "Group similar orders to reduce travel time"
          },
          {
            id: 3,
            name: "Wave Picking",
            currentEfficiency: 60,
            potentialEfficiency: 80,
            timeReduction: 20,
            implementation: "High",
            recommendation: "Schedule picking in waves based on shipping schedules"
          }
        ]
      },
      inventoryManagement: {
        items: [
          {
            id: 1,
            category: "Fast-Moving Items",
            currentLocation: "Back of Warehouse",
            recommendedLocation: "Near Shipping Area",
            timeReduction: 35,
            recommendation: "Relocate fast-moving items closer to shipping area"
          },
          {
            id: 2,
            category: "Seasonal Items",
            currentLocation: "Prime Storage",
            recommendedLocation: "Overflow Storage",
            spaceOptimization: 25,
            recommendation: "Move seasonal items to overflow storage during off-season"
          },
          {
            id: 3,
            category: "Bulk Items",
            currentLocation: "Mixed Storage",
            recommendedLocation: "Dedicated Bulk Area",
            handlingImprovement: 40,
            recommendation: "Create dedicated storage area for bulk items with appropriate handling equipment"
          }
        ]
      },
      recommendations: [
        "Implement cross-docking for direct-to-customer shipments to reduce handling time by 30%",
        "Optimize receiving schedule to balance workload throughout the week",
        "Implement cycle counting to improve inventory accuracy and reduce annual physical inventory costs",
        "Reorganize fast-moving items to reduce picking travel time",
        "Invest in narrow aisle equipment to increase storage capacity in East Coast Warehouse"
      ],
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating warehouse optimization analysis:', error);
    res.status(500).json({ error: 'Failed to generate warehouse optimization analysis' });
  }
});

// AI-powered analytics endpoint for materials planning
app.get('/api/ai-analytics/materials-planning', async (req, res) => {
  try {
    // Simulate AI analysis for materials planning
    const analysis = {
      inventoryOptimization: {
        items: [
          {
            id: 1,
            name: "Raw Material A",
            currentStock: 500,
            optimalStock: 350,
            excessCost: 1500,
            recommendation: "Reduce stock level by 150 units to save $1,500 in carrying costs"
          },
          {
            id: 4,
            name: "Raw Material D",
            currentStock: 80,
            optimalStock: 150,
            stockoutRisk: "High",
            recommendation: "Increase stock level by 70 units to prevent production delays"
          },
          {
            id: 6,
            name: "Finished Product F",
            currentStock: 5,
            optimalStock: 25,
            stockoutRisk: "Critical",
            recommendation: "Expedite production to increase stock by 20 units"
          }
        ]
      },
      demandForecasting: {
        products: [
          {
            id: 3,
            name: "Finished Product C",
            currentDemand: 45,
            forecastedDemand: 65,
            trend: "Increasing",
            confidence: 85,
            seasonalFactors: ["Holiday Season", "Promotional Event"]
          },
          {
            id: 6,
            name: "Finished Product F",
            currentDemand: 30,
            forecastedDemand: 15,
            trend: "Decreasing",
            confidence: 78,
            seasonalFactors: ["End of Season"]
          },
          {
            id: 9,
            name: "Finished Product I",
            currentDemand: 25,
            forecastedDemand: 40,
            trend: "Increasing",
            confidence: 92,
            seasonalFactors: ["New Market Entry"]
          }
        ]
      },
      productionPlanning: {
        schedules: [
          {
            id: 1,
            product: "Finished Product C",
            currentCapacity: 50,
            requiredCapacity: 65,
            bottleneck: "Assembly Line 2",
            recommendation: "Add additional shift to increase capacity by 20 units"
          },
          {
            id: 2,
            product: "Finished Product F",
            currentCapacity: 40,
            requiredCapacity: 25,
            excessCapacity: 15,
            recommendation: "Reallocate resources to other product lines"
          },
          {
            id: 3,
            product: "Finished Product I",
            currentCapacity: 35,
            requiredCapacity: 40,
            bottleneck: "Quality Control",
            recommendation: "Optimize QC process to increase throughput"
          }
        ]
      },
      recommendations: [
        "Implement just-in-time inventory management for Raw Material A to reduce carrying costs",
        "Increase safety stock for Raw Material D to prevent production delays",
        "Adjust production schedule to accommodate increased demand for Finished Product C",
        "Develop contingency plan for potential stockout of Finished Product F",
        "Optimize quality control process to increase production capacity for Finished Product I"
      ],
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating materials planning analysis:', error);
    res.status(500).json({ error: 'Failed to generate materials planning analysis' });
  }
});

// AI-powered analytics endpoint for task optimization
app.get('/api/ai-analytics/task-optimization', async (req, res) => {
  try {
    // Simulate AI analysis for task optimization
    const analysis = {
      taskOptimization: {
        insights: [
          {
            id: 1,
            title: "Automate Status Updates",
            description: "Implement automated status updates based on task progress to reduce manual reporting time.",
            impact: "High",
            timeSavings: 5.5
          },
          {
            id: 2,
            title: "Batch Similar Tasks",
            description: "Group similar tasks together to reduce context switching and improve focus.",
            impact: "Medium",
            timeSavings: 3.2
          },
          {
            id: 3,
            title: "Implement Task Templates",
            description: "Create templates for recurring tasks to standardize processes and reduce setup time.",
            impact: "High",
            timeSavings: 4.8
          }
        ]
      },
      resourceAllocation: {
        team: [
          {
            id: 1,
            name: "John Doe",
            workloadStatus: "Overallocated",
            currentTasks: 12,
            optimalTasks: 8,
            skillMatch: 85,
            workloadPercentage: 150
          },
          {
            id: 2,
            name: "Jane Smith",
            workloadStatus: "Optimal",
            currentTasks: 7,
            optimalTasks: 7,
            skillMatch: 92,
            workloadPercentage: 100
          },
          {
            id: 3,
            name: "Mike Johnson",
            workloadStatus: "Underutilized",
            currentTasks: 4,
            optimalTasks: 6,
            skillMatch: 78,
            workloadPercentage: 67
          }
        ]
      },
      workflowImprovements: {
        workflows: [
          {
            id: 1,
            name: "Task Approval Process",
            description: "Streamline the approval process by reducing unnecessary review steps.",
            currentEfficiency: 65,
            potentialEfficiency: 90,
            implementationComplexity: "Low"
          },
          {
            id: 2,
            name: "Bug Tracking Workflow",
            description: "Improve bug tracking by adding automated categorization and priority assignment.",
            currentEfficiency: 72,
            potentialEfficiency: 88,
            implementationComplexity: "Medium"
          }
        ]
      },
      recommendations: [
        "Implement a daily standup meeting to improve team coordination and reduce blockers",
        "Set up automated reminders for approaching deadlines to prevent last-minute rushes",
        "Create a knowledge base for common issues and solutions to reduce repetitive problem-solving",
        "Establish clear escalation paths for different types of issues to improve response times"
      ],
      potentialImpact: {
        timeSavings: "13.5 hours per week",
        efficiencyIncrease: "25-30%",
        qualityImprovement: "15-20%",
        teamSatisfaction: "Significant improvement expected"
      },
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating task optimization analysis:', error);
    res.status(500).json({ error: 'Failed to generate task optimization analysis' });
  }
});

// AI-powered analytics endpoint for customer relationship management
app.get('/api/ai-analytics/customer-insights', async (req, res) => {
  try {
    // Simulate AI analysis for customer relationship management
    const analysis = {
      insights: [
        {
          type: 'customer_segmentation',
          segments: [
            {
              name: 'High-Value Loyalists',
              percentage: 15,
              avg_revenue: 12500,
              retention_rate: 92,
              growth_potential: 'Medium',
              characteristics: [
                'Purchase frequency: 2.3x per month',
                'Average order value: $850',
                'Product categories: Premium, Enterprise',
                'Customer tenure: 4.5+ years'
              ],
              recommendations: [
                'Implement VIP loyalty program with exclusive benefits',
                'Provide dedicated account management',
                'Offer early access to new products/features',
                'Create personalized upsell opportunities'
              ]
            },
            {
              name: 'Growth Opportunities',
              percentage: 35,
              avg_revenue: 5200,
              retention_rate: 78,
              growth_potential: 'High',
              characteristics: [
                'Purchase frequency: 1.5x per month',
                'Average order value: $450',
                'Product categories: Standard, Premium',
                'Customer tenure: 1-3 years'
              ],
              recommendations: [
                'Develop targeted cross-sell campaigns',
                'Implement usage-based incentives',
                'Create educational content on advanced features',
                'Offer bundle discounts on complementary products'
              ]
            },
            {
              name: 'At-Risk Customers',
              percentage: 22,
              avg_revenue: 3800,
              retention_rate: 45,
              growth_potential: 'Low',
              characteristics: [
                'Purchase frequency: 0.7x per month',
                'Average order value: $350',
                'Product categories: Basic, Standard',
                'Customer tenure: 1-2 years',
                'Declining engagement patterns'
              ],
              recommendations: [
                'Implement proactive retention outreach',
                'Offer renewal incentives',
                'Conduct satisfaction surveys',
                'Provide product training and support'
              ]
            }
          ]
        },
        {
          type: 'customer_journey_analysis',
          touchpoints: [
            {
              stage: 'Awareness',
              effectiveness: 72,
              friction_points: [
                'Limited brand recognition in new markets',
                'Inconsistent messaging across channels',
                'Low conversion from ad impressions to website visits'
              ],
              recommendations: [
                'Increase targeted digital advertising in key markets',
                'Develop consistent brand messaging guidelines',
                'Optimize ad creative and targeting parameters'
              ]
            },
            {
              stage: 'Consideration',
              effectiveness: 65,
              friction_points: [
                'Complex product comparison process',
                'Limited product information availability',
                'High cart abandonment rate (68%)'
              ],
              recommendations: [
                'Simplify product comparison tools',
                'Enhance product detail pages with comprehensive information',
                'Implement cart abandonment recovery campaigns'
              ]
            },
            {
              stage: 'Purchase',
              effectiveness: 81,
              friction_points: [
                'Multi-step checkout process',
                'Limited payment options',
                'Shipping cost transparency issues'
              ],
              recommendations: [
                'Streamline checkout to 3 steps or fewer',
                'Expand payment options to include digital wallets',
                'Display shipping costs earlier in the purchase process'
              ]
            },
            {
              stage: 'Retention',
              effectiveness: 58,
              friction_points: [
                'Inconsistent post-purchase communication',
                'Limited self-service support options',
                'Delayed response to service issues'
              ],
              recommendations: [
                'Develop automated post-purchase communication sequence',
                'Enhance knowledge base and self-service tools',
                'Implement service level agreements for support response'
              ]
            }
          ]
        },
        {
          type: 'sentiment_analysis',
          overall_sentiment: 'Positive',
          sentiment_score: 72,
          sentiment_trends: {
            '3_months_ago': 68,
            '2_months_ago': 70,
            '1_month_ago': 71,
            'current': 72
          },
          key_topics: [
            {
              topic: 'Product Quality',
              sentiment: 'Very Positive',
              score: 85,
              sample_feedback: 'The product consistently exceeds our expectations in terms of reliability and performance.'
            },
            {
              topic: 'Ease of Use',
              sentiment: 'Positive',
              score: 78,
              sample_feedback: 'The interface is intuitive and makes complex tasks simple to execute.'
            },
            {
              topic: 'Customer Support',
              sentiment: 'Neutral',
              score: 60,
              sample_feedback: 'Support is knowledgeable but response times could be improved.'
            },
            {
              topic: 'Pricing',
              sentiment: 'Negative',
              score: 42,
              sample_feedback: 'The product is excellent but priced higher than comparable alternatives.'
            }
          ]
        }
      ],
      recommendations: [
        'Implement a tiered loyalty program to increase retention of high-value customers',
        'Streamline the checkout process to reduce cart abandonment by 25%',
        'Develop personalized email campaigns based on customer segment and behavior',
        'Enhance self-service support options to improve customer satisfaction',
        'Create targeted win-back campaigns for at-risk customers'
      ],
      potential_impact: {
        revenue_increase: '12-15%',
        customer_retention_improvement: '18-22%',
        customer_satisfaction_increase: '15-20 points',
        customer_acquisition_cost_reduction: '10-15%'
      },
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating customer insights analysis:', error);
    res.status(500).json({ error: 'Failed to generate customer insights analysis' });
  }
});

// AI-powered analytics endpoint for project insights
app.get('/api/ai-analytics/project-insights', async (req, res) => {
  try {
    // Fetch project data from PMS
    const pmsResponse = await fetch('http://localhost:3007/api/projects').catch(() => null);
    const projectData = pmsResponse && pmsResponse.ok ? await pmsResponse.json() : null;

    // Simulate AI analysis for project management
    const analysis = {
      insights: [
        {
          type: 'project_performance',
          projects: [
            {
              id: 1,
              name: 'Website Redesign',
              status: 'In Progress',
              progress: 45,
              timeline: {
                planned: { start: '2025-03-01', end: '2025-06-30' },
                projected: { start: '2025-03-01', end: '2025-07-15' },
                variance: 15 // days
              },
              budget: {
                planned: 50000,
                actual: 45000,
                projected: 55000,
                variance: 5000
              },
              riskLevel: 'Medium'
            },
            {
              id: 2,
              name: 'Mobile App Development',
              status: 'Planning',
              progress: 10,
              timeline: {
                planned: { start: '2025-05-15', end: '2025-09-30' },
                projected: { start: '2025-05-15', end: '2025-09-30' },
                variance: 0 // days
              },
              budget: {
                planned: 75000,
                actual: 8000,
                projected: 78000,
                variance: 3000
              },
              riskLevel: 'Low'
            },
            {
              id: 3,
              name: 'ERP Implementation',
              status: 'Completed',
              progress: 100,
              timeline: {
                planned: { start: '2025-01-01', end: '2025-04-15' },
                projected: { start: '2025-01-01', end: '2025-04-15' },
                variance: 0 // days
              },
              budget: {
                planned: 120000,
                actual: 115000,
                projected: 115000,
                variance: -5000
              },
              riskLevel: 'Low'
            }
          ]
        },
        {
          type: 'resource_allocation',
          team_members: [
            {
              id: 1,
              name: 'John Smith',
              allocation: 120, // percentage
              projects: ['Website Redesign', 'Mobile App Development'],
              skills: ['Project Management', 'Business Analysis'],
              workload: 'Overallocated'
            },
            {
              id: 2,
              name: 'Jane Doe',
              allocation: 85, // percentage
              projects: ['Website Redesign'],
              skills: ['UI/UX Design', 'Frontend Development'],
              workload: 'Optimal'
            },
            {
              id: 3,
              name: 'Bob Johnson',
              allocation: 95, // percentage
              projects: ['Website Redesign'],
              skills: ['Frontend Development', 'Backend Development'],
              workload: 'Optimal'
            },
            {
              id: 4,
              name: 'Alice Smith',
              allocation: 60, // percentage
              projects: ['Website Redesign'],
              skills: ['Backend Development', 'Database Design'],
              workload: 'Underallocated'
            }
          ]
        },
        {
          type: 'risk_assessment',
          risks: [
            {
              id: 1,
              project: 'Website Redesign',
              description: 'Backend integration may take longer than expected',
              probability: 0.7, // 0-1
              impact: 'High',
              mitigation: 'Start integration tasks earlier and allocate additional resources'
            },
            {
              id: 2,
              project: 'Mobile App Development',
              description: 'Requirements may change significantly after planning phase',
              probability: 0.5, // 0-1
              impact: 'Medium',
              mitigation: 'Implement agile methodology with frequent client reviews'
            },
            {
              id: 3,
              project: 'Website Redesign',
              description: 'Key team member may be unavailable during critical phase',
              probability: 0.3, // 0-1
              impact: 'High',
              mitigation: 'Cross-train team members and document processes'
            }
          ]
        }
      ],
      recommendations: [
        'Reallocate resources from Alice Smith to help John Smith with his overallocation',
        'Consider extending the Website Redesign timeline by 2 weeks to account for potential delays',
        'Start backend integration tasks for Website Redesign earlier than planned',
        'Schedule more frequent client reviews for Mobile App Development to prevent requirement changes',
        'Implement cross-training program to mitigate key person dependencies'
      ],
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating project insights analysis:', error);
    res.status(500).json({ error: 'Failed to generate project insights analysis' });
  }
});

// New AI-powered analytics endpoint for business intelligence
app.get('/api/ai-analytics/business-intelligence', async (req, res) => {
  try {
    // Fetch data from multiple systems
    const bmsResponse = await fetch('http://localhost:3001/api/business-data').catch(() => null);
    const crmResponse = await fetch('http://localhost:3003/api/customers').catch(() => null);
    const scmResponse = await fetch('http://localhost:3008/api/suppliers').catch(() => null);

    const businessData = bmsResponse && bmsResponse.ok ? await bmsResponse.json() : null;
    const customerData = crmResponse && crmResponse.ok ? await crmResponse.json() : null;
    const supplierData = scmResponse && scmResponse.ok ? await scmResponse.json() : null;

    if (!businessData && !customerData && !supplierData) {
      return res.status(500).json({ error: 'Failed to fetch required data for business intelligence' });
    }

    // Simulate AI analysis for business intelligence
    const analysis = {
      insights: [
        {
          type: 'financial_trends',
          metrics: [
            { name: 'Revenue Growth', value: '12.5%', trend: 'positive', benchmark: '8.3%' },
            { name: 'Profit Margin', value: '18.2%', trend: 'stable', benchmark: '15.7%' },
            { name: 'Operating Expenses', value: '22.1%', trend: 'negative', benchmark: '19.5%' }
          ]
        },
        {
          type: 'market_opportunities',
          opportunities: [
            {
              market: 'Healthcare',
              growthPotential: 'high',
              competitivePosition: 'strong',
              recommendedStrategy: 'Expand product offerings and sales team'
            },
            {
              market: 'Manufacturing',
              growthPotential: 'medium',
              competitivePosition: 'moderate',
              recommendedStrategy: 'Focus on customer retention and incremental improvements'
            }
          ]
        },
        {
          type: 'operational_efficiency',
          metrics: [
            { name: 'Order Fulfillment Time', value: '3.2 days', trend: 'improving', benchmark: '4.5 days' },
            { name: 'Customer Acquisition Cost', value: '$1,250', trend: 'worsening', benchmark: '$950' },
            { name: 'Employee Productivity', value: '$215K/employee', trend: 'stable', benchmark: '$195K/employee' }
          ]
        }
      ],
      recommendations: [
        'Investigate and address rising customer acquisition costs',
        'Develop expansion strategy for healthcare market segment',
        'Maintain focus on operational efficiency to preserve competitive advantage'
      ],
      timestamp: new Date().toISOString()
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error generating business intelligence:', error);
    res.status(500).json({ error: 'Failed to generate business intelligence' });
  }
});

// Cross-application workflow endpoints
app.post('/api/workflows/order-to-cash', async (req, res) => {
  try {
    const { customerId, products, shippingAddress, paymentMethod } = req.body;

    if (!customerId || !products || !shippingAddress || !paymentMethod) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Step 1: Validate customer in CRM
    const crmResponse = await fetch(`http://localhost:3003/api/customers/${customerId}`).catch(() => null);
    const customerData = crmResponse && crmResponse.ok ? await crmResponse.json() : null;

    if (!customerData) {
      return res.status(400).json({ error: 'Invalid customer ID' });
    }

    // Step 2: Check inventory in MRP
    const mrpResponse = await fetch('http://localhost:3002/api/inventory/check', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ products })
    }).catch(() => null);

    const inventoryCheck = mrpResponse && mrpResponse.ok ? await mrpResponse.json() : null;

    if (!inventoryCheck || !inventoryCheck.available) {
      return res.status(400).json({
        error: 'Inventory not available',
        details: inventoryCheck ? inventoryCheck.unavailableItems : []
      });
    }

    // Step 3: Create order in BMS
    const bmsResponse = await fetch('http://localhost:3001/api/orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        customerId,
        products,
        shippingAddress,
        paymentMethod,
        status: 'pending'
      })
    }).catch(() => null);

    const orderData = bmsResponse && bmsResponse.ok ? await bmsResponse.json() : null;

    if (!orderData) {
      return res.status(500).json({ error: 'Failed to create order' });
    }

    // Step 4: Create warehouse picking task in WMS
    const wmsResponse = await fetch('http://localhost:3004/api/tasks', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'picking',
        orderId: orderData.orderId,
        products,
        priority: 'normal'
      })
    }).catch(() => null);

    const pickingTask = wmsResponse && wmsResponse.ok ? await wmsResponse.json() : null;

    // Step 5: Update inventory in MRP
    const mrpUpdateResponse = await fetch('http://localhost:3002/api/inventory/allocate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        orderId: orderData.orderId,
        products
      })
    }).catch(() => null);

    // Step 6: Create task in TM for order fulfillment
    const tmResponse = await fetch('http://localhost:3009/api/tasks', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: `Fulfill Order #${orderData.orderId}`,
        assignee: 'Warehouse Team',
        dueDate: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Tomorrow
        status: 'Planned',
        priority: 'Medium',
        progress: 0
      })
    }).catch(() => null);

    // Create notification for the new order
    const notification = {
      source: 'workflow',
      type: 'order_created',
      message: `New order #${orderData.orderId} created for customer ${customerData.name}`,
      timestamp: new Date().toISOString()
    };

    io.emit('notification', notification);
    notifications.push(notification);

    res.status(201).json({
      status: 'success',
      orderId: orderData.orderId,
      message: 'Order created successfully',
      workflow: {
        customerValidated: !!customerData,
        inventoryChecked: !!inventoryCheck,
        orderCreated: !!orderData,
        pickingTaskCreated: !!pickingTask,
        inventoryAllocated: !!(mrpUpdateResponse && mrpUpdateResponse.ok),
        taskCreated: !!(tmResponse && tmResponse.ok)
      }
    });
  } catch (error) {
    console.error('Error in order-to-cash workflow:', error);
    res.status(500).json({ error: 'Failed to process order-to-cash workflow' });
  }
});

app.post('/api/workflows/procurement', async (req, res) => {
  try {
    const { supplierId, items, deliveryDate, purchaseRequestId } = req.body;

    if (!supplierId || !items || !deliveryDate) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Step 1: Validate supplier in SCM
    const scmResponse = await fetch(`http://localhost:3008/api/suppliers/${supplierId}`).catch(() => null);
    const supplierData = scmResponse && scmResponse.ok ? await scmResponse.json() : null;

    if (!supplierData) {
      return res.status(400).json({ error: 'Invalid supplier ID' });
    }

    // Step 2: Create purchase order in BMS
    const bmsResponse = await fetch('http://localhost:3001/api/purchase-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        supplierId,
        items,
        deliveryDate,
        purchaseRequestId,
        status: 'pending'
      })
    }).catch(() => null);

    const poData = bmsResponse && bmsResponse.ok ? await bmsResponse.json() : null;

    if (!poData) {
      return res.status(500).json({ error: 'Failed to create purchase order' });
    }

    // Step 3: Update MRP with expected delivery
    const mrpResponse = await fetch('http://localhost:3002/api/inventory/expected', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        purchaseOrderId: poData.poId,
        items,
        expectedDeliveryDate: deliveryDate
      })
    }).catch(() => null);

    // Step 4: Create task in TM for purchase order follow-up
    const tmResponse = await fetch('http://localhost:3009/api/tasks', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: `Follow up on PO #${poData.poId}`,
        assignee: 'Procurement Team',
        dueDate: new Date(new Date(deliveryDate).getTime() - 259200000).toISOString().split('T')[0], // 3 days before delivery
        status: 'Planned',
        priority: 'Medium',
        progress: 0
      })
    }).catch(() => null);

    // Step 5: Schedule receiving in WMS
    const wmsResponse = await fetch('http://localhost:3004/api/receiving-schedule', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        purchaseOrderId: poData.poId,
        supplierId,
        items,
        scheduledDate: deliveryDate
      })
    }).catch(() => null);

    // Create notification for the new purchase order
    const notification = {
      source: 'workflow',
      type: 'po_created',
      message: `New purchase order #${poData.poId} created for supplier ${supplierData.name}`,
      timestamp: new Date().toISOString()
    };

    io.emit('notification', notification);
    notifications.push(notification);

    res.status(201).json({
      status: 'success',
      purchaseOrderId: poData.poId,
      message: 'Purchase order created successfully',
      workflow: {
        supplierValidated: !!supplierData,
        purchaseOrderCreated: !!poData,
        mrpUpdated: !!(mrpResponse && mrpResponse.ok),
        taskCreated: !!(tmResponse && tmResponse.ok),
        receivingScheduled: !!(wmsResponse && wmsResponse.ok)
      }
    });
  } catch (error) {
    console.error('Error in procurement workflow:', error);
    res.status(500).json({ error: 'Failed to process procurement workflow' });
  }
});

// API endpoint to get notifications
app.get('/api/notifications', (req, res) => {
  const limit = parseInt(req.query.limit) || 10;
  const source = req.query.source;
  const type = req.query.type;

  let filteredNotifications = [...notifications];

  if (source) {
    filteredNotifications = filteredNotifications.filter(n => n.source === source);
  }

  if (type) {
    filteredNotifications = filteredNotifications.filter(n => n.type === type);
  }

  // Return the most recent notifications first
  const result = filteredNotifications.reverse().slice(0, limit);

  res.json(result);
});

// Notifications array is already initialized at the top of the file

// Google API endpoints
app.post('/api/google-translate/translate', async (req, res) => {
  try {
    const { text, target, source } = req.body;

    if (!text || !target) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameters: text and target language are required'
      });
    }

    // Load shared features for Google API
    if (!sharedFeatures || !sharedFeatures.google) {
      return res.status(500).json({
        status: 'error',
        message: 'Google API integration is not available'
      });
    }

    // Initialize Google API if not already initialized
    await sharedFeatures.google.initGoogleAPI();

    // Call the translate function
    const result = await sharedFeatures.google.Translate.translateText(text, target, source);

    // Extract the translated text from the result
    const translatedText = result.data.translations[0].translatedText;
    const detectedSourceLanguage = result.data.translations[0].detectedSourceLanguage;

    res.json({
      status: 'success',
      data: {
        translatedText,
        detectedSourceLanguage,
        originalText: text,
        targetLanguage: target
      }
    });
  } catch (error) {
    console.error('Error translating text:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to translate text: ' + error.message
    });
  }
});

app.get('/api/google-maps/geocode', async (req, res) => {
  try {
    const { address } = req.query;

    if (!address) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameter: address'
      });
    }

    // Load shared features for Google API
    if (!sharedFeatures || !sharedFeatures.google) {
      return res.status(500).json({
        status: 'error',
        message: 'Google API integration is not available'
      });
    }

    // Initialize Google API if not already initialized
    await sharedFeatures.google.initGoogleAPI();

    // Call the geocode function
    const result = await sharedFeatures.google.Maps.geocode(address);

    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    console.error('Error geocoding address:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to geocode address: ' + error.message
    });
  }
});

app.get('/api/google-analytics/data', async (req, res) => {
  try {
    const { viewId, startDate, endDate, metrics, dimensions } = req.query;

    if (!viewId || !startDate || !endDate || !metrics) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameters: viewId, startDate, endDate, and metrics are required'
      });
    }

    // Load shared features for Google API
    if (!sharedFeatures || !sharedFeatures.google) {
      return res.status(500).json({
        status: 'error',
        message: 'Google API integration is not available'
      });
    }

    // Initialize Google API if not already initialized
    await sharedFeatures.google.initGoogleAPI();

    // Parse metrics and dimensions
    const metricsArray = metrics.split(',');
    const dimensionsArray = dimensions ? dimensions.split(',') : [];

    // Call the analytics function
    const result = await sharedFeatures.google.Analytics.getData(
      viewId,
      startDate,
      endDate,
      metricsArray,
      dimensionsArray
    );

    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch analytics data: ' + error.message
    });
  }
});

// Set up periodic app status checks
setInterval(async () => {
  const apps = [
    { id: 'bms', url: 'http://localhost:3001' },
    { id: 'crm', url: 'http://localhost:3003' },
    { id: 'mrp', url: 'http://localhost:3002' },
    { id: 'wms', url: 'http://localhost:3004' },
    { id: 'aps', url: 'http://localhost:3005' },
    { id: 'apm', url: 'http://localhost:3006' },
    { id: 'pms', url: 'http://localhost:3007' },
    { id: 'scm', url: 'http://localhost:3008' },
    { id: 'tm', url: 'http://localhost:3009' }
  ];

  for (const app of apps) {
    try {
      const response = await fetch(`${app.url}/health`, { timeout: 2000 }).catch(() => null);
      const status = response && response.ok ? 'online' : 'offline';
      io.emit('app-status-change', { app: app.id, status });
    } catch (error) {
      io.emit('app-status-change', { app: app.id, status: 'offline' });
    }
  }
}, 30000); // Check every 30 seconds

// Start server
const PORT = process.env.PORT || 8000;
server.listen(PORT, () => {
  console.log(`Integration Hub running on port ${PORT}`);
});

// Export for external use
module.exports = { app, server, io };
