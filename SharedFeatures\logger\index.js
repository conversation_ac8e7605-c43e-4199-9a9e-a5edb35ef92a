// Logger Module

/**
 * Create a logger for a specific module
 */
function createLogger(moduleName) {
  return {
    info: (message, meta = {}) => {
      console.log(`[INFO] [${moduleName}] ${message}`, meta);
    },
    warn: (message, meta = {}) => {
      console.warn(`[WARN] [${moduleName}] ${message}`, meta);
    },
    error: (message, meta = {}) => {
      console.error(`[ERROR] [${moduleName}] ${message}`, meta);
    },
    debug: (message, meta = {}) => {
      if (process.env.NODE_ENV === 'development') {
        console.debug(`[DEBUG] [${moduleName}] ${message}`, meta);
      }
    },
  };
}

module.exports = {
  createLogger,
};
