<!DOCTYPE html>
<!--
    This HTML file serves as the main entry point for the Advanced Planning and Scheduling (APS) System.
    It includes a responsive layout with a sidebar, top navbar, and various sections for displaying data.
    The design is based on Bootstrap and includes custom styles for a cohesive look.
-->
<!--
    The sidebar contains links to different sections of the APS System, including Dashboard, Schedules, Resources, Optimization, Constraints, Simulation, Reports, and Settings.
    The top navbar includes the system title and user controls for notifications and profile settings.
    The main content area displays dashboard cards, charts, and tables related to planning and scheduling.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Planning and Scheduling System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --app-primary-color: #1abc9c; /* Teal for APS */
            --app-primary-dark: #16a085;
            --app-primary-light: #e8f8f5;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            padding-top: 56px; /* Height of navbar */
        }

        /* Sidebar styles */
        .sidebar {
            position: fixed;
            top: 56px; /* Height of navbar */
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: var(--app-primary-color);
            width: 250px;
            transition: all 0.3s;
            overflow-y: auto; /* Add scrollbar when content overflows */
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--app-primary-dark);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar .nav-link {
            color: #f8f9fa;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-heading {
            padding: 0.75rem 1.25rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.1rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Main content area */
        .main-content {
            margin-left: 0;
            transition: margin-left 0.3s;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
            overflow-x: hidden; /* Prevent horizontal overflow */
        }

        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px; /* Width of sidebar */
                width: calc(100% - 250px); /* Adjust width to account for sidebar */
            }
        }

        /* Google integration container styles */
        .container-fluid {
            width: 100%;
            padding-right: 15px;
            padding-left: 15px;
            margin-right: auto;
            margin-left: auto;
            box-sizing: border-box;
        }

        /* Fix for Google integration components to prevent them from going under sidebar */
        .google-integration-component {
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        /* Additional fixes for Google integration layout */
        .main-content .row {
            margin-right: 0;
            margin-left: 0;
            width: 100%;
            position: relative;
            z-index: 1;
        }

        .main-content .container-fluid {
            padding-left: 0;
            padding-right: 0;
        }

        /* Fix for Google integration components in APS */
        .google-sheets-component,
        .google-docs-component,
        .google-drive-component,
        .google-gmail-component {
            position: relative;
            z-index: 1;
            width: 100%;
            display: block;
        }

        /* Navbar */
        .navbar {
            background-color: var(--app-primary-color) !important;
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
        }

        /* Toggle sidebar button */
        #sidebarToggle {
            cursor: pointer;
            background: transparent;
            border: none;
            color: white;
        }

        /* For mobile view */
        @media (max-width: 767.98px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .navbar {
                padding: 0.5rem;
            }
        }
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .card-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .schedule-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .schedule-card:hover {
            transform: translateY(-5px);
        }
        .schedule-header {
            padding: 15px;
            color: white;
        }
        .schedule-active {
            background-color: #28a745;
        }
        .schedule-pending {
            background-color: #ffc107;
        }
        .schedule-completed {
            background-color: #6c757d;
        }
        .schedule-body {
            padding: 20px;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }
        .gantt-chart {
            height: 400px;
            margin-bottom: 20px;
            overflow-x: auto;
        }
        .gantt-row {
            display: flex;
            height: 40px;
            margin-bottom: 5px;
            align-items: center;
        }
        .gantt-row-header {
            width: 200px;
            padding-right: 15px;
            text-align: right;
            font-weight: bold;
        }
        .gantt-row-timeline {
            flex-grow: 1;
            position: relative;
            height: 30px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .gantt-bar {
            position: absolute;
            height: 30px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .gantt-bar-active {
            background-color: #28a745;
        }
        .gantt-bar-pending {
            background-color: #ffc107;
        }
        .gantt-bar-completed {
            background-color: #6c757d;
        }
        .gantt-timeline-header {
            display: flex;
            margin-left: 200px;
            margin-bottom: 10px;
        }
        .gantt-day {
            flex: 1;
            text-align: center;
            font-size: 0.8rem;
            color: #6c757d;
        }
        .resource-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .resource-header {
            padding: 15px;
            background-color: #343a40;
            color: white;
        }
        .resource-body {
            padding: 20px;
        }
        .progress {
            height: 20px;
            margin-top: 10px;
            margin-bottom: 10px;
            border-radius: 10px;
        }
        .optimization-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .optimization-card:hover {
            transform: translateY(-5px);
        }
        .optimization-header {
            padding: 15px;
            background-color: #007bff;
            color: white;
        }
        .optimization-body {
            padding: 20px;
        }
        .optimization-metric {
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .optimization-value {
            font-size: 24px;
            font-weight: bold;
            color: #343a40;
        }
        .optimization-label {
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-dark fixed-top" style="background-color: #1abc9c;">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <button id="sidebarToggle" class="d-md-none me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-list fs-4"></i>
                </button>
                <a class="navbar-brand" href="/">APS System</a>
            </div>
            <div class="d-flex">
                <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3" style="background: transparent; border: 1px solid white; color: white; padding: 5px 10px;">
                    <i class="bi bi-box-arrow-left me-1"></i> Back to Hub
                </a>
                <button class="btn position-relative me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-bell fs-5"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning text-dark">
                        2
                    </span>
                </button>
                <button class="btn" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-person-circle fs-5"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="pt-2">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#schedules">
                        <i class="bi bi-calendar3"></i>
                        Production Schedules
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#resources">
                        <i class="bi bi-gear"></i>
                        Resources
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#optimization">
                        <i class="bi bi-graph-up"></i>
                        Optimization
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#constraints">
                        <i class="bi bi-exclamation-triangle"></i>
                        Constraints
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#simulation">
                        <i class="bi bi-play-circle"></i>
                        Simulation
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#reports">
                        <i class="bi bi-file-earmark-text"></i>
                        Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#settings">
                        <i class="bi bi-gear-fill"></i>
                        Settings
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                <span>Integrations</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="#mrp">
                        <i class="bi bi-box-seam"></i>
                        MRP System
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#wms">
                        <i class="bi bi-building"></i>
                        WMS System
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal">
                        <i class="bi bi-calendar3"></i>
                        Google Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal">
                        <i class="bi bi-folder"></i>
                        Google Drive
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal">
                        <i class="bi bi-file-earmark-text"></i>
                        Google Docs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                        <i class="bi bi-file-earmark-spreadsheet"></i>
                        Google Sheets
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-maps" data-bs-toggle="modal" data-bs-target="#mapsModal">
                        <i class="bi bi-geo-alt"></i>
                        Google Maps
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#gmail" data-bs-toggle="modal" data-bs-target="#gmailModal">
                        <i class="bi bi-envelope"></i>
                        Gmail
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#slack">
                        <i class="bi bi-slack"></i>
                        Slack
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#attachments" data-bs-toggle="modal" data-bs-target="#attachmentsModal">
                        <i class="bi bi-paperclip"></i>
                        Attachments
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();">
                        <i class="bi bi-box-arrow-left"></i>
                        Back to Hub
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">Advanced Planning & Scheduling Dashboard</h1>
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                </div>
                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle">
                    <i class="bi bi-calendar"></i>
                    This month
                </button>
            </div>
        </div>



                <!-- AI Insights Card -->
                <div class="card mb-4" style="border-left: 4px solid #007bff;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-robot"></i> AI-Powered Schedule Optimization
                            <span class="badge bg-primary ms-2">AI Insights</span>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" id="refresh-insights-btn">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                            <div class="dropdown d-inline-block ms-2">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="insightsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-gear"></i>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="insightsDropdown">
                                    <li><a class="dropdown-item" href="#" id="expand-all-insights">Expand All</a></li>
                                    <li><a class="dropdown-item" href="#" id="collapse-all-insights">Collapse All</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" id="export-insights">Export Insights</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="ai-insights-container">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Cards -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-calendar3 card-icon"></i>
                                <h5 class="card-title">Active Schedules</h5>
                                <h2 class="card-text">5</h2>
                                <p class="card-text">Production lines</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-check-circle card-icon"></i>
                                <h5 class="card-title">On-Time</h5>
                                <h2 class="card-text">92%</h2>
                                <p class="card-text">Production efficiency</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-warning text-dark">
                            <div class="card-body text-center">
                                <i class="bi bi-gear card-icon"></i>
                                <h5 class="card-title">Resource Utilization</h5>
                                <h2 class="card-text">78%</h2>
                                <p class="card-text">Average across lines</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-graph-up card-icon"></i>
                                <h5 class="card-title">Optimization</h5>
                                <h2 class="card-text">15%</h2>
                                <p class="card-text">Potential improvement</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gantt Chart -->
                <div class="card dashboard-card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">Production Schedule Gantt Chart</h5>
                    </div>
                    <div class="card-body">
                        <div class="gantt-chart" id="ganttChart">
                            <!-- Gantt chart will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Production Schedules -->
                <h2 class="mt-4">Production Schedules</h2>
                <div class="row" id="scheduleCards">
                    <!-- Schedule cards will be populated by JavaScript -->
                </div>

                <!-- Resource Allocation -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Resource Allocation</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="resourceChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Production Efficiency</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="efficiencyChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Optimization Opportunities -->
                <h2 class="mt-4">Optimization Opportunities</h2>
                <div class="row">
                    <div class="col-md-4">
                        <div class="optimization-card">
                            <div class="optimization-header">
                                <h5>Resource Balancing</h5>
                            </div>
                            <div class="optimization-body">
                                <div class="optimization-metric">
                                    <div class="optimization-value">8%</div>
                                    <div class="optimization-label">Potential Improvement</div>
                                </div>
                                <p>Balancing workload across production lines can improve overall efficiency.</p>
                                <button class="btn btn-primary btn-sm">Apply Optimization</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="optimization-card">
                            <div class="optimization-header">
                                <h5>Sequence Optimization</h5>
                            </div>
                            <div class="optimization-body">
                                <div class="optimization-metric">
                                    <div class="optimization-value">5%</div>
                                    <div class="optimization-label">Potential Improvement</div>
                                </div>
                                <p>Optimizing production sequence can reduce setup times and increase throughput.</p>
                                <button class="btn btn-primary btn-sm">Apply Optimization</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="optimization-card">
                            <div class="optimization-header">
                                <h5>Constraint Relaxation</h5>
                            </div>
                            <div class="optimization-body">
                                <div class="optimization-metric">
                                    <div class="optimization-value">12%</div>
                                    <div class="optimization-label">Potential Improvement</div>
                                </div>
                                <p>Identifying and relaxing non-critical constraints can improve scheduling flexibility.</p>
                                <button class="btn btn-primary btn-sm">Apply Optimization</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Google Integrations -->
                <div class="row mt-5">
                    <div class="col-12">
                        <h4 class="mb-3">Google Integrations</h4>
                    </div>

                    <!-- Google Gmail -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-gmail-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-envelope"></i> Gmail</h5>
                                <div class="component-actions">
                                    <button id="refresh-gmail" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="compose-email" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#gmailModal"><i class="bi bi-pencil"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <a href="#" onclick="openGoogleItem('gmail', 'email', 'schedule-update-line-a')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">PM</div>
                                                <div>
                                                    <div class="fw-bold">Production Manager</div>
                                                    <div class="small text-truncate" style="max-width: 200px;">Schedule update for Line A</div>
                                                </div>
                                            </div>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">10m</span>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('gmail', 'email', 'resource-allocation-report')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">RM</div>
                                                <div>
                                                    <div class="fw-bold">Resource Manager</div>
                                                    <div class="small text-truncate" style="max-width: 200px;">Resource allocation report</div>
                                                </div>
                                            </div>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">1h</span>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('gmail', 'email', 'production-constraints-update')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">TL</div>
                                                <div>
                                                    <div class="fw-bold">Team Lead</div>
                                                    <div class="small text-truncate" style="max-width: 200px;">Production constraints update</div>
                                                </div>
                                            </div>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">3h</span>
                                    </a>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#gmailModal">
                                        <i class="bi bi-envelope me-2"></i>Open Gmail
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Drive -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-drive-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-folder"></i> Google Drive</h5>
                                <div class="component-actions">
                                    <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="upload-file" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-upload"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <a href="#" onclick="openGoogleItem('drive', 'folder', 'production-schedules')" class="list-group-item list-group-item-action">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">Production Schedules</h6>
                                                <small>15 files - Last updated: 2 days ago</small>
                                            </div>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'production-schedules'); event.stopPropagation();">
                                                  <i class="bi bi-folder-symlink"></i> Open
                                                </button>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('drive', 'folder', 'resource-allocation')" class="list-group-item list-group-item-action">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">Resource Allocation</h6>
                                                <small>8 files - Last updated: 1 week ago</small>
                                            </div>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'resource-allocation'); event.stopPropagation();">
                                                  <i class="bi bi-folder-symlink"></i> Open
                                                </button>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('drive', 'pdf', 'production-plan-q2-2025')" class="list-group-item list-group-item-action">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">Production_Plan_Q2_2025.pdf</h6>
                                                <small>2.5 MB - Last updated: Yesterday</small>
                                            </div>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'production-plan-q2-2025'); event.stopPropagation();">
                                                  <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="downloadFile('production-plan-q2-2025.pdf'); event.stopPropagation();">
                                                  <i class="bi bi-download"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('production-plan-q2-2025.pdf'); event.stopPropagation();">
                                                  <i class="bi bi-share"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                                  <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
                                        <i class="bi bi-folder me-2"></i>Open Drive
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Docs -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-docs-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                                <div class="component-actions">
                                    <button id="refresh-docs" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="create-new-doc" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#docsModal"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <a href="#" onclick="openGoogleItem('docs', 'document', 'production-guidelines')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <span>Production Guidelines</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-primary rounded-pill me-2">Today</span>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'production-guidelines'); event.stopPropagation();">
                                                  <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="downloadFile('production-guidelines.docx'); event.stopPropagation();">
                                                  <i class="bi bi-download"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('production-guidelines.docx'); event.stopPropagation();">
                                                  <i class="bi bi-share"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                                  <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('docs', 'document', 'scheduling-procedures')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <span>Scheduling Procedures</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-primary rounded-pill me-2">Yesterday</span>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'scheduling-procedures'); event.stopPropagation();">
                                                  <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="downloadFile('scheduling-procedures.docx'); event.stopPropagation();">
                                                  <i class="bi bi-download"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('scheduling-procedures.docx'); event.stopPropagation();">
                                                  <i class="bi bi-share"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                                  <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('docs', 'document', 'optimization-document')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <span>Optimization Document</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-primary rounded-pill me-2">3 days ago</span>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'optimization-document'); event.stopPropagation();">
                                                  <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="downloadFile('optimization-document.docx'); event.stopPropagation();">
                                                  <i class="bi bi-download"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('optimization-document.docx'); event.stopPropagation();">
                                                  <i class="bi bi-share"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                                  <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#docsModal">
                                        <i class="bi bi-file-earmark-text me-2"></i>View All Documents
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Sheets -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-sheets-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                                <div class="component-actions">
                                    <button id="refresh-sheets" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="create-new-sheet" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#sheetsModal"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'production-schedule-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <span>Production_Schedule_Q2_2025.xlsx</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-primary rounded-pill me-2">2 days ago</span>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'production-schedule-q2-2025'); event.stopPropagation();">
                                                  <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="downloadFile('production-schedule-q2-2025.xlsx'); event.stopPropagation();">
                                                  <i class="bi bi-download"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('production-schedule-q2-2025.xlsx'); event.stopPropagation();">
                                                  <i class="bi bi-share"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                                  <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'resource-allocation-matrix')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <span>Resource_Allocation_Matrix.xlsx</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-primary rounded-pill me-2">1 week ago</span>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'resource-allocation-matrix'); event.stopPropagation();">
                                                  <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="downloadFile('resource-allocation-matrix.xlsx'); event.stopPropagation();">
                                                  <i class="bi bi-download"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('resource-allocation-matrix.xlsx'); event.stopPropagation();">
                                                  <i class="bi bi-share"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                                  <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'optimization-metrics-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <span>Optimization_Metrics_2025.xlsx</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-primary rounded-pill me-2">2 weeks ago</span>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'optimization-metrics-2025'); event.stopPropagation();">
                                                  <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="downloadFile('optimization-metrics-2025.xlsx'); event.stopPropagation();">
                                                  <i class="bi bi-download"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('optimization-metrics-2025.xlsx'); event.stopPropagation();">
                                                  <i class="bi bi-share"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                                  <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                                        <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Calendar -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-calendar-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                                <div class="component-actions">
                                    <button id="refresh-calendar" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="create-new-event" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#calendarModal"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <a href="#" onclick="openGoogleItem('calendar', 'event', 'production-planning-meeting')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Production Planning Meeting</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-clock me-1"></i>Today, 10:00 AM - 11:30 AM
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-warning rounded-pill me-2">Today</span>
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'production-planning-meeting'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                        </div>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('calendar', 'event', 'resource-allocation-review')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Resource Allocation Review</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-clock me-1"></i>Tomorrow, 2:00 PM - 3:00 PM
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-info rounded-pill me-2">Tomorrow</span>
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'resource-allocation-review'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                        </div>
                                    </a>
                                    <a href="#" onclick="openGoogleItem('calendar', 'event', 'optimization-strategy-session')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Optimization Strategy Session</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-clock me-1"></i>May 15, 9:00 AM - 12:00 PM
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-secondary rounded-pill me-2">Next Week</span>
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'optimization-strategy-session'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                        </div>
                                    </a>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#calendarModal">
                                        <i class="bi bi-calendar3 me-2"></i>View Full Calendar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Function to generate sample insights when the API is not available
        function generateSampleInsights() {
            const sampleData = {
                scheduleOptimization: {
                    productionLines: [
                        {
                            id: 1,
                            name: "Production Line A",
                            currentEfficiency: 85,
                            potentialEfficiency: 92,
                            bottleneck: "Setup Time",
                            recommendation: "Reduce setup time by implementing quick-change tooling"
                        },
                        {
                            id: 2,
                            name: "Production Line B",
                            currentEfficiency: 78,
                            potentialEfficiency: 88,
                            bottleneck: "Material Handling",
                            recommendation: "Optimize material flow to reduce waiting time"
                        },
                        {
                            id: 3,
                            name: "Production Line C",
                            currentEfficiency: 82,
                            potentialEfficiency: 90,
                            bottleneck: "Quality Inspection",
                            recommendation: "Implement inline quality inspection to reduce delays"
                        }
                    ]
                },
                resourceAllocation: {
                    resources: [
                        {
                            id: 1,
                            name: "Machine Operators",
                            currentUtilization: 75,
                            optimalUtilization: 85,
                            recommendation: "Cross-train operators to improve flexibility"
                        },
                        {
                            id: 2,
                            name: "CNC Machines",
                            currentUtilization: 65,
                            optimalUtilization: 90,
                            recommendation: "Implement predictive maintenance to reduce downtime"
                        },
                        {
                            id: 3,
                            name: "Assembly Stations",
                            currentUtilization: 80,
                            optimalUtilization: 95,
                            recommendation: "Reorganize workstations to improve workflow"
                        }
                    ]
                },
                sequenceOptimization: {
                    sequences: [
                        {
                            id: 1,
                            productGroup: "Product Family A",
                            currentSetupTime: 45,
                            optimizedSetupTime: 25,
                            timeSavings: 20,
                            recommendation: "Group similar products to minimize changeover time"
                        },
                        {
                            id: 2,
                            productGroup: "Product Family B",
                            currentSetupTime: 60,
                            optimizedSetupTime: 35,
                            timeSavings: 25,
                            recommendation: "Optimize sequence to minimize color and material changes"
                        },
                        {
                            id: 3,
                            productGroup: "Product Family C",
                            currentSetupTime: 30,
                            optimizedSetupTime: 15,
                            timeSavings: 15,
                            recommendation: "Implement SMED techniques for faster changeovers"
                        }
                    ]
                },
                recommendations: [
                    "Implement advanced scheduling algorithm to optimize production sequence",
                    "Develop cross-training program to improve resource flexibility",
                    "Invest in quick-change tooling to reduce setup times",
                    "Implement real-time monitoring to identify bottlenecks faster",
                    "Develop contingency plans for critical resource failures"
                ],
                timestamp: new Date().toISOString()
            };

            const insightsContainer = document.getElementById('ai-insights-container');
            insightsContainer.innerHTML = '';

            // Store insights data globally for export
            window.aiInsightsData = sampleData;

            // Create insights sections
            createScheduleOptimizationSection(sampleData, insightsContainer);
            createResourceAllocationSection(sampleData, insightsContainer);
            createSequenceOptimizationSection(sampleData, insightsContainer);
            createRecommendationsSection(sampleData, insightsContainer);

            // Add event listeners for interactive elements
            addInteractiveEventListeners(sampleData);
        }

        // Function to fetch and display AI-powered schedule optimization insights
        function fetchAIInsights() {
            const insightsContainer = document.getElementById('ai-insights-container');

            // Show loading spinner
            insightsContainer.innerHTML = `
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;

            // Fetch insights from the Integration Hub
            fetch('http://localhost:8000/api/ai-analytics/schedule-optimization')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('AI insights:', data);

                    // Store insights data globally for export
                    window.aiInsightsData = data;

                    // Clear the container
                    insightsContainer.innerHTML = '';

                    // Generate sample insights if the API doesn't return the expected data structure
                    if (!data.scheduleOptimization || !data.resourceAllocation || !data.sequenceOptimization) {
                        generateSampleInsights();
                        return;
                    }

                    // Create insights sections
                    createScheduleOptimizationSection(data, insightsContainer);
                    createResourceAllocationSection(data, insightsContainer);
                    createSequenceOptimizationSection(data, insightsContainer);
                    createRecommendationsSection(data, insightsContainer);

                    // Add event listeners for interactive elements
                    addInteractiveEventListeners(data);
                })
                .catch(error => {
                    console.error('Error fetching AI insights:', error);
                    insightsContainer.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            Unable to connect to AI analytics engine. Please make sure the Integration Hub is running.
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-primary" id="generate-sample-insights">
                                    Generate Sample Insights
                                </button>
                            </div>
                        </div>
                    `;

                    // Add event listener for the sample insights button
                    document.getElementById('generate-sample-insights').addEventListener('click', generateSampleInsights);
                });
        }

        // Sample schedule data
        const scheduleData = [
            { id: 1, name: 'Production Line A', startDate: '2025-05-01', endDate: '2025-05-15', status: 'active' },
            { id: 2, name: 'Production Line B', startDate: '2025-05-10', endDate: '2025-05-25', status: 'pending' },
            { id: 3, name: 'Maintenance Schedule', startDate: '2025-05-05', endDate: '2025-05-07', status: 'completed' },
            { id: 4, name: 'Production Line C', startDate: '2025-05-03', endDate: '2025-05-18', status: 'active' },
            { id: 5, name: 'Quality Testing', startDate: '2025-05-12', endDate: '2025-05-14', status: 'pending' }
        ];

        // Sample resource data
        const resourceData = {
            labels: ['Production Line A', 'Production Line B', 'Production Line C', 'Maintenance', 'Quality Testing'],
            datasets: [
                {
                    label: 'Allocated Hours',
                    data: [120, 80, 100, 40, 60],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Available Hours',
                    data: [160, 160, 160, 80, 80],
                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                }
            ]
        };

        // Sample efficiency data
        const efficiencyData = {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            datasets: [
                {
                    label: 'Actual Production',
                    data: [85, 92, 88, 95],
                    borderColor: 'rgba(40, 167, 69, 1)',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'Target Production',
                    data: [90, 90, 90, 90],
                    borderColor: 'rgba(220, 53, 69, 1)',
                    backgroundColor: 'transparent',
                    borderDash: [5, 5],
                    tension: 0
                }
            ]
        };

        // Populate schedule cards
        function populateScheduleCards() {
            const scheduleCardsContainer = document.getElementById('scheduleCards');
            scheduleCardsContainer.innerHTML = '';

            scheduleData.forEach(schedule => {
                // Determine schedule class based on status
                let statusClass = 'schedule-active';
                if (schedule.status === 'pending') {
                    statusClass = 'schedule-pending';
                } else if (schedule.status === 'completed') {
                    statusClass = 'schedule-completed';
                }

                const card = document.createElement('div');
                card.className = 'col-md-4';
                card.innerHTML = `
                    <div class="schedule-card">
                        <div class="schedule-header ${statusClass}">
                            <h4>${schedule.name}</h4>
                            <p class="mb-0">${schedule.status.charAt(0).toUpperCase() + schedule.status.slice(1)}</p>
                        </div>
                        <div class="schedule-body">
                            <p><strong>Start Date:</strong> ${schedule.startDate}</p>
                            <p><strong>End Date:</strong> ${schedule.endDate}</p>
                            <div class="mt-3">
                                <button class="btn btn-primary btn-sm">View Details</button>
                                <button class="btn btn-outline-secondary btn-sm">Edit</button>
                            </div>
                        </div>
                    </div>
                `;

                scheduleCardsContainer.appendChild(card);
            });
        }

        // Create Gantt chart
        function createGanttChart() {
            const ganttChart = document.getElementById('ganttChart');

            // Create timeline header with days
            const timelineHeader = document.createElement('div');
            timelineHeader.className = 'gantt-timeline-header';

            // Generate days for the timeline (31 days)
            for (let i = 1; i <= 31; i++) {
                const day = document.createElement('div');
                day.className = 'gantt-day';
                day.textContent = `May ${i}`;
                timelineHeader.appendChild(day);
            }

            ganttChart.appendChild(timelineHeader);

            // Create Gantt rows for each schedule
            scheduleData.forEach(schedule => {
                const row = document.createElement('div');
                row.className = 'gantt-row';

                // Row header with schedule name
                const rowHeader = document.createElement('div');
                rowHeader.className = 'gantt-row-header';
                rowHeader.textContent = schedule.name;
                row.appendChild(rowHeader);

                // Row timeline
                const rowTimeline = document.createElement('div');
                rowTimeline.className = 'gantt-row-timeline';

                // Calculate position and width of the Gantt bar
                const startDay = parseInt(schedule.startDate.split('-')[2]);
                const endDay = parseInt(schedule.endDate.split('-')[2]);
                const duration = endDay - startDay + 1;

                // Create Gantt bar
                const bar = document.createElement('div');
                bar.className = `gantt-bar gantt-bar-${schedule.status}`;
                bar.style.left = `${(startDay - 1) * (100 / 31)}%`;
                bar.style.width = `${duration * (100 / 31)}%`;
                bar.textContent = schedule.name;

                rowTimeline.appendChild(bar);
                row.appendChild(rowTimeline);

                ganttChart.appendChild(row);
            });
        }

        // Create resource allocation chart
        function createResourceChart() {
            const ctx = document.getElementById('resourceChart').getContext('2d');

            new Chart(ctx, {
                type: 'bar',
                data: resourceData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Hours'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Resources'
                            }
                        }
                    }
                }
            });
        }

        // Create efficiency chart
        function createEfficiencyChart() {
            const ctx = document.getElementById('efficiencyChart').getContext('2d');

            new Chart(ctx, {
                type: 'line',
                data: efficiencyData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Efficiency (%)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Time Period'
                            }
                        }
                    }
                }
            });
        }

        // Function to create the Schedule Optimization section
        function createScheduleOptimizationSection(data, container) {
            if (!data.scheduleOptimization || !data.scheduleOptimization.productionLines) {
                return;
            }

            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'scheduleOptimization';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
                <h6 class="mb-0">
                    <i class="bi bi-calendar3 me-2"></i>
                    Production Line Optimization
                </h6>
                <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#scheduleOptimizationInsights">
                    <i class="bi bi-chevron-down"></i>
                </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'scheduleOptimizationInsights';

            const linesRow = document.createElement('div');
            linesRow.className = 'row';

            data.scheduleOptimization.productionLines.forEach(line => {
                const lineCol = document.createElement('div');
                lineCol.className = 'col-md-4 mb-3';

                const lineCard = document.createElement('div');
                lineCard.className = 'card h-100';

                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';

                // Calculate efficiency improvement
                const efficiencyImprovement = line.potentialEfficiency - line.currentEfficiency;

                cardBody.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${line.name}</h6>
                        <span class="badge bg-primary">Efficiency Improvement</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Current Efficiency:</span>
                        <span class="fw-bold">${line.currentEfficiency}%</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Potential Efficiency:</span>
                        <span class="fw-bold">${line.potentialEfficiency}%</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Improvement:</span>
                        <span class="fw-bold text-success">+${efficiencyImprovement}%</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Bottleneck:</span>
                        <span class="fw-bold text-danger">${line.bottleneck}</span>
                    </div>
                    <div class="progress mb-2" style="height: 5px;">
                        <div class="progress-bar bg-primary" style="width: ${line.currentEfficiency}%"></div>
                    </div>
                    <div class="progress mb-3" style="height: 5px;">
                        <div class="progress-bar bg-success" style="width: ${line.potentialEfficiency}%"></div>
                    </div>
                    <p class="small text-muted mb-2">${line.recommendation}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2 optimize-schedule-btn"
                            data-line-id="${line.id}"
                            data-line-name="${line.name}">
                        View Optimization Plan
                    </button>
                `;

                lineCard.appendChild(cardBody);
                lineCol.appendChild(lineCard);
                linesRow.appendChild(lineCol);
            });

            sectionBody.appendChild(linesRow);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            container.appendChild(sectionCol);
        }

        // Function to create the Resource Allocation section
        function createResourceAllocationSection(data, container) {
            if (!data.resourceAllocation || !data.resourceAllocation.resources) {
                return;
            }

            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'resourceAllocation';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
                <h6 class="mb-0">
                    <i class="bi bi-gear me-2"></i>
                    Resource Allocation
                </h6>
                <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#resourceAllocationInsights">
                    <i class="bi bi-chevron-down"></i>
                </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'resourceAllocationInsights';

            const resourcesRow = document.createElement('div');
            resourcesRow.className = 'row';

            data.resourceAllocation.resources.forEach(resource => {
                const resourceCol = document.createElement('div');
                resourceCol.className = 'col-md-4 mb-3';

                const resourceCard = document.createElement('div');
                resourceCard.className = 'card h-100';

                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';

                // Calculate utilization improvement
                const utilizationImprovement = resource.optimalUtilization - resource.currentUtilization;

                cardBody.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${resource.name}</h6>
                        <span class="badge bg-primary">Utilization Improvement</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Current Utilization:</span>
                        <span class="fw-bold">${resource.currentUtilization}%</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Optimal Utilization:</span>
                        <span class="fw-bold">${resource.optimalUtilization}%</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Improvement:</span>
                        <span class="fw-bold text-success">+${utilizationImprovement}%</span>
                    </div>
                    <div class="progress mb-2" style="height: 5px;">
                        <div class="progress-bar bg-primary" style="width: ${resource.currentUtilization}%"></div>
                    </div>
                    <div class="progress mb-3" style="height: 5px;">
                        <div class="progress-bar bg-success" style="width: ${resource.optimalUtilization}%"></div>
                    </div>
                    <p class="small text-muted mb-2">${resource.recommendation}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2 optimize-resource-btn"
                            data-resource-id="${resource.id}"
                            data-resource-name="${resource.name}">
                        View Allocation Plan
                    </button>
                `;

                resourceCard.appendChild(cardBody);
                resourceCol.appendChild(resourceCard);
                resourcesRow.appendChild(resourceCol);
            });

            sectionBody.appendChild(resourcesRow);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            container.appendChild(sectionCol);
        }

        // Function to create the Sequence Optimization section
        function createSequenceOptimizationSection(data, container) {
            if (!data.sequenceOptimization || !data.sequenceOptimization.sequences) {
                return;
            }

            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'sequenceOptimization';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
                <h6 class="mb-0">
                    <i class="bi bi-arrow-left-right me-2"></i>
                    Sequence Optimization
                </h6>
                <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#sequenceOptimizationInsights">
                    <i class="bi bi-chevron-down"></i>
                </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'sequenceOptimizationInsights';

            const sequencesRow = document.createElement('div');
            sequencesRow.className = 'row';

            data.sequenceOptimization.sequences.forEach(sequence => {
                const sequenceCol = document.createElement('div');
                sequenceCol.className = 'col-md-4 mb-3';

                const sequenceCard = document.createElement('div');
                sequenceCard.className = 'card h-100';

                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';

                // Calculate percentage improvement
                const percentImprovement = ((sequence.currentSetupTime - sequence.optimizedSetupTime) / sequence.currentSetupTime * 100).toFixed(0);

                cardBody.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${sequence.productGroup}</h6>
                        <span class="badge bg-primary">Setup Time Reduction</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Current Setup Time:</span>
                        <span class="fw-bold">${sequence.currentSetupTime} min</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Optimized Setup Time:</span>
                        <span class="fw-bold">${sequence.optimizedSetupTime} min</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Time Savings:</span>
                        <span class="fw-bold text-success">${sequence.timeSavings} min (${percentImprovement}%)</span>
                    </div>
                    <div class="progress mb-3" style="height: 5px;">
                        <div class="progress-bar bg-success" style="width: ${percentImprovement}%"></div>
                    </div>
                    <p class="small text-muted mb-2">${sequence.recommendation}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2 optimize-sequence-btn"
                            data-sequence-id="${sequence.id}"
                            data-product-group="${sequence.productGroup}">
                        View Sequence Plan
                    </button>
                `;

                sequenceCard.appendChild(cardBody);
                sequenceCol.appendChild(sequenceCard);
                sequencesRow.appendChild(sequenceCol);
            });

            sectionBody.appendChild(sequencesRow);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            container.appendChild(sectionCol);
        }

        // Function to add interactive event listeners to the AI insights
        function addInteractiveEventListeners(data) {
            // Add event listeners for schedule optimization buttons
            document.querySelectorAll('.optimize-schedule-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const lineId = this.dataset.lineId;
                    const lineName = this.dataset.lineName;

                    // Find the production line in the data
                    let line;
                    if (data.scheduleOptimization && data.scheduleOptimization.productionLines) {
                        line = data.scheduleOptimization.productionLines.find(l => l.id == lineId);
                    }

                    if (!line) return;

                    // Create modal for schedule optimization
                    const modalHTML = `
                        <div class="modal fade" id="scheduleOptimizationModal" tabindex="-1" aria-labelledby="scheduleOptimizationModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="scheduleOptimizationModalLabel">Production Line Optimization: ${lineName}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle"></i>
                                            ${line.recommendation}
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Current Status</div>
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Current Efficiency:</span>
                                                            <span class="fw-bold">${line.currentEfficiency}%</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Potential Efficiency:</span>
                                                            <span class="fw-bold">${line.potentialEfficiency}%</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Bottleneck:</span>
                                                            <span class="fw-bold text-danger">${line.bottleneck}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Implementation Plan</div>
                                                    <div class="card-body">
                                                        <ol>
                                                            <li class="mb-2">Analyze current ${line.bottleneck.toLowerCase()} process</li>
                                                            <li class="mb-2">Identify specific improvement opportunities</li>
                                                            <li class="mb-2">Develop implementation plan</li>
                                                            <li class="mb-2">Train staff on new procedures</li>
                                                            <li class="mb-2">Implement changes during scheduled downtime</li>
                                                            <li class="mb-2">Monitor results and make adjustments</li>
                                                        </ol>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-3">
                                            <div class="card-header">Expected Benefits</div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h6>Efficiency Improvements</h6>
                                                        <ul>
                                                            <li>Efficiency increase: ${line.potentialEfficiency - line.currentEfficiency}%</li>
                                                            <li>Throughput increase: ~${Math.round((line.potentialEfficiency - line.currentEfficiency) * 1.2)}%</li>
                                                            <li>Setup time reduction: ~${Math.round((line.potentialEfficiency - line.currentEfficiency) * 0.8)}%</li>
                                                            <li>Quality improvement: ~${Math.round((line.potentialEfficiency - line.currentEfficiency) * 0.5)}%</li>
                                                        </ul>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h6>Implementation Details</h6>
                                                        <ul>
                                                            <li>Implementation time: 2-3 weeks</li>
                                                            <li>Required resources: 3-4 team members</li>
                                                            <li>Downtime required: 1-2 days</li>
                                                            <li>ROI timeframe: 3-4 months</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="notifyTeam">
                                            <label class="form-check-label" for="notifyTeam">Notify production team about this plan</label>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary">Implement Plan</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Remove any existing modal
                    const existingModal = document.getElementById('scheduleOptimizationModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // Add modal to the document
                    document.body.insertAdjacentHTML('beforeend', modalHTML);

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('scheduleOptimizationModal'));
                    modal.show();
                });
            });

            // Add event listeners for resource allocation buttons
            document.querySelectorAll('.optimize-resource-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const resourceId = this.dataset.resourceId;
                    const resourceName = this.dataset.resourceName;

                    // Find the resource in the data
                    let resource;
                    if (data.resourceAllocation && data.resourceAllocation.resources) {
                        resource = data.resourceAllocation.resources.find(r => r.id == resourceId);
                    }

                    if (!resource) return;

                    // Create modal for resource allocation
                    const modalHTML = `
                        <div class="modal fade" id="resourceAllocationModal" tabindex="-1" aria-labelledby="resourceAllocationModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="resourceAllocationModalLabel">Resource Allocation: ${resourceName}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle"></i>
                                            ${resource.recommendation}
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Current Status</div>
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Current Utilization:</span>
                                                            <span class="fw-bold">${resource.currentUtilization}%</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Optimal Utilization:</span>
                                                            <span class="fw-bold">${resource.optimalUtilization}%</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Improvement:</span>
                                                            <span class="fw-bold text-success">+${resource.optimalUtilization - resource.currentUtilization}%</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Allocation Plan</div>
                                                    <div class="card-body">
                                                        <ol>
                                                            <li class="mb-2">Review current resource allocation</li>
                                                            <li class="mb-2">Identify underutilized or overutilized periods</li>
                                                            <li class="mb-2">Develop balanced allocation schedule</li>
                                                            <li class="mb-2">Implement resource sharing across production lines</li>
                                                            <li class="mb-2">Monitor utilization and adjust as needed</li>
                                                        </ol>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-3">
                                            <div class="card-header">Resource Schedule</div>
                                            <div class="card-body">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>Time Period</th>
                                                            <th>Current Allocation</th>
                                                            <th>Proposed Allocation</th>
                                                            <th>Change</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td>Morning Shift</td>
                                                            <td>Production Line A</td>
                                                            <td>Production Line A</td>
                                                            <td><span class="badge bg-secondary">No Change</span></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Afternoon Shift</td>
                                                            <td>Production Line B</td>
                                                            <td>Production Line C</td>
                                                            <td><span class="badge bg-warning">Reassign</span></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Evening Shift</td>
                                                            <td>Maintenance</td>
                                                            <td>Production Line B</td>
                                                            <td><span class="badge bg-danger">Change</span></td>
                                                        </tr>
                                                        <tr>
                                                            <td>Weekend</td>
                                                            <td>Idle</td>
                                                            <td>Maintenance</td>
                                                            <td><span class="badge bg-success">Add</span></td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="notifyResourceManagers">
                                            <label class="form-check-label" for="notifyResourceManagers">Notify resource managers</label>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary">Implement Plan</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Remove any existing modal
                    const existingModal = document.getElementById('resourceAllocationModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // Add modal to the document
                    document.body.insertAdjacentHTML('beforeend', modalHTML);

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('resourceAllocationModal'));
                    modal.show();
                });
            });

            // Add event listeners for sequence optimization buttons
            document.querySelectorAll('.optimize-sequence-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const sequenceId = this.dataset.sequenceId;
                    const productGroup = this.dataset.productGroup;

                    // Find the sequence in the data
                    let sequence;
                    if (data.sequenceOptimization && data.sequenceOptimization.sequences) {
                        sequence = data.sequenceOptimization.sequences.find(s => s.id == sequenceId);
                    }

                    if (!sequence) return;

                    // Create modal for sequence optimization
                    const modalHTML = `
                        <div class="modal fade" id="sequenceOptimizationModal" tabindex="-1" aria-labelledby="sequenceOptimizationModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="sequenceOptimizationModalLabel">Sequence Optimization: ${productGroup}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle"></i>
                                            ${sequence.recommendation}
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Current Sequence</div>
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Current Setup Time:</span>
                                                            <span class="fw-bold">${sequence.currentSetupTime} min</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Optimized Setup Time:</span>
                                                            <span class="fw-bold">${sequence.optimizedSetupTime} min</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Time Savings:</span>
                                                            <span class="fw-bold text-success">${sequence.timeSavings} min</span>
                                                        </div>
                                                        <div class="mt-3">
                                                            <h6>Current Production Sequence:</h6>
                                                            <ol>
                                                                <li>Product A (Light Color)</li>
                                                                <li>Product C (Dark Color)</li>
                                                                <li>Product B (Medium Color)</li>
                                                                <li>Product D (Light Color)</li>
                                                            </ol>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Optimized Sequence</div>
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Optimization Method:</span>
                                                            <span class="fw-bold">Color Progression</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Implementation Complexity:</span>
                                                            <span class="fw-bold">Low</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Impact:</span>
                                                            <span class="fw-bold text-success">High</span>
                                                        </div>
                                                        <div class="mt-3">
                                                            <h6>Optimized Production Sequence:</h6>
                                                            <ol>
                                                                <li>Product A (Light Color)</li>
                                                                <li>Product D (Light Color)</li>
                                                                <li>Product B (Medium Color)</li>
                                                                <li>Product C (Dark Color)</li>
                                                            </ol>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-3">
                                            <div class="card-header">Implementation Plan</div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <ol>
                                                            <li class="mb-2">Update production schedule with new sequence</li>
                                                            <li class="mb-2">Notify production team of sequence change</li>
                                                            <li class="mb-2">Update material handling procedures</li>
                                                            <li class="mb-2">Implement sequence in next production run</li>
                                                            <li class="mb-2">Monitor setup times and product quality</li>
                                                            <li class="mb-2">Document results and refine sequence if needed</li>
                                                        </ol>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="updateProductionSchedule" checked>
                                            <label class="form-check-label" for="updateProductionSchedule">Update production schedule</label>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary">Implement Sequence</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Remove any existing modal
                    const existingModal = document.getElementById('sequenceOptimizationModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // Add modal to the document
                    document.body.insertAdjacentHTML('beforeend', modalHTML);

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('sequenceOptimizationModal'));
                    modal.show();
                });
            });

            // Add event listeners for action buttons
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.dataset.recommendationIndex);
                    const recommendation = data.recommendations[index];

                    // Create modal for action plan
                    const modalHTML = `
                        <div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="actionModalLabel">Action Plan</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <p><strong>Recommendation:</strong> ${recommendation}</p>
                                        <form>
                                            <div class="mb-3">
                                                <label for="actionType" class="form-label">Action Type</label>
                                                <select class="form-select" id="actionType">
                                                    <option>Create Task</option>
                                                    <option>Schedule Meeting</option>
                                                    <option>Create Project</option>
                                                    <option>Assign to Team</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="assignee" class="form-label">Assign to</label>
                                                <select class="form-select" id="assignee">
                                                    <option>Production Manager</option>
                                                    <option>Process Engineer</option>
                                                    <option>Quality Control</option>
                                                    <option>Maintenance Team</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="dueDate" class="form-label">Due Date</label>
                                                <input type="date" class="form-control" id="dueDate">
                                            </div>
                                            <div class="mb-3">
                                                <label for="priority" class="form-label">Priority</label>
                                                <select class="form-select" id="priority">
                                                    <option>High</option>
                                                    <option selected>Medium</option>
                                                    <option>Low</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="notes" class="form-label">Notes</label>
                                                <textarea class="form-control" id="notes" rows="3"></textarea>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary">Create Task</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Remove any existing modal
                    const existingModal = document.getElementById('actionModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // Add modal to the document
                    document.body.insertAdjacentHTML('beforeend', modalHTML);

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('actionModal'));
                    modal.show();
                });
            });
        }

        // Function to create the Recommendations section
        function createRecommendationsSection(data, container) {
            if (!data.recommendations || !data.recommendations.length) {
                return;
            }

            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'recommendations';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    AI Recommendations
                </h6>
                <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#recommendationsSection">
                    <i class="bi bi-chevron-down"></i>
                </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'recommendationsSection';

            const recommendationsList = document.createElement('ul');
            recommendationsList.className = 'list-group';

            data.recommendations.forEach((recommendation, index) => {
                const item = document.createElement('li');
                item.className = 'list-group-item d-flex align-items-center';

                item.innerHTML = `
                    <div class="me-3">
                        <span class="badge bg-primary rounded-circle">
                            ${index + 1}
                        </span>
                    </div>
                    <div>
                        ${recommendation}
                    </div>
                    <div class="ms-auto">
                        <button class="btn btn-sm btn-outline-success action-btn" data-recommendation-index="${index}">
                            Take Action
                        </button>
                    </div>
                `;

                recommendationsList.appendChild(item);
            });

            sectionBody.appendChild(recommendationsList);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            container.appendChild(sectionCol);
        }

        // Google Calendar Component
        const GoogleCalendarHandler = {
            init() {
                // Dashboard component event listeners
                const refreshCalendarBtn = document.getElementById('refresh-calendar');
                if (refreshCalendarBtn) {
                    refreshCalendarBtn.addEventListener('click', this.refreshCalendar.bind(this));
                }

                const createEventBtn = document.getElementById('create-new-event');
                if (createEventBtn) {
                    createEventBtn.addEventListener('click', this.createEvent.bind(this));
                }
            },

            refreshCalendar() {
                console.log('Refreshing calendar...');
                // Simulate API call to refresh calendar data
                setTimeout(() => {
                    console.log('Calendar refreshed');
                    alert('Calendar refreshed successfully!');
                }, 1000);
            },

            createEvent() {
                console.log('Creating new event...');
                // In a real implementation, this would open a modal or form to create a new event
                const eventName = prompt('Enter event name:');
                if (eventName) {
                    alert(`Event "${eventName}" created successfully!`);
                }
            }
        };

        // Google Sheets Component
        const GoogleSheetsHandler = {
            init() {
                // Dashboard component event listeners
                const refreshSheetsBtn = document.getElementById('refresh-sheets');
                if (refreshSheetsBtn) {
                    refreshSheetsBtn.addEventListener('click', this.refreshSheets.bind(this));
                }

                const createSheetBtn = document.getElementById('create-new-sheet');
                if (createSheetBtn) {
                    createSheetBtn.addEventListener('click', this.createSheet.bind(this));
                }
            },

            refreshSheets() {
                console.log('Refreshing sheets...');
                // Simulate API call to refresh sheets data
                setTimeout(() => {
                    console.log('Sheets refreshed');
                    alert('Sheets refreshed successfully!');
                }, 1000);
            },

            createSheet() {
                console.log('Creating new sheet...');
                // Open the Google Sheets modal with the Create New tab selected
                const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
                sheetsModal.show();

                // Switch to the Create New tab
                const createTab = document.getElementById('create-tab');
                if (createTab) {
                    createTab.click();
                }
            }
        };

        // Google Docs Component
        const GoogleDocsHandler = {
            init() {
                // Dashboard component event listeners
                const refreshDocsBtn = document.getElementById('refresh-docs');
                if (refreshDocsBtn) {
                    refreshDocsBtn.addEventListener('click', this.refreshDocs.bind(this));
                }

                const createDocBtn = document.getElementById('create-new-doc');
                if (createDocBtn) {
                    createDocBtn.addEventListener('click', this.createDoc.bind(this));
                }
            },

            refreshDocs() {
                console.log('Refreshing docs...');
                // Simulate API call to refresh docs data
                setTimeout(() => {
                    console.log('Docs refreshed');
                    alert('Docs refreshed successfully!');
                }, 1000);
            },

            createDoc() {
                console.log('Creating new doc...');
                // Open the Google Docs modal with the Create New tab selected
                const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
                docsModal.show();

                // Switch to the Create New tab
                const createDocTab = document.getElementById('create-doc-tab');
                if (createDocTab) {
                    createDocTab.click();
                }
            }
        };

        // Google Drive Component
        const GoogleDriveHandler = {
            init() {
                // Dashboard component event listeners
                const refreshDriveBtn = document.getElementById('refresh-drive');
                if (refreshDriveBtn) {
                    refreshDriveBtn.addEventListener('click', this.refreshDrive.bind(this));
                }

                const uploadFileBtn = document.getElementById('upload-file');
                if (uploadFileBtn) {
                    uploadFileBtn.addEventListener('click', this.uploadFile.bind(this));
                }
            },

            refreshDrive() {
                console.log('Refreshing drive...');
                // Simulate API call to refresh drive data
                setTimeout(() => {
                    console.log('Drive refreshed');
                    alert('Drive refreshed successfully!');
                }, 1000);
            },

            uploadFile() {
                console.log('Uploading file...');
                // In a real implementation, this would open a file picker
                alert('File upload functionality would open a file picker dialog.');
            }
        };

        // Google Gmail Component
        const GoogleGmailHandler = {
            init() {
                // Dashboard component event listeners
                const refreshGmailBtn = document.getElementById('refresh-gmail');
                if (refreshGmailBtn) {
                    refreshGmailBtn.addEventListener('click', this.refreshGmail.bind(this));
                }

                const composeEmailBtn = document.getElementById('compose-email');
                if (composeEmailBtn) {
                    composeEmailBtn.addEventListener('click', this.composeEmail.bind(this));
                }
            },

            refreshGmail() {
                console.log('Refreshing Gmail...');
                // Simulate API call to refresh Gmail data
                setTimeout(() => {
                    console.log('Gmail refreshed');
                    alert('Gmail refreshed successfully!');
                }, 1000);
            },

            composeEmail() {
                console.log('Composing email...');
                // In a real implementation, this would open a modal or form to compose an email
                // The modal is triggered by data-bs-toggle and data-bs-target attributes
            }
        };

        // Initialize the page
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize sidebar toggle functionality
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    sidebar.classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 768) {
                    const isClickInsideSidebar = sidebar.contains(event.target);
                    const isClickOnToggle = sidebarToggle && sidebarToggle.contains(event.target);

                    if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768 && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });

            populateScheduleCards();
            createGanttChart();
            createResourceChart();
            createEfficiencyChart();

            // Initialize Google Integration Components
            GoogleCalendarHandler.init();
            GoogleSheetsHandler.init();
            GoogleDocsHandler.init();
            GoogleDriveHandler.init();
            GoogleGmailHandler.init();

            // Fetch AI insights
            fetchAIInsights();

            // Add event listener for refresh insights button
            document.getElementById('refresh-insights-btn').addEventListener('click', fetchAIInsights);

            // Add event listeners for expand/collapse all insights
            document.getElementById('expand-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.show();
                });
            });

            document.getElementById('collapse-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.hide();
                });
            });

            // Add event listener for export insights
            document.getElementById('export-insights').addEventListener('click', function(e) {
                e.preventDefault();
                if (window.aiInsightsData) {
                    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(window.aiInsightsData, null, 2));
                    const downloadAnchorNode = document.createElement('a');
                    downloadAnchorNode.setAttribute("href", dataStr);
                    downloadAnchorNode.setAttribute("download", "schedule_optimization_insights.json");
                    document.body.appendChild(downloadAnchorNode);
                    downloadAnchorNode.click();
                    downloadAnchorNode.remove();
                }
            });

            // Fetch real data from API
            fetch('/api/schedules')
                .then(response => response.json())
                .then(data => {
                    console.log('API data:', data);
                    // Update UI with real data if needed
                })
                .catch(error => {
                    console.error('Error fetching schedule data:', error);
                });
        });
    </script>

    <!-- Google Docs Modal -->
    <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="docsModalLabel"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="docsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="recent-docs-tab" data-bs-toggle="tab" data-bs-target="#recent-docs" type="button" role="tab" aria-controls="recent-docs" aria-selected="true">Recent Documents</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-doc-tab" data-bs-toggle="tab" data-bs-target="#create-doc" type="button" role="tab" aria-controls="create-doc" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-docs-tab" data-bs-toggle="tab" data-bs-target="#shared-docs" type="button" role="tab" aria-controls="shared-docs" aria-selected="false">Shared With Me</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="docsTabContent">
                        <div class="tab-pane fade show active" id="recent-docs" role="tabpanel" aria-labelledby="recent-docs-tab">
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('docs', 'document', 'production-guidelines')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <strong>Production Guidelines</strong>
                                        <p class="mb-1 small text-muted">Last edited: Today</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary rounded-pill me-2">Shared</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'production-guidelines'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('production-guidelines.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('production-guidelines.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('docs', 'document', 'scheduling-procedures')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <strong>Scheduling Procedures</strong>
                                        <p class="mb-1 small text-muted">Last edited: Yesterday</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-secondary rounded-pill me-2">Private</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'scheduling-procedures'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('scheduling-procedures.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('scheduling-procedures.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('docs', 'document', 'optimization-document')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <strong>Optimization Document</strong>
                                        <p class="mb-1 small text-muted">Last edited: 3 days ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary rounded-pill me-2">Shared</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'optimization-document'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('optimization-document.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('optimization-document.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="create-doc" role="tabpanel" aria-labelledby="create-doc-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="doc-title" class="form-label">Document Title</label>
                                    <input type="text" class="form-control" id="doc-title" placeholder="Enter a title for your document">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Template</label>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-file-earmark text-primary fs-3 mb-2"></i>
                                                    <h6>Blank</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="doc-template" id="blank-doc-template" value="blank" checked>
                                                        <label class="form-check-label" for="blank-doc-template">
                                                            Start from scratch
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-list-check text-success fs-3 mb-2"></i>
                                                    <h6>Procedure</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="doc-template" id="procedure-template" value="procedure">
                                                        <label class="form-check-label" for="procedure-template">
                                                            Procedure template
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-file-text text-danger fs-3 mb-2"></i>
                                                    <h6>Report</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="doc-template" id="report-template" value="report">
                                                        <label class="form-check-label" for="report-template">
                                                            Report template
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Sharing Options</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="doc-sharing" id="private-doc-sharing" value="private" checked>
                                        <label class="form-check-label" for="private-doc-sharing">
                                            Private - Only you can access
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="doc-sharing" id="team-doc-sharing" value="team">
                                        <label class="form-check-label" for="team-doc-sharing">
                                            Team - Your team members can access
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="doc-sharing" id="public-doc-sharing" value="public">
                                        <label class="form-check-label" for="public-doc-sharing">
                                            Public - Anyone with the link can access
                                        </label>
                                    </div>
                                </div>
                                <button type="button" id="create-doc-btn" class="btn btn-primary" onclick="createNewDocument()">Create Document</button>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="shared-docs" role="tabpanel" aria-labelledby="shared-docs-tab">
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('docs', 'document', 'production-capacity-analysis')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <strong>Production Capacity Analysis</strong>
                                        <p class="mb-1 small text-muted">Shared by: John Smith</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-info rounded-pill me-2">View Only</span>
                                        <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'production-capacity-analysis'); event.stopPropagation();">
                                          <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('docs', 'document', 'resource-allocation-guidelines')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <strong>Resource Allocation Guidelines</strong>
                                        <p class="mb-1 small text-muted">Shared by: Sarah Johnson</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-success rounded-pill me-2">Edit</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'resource-allocation-guidelines'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('resource-allocation-guidelines.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('resource-allocation-guidelines.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('docs', 'document', 'quarterly-planning-document')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <strong>Quarterly Planning Document</strong>
                                        <p class="mb-1 small text-muted">Shared by: Michael Brown</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-success rounded-pill me-2">Edit</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'quarterly-planning-document'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('quarterly-planning-document.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('quarterly-planning-document.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://docs.google.com" target="_blank" class="btn btn-primary">Open Google Docs</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Sheets Modal -->
    <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="sheetsModalLabel"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="sheetsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="recent-tab" data-bs-toggle="tab" data-bs-target="#recent-sheets" type="button" role="tab" aria-controls="recent-sheets" aria-selected="true">Recent Sheets</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create-sheet" type="button" role="tab" aria-controls="create-sheet" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import-sheet" type="button" role="tab" aria-controls="import-sheet" aria-selected="false">Import Data</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="sheetsTabContent">
                        <div class="tab-pane fade show active" id="recent-sheets" role="tabpanel" aria-labelledby="recent-tab">
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'production-schedule-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <strong>Production_Schedule_Q2_2025.xlsx</strong>
                                        <p class="mb-1 small text-muted">Last edited: 2 days ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary rounded-pill me-2">Shared</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'production-schedule-q2-2025'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Production_Schedule_Q2_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Production_Schedule_Q2_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'resource-allocation-matrix')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <strong>Resource_Allocation_Matrix.xlsx</strong>
                                        <p class="mb-1 small text-muted">Last edited: 1 week ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-secondary rounded-pill me-2">Private</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'resource-allocation-matrix'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Resource_Allocation_Matrix.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Resource_Allocation_Matrix.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'optimization-metrics-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <strong>Optimization_Metrics_2025.xlsx</strong>
                                        <p class="mb-1 small text-muted">Last edited: 2 weeks ago</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary rounded-pill me-2">Shared</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'optimization-metrics-2025'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Optimization_Metrics_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Optimization_Metrics_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="create-sheet" role="tabpanel" aria-labelledby="create-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="sheet-title" class="form-label">Spreadsheet Title</label>
                                    <input type="text" class="form-control" id="sheet-title" placeholder="Enter a title for your spreadsheet">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Template</label>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-file-earmark text-primary fs-3 mb-2"></i>
                                                    <h6>Blank</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="template" id="blank-template" value="blank" checked>
                                                        <label class="form-check-label" for="blank-template">
                                                            Start from scratch
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-calendar-week text-success fs-3 mb-2"></i>
                                                    <h6>Schedule</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="template" id="schedule-template" value="schedule">
                                                        <label class="form-check-label" for="schedule-template">
                                                            Schedule template
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-graph-up text-danger fs-3 mb-2"></i>
                                                    <h6>Resource Tracker</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="template" id="resource-template" value="resource">
                                                        <label class="form-check-label" for="resource-template">
                                                            Resource tracker
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Sharing Options</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="sharing" id="private-sharing" value="private" checked>
                                        <label class="form-check-label" for="private-sharing">
                                            Private - Only you can access
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="sharing" id="team-sharing" value="team">
                                        <label class="form-check-label" for="team-sharing">
                                            Team - Your team members can access
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="sharing" id="public-sharing" value="public">
                                        <label class="form-check-label" for="public-sharing">
                                            Public - Anyone with the link can access
                                        </label>
                                    </div>
                                </div>
                                <button type="button" id="create-sheet-btn" class="btn btn-primary" onclick="createNewSpreadsheet()">Create Spreadsheet</button>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="import-sheet" role="tabpanel" aria-labelledby="import-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="import-file" class="form-label">Upload File</label>
                                    <input class="form-control" type="file" id="import-file">
                                    <div class="form-text">Supported formats: .xlsx, .xls, .csv, .tsv</div>
                                </div>
                                <div class="mb-3">
                                    <label for="import-sheet-name" class="form-label">Sheet Name</label>
                                    <input type="text" class="form-control" id="import-sheet-name" placeholder="Enter a name for your spreadsheet">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="first-row-headers" checked>
                                        <label class="form-check-label" for="first-row-headers">
                                            First row contains headers
                                        </label>
                                    </div>
                                </div>
                                <button type="button" id="import-sheet-btn" class="btn btn-primary" onclick="importSpreadsheetData()">Import Data</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://docs.google.com/spreadsheets" target="_blank" class="btn btn-primary">Open Google Sheets</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Gmail Modal -->
    <div class="modal fade" id="gmailModal" tabindex="-1" aria-labelledby="gmailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="gmailModalLabel"><i class="bi bi-envelope"></i> Gmail</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="gmailTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox" type="button" role="tab" aria-controls="inbox" aria-selected="true">Inbox</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose" type="button" role="tab" aria-controls="compose" aria-selected="false">Compose</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab" aria-controls="sent" aria-selected="false">Sent</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="read-email-tab" data-bs-toggle="tab" data-bs-target="#read-email-content" type="button" role="tab" aria-controls="read-email-content" aria-selected="false" style="display: none;">Read Email</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="reply-email-tab" data-bs-toggle="tab" data-bs-target="#reply-email-content" type="button" role="tab" aria-controls="reply-email-content" aria-selected="false" style="display: none;">Reply</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="gmailTabContent">
                        <div class="tab-pane fade show active" id="inbox" role="tabpanel" aria-labelledby="inbox-tab">
                            <div class="list-group">
                                <a href="#" onclick="openEmail('schedule-update-line-a')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">PM</div>
                                            <div>
                                                <div class="fw-bold">Production Manager</div>
                                                <div class="small text-truncate" style="max-width: 400px;">Schedule update for Line A</div>
                                                <div class="small text-muted">10:15 AM</div>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">New</span>
                                </a>
                                <a href="#" onclick="openEmail('resource-allocation-report-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">RM</div>
                                            <div>
                                                <div class="fw-bold">Resource Manager</div>
                                                <div class="small text-truncate" style="max-width: 400px;">Resource allocation report for Q2 2025 is now available for review</div>
                                                <div class="small text-muted">Yesterday</div>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openEmail('production-constraints-update')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">TL</div>
                                            <div>
                                                <div class="fw-bold">Team Lead</div>
                                                <div class="small text-truncate" style="max-width: 400px;">Production constraints update - Please review the attached document</div>
                                                <div class="small text-muted">Apr 27</div>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="compose" role="tabpanel" aria-labelledby="compose-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="email-to" class="form-label">To</label>
                                    <input type="email" class="form-control" id="email-to" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label for="email-cc" class="form-label">CC</label>
                                    <input type="email" class="form-control" id="email-cc" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label for="email-subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="email-subject" placeholder="Enter subject">
                                </div>
                                <div class="mb-3">
                                    <label for="email-body" class="form-label">Message</label>
                                    <textarea class="form-control" id="email-body" rows="6" placeholder="Type your message here..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="email-attachments" class="form-label d-flex align-items-center">
                                        <i class="bi bi-paperclip me-2"></i>Attachments
                                    </label>
                                    <div class="input-group">
                                        <input type="file" class="form-control" id="email-attachments" multiple>
                                        <button class="btn btn-outline-secondary" type="button" id="browse-attachments">
                                            <i class="bi bi-folder2-open"></i>
                                        </button>
                                    </div>
                                    <div id="attachment-list" class="mt-2">
                                        <!-- Attachments will be listed here -->
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Discard</button>
                                    <button type="button" class="btn btn-primary">Send</button>
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="sent" role="tabpanel" aria-labelledby="sent-tab">
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sent-production-schedule')">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Production Schedule Update</h6>
                                        <small>Today, 9:30 AM</small>
                                    </div>
                                    <p class="mb-1 small">To: Production Team</p>
                                    <small class="text-muted">Please find attached the updated production schedule for this week.</small>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sent-resource-allocation')">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Resource Allocation Request</h6>
                                        <small>Yesterday</small>
                                    </div>
                                    <p class="mb-1 small">To: Resource Manager</p>
                                    <small class="text-muted">Requesting additional resources for Line B production starting next week.</small>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" onclick="openEmail('sent-optimization-results')">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Optimization Results</h6>
                                        <small>Apr 26</small>
                                    </div>
                                    <p class="mb-1 small">To: Management Team</p>
                                    <small class="text-muted">Here are the results of our latest optimization run for Q2 planning.</small>
                                </a>
                            </div>
                        </div>

                        <!-- Read Email Tab -->
                        <div class="tab-pane fade" id="read-email-content" role="tabpanel" aria-labelledby="read-email-tab">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0" id="read-email-subject">Schedule update for Line A</h5>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="replyToEmail()"><i class="bi bi-reply"></i> Reply</button>
                                        <button class="btn btn-sm btn-outline-primary me-1"><i class="bi bi-reply-all"></i> Reply All</button>
                                        <button class="btn btn-sm btn-outline-primary"><i class="bi bi-forward"></i> Forward</button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-3">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;" id="read-email-avatar">PM</div>
                                                <div>
                                                    <div class="fw-bold" id="read-email-from">Production Manager</div>
                                                    <div class="small text-muted">To: <span id="read-email-to">me</span></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-muted" id="read-email-time">10:15 AM</div>
                                    </div>
                                    <div class="email-body mb-4" id="read-email-body">
                                        <p>Hi,</p>
                                        <p>I'm writing to inform you about the recent updates to the production schedule for Line A. We've made some adjustments to optimize the workflow and improve efficiency.</p>
                                        <p>Key changes include:</p>
                                        <ul>
                                            <li>Rescheduled maintenance to minimize downtime</li>
                                            <li>Adjusted production sequence to reduce changeover times</li>
                                            <li>Allocated additional resources during peak periods</li>
                                            <li>Updated delivery timelines to match the new schedule</li>
                                        </ul>
                                        <p>Please review the attached schedule and let me know if you have any questions or concerns. We'll need to implement these changes by the end of the week.</p>
                                        <p>Best regards,<br>Production Manager</p>
                                    </div>
                                    <div class="email-attachments" id="read-email-attachments">
                                        <h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                                        <div class="list-group">
                                            <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'production-schedule-line-a')" class="list-group-item list-group-item-action d-flex align-items-center">
                                                <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                <div>
                                                    <div>Production_Schedule_Line_A.xlsx</div>
                                                    <small class="text-muted">1.2 MB</small>
                                                </div>
                                                <div class="btn-group ms-auto">
                                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'production-schedule-line-a'); event.stopPropagation();">
                                                        <i class="bi bi-eye"></i> View
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Production_Schedule_Line_A.xlsx'); event.stopPropagation();">
                                                        <i class="bi bi-download"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Production_Schedule_Line_A.xlsx'); event.stopPropagation();">
                                                        <i class="bi bi-share"></i>
                                                    </button>
                                                </div>
                                            </a>
                                            <a href="#" onclick="openGoogleItem('drive', 'pdf', 'line-a-optimization-report')" class="list-group-item list-group-item-action d-flex align-items-center">
                                                <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                                <div>
                                                    <div>Line_A_Optimization_Report.pdf</div>
                                                    <small class="text-muted">2.4 MB</small>
                                                </div>
                                                <div class="btn-group ms-auto">
                                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'line-a-optimization-report'); event.stopPropagation();">
                                                        <i class="bi bi-eye"></i> View
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Line_A_Optimization_Report.pdf'); event.stopPropagation();">
                                                        <i class="bi bi-download"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Line_A_Optimization_Report.pdf'); event.stopPropagation();">
                                                        <i class="bi bi-share"></i>
                                                    </button>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between mt-3">
                                <button class="btn btn-outline-secondary" onclick="document.getElementById('inbox-tab').click();"><i class="bi bi-arrow-left"></i> Back to Inbox</button>
                                <div>
                                    <button class="btn btn-outline-danger me-2"><i class="bi bi-trash"></i> Delete</button>
                                    <button class="btn btn-outline-secondary"><i class="bi bi-archive"></i> Archive</button>
                                </div>
                            </div>
                        </div>

                        <!-- Reply Email Tab -->
                        <div class="tab-pane fade" id="reply-email-content" role="tabpanel" aria-labelledby="reply-email-tab">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="fw-bold">Re: </span>
                                            <span id="reply-email-subject">Schedule update for Line A</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body bg-light">
                                    <div class="d-flex">
                                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;" id="reply-email-avatar">PM</div>
                                        <div>
                                            <div class="fw-bold" id="reply-email-from">Production Manager</div>
                                            <div class="small text-muted" id="reply-email-time">10:15 AM</div>
                                            <div class="mt-2" id="reply-email-original-body">
                                                <p>Hi,</p>
                                                <p>I'm writing to inform you about the recent updates to the production schedule for Line A. We've made some adjustments to optimize the workflow and improve efficiency.</p>
                                                <p>Key changes include:</p>
                                                <ul>
                                                    <li>Rescheduled maintenance to minimize downtime</li>
                                                    <li>Adjusted production sequence to reduce changeover times</li>
                                                    <li>Allocated additional resources during peak periods</li>
                                                    <li>Updated delivery timelines to match the new schedule</li>
                                                </ul>
                                                <p>Please review the attached schedule and let me know if you have any questions or concerns. We'll need to implement these changes by the end of the week.</p>
                                                <p>Best regards,<br>Production Manager</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="reply-email-body" class="form-label">Your Reply</label>
                                <textarea class="form-control" id="reply-email-body" rows="6" placeholder="Type your reply here..."></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="reply-email-attachments" class="form-label">Attachments</label>
                                <input class="form-control" type="file" id="reply-email-attachments" multiple>
                                <div id="reply-attachment-list" class="mt-2">
                                    <!-- Attachments will be listed here -->
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-secondary" onclick="document.getElementById('read-email-tab').click();"><i class="bi bi-arrow-left"></i> Back</button>
                                <div>
                                    <button class="btn btn-outline-secondary me-2" id="save-reply-draft-btn">Save Draft</button>
                                    <button class="btn btn-primary" id="send-reply-btn" onclick="sendReply()">Send Reply</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://mail.google.com" target="_blank" class="btn btn-primary">Open Gmail</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Drive Modal -->
    <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="driveModalLabel"><i class="bi bi-folder"></i> Google Drive</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="driveTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="my-drive-tab" data-bs-toggle="tab" data-bs-target="#my-drive" type="button" role="tab" aria-controls="my-drive" aria-selected="true">My Drive</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-drive-tab" data-bs-toggle="tab" data-bs-target="#shared-drive" type="button" role="tab" aria-controls="shared-drive" aria-selected="false">Shared with Me</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-drive" type="button" role="tab" aria-controls="upload-drive" aria-selected="false">Upload</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="driveTabContent">
                        <div class="tab-pane fade show active" id="my-drive" role="tabpanel" aria-labelledby="my-drive-tab">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" placeholder="Search in Drive" aria-label="Search in Drive">
                                <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                            </div>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-folder-fill text-primary me-2"></i>
                                        <span>Production Schedules</span>
                                    </div>
                                    <span class="badge bg-secondary rounded-pill">15 files</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-folder-fill text-primary me-2"></i>
                                        <span>Resource Allocation</span>
                                    </div>
                                    <span class="badge bg-secondary rounded-pill">8 files</span>
                                </a>
                                <a href="#" onclick="openGoogleItem('drive', 'pdf', 'production-plan-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                        <span>Production_Plan_Q2_2025.pdf</span>
                                    </div>
                                    <div>
                                        <small class="text-muted me-2">Yesterday</small>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'production-plan-q2-2025'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Production_Plan_Q2_2025.pdf'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Production_Plan_Q2_2025.pdf'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('drive', 'spreadsheet', 'resource-allocation-matrix')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <span>Resource_Allocation_Matrix.xlsx</span>
                                    </div>
                                    <div>
                                        <small class="text-muted me-2">1 week ago</small>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'spreadsheet', 'resource-allocation-matrix'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Resource_Allocation_Matrix.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Resource_Allocation_Matrix.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('drive', 'document', 'optimization-document')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                        <span>Optimization_Document.docx</span>
                                    </div>
                                    <div>
                                        <small class="text-muted me-2">2 weeks ago</small>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'optimization-document'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Optimization_Document.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Optimization_Document.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="shared-drive" role="tabpanel" aria-labelledby="shared-drive-tab">
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('drive', 'pdf', 'production-capacity-analysis')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                        <span>Production_Capacity_Analysis.pdf</span>
                                        <p class="mb-1 small text-muted">Shared by: John Smith</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-info rounded-pill me-2">View Only</span>
                                        <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'production-capacity-analysis'); event.stopPropagation();">
                                          <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('drive', 'spreadsheet', 'team-resource-planning')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <span>Team_Resource_Planning.xlsx</span>
                                        <p class="mb-1 small text-muted">Shared by: Sarah Johnson</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-success rounded-pill me-2">Edit</span>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'spreadsheet', 'team-resource-planning'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Team_Resource_Planning.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Team_Resource_Planning.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('drive', 'folder', 'project-documents')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-folder-fill text-primary me-2"></i>
                                        <span>Project Documents</span>
                                        <p class="mb-1 small text-muted">Shared by: Michael Brown</p>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success rounded-pill me-2">Edit</span>
                                        <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'project-documents'); event.stopPropagation();">
                                          <i class="bi bi-folder-symlink"></i> Open
                                        </button>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="upload-drive" role="tabpanel" aria-labelledby="upload-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="upload-files" class="form-label">Select Files</label>
                                    <input class="form-control" type="file" id="upload-files" multiple>
                                </div>
                                <div class="mb-3">
                                    <label for="upload-folder" class="form-label">Destination Folder</label>
                                    <select class="form-select" id="upload-folder">
                                        <option selected>My Drive (root)</option>
                                        <option>Production Schedules</option>
                                        <option>Resource Allocation</option>
                                        <option>Project Documents</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="convert-docs">
                                        <label class="form-check-label" for="convert-docs">
                                            Convert uploaded files to Google format
                                        </label>
                                    </div>
                                </div>
                                <div class="progress mb-3" style="display: none;">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                                </div>
                                <button type="button" id="upload-drive-btn" class="btn btn-primary" onclick="uploadFiles()">Upload Files</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://drive.google.com" target="_blank" class="btn btn-primary">Open Google Drive</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Calendar Modal -->
    <div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="calendarModalLabel"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="calendarTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="events-tab" data-bs-toggle="tab" data-bs-target="#events" type="button" role="tab" aria-controls="events" aria-selected="true">Upcoming Events</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-event-tab" data-bs-toggle="tab" data-bs-target="#create-event" type="button" role="tab" aria-controls="create-event" aria-selected="false">Create Event</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="calendars-tab" data-bs-toggle="tab" data-bs-target="#calendars" type="button" role="tab" aria-controls="calendars" aria-selected="false">My Calendars</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="calendarTabContent">
                        <div class="tab-pane fade show active" id="events" role="tabpanel" aria-labelledby="events-tab">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <button class="btn btn-sm btn-outline-secondary me-2"><i class="bi bi-chevron-left"></i></button>
                                    <span class="fw-bold">April 28 - May 4, 2025</span>
                                    <button class="btn btn-sm btn-outline-secondary ms-2"><i class="bi bi-chevron-right"></i></button>
                                </div>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary active">Week</button>
                                    <button type="button" class="btn btn-outline-secondary">Month</button>
                                    <button type="button" class="btn btn-outline-secondary">Agenda</button>
                                </div>
                            </div>
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'production-planning-meeting')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Production Planning Meeting</h6>
                                        <small>Today, 10:00 AM - 11:30 AM</small>
                                    </div>
                                    <p class="mb-1 small">Conference Room A</p>
                                    <small class="text-muted">Weekly production planning meeting with department heads.</small>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'resource-allocation-review')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Resource Allocation Review</h6>
                                        <small>Tomorrow, 2:00 PM - 3:00 PM</small>
                                    </div>
                                    <p class="mb-1 small">Virtual Meeting</p>
                                    <small class="text-muted">Review resource allocation for upcoming projects.</small>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'optimization-strategy-session')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Optimization Strategy Session</h6>
                                        <small>May 2, 9:00 AM - 12:00 PM</small>
                                    </div>
                                    <p class="mb-1 small">Conference Room B</p>
                                    <small class="text-muted">Quarterly optimization strategy session with all stakeholders.</small>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'production-line-maintenance')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Production Line Maintenance</h6>
                                        <small>May 3, All Day</small>
                                    </div>
                                    <p class="mb-1 small">Production Floor</p>
                                    <small class="text-muted">Scheduled maintenance for production line A.</small>
                                </a>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="create-event" role="tabpanel" aria-labelledby="create-event-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="event-title" class="form-label">Event Title</label>
                                    <input type="text" class="form-control" id="event-title" placeholder="Enter event title">
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="event-start-date" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="event-start-date">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="event-start-time" class="form-label">Start Time</label>
                                        <input type="time" class="form-control" id="event-start-time">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="event-end-date" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="event-end-date">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="event-end-time" class="form-label">End Time</label>
                                        <input type="time" class="form-control" id="event-end-time">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="event-location" class="form-label">Location</label>
                                    <input type="text" class="form-control" id="event-location" placeholder="Enter location">
                                </div>
                                <div class="mb-3">
                                    <label for="event-description" class="form-label">Description</label>
                                    <textarea class="form-control" id="event-description" rows="3" placeholder="Enter event description"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="event-calendar" class="form-label">Calendar</label>
                                    <select class="form-select" id="event-calendar">
                                        <option selected>My Calendar</option>
                                        <option>Production Schedule</option>
                                        <option>Team Calendar</option>
                                        <option>Resource Allocation</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="event-guests" class="form-label">Guests</label>
                                    <input type="text" class="form-control" id="event-guests" placeholder="Add guests (comma separated)">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="event-notification">
                                        <label class="form-check-label" for="event-notification">
                                            Send notification to guests
                                        </label>
                                    </div>
                                </div>
                                <button type="button" id="create-event-btn" class="btn btn-primary">Create Event</button>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="calendars" role="tabpanel" aria-labelledby="calendars-tab">
                            <div class="list-group">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="calendar-my" checked>
                                            <label class="form-check-label" for="calendar-my">
                                                My Calendar
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="badge rounded-pill" style="background-color: #4285F4;">&nbsp;</span>
                                    </div>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="calendar-production" checked>
                                            <label class="form-check-label" for="calendar-production">
                                                Production Schedule
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="badge rounded-pill" style="background-color: #0F9D58;">&nbsp;</span>
                                    </div>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="calendar-team" checked>
                                            <label class="form-check-label" for="calendar-team">
                                                Team Calendar
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="badge rounded-pill" style="background-color: #DB4437;">&nbsp;</span>
                                    </div>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="calendar-resource">
                                            <label class="form-check-label" for="calendar-resource">
                                                Resource Allocation
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="badge rounded-pill" style="background-color: #F4B400;">&nbsp;</span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-plus"></i> Add Calendar</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://calendar.google.com" target="_blank" class="btn btn-primary">Open Google Calendar</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Maps Modal -->
    <div class="modal fade" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="mapsModalLabel"><i class="bi bi-geo-alt"></i> Google Maps</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" placeholder="Search locations" aria-label="Search locations">
                        <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                    </div>
                    <div class="ratio ratio-16x9 mb-3">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30596552044!2d-74.25986548248684!3d40.69714941887875!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1619544993358!5m2!1sen!2sca" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0">Saved Locations</h6>
                                </div>
                                <div class="card-body p-0">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="bi bi-building me-2 text-primary"></i>
                                                <span>Main Production Facility</span>
                                            </div>
                                            <button class="btn btn-sm btn-outline-primary"><i class="bi bi-geo-alt"></i></button>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="bi bi-house me-2 text-success"></i>
                                                <span>Warehouse A</span>
                                            </div>
                                            <button class="btn btn-sm btn-outline-primary"><i class="bi bi-geo-alt"></i></button>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="bi bi-truck me-2 text-danger"></i>
                                                <span>Distribution Center</span>
                                            </div>
                                            <button class="btn btn-sm btn-outline-primary"><i class="bi bi-geo-alt"></i></button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Directions</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="directions-from" class="form-label">From</label>
                                        <input type="text" class="form-control" id="directions-from" placeholder="Starting point">
                                    </div>
                                    <div class="mb-3">
                                        <label for="directions-to" class="form-label">To</label>
                                        <input type="text" class="form-control" id="directions-to" placeholder="Destination">
                                    </div>
                                    <div class="d-grid">
                                        <button class="btn btn-primary" type="button">Get Directions</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://maps.google.com" target="_blank" class="btn btn-primary">Open Google Maps</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Attachments Modal -->
    <div class="modal fade" id="attachmentsModal" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="attachmentsModalLabel"><i class="bi bi-paperclip"></i> Attachments</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="attachmentsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="all-attachments-tab" data-bs-toggle="tab" data-bs-target="#all-attachments" type="button" role="tab" aria-controls="all-attachments" aria-selected="true">All Attachments</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-attachment-tab" data-bs-toggle="tab" data-bs-target="#upload-attachment" type="button" role="tab" aria-controls="upload-attachment" aria-selected="false">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab" aria-controls="categories" aria-selected="false">Categories</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="attachmentsTabContent">
                        <div class="tab-pane fade show active" id="all-attachments" role="tabpanel" aria-labelledby="all-attachments-tab">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" placeholder="Search attachments" aria-label="Search attachments">
                                <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>Size</th>
                                            <th>Date Added</th>
                                            <th>Category</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                                Production_Schedule_Q2_2025.pdf
                                            </td>
                                            <td>PDF</td>
                                            <td>2.4 MB</td>
                                            <td>Apr 28, 2025</td>
                                            <td>Schedules</td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-primary" onclick="viewAttachment('pdf', 'Production_Schedule_Q2_2025.pdf')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="downloadAttachment('Production_Schedule_Q2_2025.pdf')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-outline-info" onclick="shareFile('Production_Schedule_Q2_2025.pdf')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteAttachment('Production_Schedule_Q2_2025.pdf')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                Resource_Allocation_Matrix.xlsx
                                            </td>
                                            <td>Excel</td>
                                            <td>1.8 MB</td>
                                            <td>Apr 25, 2025</td>
                                            <td>Resources</td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Resource_Allocation_Matrix.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="downloadAttachment('Resource_Allocation_Matrix.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-outline-info" onclick="shareFile('Resource_Allocation_Matrix.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteAttachment('Resource_Allocation_Matrix.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                Optimization_Strategy.docx
                                            </td>
                                            <td>Word</td>
                                            <td>850 KB</td>
                                            <td>Apr 20, 2025</td>
                                            <td>Optimization</td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-primary" onclick="viewAttachment('document', 'Optimization_Strategy.docx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="downloadAttachment('Optimization_Strategy.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-outline-info" onclick="shareFile('Optimization_Strategy.docx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteAttachment('Optimization_Strategy.docx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <i class="bi bi-file-earmark-image text-info me-2"></i>
                                                Production_Line_Layout.png
                                            </td>
                                            <td>Image</td>
                                            <td>3.2 MB</td>
                                            <td>Apr 15, 2025</td>
                                            <td>Layouts</td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-primary" onclick="viewAttachment('image', 'Production_Line_Layout.png')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="downloadAttachment('Production_Line_Layout.png')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-outline-info" onclick="shareFile('Production_Line_Layout.png')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteAttachment('Production_Line_Layout.png')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="upload-attachment" role="tabpanel" aria-labelledby="upload-attachment-tab">
                            <form>
                                <div class="mb-3">
                                    <label for="attachment-files" class="form-label">Select Files</label>
                                    <input class="form-control" type="file" id="attachment-files" multiple>
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-category" class="form-label">Category</label>
                                    <select class="form-select" id="attachment-category">
                                        <option selected>Select a category</option>
                                        <option>Schedules</option>
                                        <option>Resources</option>
                                        <option>Optimization</option>
                                        <option>Layouts</option>
                                        <option>Reports</option>
                                        <option>Other</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-description" class="form-label">Description (Optional)</label>
                                    <textarea class="form-control" id="attachment-description" rows="3" placeholder="Enter a description for the attachment"></textarea>
                                </div>
                                <div class="progress mb-3" style="display: none;">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                                </div>
                                <button type="button" id="upload-attachment-btn" class="btn btn-primary">Upload Files</button>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="categories" role="tabpanel" aria-labelledby="categories-tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title"><i class="bi bi-calendar-check text-primary me-2"></i>Schedules</h5>
                                            <p class="card-text">Production schedules, timelines, and planning documents.</p>
                                            <p class="card-text"><small class="text-muted">5 files</small></p>
                                            <a href="#" class="btn btn-sm btn-outline-primary">View Files</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title"><i class="bi bi-gear text-success me-2"></i>Resources</h5>
                                            <p class="card-text">Resource allocation documents and matrices.</p>
                                            <p class="card-text"><small class="text-muted">3 files</small></p>
                                            <a href="#" class="btn btn-sm btn-outline-primary">View Files</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title"><i class="bi bi-graph-up text-danger me-2"></i>Optimization</h5>
                                            <p class="card-text">Optimization strategies and performance documents.</p>
                                            <p class="card-text"><small class="text-muted">4 files</small></p>
                                            <a href="#" class="btn btn-sm btn-outline-primary">View Files</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title"><i class="bi bi-grid-3x3 text-info me-2"></i>Layouts</h5>
                                            <p class="card-text">Production line layouts and facility diagrams.</p>
                                            <p class="card-text"><small class="text-muted">2 files</small></p>
                                            <a href="#" class="btn btn-sm btn-outline-primary">View Files</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-plus"></i> Add Category</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Save Changes</button>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Function to create a new document
        function createNewDocument() {
            const docTitle = document.getElementById('doc-title').value.trim();
            if (!docTitle) {
                alert('Please enter a document title');
                return;
            }

            const createDocBtn = document.getElementById('create-doc-btn');
            if (!createDocBtn) return;

            // Show loading state
            const originalText = createDocBtn.innerHTML;
            createDocBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating...';
            createDocBtn.disabled = true;

            // Simulate API call
            setTimeout(() => {
                // Reset button state
                createDocBtn.innerHTML = originalText;
                createDocBtn.disabled = false;

                // Show success message
                alert(`Document "${docTitle}" created successfully!`);

                // Reset form
                document.getElementById('doc-title').value = '';
                document.getElementById('blank-doc-template').checked = true;
                document.getElementById('private-doc-sharing').checked = true;

                // Switch to Recent Documents tab
                const recentDocsTab = document.getElementById('recent-docs-tab');
                if (recentDocsTab) {
                    recentDocsTab.click();
                }
            }, 1500);
        }

        // Function to select a document template
        function selectTemplate(templateId) {
            // Set the radio button for the selected template
            const templateRadio = document.getElementById(`${templateId}-template`);
            if (templateRadio) {
                templateRadio.checked = true;
            }
        }

        // Function to create a new spreadsheet
        function createNewSpreadsheet() {
            const sheetTitle = document.getElementById('sheet-title').value.trim();
            if (!sheetTitle) {
                alert('Please enter a spreadsheet title');
                return;
            }

            const createSheetBtn = document.getElementById('create-sheet-btn');
            if (!createSheetBtn) return;

            // Show loading state
            const originalText = createSheetBtn.innerHTML;
            createSheetBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating...';
            createSheetBtn.disabled = true;

            // Simulate API call
            setTimeout(() => {
                // Reset button state
                createSheetBtn.innerHTML = originalText;
                createSheetBtn.disabled = false;

                // Show success message
                alert(`Spreadsheet "${sheetTitle}" created successfully!`);

                // Reset form
                document.getElementById('sheet-title').value = '';
                document.getElementById('blank-template').checked = true;
                document.getElementById('private-sharing').checked = true;

                // Switch to Recent Sheets tab
                const recentTab = document.getElementById('recent-tab');
                if (recentTab) {
                    recentTab.click();
                }
            }, 1500);
        }

        // Function to import spreadsheet data
        function importSpreadsheetData() {
            const importFile = document.getElementById('import-file');
            if (!importFile.files || importFile.files.length === 0) {
                alert('Please select a file to import');
                return;
            }

            const sheetName = document.getElementById('import-sheet-name').value.trim();
            if (!sheetName) {
                alert('Please enter a name for your spreadsheet');
                return;
            }

            const importSheetBtn = document.getElementById('import-sheet-btn');
            if (!importSheetBtn) return;

            // Show loading state
            const originalText = importSheetBtn.innerHTML;
            importSheetBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Importing...';
            importSheetBtn.disabled = true;

            // Simulate API call
            setTimeout(() => {
                // Reset button state
                importSheetBtn.innerHTML = originalText;
                importSheetBtn.disabled = false;

                // Show success message
                alert(`File "${importFile.files[0].name}" imported successfully as "${sheetName}"!`);

                // Reset form
                document.getElementById('import-file').value = '';
                document.getElementById('import-sheet-name').value = '';

                // Switch to Recent Sheets tab
                const recentTab = document.getElementById('recent-tab');
                if (recentTab) {
                    recentTab.click();
                }
            }, 2000);
        }

        // Function to upload files to Google Drive
        function uploadFiles() {
            const uploadFiles = document.getElementById('upload-files');
            if (!uploadFiles.files || uploadFiles.files.length === 0) {
                alert('Please select files to upload');
                return;
            }

            const uploadBtn = document.getElementById('upload-drive-btn');
            if (!uploadBtn) return;

            // Show loading state
            const originalText = uploadBtn.innerHTML;
            uploadBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Uploading...';
            uploadBtn.disabled = true;

            // Show progress bar
            const progressBar = document.querySelector('#upload-drive .progress');
            const progressBarInner = progressBar.querySelector('.progress-bar');
            progressBar.style.display = 'block';

            // Simulate upload progress
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressBarInner.style.width = `${progress}%`;
                progressBarInner.setAttribute('aria-valuenow', progress);

                if (progress >= 100) {
                    clearInterval(interval);

                    // Reset button state
                    uploadBtn.innerHTML = originalText;
                    uploadBtn.disabled = false;

                    // Hide progress bar after a delay
                    setTimeout(() => {
                        progressBar.style.display = 'none';
                        progressBarInner.style.width = '0%';
                        progressBarInner.setAttribute('aria-valuenow', 0);
                    }, 1000);

                    // Show success message
                    const fileCount = uploadFiles.files.length;
                    const fileText = fileCount === 1 ? 'file' : 'files';
                    alert(`${fileCount} ${fileText} uploaded successfully!`);

                    // Reset form
                    document.getElementById('upload-files').value = '';

                    // Switch to My Drive tab
                    const myDriveTab = document.getElementById('my-drive-tab');
                    if (myDriveTab) {
                        myDriveTab.click();
                    }
                }
            }, 300);
        }

        // Function to download a file
        function downloadFile(fileName) {
            console.log(`Downloading file: ${fileName}`);

            // Create a temporary link element
            const link = document.createElement('a');
            link.href = '#';
            link.download = fileName;

            // Simulate download by showing alert
            alert(`Downloading ${fileName}...`);

            // Clean up
            link.remove();
        }

        // Function to share a file
        function shareFile(fileName) {
            console.log(`Sharing file: ${fileName}`);

            // Show a simple sharing dialog
            const shareWith = prompt(`Enter email addresses to share "${fileName}" with (comma separated):`);

            if (shareWith) {
                alert(`${fileName} has been shared with: ${shareWith}`);
            }
        }

        // Function to delete the current file
        function deleteCurrentFile() {
            console.log('Deleting file');

            // Show confirmation dialog
            const confirmDelete = confirm('Are you sure you want to delete this file?');

            if (confirmDelete) {
                alert('File deleted successfully!');
            }
        }

        // Function to create a file viewer modal if it doesn't exist
        function createFileViewerModal() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'fileViewerModal';
            modal.tabIndex = '-1';
            modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
            modal.setAttribute('aria-hidden', 'true');

            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                            <h5 class="modal-title" id="fileViewerTitle"></h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">Download</button>
                            <button type="button" class="btn btn-success" onclick="shareCurrentFile()">Share</button>
                            <button type="button" class="btn btn-danger" onclick="deleteCurrentFile()">Delete</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            return modal;
        }

        // Function to download the current file
        function downloadCurrentFile() {
            const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
            alert(`Downloading ${fileTitle}...`);
            // In a real application, this would trigger a download
        }

        // Function to share the current file
        function shareCurrentFile() {
            const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
            const email = prompt(`Enter email address to share ${fileTitle} with:`);
            if (email) {
                alert(`${fileTitle} has been shared with ${email}.`);
                // In a real application, this would share the file with the specified email
            }
        }

        // Function to open specific Google items
        function openGoogleItem(app, type, itemId) {
          // For general app buttons (without specific item), show the modal
          if (app === 'drive' && !type && !itemId) {
            const driveModal = new bootstrap.Modal(document.getElementById('driveModal'));
            driveModal.show();
            return;
          } else if (app === 'docs' && !type && !itemId) {
            const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
            docsModal.show();
            return;
          } else if (app === 'sheets' && !type && !itemId) {
            const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
            sheetsModal.show();
            return;
          } else if (app === 'attachments' && !type && !itemId) {
            const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
            attachmentsModal.show();
            return;
          }

          // For specific items, directly open the file in a viewer
          try {
            // Create a simulated file viewer
            const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

            // Set the file information
            const fileTitle = document.getElementById('fileViewerTitle');
            const fileContent = document.getElementById('fileViewerContent');

            // Format the item ID to make it more readable
            const readableId = itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            // Set the title based on the app and type
            fileTitle.innerHTML = `<i class="bi ${getFileIcon(app, type)}"></i> ${readableId}`;

            // Set the content based on the file type
            fileContent.innerHTML = getFileContent(app, type, itemId);

            // Show the modal
            const modal = new bootstrap.Modal(fileViewerModal);
            modal.show();
          } catch (error) {
            console.error('Error opening file:', error);
            alert('Could not open the file. Please try again later.');
          }
        }

        // Helper function to create a file viewer modal if it doesn't exist
        function createFileViewerModal() {
          const modal = document.createElement('div');
          modal.className = 'modal fade';
          modal.id = 'fileViewerModal';
          modal.tabIndex = '-1';
          modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
          modal.setAttribute('aria-hidden', 'true');

          modal.innerHTML = `
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                  <h5 class="modal-title" id="fileViewerTitle"></h5>
                  <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">Download</button>
                  <button type="button" class="btn btn-success" onclick="shareCurrentFile()">Share</button>
                  <button type="button" class="btn btn-danger" onclick="deleteCurrentFile()">Delete</button>
                </div>
              </div>
            </div>
          `;

          document.body.appendChild(modal);
          return modal;
        }

        // Helper function to get the appropriate icon for the file type
        function getFileIcon(app, type) {
          if (app === 'drive') {
            if (type === 'folder') return 'bi-folder-fill text-primary';
            if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
            if (type === 'image') return 'bi-file-earmark-image text-info';
            if (type === 'document') return 'bi-file-earmark-text text-primary';
            return 'bi-file-earmark text-secondary';
          } else if (app === 'docs') {
            return 'bi-file-earmark-text text-primary';
          } else if (app === 'sheets') {
            return 'bi-file-earmark-spreadsheet text-success';
          } else if (app === 'calendar') {
            return 'bi-calendar-event text-primary';
          }
          return 'bi-file-earmark text-secondary';
        }

        // Helper function to generate content for the file viewer
        function getFileContent(app, type, itemId) {
          // Generate simulated content based on file type
          if (app === 'drive') {
            if (type === 'folder') {
              return `<div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> This is a folder view. In a real application, this would show the contents of the folder.
                      </div>
                      <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-text me-2"></i> Document 1.docx
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-spreadsheet me-2"></i> Spreadsheet 1.xlsx
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-pdf me-2"></i> Report.pdf
                        </a>
                      </div>`;
            } else if (type === 'pdf') {
              return `<div class="text-center">
                        <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                        <h4 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <div class="alert alert-info mt-3">
                          <i class="bi bi-info-circle"></i> This is a PDF viewer. In a real application, the PDF would be displayed here.
                        </div>
                        <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                          <h5>Document Preview</h5>
                          <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                          <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                        </div>
                      </div>`;
            } else if (type === 'document') {
              return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <hr>
                        <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                        <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                      </div>`;
            }
          } else if (app === 'docs') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                      <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                    </div>`;
          } else if (app === 'sheets') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                          <thead>
                            <tr>
                              <th>Production Line</th>
                              <th>Status</th>
                              <th>Start Date</th>
                              <th>End Date</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Production Line A</td>
                              <td>Active</td>
                              <td>2025-05-01</td>
                              <td>2025-05-15</td>
                            </tr>
                            <tr>
                              <td>Production Line B</td>
                              <td>Scheduled</td>
                              <td>2025-05-16</td>
                              <td>2025-05-30</td>
                            </tr>
                            <tr>
                              <td>Production Line C</td>
                              <td>Maintenance</td>
                              <td>2025-05-10</td>
                              <td>2025-05-12</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>`;
          } else if (app === 'calendar') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <div class="row">
                        <div class="col-md-6">
                          <p><strong>Date:</strong> May 15, 2025</p>
                          <p><strong>Time:</strong> 10:00 AM - 11:30 AM</p>
                          <p><strong>Location:</strong> Conference Room A</p>
                        </div>
                        <div class="col-md-6">
                          <p><strong>Organizer:</strong> John Doe</p>
                          <p><strong>Attendees:</strong> 5</p>
                          <p><strong>Status:</strong> Confirmed</p>
                        </div>
                      </div>
                      <div class="mt-3">
                        <h5>Description</h5>
                        <p>This is a calendar event viewer. In a real application, the event details would be displayed here.</p>
                      </div>
                    </div>`;
          }

          return `<div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
                  </div>`;
        }

        // Function to download the current file
        function downloadCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          alert(`Downloading ${fileTitle}...`);
          // In a real application, this would trigger a download
        }

        // Function to share the current file
        function shareCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          const email = prompt(`Enter email address to share ${fileTitle} with:`);
          if (email) {
            alert(`${fileTitle} has been shared with ${email}.`);
            // In a real application, this would share the file with the specified email
          }
        }

        // Function to delete the current file
        function deleteCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          const confirmDelete = confirm(`Are you sure you want to delete ${fileTitle}?`);
          if (confirmDelete) {
            alert(`${fileTitle} has been deleted.`);
            // In a real application, this would delete the file
            // Close the modal after deletion
            const modal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
            if (modal) {
              modal.hide();
            }
          }
        }

        // Function to download a file
        function downloadFile(fileName) {
          alert(`Downloading ${fileName}...`);
          // In a real application, this would trigger a download
        }

        // Function to share a file
        function shareFile(fileName) {
          const email = prompt(`Enter email address to share ${fileName} with:`);
          if (email) {
            alert(`${fileName} has been shared with ${email}.`);
            // In a real application, this would share the file with the specified email
          }
        }

        // Function to view attachments
        function viewAttachment(type, fileName) {
          // Create a file viewer modal if it doesn't exist
          const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

          // Set the file information
          const fileTitle = document.getElementById('fileViewerTitle');
          const fileContent = document.getElementById('fileViewerContent');

          // Set the title based on the file type and name
          fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;

          // Set the content based on the file type
          fileContent.innerHTML = getAttachmentContent(type, fileName);

          // Show the modal
          const modal = new bootstrap.Modal(fileViewerModal);
          modal.show();

          console.log(`Viewing ${fileName} directly`);
        }

        // Helper function to get the appropriate icon for the file type
        function getFileTypeIcon(type) {
          if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
          if (type === 'document') return 'bi-file-earmark-text text-primary';
          if (type === 'spreadsheet') return 'bi-file-earmark-spreadsheet text-success';
          if (type === 'image') return 'bi-file-earmark-image text-info';
          return 'bi-file-earmark text-secondary';
        }

        // Helper function to generate content for the attachment viewer
        function getAttachmentContent(type, fileName) {
          // Generate simulated content based on file type
          if (type === 'pdf') {
            return `<div class="text-center">
                      <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                      <h4 class="mt-3">${fileName}</h4>
                      <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                        <h5>Document Preview</h5>
                        <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                        <p>The document contains information related to advanced planning and scheduling.</p>
                      </div>
                    </div>`;
          } else if (type === 'document') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${fileName}</h4>
                      <hr>
                      <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                      <p>The document contains information related to advanced planning and scheduling.</p>
                      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                    </div>`;
          } else if (type === 'spreadsheet') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${fileName}</h4>
                      <hr>
                      <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                          <thead>
                            <tr>
                              <th>Production Line</th>
                              <th>Status</th>
                              <th>Start Date</th>
                              <th>End Date</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Production Line A</td>
                              <td>Active</td>
                              <td>2025-05-01</td>
                              <td>2025-05-15</td>
                            </tr>
                            <tr>
                              <td>Production Line B</td>
                              <td>Scheduled</td>
                              <td>2025-05-16</td>
                              <td>2025-05-30</td>
                            </tr>
                            <tr>
                              <td>Production Line C</td>
                              <td>Maintenance</td>
                              <td>2025-05-10</td>
                              <td>2025-05-12</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>`;
          } else if (type === 'image') {
            return `<div class="text-center">
                      <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                        <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                        <h5 class="mt-3">${fileName}</h5>
                        <p>This is an image viewer. In a real application, the image would be displayed here.</p>
                      </div>
                    </div>`;
          }

          return `<div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
                  </div>`;
        }

        // Function for downloading attachments
        function downloadAttachment(fileName) {
          alert(`Downloading ${fileName}...`);
          // In a real application, this would trigger a download
        }

        // Function for deleting attachments
        function deleteAttachment(fileName) {
          if (confirm(`Are you sure you want to delete ${fileName}?`)) {
            alert(`${fileName} has been deleted.`);
            // In a real application, this would delete the file
          }
        }

        // Function for sharing files
        function shareFile(fileName) {
          const email = prompt(`Enter email address to share ${fileName} with:`);
          if (email) {
            alert(`${fileName} has been shared with ${email}.`);
            // In a real application, this would share the file with the specified email
          }
        }

        // Function to open an email
        function openEmail(emailId) {
            // Show the read email tab
            const readEmailTab = document.getElementById('read-email-tab');
            readEmailTab.style.display = 'block';
            readEmailTab.click();

            // Set email content based on the email ID
            const subject = document.getElementById('read-email-subject');
            const from = document.getElementById('read-email-from');
            const avatar = document.getElementById('read-email-avatar');
            const time = document.getElementById('read-email-time');
            const body = document.getElementById('read-email-body');
            const attachments = document.getElementById('read-email-attachments');

            // Default values
            let emailData = {
                subject: 'Schedule update for Line A',
                from: 'Production Manager',
                avatar: 'PM',
                time: '10:15 AM',
                body: `<p>Hi,</p>
                       <p>I'm writing to inform you about the recent updates to the production schedule for Line A. We've made some adjustments to optimize the workflow and improve efficiency.</p>
                       <p>Key changes include:</p>
                       <ul>
                           <li>Rescheduled maintenance to minimize downtime</li>
                           <li>Adjusted production sequence to reduce changeover times</li>
                           <li>Allocated additional resources during peak periods</li>
                           <li>Updated delivery timelines to match the new schedule</li>
                       </ul>
                       <p>Please review the attached schedule and let me know if you have any questions or concerns. We'll need to implement these changes by the end of the week.</p>
                       <p>Best regards,<br>Production Manager</p>`,
                hasAttachments: true
            };

            // Set email content based on the email ID
            if (emailId === 'schedule-update-line-a') {
                // Default values are already set
            } else if (emailId === 'resource-allocation-report-q2-2025') {
                emailData = {
                    subject: 'Resource allocation report for Q2 2025',
                    from: 'Resource Manager',
                    avatar: 'RM',
                    time: 'Yesterday',
                    body: `<p>Hello,</p>
                           <p>I've completed the resource allocation report for Q2 2025. The report includes detailed analysis of resource utilization and recommendations for optimization.</p>
                           <p>Key findings:</p>
                           <ul>
                               <li>Overall resource utilization is at 78%, which is below our target of 85%</li>
                               <li>Production Line A is overallocated by 15%</li>
                               <li>Production Line C is underutilized by 20%</li>
                               <li>Maintenance resources are optimally allocated</li>
                           </ul>
                           <p>Please review the attached report and let's discuss the findings in our next meeting.</p>
                           <p>Regards,<br>Resource Manager</p>`,
                    hasAttachments: true
                };
            } else if (emailId === 'production-constraints-update') {
                emailData = {
                    subject: 'Production constraints update',
                    from: 'Team Lead',
                    avatar: 'TL',
                    time: 'Apr 27',
                    body: `<p>Hi team,</p>
                           <p>I wanted to update you on the production constraints we've identified for the upcoming quarter.</p>
                           <p>We've analyzed the production data and identified the following constraints:</p>
                           <ul>
                               <li>Material supply limitations for Product A</li>
                               <li>Machine capacity constraints on Line B</li>
                               <li>Labor constraints during the holiday season</li>
                               <li>Quality inspection bottlenecks</li>
                           </ul>
                           <p>I've attached a detailed document with our analysis and recommendations for addressing these constraints.</p>
                           <p>Let me know if you have any questions.</p>
                           <p>Best regards,<br>Team Lead</p>`,
                    hasAttachments: true
                };
            } else if (emailId === 'sent-production-schedule') {
                emailData = {
                    subject: 'Production Schedule Update',
                    from: 'Me',
                    avatar: 'ME',
                    time: 'Today, 9:30 AM',
                    body: `<p>Dear Production Team,</p>
                           <p>Please find attached the updated production schedule for this week.</p>
                           <p>Key changes:</p>
                           <ul>
                               <li>Adjusted Line A schedule to accommodate maintenance</li>
                               <li>Added new product runs on Line B</li>
                               <li>Updated resource allocations for all lines</li>
                           </ul>
                           <p>Please review and let me know if you have any questions.</p>
                           <p>Regards,<br>Production Scheduler</p>`,
                    hasAttachments: true
                };
            } else if (emailId === 'sent-resource-allocation') {
                emailData = {
                    subject: 'Resource Allocation Request',
                    from: 'Me',
                    avatar: 'ME',
                    time: 'Yesterday',
                    body: `<p>Dear Resource Manager,</p>
                           <p>I'm requesting additional resources for Line B production starting next week.</p>
                           <p>We need:</p>
                           <ul>
                               <li>2 additional operators for the morning shift</li>
                               <li>Extended maintenance support</li>
                               <li>Additional QA resources for the new product launch</li>
                           </ul>
                           <p>Please let me know if these resources can be allocated.</p>
                           <p>Thank you,<br>Production Scheduler</p>`,
                    hasAttachments: false
                };
            } else if (emailId === 'sent-optimization-results') {
                emailData = {
                    subject: 'Optimization Results',
                    from: 'Me',
                    avatar: 'ME',
                    time: 'Apr 26',
                    body: `<p>Dear Management Team,</p>
                           <p>Here are the results of our latest optimization run for Q2 planning.</p>
                           <p>Key results:</p>
                           <ul>
                               <li>15% reduction in changeover times</li>
                               <li>8% increase in overall throughput</li>
                               <li>12% reduction in WIP inventory</li>
                               <li>5% improvement in on-time delivery</li>
                           </ul>
                           <p>The attached report contains detailed analysis and recommendations.</p>
                           <p>Regards,<br>Production Scheduler</p>`,
                    hasAttachments: true
                };
            }

            // Update the email content
            subject.textContent = emailData.subject;
            from.textContent = emailData.from;
            avatar.textContent = emailData.avatar;
            time.textContent = emailData.time;
            body.innerHTML = emailData.body;

            // Update reply email content
            document.getElementById('reply-email-subject').textContent = emailData.subject;
            document.getElementById('reply-email-from').textContent = emailData.from;
            document.getElementById('reply-email-avatar').textContent = emailData.avatar;
            document.getElementById('reply-email-time').textContent = emailData.time;
            document.getElementById('reply-email-original-body').innerHTML = emailData.body;

            // Show or hide attachments section
            if (emailData.hasAttachments) {
                attachments.style.display = 'block';
            } else {
                attachments.style.display = 'none';
            }
        }

        // Function to reply to an email
        function replyToEmail() {
            // Show the reply email tab
            const replyEmailTab = document.getElementById('reply-email-tab');
            replyEmailTab.style.display = 'block';
            replyEmailTab.click();
        }

        // Function to send a reply
        function sendReply() {
            alert('Your reply has been sent.');
            // Hide the reply tab and go back to inbox
            document.getElementById('reply-email-tab').style.display = 'none';
            document.getElementById('read-email-tab').style.display = 'none';
            document.getElementById('inbox-tab').click();
        }

        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }
        });
    </script>
</body>
</html>
