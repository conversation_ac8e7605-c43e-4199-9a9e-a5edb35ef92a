/**
 * ISA Data Utilities
 *
 * This file contains common utilities for search, sort, filter, refresh, and link functionality
 * to be used across all ISA Suite applications.
 */

/**
 * ISADataUtils - Namespace for all data utility functions
 */
const ISADataUtils = {
    /**
     * Initialize search functionality for a table
     * @param {string} searchInputId - ID of the search input element
     * @param {string} tableId - ID of the table to search
     * @param {Array<number>} searchableColumns - Array of column indices to search (0-based)
     */
    initTableSearch: function(searchInputId, tableId, searchableColumns = null) {
        const searchInput = document.getElementById(searchInputId);
        const table = document.getElementById(tableId);

        if (!searchInput || !table) {
            console.error(`Search initialization failed: Could not find elements with IDs ${searchInputId} or ${tableId}`);
            return;
        }

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                let found = false;
                const cells = row.querySelectorAll('td');

                // If searchableColumns is null, search all columns
                const columnsToSearch = searchableColumns || [...Array(cells.length).keys()];

                columnsToSearch.forEach(colIndex => {
                    if (colIndex < cells.length) {
                        const cellText = cells[colIndex].textContent.toLowerCase();
                        if (cellText.includes(searchTerm)) {
                            found = true;
                        }
                    }
                });

                row.style.display = found ? '' : 'none';
            });

            // Update any counters or empty state messages
            this.dispatchEvent(new CustomEvent('search-complete', {
                detail: { searchTerm: searchTerm, tableId: tableId }
            }));
        });

        console.log(`Search initialized for table ${tableId}`);
    },

    /**
     * Initialize sorting functionality for a table
     * @param {string} tableId - ID of the table to make sortable
     * @param {Object} options - Sorting options
     * @param {Array<number>} options.excludeColumns - Array of column indices to exclude from sorting (0-based)
     * @param {Array<number>} options.numericColumns - Array of column indices that contain numeric data (0-based)
     * @param {Array<number>} options.dateColumns - Array of column indices that contain date data (0-based)
     */
    initTableSort: function(tableId, options = {}) {
        const table = document.getElementById(tableId);

        if (!table) {
            console.error(`Sort initialization failed: Could not find table with ID ${tableId}`);
            return;
        }

        const excludeColumns = options.excludeColumns || [];
        const numericColumns = options.numericColumns || [];
        const dateColumns = options.dateColumns || [];

        const headers = table.querySelectorAll('thead th');

        headers.forEach((header, index) => {
            if (excludeColumns.includes(index)) {
                return; // Skip excluded columns
            }

            // Add sort indicators and cursor style
            header.style.cursor = 'pointer';
            header.classList.add('sortable');

            // Add sort direction indicator
            const sortIndicator = document.createElement('span');
            sortIndicator.className = 'sort-indicator ms-1';
            sortIndicator.innerHTML = '<i class="bi bi-sort-down text-muted"></i>';
            header.appendChild(sortIndicator);

            // Add click event
            header.addEventListener('click', function() {
                const sortDirection = this.getAttribute('data-sort-direction') === 'asc' ? 'desc' : 'asc';

                // Reset all headers
                headers.forEach(h => {
                    h.setAttribute('data-sort-direction', '');
                    h.querySelector('.sort-indicator').innerHTML = '<i class="bi bi-sort-down text-muted"></i>';
                });

                // Set current header
                this.setAttribute('data-sort-direction', sortDirection);
                this.querySelector('.sort-indicator').innerHTML = sortDirection === 'asc'
                    ? '<i class="bi bi-sort-up-alt"></i>'
                    : '<i class="bi bi-sort-down-alt"></i>';

                // Sort the table
                ISADataUtils.sortTable(table, index, sortDirection, {
                    isNumeric: numericColumns.includes(index),
                    isDate: dateColumns.includes(index)
                });
            });
        });

        console.log(`Sorting initialized for table ${tableId}`);
    },

    /**
     * Sort a table by a specific column
     * @param {HTMLElement} table - The table element to sort
     * @param {number} columnIndex - The index of the column to sort by (0-based)
     * @param {string} direction - Sort direction ('asc' or 'desc')
     * @param {Object} options - Sorting options
     * @param {boolean} options.isNumeric - Whether the column contains numeric data
     * @param {boolean} options.isDate - Whether the column contains date data
     */
    sortTable: function(table, columnIndex, direction = 'asc', options = {}) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        const sortedRows = rows.sort((rowA, rowB) => {
            const cellA = rowA.querySelectorAll('td')[columnIndex];
            const cellB = rowB.querySelectorAll('td')[columnIndex];

            if (!cellA || !cellB) return 0;

            let valueA = cellA.textContent.trim();
            let valueB = cellB.textContent.trim();

            if (options.isNumeric) {
                // Extract numeric values (ignore currency symbols, commas, etc.)
                valueA = parseFloat(valueA.replace(/[^0-9.-]+/g, '')) || 0;
                valueB = parseFloat(valueB.replace(/[^0-9.-]+/g, '')) || 0;
            } else if (options.isDate) {
                // Convert dates to timestamps
                valueA = new Date(valueA).getTime() || 0;
                valueB = new Date(valueB).getTime() || 0;
            }

            if (valueA < valueB) {
                return direction === 'asc' ? -1 : 1;
            }
            if (valueA > valueB) {
                return direction === 'asc' ? 1 : -1;
            }
            return 0;
        });

        // Remove all existing rows
        rows.forEach(row => tbody.removeChild(row));

        // Add sorted rows
        sortedRows.forEach(row => tbody.appendChild(row));

        // Dispatch event for any listeners
        table.dispatchEvent(new CustomEvent('table-sorted', {
            detail: { columnIndex, direction, options }
        }));
    },

    /**
     * Initialize filter functionality for a table
     * @param {string} tableId - ID of the table to filter
     * @param {Array<Object>} filters - Array of filter configurations
     * @param {string} filters[].filterId - ID of the filter element (select, input, etc.)
     * @param {number} filters[].columnIndex - Index of the column to filter (0-based)
     * @param {Function} filters[].filterFn - Optional custom filter function
     */
    initTableFilter: function(tableId, filters) {
        const table = document.getElementById(tableId);

        if (!table) {
            console.error(`Filter initialization failed: Could not find table with ID ${tableId}`);
            return;
        }

        filters.forEach(filter => {
            const filterElement = document.getElementById(filter.filterId);

            if (!filterElement) {
                console.error(`Filter initialization failed: Could not find filter element with ID ${filter.filterId}`);
                return;
            }

            filterElement.addEventListener('change', function() {
                const filterValue = this.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const cell = row.querySelectorAll('td')[filter.columnIndex];

                    if (!cell) return;

                    let showRow = true;

                    if (filter.filterFn) {
                        // Use custom filter function if provided
                        showRow = filter.filterFn(cell, filterValue, row);
                    } else {
                        // Default filtering behavior
                        if (filterValue && filterValue !== 'all') {
                            const cellValue = cell.textContent.toLowerCase();
                            showRow = cellValue.includes(filterValue);
                        }
                    }

                    // Apply the filter
                    if (showRow) {
                        // Only show the row if it's not hidden by other filters
                        if (row.style.display !== 'none' || row.getAttribute('data-filtered-by') === filter.filterId) {
                            row.style.display = '';
                            row.removeAttribute('data-filtered-by');
                        }
                    } else {
                        row.style.display = 'none';
                        row.setAttribute('data-filtered-by', filter.filterId);
                    }
                });

                // Dispatch event for any listeners
                table.dispatchEvent(new CustomEvent('table-filtered', {
                    detail: { filterId: filter.filterId, filterValue: filterValue }
                }));
            });
        });

        console.log(`Filtering initialized for table ${tableId}`);
    },

    /**
     * Initialize refresh functionality for a data container
     * @param {string} refreshBtnId - ID of the refresh button
     * @param {string} containerId - ID of the container to refresh
     * @param {Function} dataFetchFn - Function to fetch fresh data
     * @param {number} autoRefreshInterval - Interval in milliseconds for auto-refresh (0 to disable)
     */
    initRefresh: function(refreshBtnId, containerId, dataFetchFn, autoRefreshInterval = 0) {
        const refreshBtn = document.getElementById(refreshBtnId);
        const container = document.getElementById(containerId);

        if (!refreshBtn || !container) {
            console.error(`Refresh initialization failed: Could not find elements with IDs ${refreshBtnId} or ${containerId}`);
            return;
        }

        let refreshInterval = null;

        // Manual refresh
        refreshBtn.addEventListener('click', function() {
            this.disabled = true;
            const originalContent = this.innerHTML;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';

            // Fetch fresh data
            dataFetchFn()
                .then(() => {
                    // Success
                    this.innerHTML = originalContent;
                    this.disabled = false;

                    // Show success toast
                    ISADataUtils.showToast('Data refreshed successfully', 'success');
                })
                .catch(error => {
                    // Error
                    console.error('Refresh failed:', error);
                    this.innerHTML = originalContent;
                    this.disabled = false;

                    // Show error toast
                    ISADataUtils.showToast('Error refreshing data', 'danger');
                });
        });

        // Auto-refresh
        if (autoRefreshInterval > 0) {
            refreshInterval = setInterval(() => {
                dataFetchFn().catch(error => {
                    console.error('Auto-refresh failed:', error);
                });
            }, autoRefreshInterval);

            // Clean up interval when page unloads
            window.addEventListener('beforeunload', () => {
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                }
            });
        }

        console.log(`Refresh initialized for container ${containerId}`);
    },

    /**
     * Show a toast notification
     * @param {string} message - The message to display
     * @param {string} type - The type of toast (success, danger, warning, info)
     */
    showToast: function(message, type = 'success') {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        // Create a unique ID for this toast
        const toastId = 'toast-' + Date.now();

        // Determine icon and title based on type
        let icon, title;
        switch (type) {
            case 'success':
                icon = 'check-circle-fill text-success';
                title = 'Success';
                break;
            case 'danger':
            case 'error':
                icon = 'exclamation-circle-fill text-danger';
                title = 'Error';
                break;
            case 'warning':
                icon = 'exclamation-triangle-fill text-warning';
                title = 'Warning';
                break;
            case 'info':
                icon = 'info-circle-fill text-info';
                title = 'Information';
                break;
            default:
                icon = 'bell-fill text-primary';
                title = 'Notification';
        }

        // Create toast element
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi bi-${icon} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <small>Just now</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        // Add toast to container
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Initialize and show the toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
        toast.show();

        // Remove toast from DOM after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function () {
            toastElement.remove();
        });
    }
};

// Make the utilities available globally
window.ISADataUtils = ISADataUtils;
