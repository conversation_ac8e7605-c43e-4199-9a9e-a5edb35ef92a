# Test Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=test

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bms_test
DB_USER=postgres
DB_PASSWORD=password

# JWT Configuration
JWT_SECRET=test_jwt_secret
JWT_EXPIRATION=1d

# Integration Hub Configuration
INTEGRATION_HUB_URL=http://localhost:8000
INTEGRATION_HUB_API_KEY=test_api_key

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE_PATH=logs/bms-test.log
