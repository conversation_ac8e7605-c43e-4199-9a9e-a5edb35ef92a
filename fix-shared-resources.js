/**
 * Fix Shared Resources Script
 * 
 * This script updates all ISA Suite application servers to properly serve
 * shared resources, fixing the 404 errors and MIME type issues.
 */

const fs = require('fs');
const path = require('path');

// Applications to update (excluding CRM which is already fixed)
const applications = [
    'MRP', 'BMS', 'PMS', 'APM', 'APS', 'WMS', 'TM', 'SCM'
];

// Function to update an application's server file
function updateApplicationServer(appName) {
    const serverPath = path.join(__dirname, 'apps', appName, 'index.js');
    
    if (!fs.existsSync(serverPath)) {
        console.log(`❌ ${appName}: index.js not found at ${serverPath}`);
        return false;
    }

    try {
        let content = fs.readFileSync(serverPath, 'utf8');
        
        // Check if already has shared resources serving
        if (content.includes("app.use('/shared', express.static('../../shared'));")) {
            console.log(`ℹ️  ${appName}: Shared resources already configured`);
            return true;
        }

        // Find the middleware configuration section
        const middlewarePattern = /(\/\/ Configure middleware[\s\S]*?app\.use\(express\.static\('public'\)\);)/;
        const match = content.match(middlewarePattern);
        
        if (!match) {
            console.log(`❌ ${appName}: Could not find middleware configuration section`);
            return false;
        }

        // Add shared resources serving
        const newMiddleware = match[1] + `

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));`;

        // Replace the middleware section
        content = content.replace(middlewarePattern, newMiddleware);

        // Write the updated content back to the file
        fs.writeFileSync(serverPath, content, 'utf8');
        console.log(`✅ ${appName}: Server updated to serve shared resources`);
        return true;

    } catch (error) {
        console.log(`❌ ${appName}: Error updating server file - ${error.message}`);
        return false;
    }
}

// Main execution
function main() {
    console.log('🔧 Fixing shared resources serving for ISA Suite applications\n');

    let successCount = 0;
    let totalCount = applications.length;

    // Update each application
    applications.forEach(appName => {
        console.log(`🔧 Updating ${appName} server...`);
        if (updateApplicationServer(appName)) {
            successCount++;
        }
        console.log('');
    });

    // Summary
    console.log('📊 Update Summary:');
    console.log(`   ✅ Successfully updated: ${successCount}/${totalCount} applications`);
    console.log(`   ❌ Failed updates: ${totalCount - successCount}/${totalCount} applications`);

    if (successCount === totalCount) {
        console.log('\n🎉 All application servers updated successfully!');
        console.log('\n📋 Next steps:');
        console.log('   1. Restart all applications to apply changes');
        console.log('   2. Test Gmail functionality in each application');
        console.log('   3. Verify shared resources are loading properly');
    } else {
        console.log('\n⚠️  Some applications failed to update. Please check the errors above.');
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = { updateApplicationServer, applications };
