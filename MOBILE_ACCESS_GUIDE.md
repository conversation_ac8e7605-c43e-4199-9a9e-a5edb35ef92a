# ISA APPS Mobile Access Guide

This guide explains how to access the ISA APPS suite from your mobile device.

## Accessing the Portal

There are several ways to access the ISA APPS portal from your mobile device:

### Option 1: Scan the QR Code

1. On your computer, start the ISA APPS Portal by clicking the "ISA APPS Portal" shortcut on the desktop
2. Once the portal is running, scan the QR code displayed in the "Mobile Access" section
3. This will open the portal in your mobile device's browser

### Option 2: Enter the URL

1. On your computer, start the ISA APPS Portal
2. Note the "Network" URL displayed in the command window (e.g., http://192.168.1.100:8080)
3. Enter this URL in your mobile device's browser

## Installing as a Progressive Web App (PWA)

For quick access, you can install the ISA APPS portal as an app on your mobile device:

### On Android:

1. Open the portal in Chrome
2. Tap the menu button (three dots) in the top-right corner
3. Tap "Add to Home screen"
4. Follow the prompts to add the app to your home screen

### On iOS:

1. Open the portal in Safari
2. Tap the Share button (square with an arrow) at the bottom of the screen
3. Scroll down and tap "Add to Home Screen"
4. Tap "Add" in the top-right corner

## Accessing Individual Applications

From the portal, you can access any of the ISA APPS applications:

1. Tap on the application card you want to open
2. The application will open in a new tab
3. You can install each application as a PWA following the same steps above

## Using QR Codes for Direct Access

Each application has its own QR code for direct access:

1. On your computer, start the ISA APPS Portal
2. Scroll down to the "Mobile Access" section
3. Scan the QR code for the specific application you want to access
4. The application will open directly in your mobile device's browser

## Offline Access

Some features of the applications are available offline:

1. Install the portal and applications as PWAs
2. Use the applications while connected to the network
3. If you lose connection, basic functionality will still be available
4. When you reconnect, your data will synchronize automatically

## Troubleshooting

If you encounter issues accessing the applications on your mobile device:

1. Make sure your mobile device is connected to the same network as the computer running the applications
2. Check that all applications are running on the computer
3. Try clearing your browser cache and cookies
4. Restart the portal and try again

For additional help, contact your system administrator.
