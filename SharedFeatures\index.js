// SharedFeatures - Main Entry Point

// Import modules
const auth = require('./auth');
const logger = require('./logger');
const notifications = require('./notifications');
const google = require('./integrations/google');
const microsoft = require('./integrations/microsoft');
const slack = require('./integrations/slack');
const salesforce = require('./integrations/salesforce');
const xero = require('./integrations/xero');
const shopify = require('./integrations/shopify');
const appModes = require('./utils/app-modes');
const gamification = require('./utils/gamification');
const helpSystem = require('./utils/help-system');
const aiHelpSystem = require('./utils/ai-help-system');
const internetResources = require('./utils/internet-resources');

// Export modules
module.exports = {
  auth,
  logger,
  notifications,
  google,
  microsoft,
  slack,
  salesforce,
  xero,
  shopify,
  appModes,
  gamification,
  helpSystem,
  aiHelpSystem,
  internetResources,
};
