@echo off
echo Starting Customer Relationship Management (CRM)...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Change to the CRM directory
cd /d C:\ISASUITE\apps\CRM\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call pnpm install
)

REM Run the application with production mode by default
echo Starting CRM in production mode...
echo To access CRM, go to: http://localhost:3003
start "" http://localhost:3003

REM Start using pnpm instead of directly invoking Node
pnpm start

echo.
echo CRM application has been closed.
pause
