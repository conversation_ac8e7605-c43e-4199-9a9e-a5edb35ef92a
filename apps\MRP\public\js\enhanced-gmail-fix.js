function createEnhancedGmailModal() {
    console.log('Creating enhanced Gmail modal');

    // Check if the modal already exists
    const existingModal = document.getElementById('gmailModal');
    
    // If there's an existing Gmail modal, check if it's already enhanced
    if (existingModal) {
        const hasEnhancedFeatures = existingModal.querySelector('#enhancedGmailTab') || existingModal.querySelector('.list-group');
        if (hasEnhancedFeatures) {
            console.log('Enhanced Gmail modal already exists');
            return;
        } else {
            console.log('Removing existing basic Gmail modal to replace with enhanced version');
            existingModal.remove();
        }
    }

    // Create the modal HTML with enhanced styling
    const modalHTML = `
    // ...existing modal HTML...
    `;

    // Append the modal to the body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // IMPORTANT: Set up event listeners AFTER the HTML is added to DOM
    setTimeout(() => {
        setupEnhancedGmailEventListeners();
        updateLabelCounts();
    }, 100); // Small delay to ensure DOM is ready
}

function setupEnhancedGmailEventListeners() {
    // Close buttons
    document.getElementById('enhanced-gmail-close-btn').addEventListener('click', closeEnhancedGmailModal);
    document.getElementById('enhanced-gmail-footer-close-btn').addEventListener('click', closeEnhancedGmailModal);

    // ...existing code for compose, cancel, send, save draft, attach buttons...

    // Search functionality
    const searchInput = document.getElementById('enhanced-email-search');
    const searchBtn = document.getElementById('enhanced-email-search-btn');

    console.log('Setting up search listeners:', { searchInput, searchBtn }); // Debug log

    if (searchInput && searchBtn) {
        // Search on button click
        searchBtn.addEventListener('click', function() {
            console.log('Search button clicked with query:', searchInput.value); // Debug log
            searchEmails(searchInput.value);
        });

        // Search on Enter key
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                console.log('Enter key pressed with query:', this.value); // Debug log
                searchEmails(this.value);
            }
        });
    } else {
        console.error('Search elements not found:', { searchInput, searchBtn });
    }

    // Sort functionality
    const sortOptions = document.querySelectorAll('.sort-option');
    console.log('Found sort options:', sortOptions.length); // Debug log
    
    sortOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const sortType = this.getAttribute('data-sort');
            console.log('Sort option clicked:', sortType); // Debug log
            sortEmails(sortType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('emailSortDropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-sort-down"></i> ${this.textContent}`;
            }
        });
    });

    // Filter functionality
    const filterOptions = document.querySelectorAll('.filter-option');
    console.log('Found filter options:', filterOptions.length); // Debug log
    
    filterOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const filterType = this.getAttribute('data-filter');
            console.log('Filter option clicked:', filterType); // Debug log
            filterEmails(filterType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('emailFilterDropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-funnel"></i> ${this.textContent.trim()}`;
            }
        });
    });

    // Refresh button
    const refreshBtn = document.getElementById('enhanced-gmail-refresh-btn');
    console.log('Found refresh button:', refreshBtn); // Debug log
    
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            console.log('Refresh button clicked'); // Debug log
            
            // Reset search input
            if (searchInput) {
                searchInput.value = '';
            }

            // Reset sort dropdown
            const sortDropdown = document.getElementById('emailSortDropdown');
            if (sortDropdown) {
                sortDropdown.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
            }

            // Reset filter dropdown
            const filterDropdown = document.getElementById('emailFilterDropdown');
            if (filterDropdown) {
                filterDropdown.innerHTML = '<i class="bi bi-funnel"></i> Filter';
            }

            // Show all emails
            showAllEmails();

            // Show success message
            showToast('Emails refreshed successfully');
        });
    } else {
        console.error('Refresh button not found');
    }

    // ...rest of existing event listeners...
}