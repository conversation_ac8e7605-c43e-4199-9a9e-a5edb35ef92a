@echo off
echo Starting Asset Performance Management (APM)...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Change to the APM directory
cd /d C:\ISASUITE\apps\APM\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call C:\ISASUITE\PortableNodeJS\pnpm install
)

REM Run the application with production mode by default
echo Starting APM in production mode...
echo To access APM, go to: http://localhost:3006
start "" http://localhost:3006

REM Start using pnpm instead of directly invoking Node
pnpm start

echo.
echo APM application has been closed.
pause

