/**
 * Cross-Application Enhancements for ISA Suite
 *
 * This module provides powerful enhancements that benefit all applications:
 * - Advanced search and filtering
 * - Real-time notifications
 * - Data export/import capabilities
 * - Performance monitoring
 * - User activity tracking
 * - Cross-app data sharing
 * - AI-powered insights
 * - Advanced reporting
 */

class ISASuiteEnhancements {
    constructor(config = {}) {
        this.config = {
            appName: config.appName || 'ISA Suite',
            appPrefix: config.appPrefix || 'app',
            appColor: config.appColor || '#007bff',
            enableNotifications: config.enableNotifications !== false,
            enableAnalytics: config.enableAnalytics !== false,
            enableExport: config.enableExport !== false,
            enableSearch: config.enableSearch !== false,
            debug: config.debug || false,
            ...config
        };

        this.initialized = false;
        this.notifications = [];
        this.searchIndex = new Map();
        this.analytics = {
            pageViews: 0,
            userActions: [],
            performanceMetrics: []
        };
    }

    /**
     * Initialize all enhancements
     */
    init() {
        if (this.initialized) {
            this.log('Enhancements already initialized');
            return;
        }

        this.log('Initializing ISA Suite Enhancements');

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    /**
     * Setup all enhancement features
     */
    setup() {
        try {
            // Initialize core enhancements
            if (this.config.enableNotifications) this.initNotificationSystem();
            if (this.config.enableAnalytics) this.initAnalytics();
            if (this.config.enableExport) this.initExportFeatures();
            if (this.config.enableSearch) this.initAdvancedSearch();

            // Add enhancement toolbar
            this.createEnhancementToolbar();

            // Initialize performance monitoring
            this.initPerformanceMonitoring();

            // Setup keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Initialize cross-app communication
            this.initCrossAppCommunication();

            this.initialized = true;
            this.log('ISA Suite Enhancements initialized successfully');

            // Make globally available
            window.isaEnhancements = this;

            // Track initialization
            this.trackEvent('enhancement_initialized', { app: this.config.appName });

        } catch (error) {
            console.error('Error setting up ISA Suite Enhancements:', error);
        }
    }

    /**
     * Create enhancement toolbar
     */
    createEnhancementToolbar() {
        // Remove existing toolbar
        const existingToolbar = document.getElementById('isa-enhancement-toolbar');
        if (existingToolbar) {
            existingToolbar.remove();
        }

        const toolbarHtml = `
        <div id="isa-enhancement-toolbar" class="position-fixed top-0 end-0 m-3" style="z-index: 9998;">
            <div class="btn-group-vertical" role="group">
                <button type="button" class="btn btn-primary btn-sm" id="isa-search-btn" title="Advanced Search">
                    <i class="bi bi-search"></i>
                </button>
                <button type="button" class="btn btn-success btn-sm" id="isa-export-btn" title="Export Data">
                    <i class="bi bi-download"></i>
                </button>
                <button type="button" class="btn btn-info btn-sm" id="isa-analytics-btn" title="Analytics Dashboard">
                    <i class="bi bi-graph-up"></i>
                </button>
                <button type="button" class="btn btn-warning btn-sm" id="isa-notifications-btn" title="Notifications">
                    <i class="bi bi-bell"></i>
                    <span id="notification-badge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="display: none;">0</span>
                </button>
                <button type="button" class="btn btn-secondary btn-sm" id="isa-help-btn" title="Help & Shortcuts">
                    <i class="bi bi-question-circle"></i>
                </button>
            </div>
        </div>`;

        document.body.insertAdjacentHTML('beforeend', toolbarHtml);

        // Setup toolbar event listeners
        this.setupToolbarEvents();
    }

    /**
     * Setup toolbar event listeners
     */
    setupToolbarEvents() {
        // Advanced Search
        const searchBtn = document.getElementById('isa-search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.openAdvancedSearch());
        }

        // Export Data
        const exportBtn = document.getElementById('isa-export-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.openExportDialog());
        }

        // Analytics Dashboard
        const analyticsBtn = document.getElementById('isa-analytics-btn');
        if (analyticsBtn) {
            analyticsBtn.addEventListener('click', () => this.openAnalyticsDashboard());
        }

        // Notifications
        const notificationsBtn = document.getElementById('isa-notifications-btn');
        if (notificationsBtn) {
            notificationsBtn.addEventListener('click', () => this.openNotificationsPanel());
        }

        // Help & Shortcuts
        const helpBtn = document.getElementById('isa-help-btn');
        if (helpBtn) {
            helpBtn.addEventListener('click', () => this.openHelpDialog());
        }
    }

    /**
     * Initialize notification system
     */
    initNotificationSystem() {
        // Create notification container
        let notificationContainer = document.getElementById('isa-notification-container');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'isa-notification-container';
            notificationContainer.className = 'position-fixed top-0 start-50 translate-middle-x mt-3';
            notificationContainer.style.zIndex = '9999';
            document.body.appendChild(notificationContainer);
        }

        // Notification polling disabled to prevent spam
        // setInterval(() => {
        //     this.checkForNotifications();
        // }, 30000); // Check every 30 seconds

        this.log('Notification system initialized');
    }

    /**
     * Initialize analytics tracking
     */
    initAnalytics() {
        // Track page view
        this.analytics.pageViews++;
        this.trackEvent('page_view', {
            app: this.config.appName,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        });

        // Track user interactions (excluding Google modal interactions to prevent conflicts)
        document.addEventListener('click', (e) => {
            // Skip tracking clicks within Google modals to prevent interference
            const isInGoogleModal = e.target.closest('#driveModal, #sheetsModal, #docsModal, #gmailModal, #filePreviewModal, #downloadModal, #shareModal, #deleteModal');
            if (!isInGoogleModal) {
                this.trackEvent('click', {
                    element: e.target.tagName,
                    className: e.target.className,
                    id: e.target.id
                });
            }
        });

        this.log('Analytics tracking initialized');
    }

    /**
     * Initialize export features
     */
    initExportFeatures() {
        // Add export capabilities to tables
        this.enhanceTablesWithExport();
        this.log('Export features initialized');
    }

    /**
     * Initialize advanced search
     */
    initAdvancedSearch() {
        // Build search index from page content
        this.buildSearchIndex();
        this.log('Advanced search initialized');
    }

    /**
     * Initialize performance monitoring
     */
    initPerformanceMonitoring() {
        // Monitor page load time
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            this.analytics.performanceMetrics.push({
                type: 'page_load',
                value: loadTime,
                timestamp: new Date().toISOString()
            });
        });

        // Monitor memory usage (if available)
        if ('memory' in performance) {
            setInterval(() => {
                this.analytics.performanceMetrics.push({
                    type: 'memory_usage',
                    value: performance.memory.usedJSHeapSize,
                    timestamp: new Date().toISOString()
                });
            }, 60000); // Every minute
        }

        this.log('Performance monitoring initialized');
    }

    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.openAdvancedSearch();
            }

            // Ctrl/Cmd + E for export
            if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
                e.preventDefault();
                this.openExportDialog();
            }

            // Ctrl/Cmd + ? for help
            if ((e.ctrlKey || e.metaKey) && e.key === '?') {
                e.preventDefault();
                this.openHelpDialog();
            }
        });

        this.log('Keyboard shortcuts initialized');
    }

    /**
     * Initialize cross-app communication
     */
    initCrossAppCommunication() {
        // Setup localStorage-based communication between apps
        window.addEventListener('storage', (e) => {
            if (e.key === 'isa_cross_app_message') {
                const message = JSON.parse(e.newValue);
                this.handleCrossAppMessage(message);
            }
        });

        this.log('Cross-app communication initialized');
    }

    /**
     * Track user events
     */
    trackEvent(eventType, data = {}) {
        const event = {
            type: eventType,
            app: this.config.appName,
            timestamp: new Date().toISOString(),
            data: data
        };

        this.analytics.userActions.push(event);

        // Keep only last 1000 events to prevent memory issues
        if (this.analytics.userActions.length > 1000) {
            this.analytics.userActions = this.analytics.userActions.slice(-1000);
        }

        this.log('Event tracked:', eventType, data);
    }

    /**
     * Show notification
     */
    showNotification(title, message, type = 'info', duration = 5000) {
        const notification = {
            id: Date.now(),
            title,
            message,
            type,
            timestamp: new Date().toISOString()
        };

        this.notifications.push(notification);
        this.updateNotificationBadge();

        // Create notification element
        const notificationHtml = `
        <div id="notification-${notification.id}" class="alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show mb-2" role="alert">
            <strong>${title}</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>`;

        const container = document.getElementById('isa-notification-container');
        if (container) {
            container.insertAdjacentHTML('beforeend', notificationHtml);

            // Auto-remove after duration
            setTimeout(() => {
                const element = document.getElementById(`notification-${notification.id}`);
                if (element) {
                    element.remove();
                }
            }, duration);
        }

        this.trackEvent('notification_shown', { title, type });
    }

    /**
     * Update notification badge
     */
    updateNotificationBadge() {
        const badge = document.getElementById('notification-badge');
        if (badge) {
            const unreadCount = this.notifications.filter(n => !n.read).length;
            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    /**
     * Open advanced search dialog
     */
    openAdvancedSearch() {
        this.createModal('Advanced Search', `
            <div class="mb-3">
                <label for="global-search-input" class="form-label">Search across all content:</label>
                <input type="text" class="form-control" id="global-search-input" placeholder="Enter search terms...">
            </div>
            <div class="mb-3">
                <label class="form-label">Search in:</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="search-tables" checked>
                    <label class="form-check-label" for="search-tables">Tables and Data</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="search-text" checked>
                    <label class="form-check-label" for="search-text">Text Content</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="search-forms" checked>
                    <label class="form-check-label" for="search-forms">Forms and Inputs</label>
                </div>
            </div>
            <div id="search-results" class="mt-3"></div>
        `, [
            { text: 'Search', class: 'btn-primary', onclick: 'isaEnhancements.performAdvancedSearch()' },
            { text: 'Clear', class: 'btn-outline-secondary', onclick: 'isaEnhancements.clearSearchResults()' }
        ]);

        this.trackEvent('advanced_search_opened');
    }

    /**
     * Open export dialog
     */
    openExportDialog() {
        this.createModal('Export Data', `
            <div class="mb-3">
                <label class="form-label">Export Format:</label>
                <select class="form-select" id="export-format">
                    <option value="csv">CSV (Comma Separated Values)</option>
                    <option value="xlsx">Excel (XLSX)</option>
                    <option value="json">JSON</option>
                    <option value="pdf">PDF Report</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Data to Export:</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="export-tables" checked>
                    <label class="form-check-label" for="export-tables">All Tables</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="export-analytics">
                    <label class="form-check-label" for="export-analytics">Analytics Data</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="export-settings">
                    <label class="form-check-label" for="export-settings">Application Settings</label>
                </div>
            </div>
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                Export will include data from the current application view.
            </div>
        `, [
            { text: 'Export', class: 'btn-success', onclick: 'isaEnhancements.performExport()' },
            { text: 'Cancel', class: 'btn-outline-secondary', onclick: 'isaEnhancements.closeModal()' }
        ]);

        this.trackEvent('export_dialog_opened');
    }

    /**
     * Open analytics dashboard
     */
    openAnalyticsDashboard() {
        const analytics = this.generateAnalyticsReport();

        this.createModal('Analytics Dashboard', `
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">Usage Statistics</h6>
                            <p class="card-text">Page Views: <strong>${analytics.pageViews}</strong></p>
                            <p class="card-text">User Actions: <strong>${analytics.totalActions}</strong></p>
                            <p class="card-text">Session Duration: <strong>${analytics.sessionDuration}</strong></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">Performance</h6>
                            <p class="card-text">Avg Load Time: <strong>${analytics.avgLoadTime}ms</strong></p>
                            <p class="card-text">Memory Usage: <strong>${analytics.memoryUsage}</strong></p>
                            <p class="card-text">Error Rate: <strong>${analytics.errorRate}%</strong></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <h6>Recent Activity</h6>
                <div class="list-group" style="max-height: 200px; overflow-y: auto;">
                    ${analytics.recentActivity.map(activity => `
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${activity.type}</h6>
                                <small>${new Date(activity.timestamp).toLocaleTimeString()}</small>
                            </div>
                            <p class="mb-1">${JSON.stringify(activity.data)}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
        `, [
            { text: 'Export Report', class: 'btn-primary', onclick: 'isaEnhancements.exportAnalytics()' },
            { text: 'Clear Data', class: 'btn-outline-danger', onclick: 'isaEnhancements.clearAnalytics()' }
        ]);

        this.trackEvent('analytics_dashboard_opened');
    }

    /**
     * Open notifications panel
     */
    openNotificationsPanel() {
        const notificationsHtml = this.notifications.length > 0 ?
            this.notifications.slice(-10).reverse().map(notification => `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${notification.title}</h6>
                        <small>${new Date(notification.timestamp).toLocaleTimeString()}</small>
                    </div>
                    <p class="mb-1">${notification.message}</p>
                    <small class="text-muted">Type: ${notification.type}</small>
                </div>
            `).join('') :
            '<div class="text-center text-muted p-3">No notifications</div>';

        this.createModal('Notifications', `
            <div class="list-group" style="max-height: 400px; overflow-y: auto;">
                ${notificationsHtml}
            </div>
        `, [
            { text: 'Mark All Read', class: 'btn-primary', onclick: 'isaEnhancements.markAllNotificationsRead()' },
            { text: 'Clear All', class: 'btn-outline-danger', onclick: 'isaEnhancements.clearAllNotifications()' }
        ]);

        this.trackEvent('notifications_panel_opened');
    }

    /**
     * Open help dialog
     */
    openHelpDialog() {
        this.createModal('Help & Keyboard Shortcuts', `
            <div class="row">
                <div class="col-md-6">
                    <h6>Keyboard Shortcuts</h6>
                    <ul class="list-unstyled">
                        <li><kbd>Ctrl/Cmd + K</kbd> - Advanced Search</li>
                        <li><kbd>Ctrl/Cmd + E</kbd> - Export Data</li>
                        <li><kbd>Ctrl/Cmd + ?</kbd> - Show Help</li>
                        <li><kbd>Esc</kbd> - Close Modals</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Features</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-search text-primary"></i> Advanced Search</li>
                        <li><i class="bi bi-download text-success"></i> Data Export</li>
                        <li><i class="bi bi-graph-up text-info"></i> Analytics</li>
                        <li><i class="bi bi-bell text-warning"></i> Notifications</li>
                    </ul>
                </div>
            </div>
            <div class="mt-3">
                <h6>About ISA Suite Enhancements</h6>
                <p class="text-muted">
                    These enhancements provide powerful cross-application features to improve your productivity
                    and provide insights into your usage patterns.
                </p>
            </div>
        `, [
            { text: 'Got it!', class: 'btn-primary', onclick: 'isaEnhancements.closeModal()' }
        ]);

        this.trackEvent('help_dialog_opened');
    }

    /**
     * Create a modal dialog
     */
    createModal(title, content, buttons = []) {
        // Remove existing modal
        const existingModal = document.getElementById('isa-enhancement-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const buttonsHtml = buttons.map(btn =>
            `<button type="button" class="btn ${btn.class}" onclick="${btn.onclick}">${btn.text}</button>`
        ).join(' ');

        const modalHtml = `
        <div class="modal fade" id="isa-enhancement-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: ${this.config.appColor}; color: white;">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        ${buttonsHtml}
                    </div>
                </div>
            </div>
        </div>`;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('isa-enhancement-modal'));
        modal.show();
    }

    /**
     * Close modal
     */
    closeModal() {
        const modal = document.getElementById('isa-enhancement-modal');
        if (modal) {
            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            if (bootstrapModal) {
                bootstrapModal.hide();
            }
        }
    }

    /**
     * Perform advanced search
     */
    performAdvancedSearch() {
        const searchTerm = document.getElementById('global-search-input')?.value;
        const searchTables = document.getElementById('search-tables')?.checked;
        const searchText = document.getElementById('search-text')?.checked;
        const searchForms = document.getElementById('search-forms')?.checked;

        if (!searchTerm) {
            this.showNotification('Search Error', 'Please enter a search term', 'error');
            return;
        }

        const results = this.searchContent(searchTerm, { searchTables, searchText, searchForms });
        this.displaySearchResults(results);
        this.trackEvent('advanced_search_performed', { term: searchTerm, resultsCount: results.length });
    }

    /**
     * Search content across the page
     */
    searchContent(term, options) {
        const results = [];
        const searchTerm = term.toLowerCase();

        // Search in tables
        if (options.searchTables) {
            document.querySelectorAll('table').forEach((table, tableIndex) => {
                table.querySelectorAll('td, th').forEach((cell, cellIndex) => {
                    if (cell.textContent.toLowerCase().includes(searchTerm)) {
                        results.push({
                            type: 'Table',
                            content: cell.textContent.trim(),
                            location: `Table ${tableIndex + 1}, Cell ${cellIndex + 1}`,
                            element: cell
                        });
                    }
                });
            });
        }

        // Search in text content
        if (options.searchText) {
            document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6').forEach((element, index) => {
                if (element.textContent.toLowerCase().includes(searchTerm) && element.children.length === 0) {
                    results.push({
                        type: 'Text',
                        content: element.textContent.trim(),
                        location: `${element.tagName} element ${index + 1}`,
                        element: element
                    });
                }
            });
        }

        // Search in forms
        if (options.searchForms) {
            document.querySelectorAll('input, textarea, select').forEach((input, index) => {
                const searchableText = [input.value, input.placeholder, input.name, input.id].join(' ').toLowerCase();
                if (searchableText.includes(searchTerm)) {
                    results.push({
                        type: 'Form Field',
                        content: `${input.type || input.tagName}: ${input.value || input.placeholder || input.name || input.id}`,
                        location: `Form field ${index + 1}`,
                        element: input
                    });
                }
            });
        }

        return results.slice(0, 50); // Limit to 50 results
    }

    /**
     * Display search results
     */
    displaySearchResults(results) {
        const resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) return;

        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="alert alert-info">No results found</div>';
            return;
        }

        const resultsHtml = `
            <h6>Search Results (${results.length})</h6>
            <div class="list-group" style="max-height: 300px; overflow-y: auto;">
                ${results.map((result, index) => `
                    <div class="list-group-item list-group-item-action" onclick="isaEnhancements.highlightSearchResult(${index})">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${result.type}</h6>
                            <small>${result.location}</small>
                        </div>
                        <p class="mb-1">${result.content.substring(0, 100)}${result.content.length > 100 ? '...' : ''}</p>
                    </div>
                `).join('')}
            </div>
        `;

        resultsContainer.innerHTML = resultsHtml;
        this.searchResults = results; // Store for highlighting
    }

    /**
     * Highlight search result
     */
    highlightSearchResult(index) {
        if (!this.searchResults || !this.searchResults[index]) return;

        const result = this.searchResults[index];
        result.element.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Add highlight effect
        result.element.style.backgroundColor = '#ffeb3b';
        result.element.style.transition = 'background-color 0.3s';

        setTimeout(() => {
            result.element.style.backgroundColor = '';
        }, 2000);

        this.closeModal();
        this.trackEvent('search_result_clicked', { type: result.type });
    }

    /**
     * Clear search results
     */
    clearSearchResults() {
        const resultsContainer = document.getElementById('search-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
        }

        const searchInput = document.getElementById('global-search-input');
        if (searchInput) {
            searchInput.value = '';
        }
    }

    /**
     * Perform export
     */
    performExport() {
        const format = document.getElementById('export-format')?.value || 'csv';
        const exportTables = document.getElementById('export-tables')?.checked;
        const exportAnalytics = document.getElementById('export-analytics')?.checked;
        const exportSettings = document.getElementById('export-settings')?.checked;

        const data = this.collectExportData({ exportTables, exportAnalytics, exportSettings });
        this.downloadData(data, format);

        this.showNotification('Export Complete', `Data exported as ${format.toUpperCase()}`, 'success');
        this.trackEvent('data_exported', { format, dataTypes: { exportTables, exportAnalytics, exportSettings } });
        this.closeModal();
    }

    /**
     * Collect data for export
     */
    collectExportData(options) {
        const data = {
            timestamp: new Date().toISOString(),
            application: this.config.appName,
            data: {}
        };

        if (options.exportTables) {
            data.data.tables = this.extractTableData();
        }

        if (options.exportAnalytics) {
            data.data.analytics = this.analytics;
        }

        if (options.exportSettings) {
            data.data.settings = this.config;
        }

        return data;
    }

    /**
     * Extract table data from the page
     */
    extractTableData() {
        const tables = [];

        document.querySelectorAll('table').forEach((table, index) => {
            const tableData = {
                name: `Table_${index + 1}`,
                headers: [],
                rows: []
            };

            // Extract headers
            const headerRow = table.querySelector('thead tr, tr:first-child');
            if (headerRow) {
                headerRow.querySelectorAll('th, td').forEach(cell => {
                    tableData.headers.push(cell.textContent.trim());
                });
            }

            // Extract data rows
            const dataRows = table.querySelectorAll('tbody tr, tr:not(:first-child)');
            dataRows.forEach(row => {
                const rowData = [];
                row.querySelectorAll('td, th').forEach(cell => {
                    rowData.push(cell.textContent.trim());
                });
                if (rowData.length > 0) {
                    tableData.rows.push(rowData);
                }
            });

            tables.push(tableData);
        });

        return tables;
    }

    /**
     * Download data in specified format
     */
    downloadData(data, format) {
        let content, mimeType, filename;

        switch (format) {
            case 'json':
                content = JSON.stringify(data, null, 2);
                mimeType = 'application/json';
                filename = `${this.config.appName}_export_${Date.now()}.json`;
                break;
            case 'csv':
                content = this.convertToCSV(data);
                mimeType = 'text/csv';
                filename = `${this.config.appName}_export_${Date.now()}.csv`;
                break;
            default:
                content = JSON.stringify(data, null, 2);
                mimeType = 'application/json';
                filename = `${this.config.appName}_export_${Date.now()}.json`;
        }

        // Create download link
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    /**
     * Convert data to CSV format
     */
    convertToCSV(data) {
        let csv = '';

        if (data.data.tables) {
            data.data.tables.forEach(table => {
                csv += `\n\n${table.name}\n`;
                if (table.headers.length > 0) {
                    csv += table.headers.join(',') + '\n';
                }
                table.rows.forEach(row => {
                    csv += row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',') + '\n';
                });
            });
        }

        return csv;
    }

    /**
     * Generate analytics report
     */
    generateAnalyticsReport() {
        const now = Date.now();
        const sessionStart = this.analytics.userActions.length > 0 ?
            new Date(this.analytics.userActions[0].timestamp).getTime() : now;
        const sessionDuration = Math.round((now - sessionStart) / 1000 / 60); // minutes

        const avgLoadTime = this.analytics.performanceMetrics
            .filter(m => m.type === 'page_load')
            .reduce((sum, m, _, arr) => sum + m.value / arr.length, 0);

        const memoryMetrics = this.analytics.performanceMetrics
            .filter(m => m.type === 'memory_usage');
        const currentMemory = memoryMetrics.length > 0 ?
            Math.round(memoryMetrics[memoryMetrics.length - 1].value / 1024 / 1024) : 0;

        return {
            pageViews: this.analytics.pageViews,
            totalActions: this.analytics.userActions.length,
            sessionDuration: `${sessionDuration} minutes`,
            avgLoadTime: Math.round(avgLoadTime),
            memoryUsage: `${currentMemory} MB`,
            errorRate: 0, // Could be enhanced to track actual errors
            recentActivity: this.analytics.userActions.slice(-10)
        };
    }

    /**
     * Export analytics data
     */
    exportAnalytics() {
        const analytics = this.generateAnalyticsReport();
        this.downloadData({ analytics }, 'json');
        this.showNotification('Analytics Exported', 'Analytics data exported successfully', 'success');
        this.closeModal();
    }

    /**
     * Clear analytics data
     */
    clearAnalytics() {
        if (confirm('Are you sure you want to clear all analytics data?')) {
            this.analytics = {
                pageViews: 0,
                userActions: [],
                performanceMetrics: []
            };
            this.showNotification('Analytics Cleared', 'All analytics data has been cleared', 'success');
            this.closeModal();
        }
    }

    /**
     * Mark all notifications as read
     */
    markAllNotificationsRead() {
        this.notifications.forEach(notification => {
            notification.read = true;
        });
        this.updateNotificationBadge();
        this.showNotification('Notifications', 'All notifications marked as read', 'success');
        this.closeModal();
    }

    /**
     * Clear all notifications
     */
    clearAllNotifications() {
        if (confirm('Are you sure you want to clear all notifications?')) {
            this.notifications = [];
            this.updateNotificationBadge();
            this.showNotification('Notifications', 'All notifications cleared', 'success');
            this.closeModal();
        }
    }

    /**
     * Check for new notifications (simulated)
     */
    checkForNotifications() {
        // Simulate random notifications for demo purposes
        const notifications = [
            { title: 'System Update', message: 'New features available in the ISA Suite', type: 'info' },
            { title: 'Data Backup', message: 'Automated backup completed successfully', type: 'success' },
            { title: 'Performance Alert', message: 'High memory usage detected', type: 'warning' },
            { title: 'New Message', message: 'You have received a new email', type: 'info' }
        ];

        // Random notifications disabled to prevent spam
        // if (Math.random() < 0.1) {
        //     const notification = notifications[Math.floor(Math.random() * notifications.length)];
        //     this.showNotification(notification.title, notification.message, notification.type);
        // }
    }

    /**
     * Handle cross-app messages
     */
    handleCrossAppMessage(message) {
        if (message.type === 'notification') {
            this.showNotification(message.title, message.content, message.level || 'info');
        } else if (message.type === 'data_update') {
            this.showNotification('Data Update', `Data updated in ${message.source}`, 'info');
        }

        this.trackEvent('cross_app_message_received', { type: message.type, source: message.source });
    }

    /**
     * Send message to other apps
     */
    sendCrossAppMessage(type, data) {
        const message = {
            type,
            source: this.config.appName,
            timestamp: new Date().toISOString(),
            ...data
        };

        localStorage.setItem('isa_cross_app_message', JSON.stringify(message));
        this.trackEvent('cross_app_message_sent', { type, target: 'all' });
    }

    /**
     * Build search index from page content
     */
    buildSearchIndex() {
        this.searchIndex.clear();

        // Index all text content
        document.querySelectorAll('*').forEach((element, index) => {
            if (element.children.length === 0 && element.textContent.trim()) {
                const text = element.textContent.trim().toLowerCase();
                const words = text.split(/\s+/);

                words.forEach(word => {
                    if (word.length > 2) { // Only index words longer than 2 characters
                        if (!this.searchIndex.has(word)) {
                            this.searchIndex.set(word, []);
                        }
                        this.searchIndex.get(word).push({
                            element,
                            text: element.textContent.trim(),
                            index
                        });
                    }
                });
            }
        });

        this.log('Search index built with', this.searchIndex.size, 'terms');
    }

    /**
     * Enhance tables with export functionality
     */
    enhanceTablesWithExport() {
        document.querySelectorAll('table').forEach((table, index) => {
            // Add export button to table if it doesn't exist
            if (!table.querySelector('.isa-table-export-btn')) {
                const exportBtn = document.createElement('button');
                exportBtn.className = 'btn btn-sm btn-outline-primary isa-table-export-btn';
                exportBtn.innerHTML = '<i class="bi bi-download"></i> Export';
                exportBtn.style.position = 'absolute';
                exportBtn.style.top = '5px';
                exportBtn.style.right = '5px';
                exportBtn.style.zIndex = '10';

                exportBtn.addEventListener('click', () => {
                    this.exportSingleTable(table, `Table_${index + 1}`);
                });

                // Make table container relative if not already
                const container = table.closest('.table-responsive') || table.parentElement;
                if (container) {
                    container.style.position = 'relative';
                    container.appendChild(exportBtn);
                }
            }
        });
    }

    /**
     * Export a single table
     */
    exportSingleTable(table, name) {
        const tableData = {
            name,
            headers: [],
            rows: []
        };

        // Extract headers
        const headerRow = table.querySelector('thead tr, tr:first-child');
        if (headerRow) {
            headerRow.querySelectorAll('th, td').forEach(cell => {
                tableData.headers.push(cell.textContent.trim());
            });
        }

        // Extract data rows
        const dataRows = table.querySelectorAll('tbody tr, tr:not(:first-child)');
        dataRows.forEach(row => {
            const rowData = [];
            row.querySelectorAll('td, th').forEach(cell => {
                rowData.push(cell.textContent.trim());
            });
            if (rowData.length > 0) {
                tableData.rows.push(rowData);
            }
        });

        // Convert to CSV and download
        let csv = tableData.headers.join(',') + '\n';
        tableData.rows.forEach(row => {
            csv += row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',') + '\n';
        });

        const blob = new Blob([csv], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${name}_${Date.now()}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.showNotification('Table Exported', `${name} exported successfully`, 'success');
        this.trackEvent('single_table_exported', { tableName: name });
    }

    /**
     * Log messages (if debug is enabled)
     */
    log(message, ...args) {
        if (this.config.debug) {
            console.log(`[ISAEnhancements:${this.config.appName}]`, message, ...args);
        }
    }
}

// Make globally available
window.ISASuiteEnhancements = ISASuiteEnhancements;

// Auto-initialize if config is available
if (typeof window.isaEnhancementConfig !== 'undefined') {
    const enhancements = new ISASuiteEnhancements(window.isaEnhancementConfig);
    enhancements.init();
}
