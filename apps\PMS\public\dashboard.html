<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        :root {
            --app-primary-color: #9b59b6; /* Purple for PMS */
            --app-primary-dark: #8e44ad;
            --app-primary-light: rgba(155, 89, 182, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }

        h1 {
            margin-bottom: 20px;
            color: #333;
        }

        .back-button {
            display: inline-block;
            padding: 6px 12px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            margin-bottom: 20px;
        }

        .back-button:hover {
            background-color: #e9ecef;
        }

        .kpi-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .kpi-box {
            flex: 1;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 0 10px;
        }

        .kpi-box:first-child {
            margin-left: 0;
        }

        .kpi-box:last-child {
            margin-right: 0;
        }

        .kpi-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--app-primary-color);
            margin-bottom: 5px;
        }

        .kpi-label {
            color: #6c757d;
            font-size: 14px;
        }

        .section {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 18px;
            color: #333;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            text-align: left;
            padding: 12px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            color: #495057;
            font-weight: 600;
        }

        td {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
            color: #212529;
        }

        tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .status-completed {
            background-color: #2ecc71;
        }

        .status-in-progress {
            background-color: #f1c40f;
        }

        .status-at-risk {
            background-color: #e74c3c;
        }

        .status-on-track {
            background-color: #2ecc71;
        }

        .progress-bar {
            height: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--app-primary-color);
        }
    </style>
</head>
<body>
    <h1>Project Management System</h1>

    <a href="http://localhost:8000" class="back-button">Back to Hub</a>

    <div class="kpi-container">
        <div class="kpi-box">
            <div class="kpi-value">3</div>
            <div class="kpi-label">Active Projects</div>
        </div>
        <div class="kpi-box">
            <div class="kpi-value">68%</div>
            <div class="kpi-label">Average Progress</div>
        </div>
        <div class="kpi-box">
            <div class="kpi-value">$1.2M</div>
            <div class="kpi-label">Total Budget</div>
        </div>
    </div>

    <div class="section">
        <h2>Project Overview</h2>
        <table>
            <thead>
                <tr>
                    <th>Project</th>
                    <th>Manager</th>
                    <th>Timeline</th>
                    <th>Budget</th>
                    <th>Status</th>
                    <th>Progress</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>ERP System Implementation</td>
                    <td>Sarah Wilson</td>
                    <td>2024-01-15 - 2024-06-30</td>
                    <td>$350,000 / $500,000</td>
                    <td><span class="status-badge status-in-progress">In Progress</span></td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 70%;"></div>
                        </div>
                        <div style="text-align: right; font-size: 12px; color: #6c757d;">70%</div>
                    </td>
                </tr>
                <tr>
                    <td>Mobile App Development</td>
                    <td>David Chen</td>
                    <td>2024-01-01 - 2024-08-15</td>
                    <td>$120,000 / $300,000</td>
                    <td><span class="status-badge status-on-track">On Track</span></td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 40%;"></div>
                        </div>
                        <div style="text-align: right; font-size: 12px; color: #6c757d;">40%</div>
                    </td>
                </tr>
                <tr>
                    <td>Cloud Migration</td>
                    <td>Emily Brown</td>
                    <td>2024-02-01 - 2024-07-15</td>
                    <td>$380,000 / $400,000</td>
                    <td><span class="status-badge status-at-risk">At Risk</span></td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 95%;"></div>
                        </div>
                        <div style="text-align: right; font-size: 12px; color: #6c757d;">95%</div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>Project Milestones</h2>
        <table>
            <thead>
                <tr>
                    <th>Project</th>
                    <th>Milestone</th>
                    <th>Due Date</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>ERP System Implementation</td>
                    <td>Core Modules Development</td>
                    <td>2024-03-30</td>
                    <td><span class="status-badge status-completed">Completed</span></td>
                </tr>
                <tr>
                    <td>ERP System Implementation</td>
                    <td>Integration Testing</td>
                    <td>2024-05-15</td>
                    <td><span class="status-badge status-in-progress">In Progress</span></td>
                </tr>
                <tr>
                    <td>Mobile App Development</td>
                    <td>UI/UX Design</td>
                    <td>2024-03-15</td>
                    <td><span class="status-badge status-completed">Completed</span></td>
                </tr>
                <tr>
                    <td>Cloud Migration</td>
                    <td>Data Migration</td>
                    <td>2024-05-15</td>
                    <td><span class="status-badge status-at-risk">At Risk</span></td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        // Create a simple health endpoint for status checks
        if (window.fetch) {
            // Create a fake health endpoint for the hub to check
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                if (url.includes('/health')) {
                    return Promise.resolve({
                        ok: true,
                        status: 200,
                        json: () => Promise.resolve({ status: 'ok' })
                    });
                }
                return originalFetch(url, options);
            };
        }
    </script>
</body>
</html>
