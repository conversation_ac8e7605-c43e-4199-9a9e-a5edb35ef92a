// Advanced Planning and Scheduling System - Main Entry Point

const express = require('express');
const cors = require('cors');
const app = express();
const port = 3005;

// Import shared features
const sharedFeatures = require('../../SharedFeatures');
const auth = sharedFeatures.auth;
const logger = sharedFeatures.logger.createLogger('APS');
const google = require('../../SharedFeatures/integrations/google');
const slack = sharedFeatures.slack;

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));

// Initialize integrations
google.initGoogleAPI().catch((err) => {
  logger && logger.error ? logger.error('Failed to initialize Google API', { error: err.message }) : console.error('Failed to initialize Google API', err);
});

// Define routes
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/api/schedules', (req, res) => {
  res.json({
    status: 'success',
    data: {
      schedules: [
        {
          id: 1,
          name: 'Production Line A',
          startDate: '2025-05-01',
          endDate: '2025-05-15',
          status: 'active',
        },
        {
          id: 2,
          name: 'Production Line B',
          startDate: '2025-05-10',
          endDate: '2025-05-25',
          status: 'pending',
        },
        {
          id: 3,
          name: 'Maintenance Schedule',
          startDate: '2025-05-05',
          endDate: '2025-05-07',
          status: 'completed',
        },
      ],
    },
  });
});

// Google Calendar Integration for Production Schedules
app.get('/api/calendar/schedules', auth.authenticate, async (req, res) => {
  try {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 3); // Three months ahead

    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger.error('Failed to fetch production schedules from calendar', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Sheets Integration for Production Planning
app.get('/api/planning/spreadsheet/:spreadsheetId', auth.authenticate, async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Production!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger.error('Failed to fetch production planning spreadsheet', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration for Production Documents
app.get('/api/documents', auth.authenticate, async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger.error('Failed to fetch production documents', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Slack Integration for Production Alerts
app.post('/api/production/alert', auth.authenticate, async (req, res) => {
  try {
    const { lineId, lineName, issue, priority, channel } = req.body;

    const message = `*PRODUCTION ALERT*: Issue on ${lineName} (ID: ${lineId})\nIssue: ${issue}\nPriority: ${priority}`;
    const result = await slack.sendMessage(channel, message);

    res.json({ status: 'success', data: { alerted: true, result } });
  } catch (error) {
    logger.error('Failed to send production alert', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Sample Google Sheets endpoint
app.get('/api/google-sheets/:spreadsheetId', async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Sheets data', { error: error.message }) : console.error('Failed to fetch Google Sheets data', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Drive files', { error: error.message }) : console.error('Failed to fetch Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration
app.get('/api/google-calendar/events', async (req, res) => {
  try {
    const startDate = new Date(req.query.startDate || Date.now());
    const endDate = new Date(req.query.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Calendar events', { error: error.message }) : console.error('Failed to fetch Google Calendar events', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/google-gmail/send', async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to send Gmail email', { error: error.message }) : console.error('Failed to send Gmail email', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration
app.post('/api/google-docs/create', async (req, res) => {
  try {
    const { title } = req.body;
    const doc = await google.Docs.createDocument(title);
    res.json({ status: 'success', data: doc });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to create Google Doc', { error: error.message }) : console.error('Failed to create Google Doc', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google-contacts', async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Contacts', { error: error.message }) : console.error('Failed to fetch Google Contacts', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Start server
app.listen(port, () => {
  console.log(`APS running at http://localhost:${port}`);
  console.log('Connected to IntegrationHub');
});
