
> @isasuite/hub@1.0.0 dev C:\ISASUITE\apps\hub
> next dev "--" "-p" "8000"

 ⚠ You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env
   ▲ Next.js 14.1.0
   - Local:        http://localhost:8000

npm warn config ignoring workspace config at C:\ISASUITE\apps\hub/.npmrc
npm error code ENOWORKSPACES
npm error This command does not support workspaces.
npm error A complete log of this run can be found in: C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-06-05T11_21_34_827Z-debug-0.log
 ⚠ You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env
 ✓ Ready in 4.2s
 ○ Compiling / ...
 ✓ Compiled / in 631ms (204 modules)
 ✓ Compiled /_error in 173ms (206 modules)
^C ELIFECYCLE  Command failed with exit code 3221225786.
