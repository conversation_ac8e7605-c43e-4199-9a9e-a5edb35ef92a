const express = require('express');
// const bodyParser = require('body-parser');
// const logger = require('./logger');
const google = require('../../SharedFeatures/integrations/google');

const app = express();
app.use(express.json());

// Serve static files
app.use(express.static('public'));

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));

google.initGoogleAPI().catch((err) => {
  console.error('Failed to initialize Google API', err);
});

// Sample Google Sheets endpoint
app.get('/api/google-sheets/:spreadsheetId', async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Sheets data', { error: error.message }) : console.error('Failed to fetch Google Sheets data', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Drive files', { error: error.message }) : console.error('Failed to fetch Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration
app.get('/api/google-calendar/events', async (req, res) => {
  try {
    const startDate = new Date(req.query.startDate || Date.now());
    const endDate = new Date(req.query.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Calendar events', { error: error.message }) : console.error('Failed to fetch Google Calendar events', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/google-gmail/send', async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to send Gmail email', { error: error.message }) : console.error('Failed to send Gmail email', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration
app.post('/api/google-docs/create', async (req, res) => {
  try {
    const { title } = req.body;
    const doc = await google.Docs.createDocument(title);
    res.json({ status: 'success', data: doc });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to create Google Doc', { error: error.message }) : console.error('Failed to create Google Doc', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google-contacts', async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Contacts', { error: error.message }) : console.error('Failed to fetch Google Contacts', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

const PORT = process.env.PORT || 3008;
app.listen(PORT, () => {
  console.log(`SCM running at http://localhost:${PORT}`);
  console.log('Connected to IntegrationHub');
});