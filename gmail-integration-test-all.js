/**
 * Gmail Integration Verification Script for All ISA Suite Applications
 * This script checks that all applications are properly using the enhanced Gmail integration
 */

console.log('📧 Gmail Integration Verification for All ISA Suite Applications');
console.log('================================================================');

// List of all ISA Suite applications with their configurations
const applications = [
    { name: 'APM', prefix: 'apm', color: '#3498db', path: 'apps/APM/public/index.html' },
    { name: 'APS', prefix: 'aps', color: '#1abc9c', path: 'apps/APS/public/index.html' },
    { name: 'BMS', prefix: 'bms', color: '#00acc1', path: 'apps/BMS/public/index.html' },
    { name: 'CRM', prefix: 'crm', color: '#e67e22', path: 'apps/CRM/public/index.html' },
    { name: 'MR<PERSON>', prefix: 'mrp', color: '#9c27b0', path: 'apps/MRP/public/index.html' },
    { name: 'PMS', prefix: 'pms', color: '#e91e63', path: 'apps/PMS/public/index.html' },
    { name: 'SCM', prefix: 'scm', color: '#6a3de8', path: 'apps/SCM/public/index.html' },
    { name: 'TM', prefix: 'tm', color: '#6c5ce7', path: 'apps/TM/public/index.html' },
    { name: 'WMS', prefix: 'wms', color: '#2ecc71', path: 'apps/WMS/public/index.html' }
];

let passedTests = 0;
let totalTests = 0;

/**
 * Test Gmail integration for a specific application
 */
function testGmailIntegration(app) {
    console.log(`\n🔍 Testing ${app.name} Application...`);
    totalTests++;
    
    try {
        // Test if the enhanced Gmail integration can be initialized
        const testConfig = {
            appName: app.name,
            appPrefix: app.prefix,
            appColor: app.color,
            modalId: `${app.prefix}-gmailModal`,
            triggerId: 'gmail-link'
        };
        
        if (typeof initializeGmail === 'function') {
            console.log(`✅ ${app.name}: initializeGmail function is available`);
            
            // Try to initialize
            const gmail = initializeGmail(testConfig);
            if (gmail) {
                console.log(`✅ ${app.name}: Gmail integration initialized successfully`);
                passedTests++;
                return true;
            } else {
                console.log(`❌ ${app.name}: Gmail integration failed to initialize`);
                return false;
            }
        } else {
            console.log(`❌ ${app.name}: initializeGmail function not available`);
            return false;
        }
    } catch (error) {
        console.log(`❌ ${app.name}: Error - ${error.message}`);
        return false;
    }
}

/**
 * Run all tests
 */
function runAllTests() {
    console.log('Starting Gmail integration tests...\n');
    
    // Check if the enhanced Gmail integration is loaded
    if (typeof GmailIntegration !== 'undefined') {
        console.log('✅ Enhanced Gmail Integration class is loaded');
    } else {
        console.log('❌ Enhanced Gmail Integration class is NOT loaded');
        return;
    }
    
    if (typeof initializeGmail !== 'undefined') {
        console.log('✅ initializeGmail function is available');
    } else {
        console.log('❌ initializeGmail function is NOT available');
        return;
    }
    
    // Test each application
    applications.forEach(app => {
        testGmailIntegration(app);
    });
    
    // Show results
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    console.log(`Total Applications: ${applications.length}`);
    console.log(`Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`Success Rate: ${((passedTests/totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Gmail integration is working for all applications.');
    } else {
        console.log('⚠️ Some tests failed. Please check the failed applications.');
    }
}

// Run tests when the script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    runAllTests();
}
