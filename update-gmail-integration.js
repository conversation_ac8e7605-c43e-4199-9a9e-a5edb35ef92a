/**
 * Gmail Integration Update Script
 * 
 * This script updates all ISA Suite applications to use the unified Gmail integration
 * system, replacing all conflicting and broken Gmail implementations.
 */

const fs = require('fs');
const path = require('path');

// Application configurations
const applications = [
    { name: 'B<PERSON>', prefix: 'bms', color: '#28a745', port: 3001 },
    { name: 'PMS', prefix: 'pms', color: '#e91e63', port: 3004 },
    { name: 'APM', prefix: 'apm', color: '#ff9800', port: 3005 },
    { name: 'APS', prefix: 'aps', color: '#2196f3', port: 3006 },
    { name: 'WMS', prefix: 'wms', color: '#4caf50', port: 3007 },
    { name: 'TM', prefix: 'tm', color: '#9c27b0', port: 3008 },
    { name: 'SCM', prefix: 'scm', color: '#6a3de8', port: 3009 }
];

// Gmail integration patterns to replace
const oldPatterns = [
    /<!-- Gmail Simple Link Handler.*?<\/script>/gs,
    /<!-- Enhanced Gmail Integration.*?<\/script>/gs,
    /<!-- Initialize Enhanced Gmail Integration.*?<\/script>/gs,
    /<script src=".*?gmail-simple-link\.js"><\/script>/g,
    /<script src=".*?gmail-integration\.js"><\/script>/g,
    /<script src=".*?gmail-integration-enhanced\.js"><\/script>/g,
    /<script src=".*?gmail-implementation\.js"><\/script>/g,
    /initializeGmail\({[\s\S]*?}\);/g
];

// New unified Gmail integration template
function getUnifiedGmailIntegration(app) {
    return `    <!-- Unified Gmail Integration - Single working system -->
    <script src="../../SharedFeatures/ui/gmail-integration-unified.js"></script>

    <!-- Initialize Unified Gmail Integration -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the unified Gmail integration for ${app.name}
            if (typeof initializeUnifiedGmail === 'function') {
                const gmail = initializeUnifiedGmail({
                    appName: '${app.name}',
                    appPrefix: '${app.prefix}',
                    appColor: '${app.color}', // ${app.name} theme color
                    modalId: '${app.prefix}-gmailModal',
                    triggerId: 'gmail-link', // The sidebar Gmail link
                    debug: process.env.NODE_ENV !== 'production'
                });
                console.log('Unified Gmail integration initialized for ${app.name} application');
                
                // Make globally available for debugging
                window.${app.prefix}Gmail = gmail;
            } else {
                console.warn('Unified Gmail integration not available');
            }
        });
    </script>`;
}

// Function to update an application's index.html file
function updateApplication(app) {
    const indexPath = path.join(__dirname, 'apps', app.name, 'public', 'index.html');
    
    if (!fs.existsSync(indexPath)) {
        console.log(`❌ ${app.name}: index.html not found at ${indexPath}`);
        return false;
    }

    try {
        let content = fs.readFileSync(indexPath, 'utf8');
        let modified = false;

        // Remove old Gmail integration patterns
        oldPatterns.forEach(pattern => {
            const originalContent = content;
            content = content.replace(pattern, '');
            if (content !== originalContent) {
                modified = true;
                console.log(`   Removed old Gmail pattern from ${app.name}`);
            }
        });

        // Find the insertion point (before </body>)
        const bodyEndIndex = content.lastIndexOf('</body>');
        if (bodyEndIndex === -1) {
            console.log(`❌ ${app.name}: Could not find </body> tag`);
            return false;
        }

        // Insert the new unified Gmail integration
        const newIntegration = getUnifiedGmailIntegration(app);
        content = content.slice(0, bodyEndIndex) + newIntegration + '\n' + content.slice(bodyEndIndex);
        modified = true;

        // Write the updated content back to the file
        if (modified) {
            fs.writeFileSync(indexPath, content, 'utf8');
            console.log(`✅ ${app.name}: Gmail integration updated successfully`);
            return true;
        } else {
            console.log(`ℹ️  ${app.name}: No changes needed`);
            return true;
        }

    } catch (error) {
        console.log(`❌ ${app.name}: Error updating file - ${error.message}`);
        return false;
    }
}

// Function to remove conflicting Gmail files
function removeConflictingFiles() {
    console.log('\n🧹 Removing conflicting Gmail files...');
    
    const filesToRemove = [
        'shared/gmail-simple-link.js',
        'shared/enhanced-gmail.js',
        'shared/enhanced-gmail-functions.js',
        'shared/enhanced-gmail-utils.js'
    ];

    filesToRemove.forEach(filePath => {
        const fullPath = path.join(__dirname, filePath);
        if (fs.existsSync(fullPath)) {
            try {
                fs.unlinkSync(fullPath);
                console.log(`   ✅ Removed: ${filePath}`);
            } catch (error) {
                console.log(`   ❌ Failed to remove: ${filePath} - ${error.message}`);
            }
        } else {
            console.log(`   ℹ️  Not found: ${filePath}`);
        }
    });
}

// Main execution
function main() {
    console.log('🚀 Starting Gmail Integration Update for ISA Suite\n');
    console.log('This will replace all broken Gmail integrations with the unified system.\n');

    let successCount = 0;
    let totalCount = applications.length;

    // Update each application
    applications.forEach(app => {
        console.log(`📱 Updating ${app.name}...`);
        if (updateApplication(app)) {
            successCount++;
        }
        console.log('');
    });

    // Remove conflicting files
    removeConflictingFiles();

    // Summary
    console.log('\n📊 Update Summary:');
    console.log(`   ✅ Successfully updated: ${successCount}/${totalCount} applications`);
    console.log(`   ❌ Failed updates: ${totalCount - successCount}/${totalCount} applications`);

    if (successCount === totalCount) {
        console.log('\n🎉 All applications updated successfully!');
        console.log('\n📋 Next steps:');
        console.log('   1. Test Gmail functionality in each application');
        console.log('   2. Verify sidebar Gmail links work');
        console.log('   3. Test email viewing, reply, and compose features');
        console.log('   4. Check search, sort, and filter functionality');
    } else {
        console.log('\n⚠️  Some applications failed to update. Please check the errors above.');
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = { updateApplication, applications, getUnifiedGmailIntegration };
