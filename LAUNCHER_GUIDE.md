# ISA Suite Launcher Guide

This document describes the various ways to start the ISA Suite applications and when to use each method.

## Recommended Launchers

### Primary Launcher
- **`single-window-launcher.bat`** - The main launcher script that starts all applications in a single window.
  - Automatically installs dependencies for all apps
  - Detects and installs missing modules
  - Logs all output to a single log file
  - Opens the Integration Hub in your browser
  - Provides an option to stop all apps at once

### Alternative Launchers
- **`start-apps.bat`** - Simple script to start all apps without additional features.
- **Individual launchers** - Use these to start specific apps for development or testing:
  - `launch-BMS.bat` - Launch Business Management System only
  - `launch-CRM.bat` - Launch Customer Relationship Management only
  - `launch-MRP.bat` - Launch Materials Requirements Planning only
  - `launch-APS.bat` - Launch Advanced Planning and Scheduling only
  - `launch-APM.bat` - Launch Asset Performance Management only
  - `launch-PMS.bat` - Launch Project Management System only
  - `launch-SCM.bat` - Launch Supply Chain Management only
  - `launch-TM.bat` - Launch Task Management only
  - `launch-WMS.bat` - Launch Warehouse Management System only

## Starting vs. Stopping

- **To start all apps**: Use `single-window-launcher.bat`
- **To stop all apps**: Press any key in the launcher window or use `stop-apps.bat`
- **To restart**: First stop all apps, then start them again using the launcher

## Troubleshooting

If you encounter issues starting applications:

1. Check the log files in the `logs` directory
2. Make sure no other applications are using the same ports (8000, 3001-3009)
3. Try running individual app launchers to identify the problematic app

## Notes

- Running multiple launchers simultaneously may cause port conflicts
- All launcher scripts must be run from Command Prompt (cmd.exe), not PowerShell