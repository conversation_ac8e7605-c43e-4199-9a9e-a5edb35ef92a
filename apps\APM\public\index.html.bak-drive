<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Performance Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --app-primary-color: #3498db; /* Blue for APM */
            --app-primary-dark: #2980b9;
            --app-primary-light: rgba(52, 152, 219, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            padding-top: 56px; /* Height of navbar */
        }

        /* Sidebar styles */
        .sidebar {
            background-color: var(--app-primary-color);
            color: white;
            height: calc(100vh - 56px);
            position: fixed;
            top: 56px; /* Height of navbar */
            padding-top: 20px;
            width: 250px;
            z-index: 100;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto; /* Add scrollbar when content overflows */
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--app-primary-dark);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar .nav-link {
            color: #f8f9fa;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-heading {
            padding: 0.75rem 1.25rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.1rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Main content area */
        .main-content {
            margin-left: 250px;
            padding: 20px;
            transition: margin-left 0.3s;
        }

        /* Dashboard cards */
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        .card-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        /* Navbar */
        .navbar {
            background-color: var(--app-primary-color) !important;
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
        }

        /* Toggle sidebar button */
        #sidebarToggle {
            cursor: pointer;
            background: transparent;
            border: none;
            color: white;
        }

        /* For mobile view */
        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: -250px;
                height: calc(100vh - 56px);
                transition: left 0.3s;
                z-index: 1030;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0;
                padding: 15px;
            }

            .navbar {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-dark fixed-top" style="background-color: #3498db;">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <button id="sidebarToggle" class="d-md-none me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-list fs-4"></i>
                </button>
                <a class="navbar-brand" href="/">APM System</a>
            </div>
            <div class="d-flex">
                <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3" style="background: transparent; border: 1px solid white; color: white; padding: 5px 10px;">
                    <i class="bi bi-box-arrow-left me-1"></i> Back to Hub
                </a>
                <button class="btn position-relative me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-bell fs-5"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        4
                    </span>
                </button>
                <button class="btn" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-person-circle fs-5"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="pt-2">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="/">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/assets">
                        <i class="bi bi-gear"></i>
                        Assets
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/maintenance">
                        <i class="bi bi-tools"></i>
                        Maintenance
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="bi bi-file-earmark-text"></i>
                        Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/analytics">
                        <i class="bi bi-graph-up"></i>
                        Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/settings">
                        <i class="bi bi-gear-fill"></i>
                        Settings
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                <span>Integrations</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal">
                        <i class="bi bi-calendar3"></i>
                        Google Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal">
                        <i class="bi bi-folder"></i>
                        Google Drive
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal">
                        <i class="bi bi-file-earmark-text"></i>
                        Google Docs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                        <i class="bi bi-file-earmark-spreadsheet"></i>
                        Google Sheets
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-maps" data-bs-toggle="modal" data-bs-target="#mapsModal">
                        <i class="bi bi-geo-alt"></i>
                        Google Maps
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#gmail" data-bs-toggle="modal" data-bs-target="#gmailModal">
                        <i class="bi bi-envelope"></i>
                        Gmail
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#attachments" data-bs-toggle="modal" data-bs-target="#attachmentsModal">
                        <i class="bi bi-paperclip"></i>
                        Attachments
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();">
                        <i class="bi bi-box-arrow-left"></i>
                        Back to Hub
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">Asset Performance Dashboard</h1>
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-primary">Share</button>
                    <button type="button" class="btn btn-sm btn-outline-primary">Export</button>
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle">
                    <i class="bi bi-calendar"></i>
                    This month
                </button>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="card dashboard-card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-gear card-icon"></i>
                        <h5 class="card-title">Total Assets</h5>
                        <h2 class="card-text">42</h2>
                        <p class="card-text">Monitored equipment</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-check-circle card-icon"></i>
                        <h5 class="card-title">Operational</h5>
                        <h2 class="card-text">38</h2>
                        <p class="card-text">90.5% availability</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card bg-warning text-dark">
                    <div class="card-body text-center">
                        <i class="bi bi-tools card-icon"></i>
                        <h5 class="card-title">Maintenance</h5>
                        <h2 class="card-text">3</h2>
                        <p class="card-text">Scheduled today</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card bg-danger text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-exclamation-triangle card-icon"></i>
                        <h5 class="card-title">Alerts</h5>
                        <h2 class="card-text">4</h2>
                        <p class="card-text">Require attention</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Asset Status and Alerts -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5>Asset Status</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Asset</th>
                                    <th>Status</th>
                                    <th>Last Maintenance</th>
                                    <th>Next Maintenance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Production Machine A</td>
                                    <td><span class="badge bg-success">Operational</span></td>
                                    <td>2025-04-15</td>
                                    <td>2025-05-15</td>
                                </tr>
                                <tr>
                                    <td>Forklift B</td>
                                    <td><span class="badge bg-warning">Maintenance</span></td>
                                    <td>2025-03-10</td>
                                    <td>2025-04-30</td>
                                </tr>
                                <tr>
                                    <td>Conveyor System C</td>
                                    <td><span class="badge bg-success">Operational</span></td>
                                    <td>2025-04-01</td>
                                    <td>2025-05-01</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5>Recent Alerts</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <strong>Critical:</strong> Motor failure detected on Packaging Machine D
                            <small class="d-block">2025-04-28 09:15:22</small>
                        </div>
                        <div class="alert alert-warning">
                            <strong>Warning:</strong> Unusual vibration detected on Conveyor System C
                            <small class="d-block">2025-04-28 10:30:45</small>
                        </div>
                        <div class="alert alert-warning">
                            <strong>Warning:</strong> Temperature above threshold on Production Machine A
                            <small class="d-block">2025-04-27 15:45:12</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Google Integrations -->
        <div class="row mt-4">
            <div class="col-12">
                <h4 class="mb-3">Google Integrations</h4>
            </div>

            <!-- Google Calendar -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-calendar-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                        <div class="component-actions">
                            <button id="refresh-calendar" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="create-new-event" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#calendarModal"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" onclick="openGoogleItem('calendar', 'event', 'maintenance-schedule-review')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="fw-bold">Maintenance Schedule Review</div>
                                    <div class="small text-muted">
                                        <i class="bi bi-clock me-1"></i>Today, 10:00 AM - 11:30 AM
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-warning rounded-pill me-2">Today</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'maintenance-schedule-review'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                            <a href="#" onclick="openGoogleItem('calendar', 'event', 'equipment-inspection')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="fw-bold">Equipment Inspection</div>
                                    <div class="small text-muted">
                                        <i class="bi bi-clock me-1"></i>Tomorrow, 2:00 PM - 4:00 PM
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-info rounded-pill me-2">Tomorrow</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'equipment-inspection'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                            <a href="#" onclick="openGoogleItem('calendar', 'event', 'quarterly-performance-review')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="fw-bold">Quarterly Performance Review</div>
                                    <div class="small text-muted">
                                        <i class="bi bi-clock me-1"></i>May 15, 9:00 AM - 10:30 AM
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-secondary rounded-pill me-2">Next Week</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'quarterly-performance-review'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#calendarModal">
                                <i class="bi bi-calendar3 me-2"></i>View Full Calendar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Gmail -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-gmail-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-envelope"></i> Gmail</h5>
                        <div class="component-actions">
                            <button id="refresh-gmail" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="compose-email" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#gmailModal"><i class="bi bi-pencil"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">MT</div>
                                        <div>
                                            <div class="fw-bold">Maintenance Team</div>
                                            <div class="small text-truncate" style="max-width: 200px;">Asset maintenance schedule updated</div>
                                        </div>
                                    </div>
                                </div>
                                <span class="badge bg-primary rounded-pill">15m</span>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">JD</div>
                                        <div>
                                            <div class="fw-bold">John Davis</div>
                                            <div class="small text-truncate" style="max-width: 200px;">Equipment inspection report</div>
                                        </div>
                                    </div>
                                </div>
                                <span class="badge bg-primary rounded-pill">2h</span>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">SM</div>
                                        <div>
                                            <div class="fw-bold">Sarah Miller</div>
                                            <div class="small text-truncate" style="max-width: 200px;">Maintenance parts order confirmation</div>
                                        </div>
                                    </div>
                                </div>
                                <span class="badge bg-primary rounded-pill">4h</span>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#gmailModal">
                                <i class="bi bi-envelope me-2"></i>Open Gmail
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Drive -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-drive-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-folder"></i> Google Drive</h5>
                        <div class="component-actions">
                            <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="upload-file" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-upload"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" onclick="openGoogleItem('drive', 'folder', 'equipment-manuals')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                    <span>Equipment Manuals</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-secondary rounded-pill me-2">24 files</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'equipment-manuals'); event.stopPropagation();">
                                      <i class="bi bi-folder-symlink"></i> Open
                                    </button>
                                </div>
                            </a>
                            <a href="#" onclick="openGoogleItem('drive', 'folder', 'maintenance-records')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                    <span>Maintenance Records</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-secondary rounded-pill me-2">18 files</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'maintenance-records'); event.stopPropagation();">
                                      <i class="bi bi-folder-symlink"></i> Open
                                    </button>
                                </div>
                            </a>
                            <a href="#" onclick="openGoogleItem('drive', 'pdf', 'asset-performance-report-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                    <span>Asset_Performance_Report_Q2_2025.pdf</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary rounded-pill me-2">Yesterday</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'asset-performance-report-q2-2025'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
                                <i class="bi bi-folder me-2"></i>Open Drive
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Docs -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-docs-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                        <div class="component-actions">
                            <button id="refresh-docs" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="create-new-doc" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#docsModal"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" onclick="openGoogleItem('docs', 'document', 'maintenance-procedures')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                    <span>Maintenance Procedures</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary rounded-pill me-2">Today</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'maintenance-procedures'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                            <a href="#" onclick="openGoogleItem('docs', 'document', 'asset-inspection-checklist')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                    <span>Asset Inspection Checklist</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary rounded-pill me-2">Yesterday</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'asset-inspection-checklist'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                            <a href="#" onclick="openGoogleItem('docs', 'document', 'equipment-troubleshooting-guide')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                    <span>Equipment Troubleshooting Guide</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary rounded-pill me-2">3 days ago</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'equipment-troubleshooting-guide'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" onclick="openGoogleItem('docs')">
                                <i class="bi bi-file-earmark-text me-2"></i>View All Documents
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Sheets -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-sheets-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                        <div class="component-actions">
                            <button id="refresh-sheets" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="create-new-sheet" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#sheetsModal"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'asset-inventory-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                    <span>Asset_Inventory_2025.xlsx</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary rounded-pill me-2">2 days ago</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'asset-inventory-2025'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                            <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'maintenance-schedule-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                    <span>Maintenance_Schedule_Q2_2025.xlsx</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary rounded-pill me-2">1 week ago</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'maintenance-schedule-q2-2025'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                            <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'equipment-performance-metrics')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                    <span>Equipment_Performance_Metrics.xlsx</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary rounded-pill me-2">2 weeks ago</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'equipment-performance-metrics'); event.stopPropagation();">
                                      <i class="bi bi-eye"></i> View
                                    </button>
                                </div>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                                <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Insights Section -->
        <div class="row mt-4 mb-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>AI-Powered Asset Insights</h5>
                        <div>
                            <button id="refresh-insights-btn" class="btn btn-sm btn-primary me-2">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                            <button id="expand-all-insights" class="btn btn-sm btn-outline-primary me-2">
                                <i class="bi bi-arrows-expand"></i> Expand All
                            </button>
                            <button id="collapse-all-insights" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-arrows-collapse"></i> Collapse All
                            </button>
                            <button id="export-insights" class="btn btn-sm btn-outline-success ms-2">
                                <i class="bi bi-download"></i> Export
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="ai-insights-container">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gmail Modal -->
    <div class="modal fade" id="gmailModal" tabindex="-1" aria-labelledby="gmailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="gmailModalLabel"><i class="bi bi-envelope"></i> Gmail</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="gmailTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox-content" type="button" role="tab" aria-controls="inbox-content" aria-selected="true">Inbox</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="read-email-tab" data-bs-toggle="tab" data-bs-target="#read-email-content" type="button" role="tab" aria-controls="read-email-content" aria-selected="false">Read Email</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="reply-email-tab" data-bs-toggle="tab" data-bs-target="#reply-email-content" type="button" role="tab" aria-controls="reply-email-content" aria-selected="false">Reply</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose-content" type="button" role="tab" aria-controls="compose-content" aria-selected="false">Compose</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent-content" type="button" role="tab" aria-controls="sent-content" aria-selected="false">Sent</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="gmailTabContent">
                        <!-- Inbox Tab -->
                        <div class="tab-pane fade show active" id="inbox-content" role="tabpanel" aria-labelledby="inbox-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="email-search" class="form-control" placeholder="Search emails...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Emails</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="unread">Unread</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="important">Important</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <a href="#" onclick="openEmail('asset-maintenance-schedule-updated')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Maintenance Team</h6>
                                        <small class="text-muted">15m ago</small>
                                    </div>
                                    <p class="mb-1">Asset maintenance schedule updated</p>
                                    <small class="text-muted">The maintenance schedule for Q2 2025 has been updated with new equipment.</small>
                                </a>
                                <a href="#" onclick="openEmail('equipment-inspection-report')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">John Davis</h6>
                                        <small class="text-muted">2h ago</small>
                                    </div>
                                    <p class="mb-1">Equipment inspection report</p>
                                    <small class="text-muted">Please find attached the inspection report for the conveyor system.</small>
                                </a>
                                <a href="#" onclick="openEmail('maintenance-parts-order-confirmation')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Sarah Miller</h6>
                                        <small class="text-muted">4h ago</small>
                                    </div>
                                    <p class="mb-1">Maintenance parts order confirmation</p>
                                    <small class="text-muted">This is to confirm that the parts for the scheduled maintenance have been ordered.</small>
                                </a>
                            </div>
                        </div>

                        <!-- Read Email Tab -->
                        <div class="tab-pane fade" id="read-email-content" role="tabpanel" aria-labelledby="read-email-tab">
                            <div class="email-header mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 id="email-subject" class="mb-0">Email Subject</h5>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="replyToEmail()">
                                            <i class="bi bi-reply"></i> Reply
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary me-1">
                                            <i class="bi bi-reply-all"></i> Reply All
                                        </button>
                                        <button class="btn btn-sm btn-outline-success">
                                            <i class="bi bi-forward"></i> Forward
                                        </button>
                                    </div>
                                </div>
                                <div class="email-meta mt-2">
                                    <div class="d-flex align-items-center">
                                        <div id="email-sender-avatar" class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">MT</div>
                                        <div>
                                            <div id="email-sender" class="fw-bold">Sender Name</div>
                                            <div id="email-recipients" class="small text-muted">To: me</div>
                                        </div>
                                    </div>
                                    <div id="email-time" class="text-muted mt-1">Date and Time</div>
                                </div>
                            </div>
                            <div class="email-body p-3 border rounded" style="min-height: 200px;">
                                <div id="email-content">
                                    <p>Email content will appear here.</p>
                                </div>
                                <div id="email-attachments" class="mt-3 border-top pt-3">
                                    <h6><i class="bi bi-paperclip"></i> Attachments</h6>
                                    <div class="list-group">
                                        <!-- Attachments will be added here dynamically -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reply Email Tab -->
                        <div class="tab-pane fade" id="reply-email-content" role="tabpanel" aria-labelledby="reply-email-tab">
                            <div class="email-header mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 id="reply-email-subject" class="mb-0">Re: Email Subject</h5>
                                    <div>
                                        <button class="btn btn-sm btn-primary" onclick="sendReply()">
                                            <i class="bi bi-send"></i> Send
                                        </button>
                                    </div>
                                </div>
                                <div class="email-meta mt-2">
                                    <div class="d-flex align-items-center">
                                        <div class="me-2">
                                            <div id="reply-email-recipients" class="small">To: <span class="fw-bold">Recipient Name</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="reply-body p-3 border rounded" style="min-height: 200px;">
                                <textarea id="reply-email-content-textarea" class="form-control border-0" rows="8" placeholder="Type your reply here..."></textarea>
                                <div class="mt-3 border-top pt-3">
                                    <div class="original-email p-2 bg-light rounded">
                                        <div class="text-muted mb-2">On <span id="original-email-date">date</span>, <span id="original-email-sender">Sender</span> wrote:</div>
                                        <div id="original-email-content" class="ps-3 border-start">
                                            <p>Original email content will appear here.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Compose Tab -->
                        <div class="tab-pane fade" id="compose-content" role="tabpanel" aria-labelledby="compose-tab">
                            <form id="compose-email-form">
                                <div class="mb-3">
                                    <label for="email-to" class="form-label">To</label>
                                    <input type="email" class="form-control" id="email-to" placeholder="Enter recipient email">
                                </div>
                                <div class="mb-3">
                                    <label for="email-cc" class="form-label">CC</label>
                                    <input type="email" class="form-control" id="email-cc" placeholder="Enter CC email">
                                </div>
                                <div class="mb-3">
                                    <label for="email-subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="email-subject" placeholder="Enter subject">
                                </div>
                                <div class="mb-3">
                                    <label for="email-body" class="form-label">Message</label>
                                    <textarea class="form-control" id="email-body" rows="6" placeholder="Enter your message"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="email-attachment" class="form-label">Attachments</label>
                                    <input class="form-control" type="file" id="email-attachment" multiple>
                                </div>
                                <button type="button" class="btn btn-primary" id="send-email-btn">Send</button>
                                <button type="button" class="btn btn-outline-secondary" id="save-draft-btn">Save Draft</button>
                            </form>
                        </div>
                        <!-- Sent Tab -->
                        <div class="tab-pane fade" id="sent-content" role="tabpanel" aria-labelledby="sent-tab">
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">To: Maintenance Team</h6>
                                        <small class="text-muted">Yesterday</small>
                                    </div>
                                    <p class="mb-1">Updated maintenance schedule</p>
                                    <small class="text-muted">Please review the attached updated maintenance schedule for the next quarter.</small>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">To: Procurement Department</h6>
                                        <small class="text-muted">2 days ago</small>
                                    </div>
                                    <p class="mb-1">Spare parts requisition</p>
                                    <small class="text-muted">Requesting spare parts for the upcoming maintenance of Production Machine A.</small>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">To: Management Team</h6>
                                        <small class="text-muted">1 week ago</small>
                                    </div>
                                    <p class="mb-1">Monthly asset performance report</p>
                                    <small class="text-muted">Please find attached the monthly asset performance report for April 2025.</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://mail.google.com" target="_blank" class="btn btn-primary">Open in Gmail</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Drive Modal -->
    <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="driveModalLabel"><i class="bi bi-folder"></i> Google Drive</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="driveTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content" type="button" role="tab" aria-controls="files-content" aria-selected="true">Files</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button" role="tab" aria-controls="upload-content" aria-selected="false">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-tab" data-bs-toggle="tab" data-bs-target="#shared-content" type="button" role="tab" aria-controls="shared-content" aria-selected="false">Shared with me</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="driveTabContent">
                        <!-- Files Tab -->
                        <div class="tab-pane fade show active" id="files-content" role="tabpanel" aria-labelledby="files-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="file-search" class="form-control" placeholder="Search files...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-sort-down"></i> Sort by
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="date">Date</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="size">Size</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="type">Type</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('drive', 'folder', 'equipment-manuals')" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Equipment Manuals</h6>
                                            <small>24 files - Last updated: Yesterday</small>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'equipment-manuals'); event.stopPropagation();">
                                          <i class="bi bi-folder-symlink"></i> Open
                                        </button>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('drive', 'folder', 'maintenance-records')" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Maintenance Records</h6>
                                            <small>18 files - Last updated: 3 days ago</small>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'maintenance-records'); event.stopPropagation();">
                                          <i class="bi bi-folder-symlink"></i> Open
                                        </button>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('drive', 'pdf', 'asset-performance-report-q2-2025')" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Asset_Performance_Report_Q2_2025.pdf</h6>
                                            <small>2.4 MB - Last updated: Yesterday</small>
                                        </div>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'asset-performance-report-q2-2025'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('drive', 'spreadsheet', 'asset-inventory-2025')" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Asset_Inventory_2025.xlsx</h6>
                                            <small>1.8 MB - Last updated: 2 days ago</small>
                                        </div>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'spreadsheet', 'asset-inventory-2025'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('drive', 'document', 'maintenance-procedures')" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Maintenance_Procedures.docx</h6>
                                            <small>1.2 MB - Last updated: 1 week ago</small>
                                        </div>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'maintenance-procedures'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <!-- Upload Tab -->
                        <div class="tab-pane fade" id="upload-content" role="tabpanel" aria-labelledby="upload-tab">
                            <div class="mb-3">
                                <label for="upload-file" class="form-label">Select file to upload</label>
                                <input class="form-control" type="file" id="upload-file">
                            </div>
                            <div class="mb-3">
                                <label for="upload-folder" class="form-label">Destination folder</label>
                                <select class="form-select" id="upload-folder">
                                    <option selected>My Drive</option>
                                    <option>Equipment Manuals</option>
                                    <option>Maintenance Records</option>
                                    <option>Team Shared Folder</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="convert-to-google-format">
                                    <label class="form-check-label" for="convert-to-google-format">
                                        Convert to Google format
                                    </label>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary" id="upload-file-btn">Upload</button>
                        </div>
                        <!-- Shared with me Tab -->
                        <div class="tab-pane fade" id="shared-content" role="tabpanel" aria-labelledby="shared-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="shared-search" class="form-control" placeholder="Search shared files...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="other">Other</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                        <div>
                                            <h6 class="mb-1">Equipment_Specifications.pdf</h6>
                                            <small>Shared by: John Davis - 3 days ago</small>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                        <div>
                                            <h6 class="mb-1">Maintenance_Budget_2025.xlsx</h6>
                                            <small>Shared by: Sarah Wilson - 1 week ago</small>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                        <div>
                                            <h6 class="mb-1">Asset_Management_Policy.docx</h6>
                                            <small>Shared by: David Chen - 2 weeks ago</small>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://drive.google.com" target="_blank" class="btn btn-primary">Open in Google Drive</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Sheets Modal -->
    <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="sheetsModalLabel"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="sheetsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="sheets-list-tab" data-bs-toggle="tab" data-bs-target="#sheets-list-content" type="button" role="tab" aria-controls="sheets-list-content" aria-selected="true">My Sheets</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-sheet-tab" data-bs-toggle="tab" data-bs-target="#create-sheet-content" type="button" role="tab" aria-controls="create-sheet-content" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-sheet-tab" data-bs-toggle="tab" data-bs-target="#import-sheet-content" type="button" role="tab" aria-controls="import-sheet-content" aria-selected="false">Import</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="sheetsTabContent">
                        <!-- Sheets List Tab -->
                        <div class="tab-pane fade show active" id="sheets-list-content" role="tabpanel" aria-labelledby="sheets-list-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="sheets-search" class="form-control" placeholder="Search sheets...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-sort-down"></i> Sort by
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="date">Date</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'asset-inventory-2025')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Asset_Inventory_2025.xlsx</h6>
                                        <small class="text-muted">2 days ago</small>
                                    </div>
                                    <p class="mb-1">Complete inventory of all assets with details</p>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">Last edited by: You</small>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'asset-inventory-2025'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Asset_Inventory_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Asset_Inventory_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'maintenance-schedule-q2-2025')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Maintenance_Schedule_Q2_2025.xlsx</h6>
                                        <small class="text-muted">1 week ago</small>
                                    </div>
                                    <p class="mb-1">Scheduled maintenance for all equipment</p>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">Last edited by: John Davis</small>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'maintenance-schedule-q2-2025'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Maintenance_Schedule_Q2_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Maintenance_Schedule_Q2_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'equipment-performance-metrics')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Equipment_Performance_Metrics.xlsx</h6>
                                        <small class="text-muted">2 weeks ago</small>
                                    </div>
                                    <p class="mb-1">Performance metrics for all equipment</p>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">Last edited by: Sarah Miller</small>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'equipment-performance-metrics'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Equipment_Performance_Metrics.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Equipment_Performance_Metrics.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <!-- Create Sheet Tab -->
                        <div class="tab-pane fade" id="create-sheet-content" role="tabpanel" aria-labelledby="create-sheet-tab">
                            <form id="create-sheet-form">
                                <div class="mb-3">
                                    <label for="sheet-title" class="form-label">Sheet Title</label>
                                    <input type="text" class="form-control" id="sheet-title" placeholder="Enter sheet title">
                                </div>
                                <div class="mb-3">
                                    <label for="sheet-template" class="form-label">Template</label>
                                    <select class="form-select" id="sheet-template">
                                        <option selected>Blank</option>
                                        <option>Asset Inventory</option>
                                        <option>Maintenance Schedule</option>
                                        <option>Performance Metrics</option>
                                        <option>Budget Tracker</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="sheet-folder" class="form-label">Save to Folder</label>
                                    <select class="form-select" id="sheet-folder">
                                        <option selected>My Drive</option>
                                        <option>Asset Management</option>
                                        <option>Maintenance Records</option>
                                        <option>Team Shared Folder</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sheet-share-team" checked>
                                        <label class="form-check-label" for="sheet-share-team">
                                            Share with team
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" id="create-sheet-btn">Create Sheet</button>
                            </form>
                        </div>
                        <!-- Import Sheet Tab -->
                        <div class="tab-pane fade" id="import-sheet-content" role="tabpanel" aria-labelledby="import-sheet-tab">
                            <form id="import-sheet-form">
                                <div class="mb-3">
                                    <label for="import-file" class="form-label">Select file to import</label>
                                    <input class="form-control" type="file" id="import-file" accept=".xlsx,.xls,.csv">
                                    <div class="form-text">Supported formats: .xlsx, .xls, .csv</div>
                                </div>
                                <div class="mb-3">
                                    <label for="import-title" class="form-label">Sheet Title</label>
                                    <input type="text" class="form-control" id="import-title" placeholder="Enter sheet title">
                                </div>
                                <div class="mb-3">
                                    <label for="import-folder" class="form-label">Save to Folder</label>
                                    <select class="form-select" id="import-folder">
                                        <option selected>My Drive</option>
                                        <option>Asset Management</option>
                                        <option>Maintenance Records</option>
                                        <option>Team Shared Folder</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="import-first-row" checked>
                                        <label class="form-check-label" for="import-first-row">
                                            First row is header
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" id="import-sheet-btn">Import</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://docs.google.com/spreadsheets" target="_blank" class="btn btn-primary">Open in Google Sheets</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Docs Modal -->
    <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="docsModalLabel"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="docsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="docs-list-tab" data-bs-toggle="tab" data-bs-target="#docs-list-content" type="button" role="tab" aria-controls="docs-list-content" aria-selected="true">My Documents</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-doc-tab" data-bs-toggle="tab" data-bs-target="#create-doc-content" type="button" role="tab" aria-controls="create-doc-content" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-doc-tab" data-bs-toggle="tab" data-bs-target="#import-doc-content" type="button" role="tab" aria-controls="import-doc-content" aria-selected="false">Import</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="docsTabContent">
                        <!-- Docs List Tab -->
                        <div class="tab-pane fade show active" id="docs-list-content" role="tabpanel" aria-labelledby="docs-list-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="docs-search" class="form-control" placeholder="Search documents...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-sort-down"></i> Sort by
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="date">Date</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('docs', 'document', 'maintenance-procedures')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Maintenance Procedures</h6>
                                        <small class="text-muted">Today</small>
                                    </div>
                                    <p class="mb-1">Standard operating procedures for equipment maintenance</p>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">Last edited by: You</small>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'maintenance-procedures'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Maintenance_Procedures.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Maintenance_Procedures.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('docs', 'document', 'asset-inspection-checklist')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Asset Inspection Checklist</h6>
                                        <small class="text-muted">Yesterday</small>
                                    </div>
                                    <p class="mb-1">Comprehensive checklist for asset inspections</p>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">Last edited by: John Davis</small>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'asset-inspection-checklist'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Asset_Inspection_Checklist.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Asset_Inspection_Checklist.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('docs', 'document', 'equipment-troubleshooting-guide')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Equipment Troubleshooting Guide</h6>
                                        <small class="text-muted">3 days ago</small>
                                    </div>
                                    <p class="mb-1">Guide for troubleshooting common equipment issues</p>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">Last edited by: Sarah Miller</small>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('docs', 'document', 'equipment-troubleshooting-guide'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Equipment_Troubleshooting_Guide.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Equipment_Troubleshooting_Guide.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile(); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <!-- Create Doc Tab -->
                        <div class="tab-pane fade" id="create-doc-content" role="tabpanel" aria-labelledby="create-doc-tab">
                            <form id="create-doc-form">
                                <div class="mb-3">
                                    <label for="doc-title" class="form-label">Document Title</label>
                                    <input type="text" class="form-control" id="doc-title" placeholder="Enter document title">
                                </div>
                                <div class="mb-3">
                                    <label for="doc-template" class="form-label">Template</label>
                                    <select class="form-select" id="doc-template">
                                        <option selected>Blank</option>
                                        <option>Maintenance Procedure</option>
                                        <option>Inspection Checklist</option>
                                        <option>Troubleshooting Guide</option>
                                        <option>Training Manual</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="doc-folder" class="form-label">Save to Folder</label>
                                    <select class="form-select" id="doc-folder">
                                        <option selected>My Drive</option>
                                        <option>Asset Management</option>
                                        <option>Maintenance Records</option>
                                        <option>Team Shared Folder</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="doc-share-team" checked>
                                        <label class="form-check-label" for="doc-share-team">
                                            Share with team
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" id="create-doc-btn">Create Document</button>
                            </form>
                        </div>
                        <!-- Import Doc Tab -->
                        <div class="tab-pane fade" id="import-doc-content" role="tabpanel" aria-labelledby="import-doc-tab">
                            <form id="import-doc-form">
                                <div class="mb-3">
                                    <label for="import-doc-file" class="form-label">Select file to import</label>
                                    <input class="form-control" type="file" id="import-doc-file" accept=".docx,.doc,.txt,.pdf">
                                    <div class="form-text">Supported formats: .docx, .doc, .txt, .pdf</div>
                                </div>
                                <div class="mb-3">
                                    <label for="import-doc-title" class="form-label">Document Title</label>
                                    <input type="text" class="form-control" id="import-doc-title" placeholder="Enter document title">
                                </div>
                                <div class="mb-3">
                                    <label for="import-doc-folder" class="form-label">Save to Folder</label>
                                    <select class="form-select" id="import-doc-folder">
                                        <option selected>My Drive</option>
                                        <option>Asset Management</option>
                                        <option>Maintenance Records</option>
                                        <option>Team Shared Folder</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="convert-to-google-doc" checked>
                                        <label class="form-check-label" for="convert-to-google-doc">
                                            Convert to Google Docs format
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" id="import-doc-btn">Import</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://docs.google.com/document" target="_blank" class="btn btn-primary">Open in Google Docs</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Calendar Modal -->
    <div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="calendarModalLabel"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="calendarTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="events-tab" data-bs-toggle="tab" data-bs-target="#events-content" type="button" role="tab" aria-controls="events-content" aria-selected="true">Events</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-event-tab" data-bs-toggle="tab" data-bs-target="#create-event-content" type="button" role="tab" aria-controls="create-event-content" aria-selected="false">Create Event</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="calendar-settings-tab" data-bs-toggle="tab" data-bs-target="#calendar-settings-content" type="button" role="tab" aria-controls="calendar-settings-content" aria-selected="false">Settings</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="calendarTabContent">
                        <!-- Events Tab -->
                        <div class="tab-pane fade show active" id="events-content" role="tabpanel" aria-labelledby="events-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="event-search" class="form-control" placeholder="Search events...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Events</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="today">Today</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="week">This Week</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="month">This Month</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'maintenance-schedule-review')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Maintenance Schedule Review</h6>
                                        <small class="text-warning">Today</small>
                                    </div>
                                    <p class="mb-1">10:00 AM - 11:30 AM</p>
                                    <small>Review and update the maintenance schedule for all assets</small>
                                    <div class="d-flex justify-content-end mt-2">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'maintenance-schedule-review'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Maintenance_Schedule_Review.ics'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Maintenance_Schedule_Review.ics'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Maintenance_Schedule_Review.ics'); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'equipment-inspection')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Equipment Inspection</h6>
                                        <small class="text-info">Tomorrow</small>
                                    </div>
                                    <p class="mb-1">2:00 PM - 4:00 PM</p>
                                    <small>Inspection of Production Machine A and Conveyor System C</small>
                                    <div class="d-flex justify-content-end mt-2">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'equipment-inspection'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Equipment_Inspection.ics'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Equipment_Inspection.ics'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Equipment_Inspection.ics'); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'quarterly-performance-review')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Quarterly Performance Review</h6>
                                        <small class="text-secondary">Next Week</small>
                                    </div>
                                    <p class="mb-1">May 15, 9:00 AM - 10:30 AM</p>
                                    <small>Review of asset performance metrics for Q1 2025</small>
                                    <div class="d-flex justify-content-end mt-2">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'quarterly-performance-review'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Quarterly_Performance_Review.ics'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Quarterly_Performance_Review.ics'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Quarterly_Performance_Review.ics'); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'maintenance-team-meeting')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Maintenance Team Meeting</h6>
                                        <small class="text-secondary">Next Week</small>
                                    </div>
                                    <p class="mb-1">May 17, 1:00 PM - 2:30 PM</p>
                                    <small>Weekly meeting with the maintenance team</small>
                                    <div class="d-flex justify-content-end mt-2">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'maintenance-team-meeting'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Maintenance_Team_Meeting.ics'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Maintenance_Team_Meeting.ics'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Maintenance_Team_Meeting.ics'); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'equipment-training-session')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Equipment Training Session</h6>
                                        <small class="text-secondary">Next Month</small>
                                    </div>
                                    <p class="mb-1">June 5, 3:00 PM - 5:00 PM</p>
                                    <small>Training session for new equipment operators</small>
                                    <div class="d-flex justify-content-end mt-2">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('calendar', 'event', 'equipment-training-session'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile('Equipment_Training_Session.ics'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Equipment_Training_Session.ics'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Equipment_Training_Session.ics'); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <!-- Create Event Tab -->
                        <div class="tab-pane fade" id="create-event-content" role="tabpanel" aria-labelledby="create-event-tab">
                            <form id="create-event-form">
                                <div class="mb-3">
                                    <label for="event-title" class="form-label">Event Title</label>
                                    <input type="text" class="form-control" id="event-title" placeholder="Enter event title">
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="event-date" class="form-label">Date</label>
                                        <input type="date" class="form-control" id="event-date">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="event-start-time" class="form-label">Start Time</label>
                                        <input type="time" class="form-control" id="event-start-time">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="event-end-time" class="form-label">End Time</label>
                                        <input type="time" class="form-control" id="event-end-time">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="event-location" class="form-label">Location</label>
                                    <input type="text" class="form-control" id="event-location" placeholder="Enter location">
                                </div>
                                <div class="mb-3">
                                    <label for="event-description" class="form-label">Description</label>
                                    <textarea class="form-control" id="event-description" rows="3" placeholder="Enter event description"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="event-attendees" class="form-label">Attendees</label>
                                    <input type="text" class="form-control" id="event-attendees" placeholder="Enter email addresses (comma separated)">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="event-reminder">
                                        <label class="form-check-label" for="event-reminder">
                                            Set reminder
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" id="create-event-btn">Create Event</button>
                            </form>
                        </div>
                        <!-- Settings Tab -->
                        <div class="tab-pane fade" id="calendar-settings-content" role="tabpanel" aria-labelledby="calendar-settings-tab">
                            <div class="mb-3">
                                <label for="default-calendar" class="form-label">Default Calendar</label>
                                <select class="form-select" id="default-calendar">
                                    <option selected>Asset Management Calendar</option>
                                    <option>Maintenance Calendar</option>
                                    <option>Team Calendar</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="default-view" class="form-label">Default View</label>
                                <select class="form-select" id="default-view">
                                    <option selected>Week</option>
                                    <option>Month</option>
                                    <option>Day</option>
                                    <option>Agenda</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="default-reminder" class="form-label">Default Reminder Time</label>
                                <select class="form-select" id="default-reminder">
                                    <option selected>10 minutes before</option>
                                    <option>30 minutes before</option>
                                    <option>1 hour before</option>
                                    <option>1 day before</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email-notifications" checked>
                                    <label class="form-check-label" for="email-notifications">
                                        Email notifications
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="desktop-notifications" checked>
                                    <label class="form-check-label" for="desktop-notifications">
                                        Desktop notifications
                                    </label>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary" id="save-calendar-settings-btn">Save Settings</button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://calendar.google.com" target="_blank" class="btn btn-primary">Open in Google Calendar</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Attachments Modal -->
    <div class="modal fade" id="attachmentsModal" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="attachmentsModalLabel"><i class="bi bi-paperclip"></i> Attachments</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="attachmentsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="all-attachments-tab" data-bs-toggle="tab" data-bs-target="#all-attachments-content" type="button" role="tab" aria-controls="all-attachments-content" aria-selected="true">All Attachments</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-attachment-tab" data-bs-toggle="tab" data-bs-target="#upload-attachment-content" type="button" role="tab" aria-controls="upload-attachment-content" aria-selected="false">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="recent-attachments-tab" data-bs-toggle="tab" data-bs-target="#recent-attachments-content" type="button" role="tab" aria-controls="recent-attachments-content" aria-selected="false">Recent</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="attachmentsTabContent">
                        <!-- All Attachments Tab -->
                        <div class="tab-pane fade show active" id="all-attachments-content" role="tabpanel" aria-labelledby="all-attachments-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="attachment-search" class="form-control" placeholder="Search attachments...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="spreadsheets">Spreadsheets</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="pdfs">PDFs</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>Size</th>
                                            <th>Uploaded</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2 fs-5"></i>
                                                    <span>Asset_Performance_Report_Q2_2025.pdf</span>
                                                </div>
                                            </td>
                                            <td>PDF</td>
                                            <td>2.4 MB</td>
                                            <td>Yesterday</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Asset_Performance_Report_Q2_2025.pdf')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Asset_Performance_Report_Q2_2025.pdf')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="shareFile('Asset_Performance_Report_Q2_2025.pdf')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Asset_Performance_Report_Q2_2025.pdf')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2 fs-5"></i>
                                                    <span>Asset_Inventory_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>Spreadsheet</td>
                                            <td>1.8 MB</td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Asset_Inventory_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Asset_Inventory_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="shareFile('Asset_Inventory_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Asset_Inventory_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Maintenance_Procedures.docx</span>
                                                </div>
                                            </td>
                                            <td>Document</td>
                                            <td>1.2 MB</td>
                                            <td>1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Maintenance_Procedures.docx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Maintenance_Procedures.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="shareFile('Maintenance_Procedures.docx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Maintenance_Procedures.docx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-image text-info me-2 fs-5"></i>
                                                    <span>Equipment_Diagram.png</span>
                                                </div>
                                            </td>
                                            <td>Image</td>
                                            <td>3.5 MB</td>
                                            <td>2 weeks ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('image', 'Equipment_Diagram.png')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Equipment_Diagram.png')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="shareFile('Equipment_Diagram.png')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Equipment_Diagram.png')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- Upload Attachment Tab -->
                        <div class="tab-pane fade" id="upload-attachment-content" role="tabpanel" aria-labelledby="upload-attachment-tab">
                            <form id="upload-attachment-form">
                                <div class="mb-3">
                                    <label for="attachment-file" class="form-label">Select file to upload</label>
                                    <input class="form-control" type="file" id="attachment-file">
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-name" class="form-label">File Name (optional)</label>
                                    <input type="text" class="form-control" id="attachment-name" placeholder="Enter a name for the file">
                                    <div class="form-text">If left blank, the original file name will be used.</div>
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-asset" class="form-label">Associated Asset</label>
                                    <select class="form-select" id="attachment-asset">
                                        <option selected>Production Machine A</option>
                                        <option>Conveyor System C</option>
                                        <option>Forklift B</option>
                                        <option>Packaging Machine D</option>
                                        <option>None</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-description" class="form-label">Description (optional)</label>
                                    <textarea class="form-control" id="attachment-description" rows="3" placeholder="Enter a description for the file"></textarea>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="attachment-notify">
                                        <label class="form-check-label" for="attachment-notify">
                                            Notify team members
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" id="upload-attachment-btn">Upload</button>
                            </form>
                        </div>
                        <!-- Recent Attachments Tab -->
                        <div class="tab-pane fade" id="recent-attachments-content" role="tabpanel" aria-labelledby="recent-attachments-tab">
                            <div class="list-group">
                                <a href="#" onclick="viewAttachment('pdf', 'Asset_Performance_Report_Q2_2025.pdf')" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Asset_Performance_Report_Q2_2025.pdf</h6>
                                            <small>2.4 MB - Uploaded: Yesterday</small>
                                        </div>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Asset_Performance_Report_Q2_2025.pdf'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Asset_Performance_Report_Q2_2025.pdf'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Asset_Performance_Report_Q2_2025.pdf'); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="viewAttachment('spreadsheet', 'Asset_Inventory_2025.xlsx')" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Asset_Inventory_2025.xlsx</h6>
                                            <small>1.8 MB - Uploaded: 2 days ago</small>
                                        </div>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Asset_Inventory_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Asset_Inventory_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Asset_Inventory_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Asset_Inventory_2025.xlsx'); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="viewAttachment('document', 'Maintenance_Procedures.docx')" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Maintenance_Procedures.docx</h6>
                                            <small>1.2 MB - Uploaded: 1 week ago</small>
                                        </div>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Maintenance_Procedures.docx'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Maintenance_Procedures.docx'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Maintenance_Procedures.docx'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Maintenance_Procedures.docx'); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" onclick="viewAttachment('image', 'Equipment_Diagram.png')" class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-image text-info me-3 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Equipment_Diagram.png</h6>
                                            <small>3.5 MB - Uploaded: 2 weeks ago</small>
                                        </div>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('image', 'Equipment_Diagram.png'); event.stopPropagation();">
                                              <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadAttachment('Equipment_Diagram.png'); event.stopPropagation();">
                                              <i class="bi bi-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('Equipment_Diagram.png'); event.stopPropagation();">
                                              <i class="bi bi-share"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Equipment_Diagram.png'); event.stopPropagation();">
                                              <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>

    <script>
        // Function to open specific Google items
        function openGoogleItem(app, type, itemId) {
          // For general app buttons (without specific item), show the modal
          if (app === 'drive' && !type && !itemId) {
            const driveModal = new bootstrap.Modal(document.getElementById('driveModal'));
            driveModal.show();
            return;
          } else if (app === 'docs' && !type && !itemId) {
            const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
            docsModal.show();
            return;
          } else if (app === 'sheets' && !type && !itemId) {
            const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
            sheetsModal.show();
            return;
          } else if (app === 'attachments' && !type && !itemId) {
            const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
            attachmentsModal.show();
            return;
          }

          // For specific items, directly open the file in a viewer
          try {
            // Create a simulated file viewer
            const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

            // Set the file information
            const fileTitle = document.getElementById('fileViewerTitle');
            const fileContent = document.getElementById('fileViewerContent');

            // Format the item ID to make it more readable
            const readableId = itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            // Set the title based on the app and type
            fileTitle.innerHTML = `<i class="bi ${getFileIcon(app, type)}"></i> ${readableId}`;

            // Set the content based on the file type
            fileContent.innerHTML = getFileContent(app, type, itemId);

            // Show the modal
            const modal = new bootstrap.Modal(fileViewerModal);
            modal.show();
          } catch (error) {
            console.error('Error opening file:', error);
            alert('Could not open the file. Please try again later.');
          }
        }

        // Helper function to create a file viewer modal if it doesn't exist
        function createFileViewerModal() {
          const modal = document.createElement('div');
          modal.className = 'modal fade';
          modal.id = 'fileViewerModal';
          modal.tabIndex = '-1';
          modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
          modal.setAttribute('aria-hidden', 'true');

          modal.innerHTML = `
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                  <h5 class="modal-title" id="fileViewerTitle"></h5>
                  <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">Download</button>
                  <button type="button" class="btn btn-success" onclick="shareCurrentFile()">Share</button>
                  <button type="button" class="btn btn-danger" onclick="deleteCurrentFile()">Delete</button>
                </div>
              </div>
            </div>
          `;

          document.body.appendChild(modal);
          return modal;
        }

        // Helper function to get the appropriate icon for the file type
        function getFileIcon(app, type) {
          if (app === 'drive') {
            if (type === 'folder') return 'bi-folder-fill text-primary';
            if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
            if (type === 'image') return 'bi-file-earmark-image text-info';
            if (type === 'document') return 'bi-file-earmark-text text-primary';
            return 'bi-file-earmark text-secondary';
          } else if (app === 'docs') {
            return 'bi-file-earmark-text text-primary';
          } else if (app === 'sheets') {
            return 'bi-file-earmark-spreadsheet text-success';
          } else if (app === 'calendar') {
            return 'bi-calendar-event text-primary';
          }
          return 'bi-file-earmark text-secondary';
        }

        // Helper function to generate content for the file viewer
        function getFileContent(app, type, itemId) {
          // Generate simulated content based on file type
          if (app === 'drive') {
            if (type === 'folder') {
              return `<div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> This is a folder view. In a real application, this would show the contents of the folder.
                      </div>
                      <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-text me-2"></i> Document 1.docx
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-spreadsheet me-2"></i> Spreadsheet 1.xlsx
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-pdf me-2"></i> Report.pdf
                        </a>
                      </div>`;
            } else if (type === 'pdf') {
              return `<div class="text-center">
                        <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                        <h4 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <div class="alert alert-info mt-3">
                          <i class="bi bi-info-circle"></i> This is a PDF viewer. In a real application, the PDF would be displayed here.
                        </div>
                        <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                          <h5>Document Preview</h5>
                          <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                          <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                        </div>
                      </div>`;
            } else if (type === 'document') {
              return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <hr>
                        <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                        <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                      </div>`;
            }
          } else if (app === 'docs') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                      <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                    </div>`;
          } else if (app === 'sheets') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                          <thead>
                            <tr>
                              <th>Asset</th>
                              <th>Status</th>
                              <th>Last Maintenance</th>
                              <th>Next Maintenance</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Production Machine A</td>
                              <td>Operational</td>
                              <td>2025-04-15</td>
                              <td>2025-05-15</td>
                            </tr>
                            <tr>
                              <td>Forklift B</td>
                              <td>Maintenance</td>
                              <td>2025-03-10</td>
                              <td>2025-04-30</td>
                            </tr>
                            <tr>
                              <td>Conveyor System C</td>
                              <td>Operational</td>
                              <td>2025-04-01</td>
                              <td>2025-05-01</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>`;
          } else if (app === 'calendar') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <div class="row">
                        <div class="col-md-6">
                          <p><strong>Date:</strong> May 15, 2025</p>
                          <p><strong>Time:</strong> 10:00 AM - 11:30 AM</p>
                          <p><strong>Location:</strong> Conference Room A</p>
                        </div>
                        <div class="col-md-6">
                          <p><strong>Organizer:</strong> John Doe</p>
                          <p><strong>Attendees:</strong> 5</p>
                          <p><strong>Status:</strong> Confirmed</p>
                        </div>
                      </div>
                      <div class="mt-3">
                        <h5>Description</h5>
                        <p>This is a calendar event viewer. In a real application, the event details would be displayed here.</p>
                      </div>
                    </div>`;
          }

          return `<div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
                  </div>`;
        }

        // Function to download the current file
        function downloadCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          alert(`Downloading ${fileTitle}...`);
          // In a real application, this would trigger a download
        }

        // Function to share the current file
        function shareCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          const email = prompt(`Enter email address to share ${fileTitle} with:`);
          if (email) {
            alert(`${fileTitle} has been shared with ${email}.`);
            // In a real application, this would share the file with the specified email
          }
        }

        // Function to delete the current file
        function deleteCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();

          if (confirm(`Are you sure you want to delete ${fileTitle}?`)) {
            alert(`${fileTitle} has been deleted.`);
            // In a real application, this would delete the file

            // Close the modal after deletion
            const modal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
            if (modal) {
              modal.hide();
            }
          }
        }

        // Function to view attachments
        function viewAttachment(type, fileName) {
          // Create a file viewer modal if it doesn't exist
          const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

          // Set the file information
          const fileTitle = document.getElementById('fileViewerTitle');
          const fileContent = document.getElementById('fileViewerContent');

          // Set the title based on the file type and name
          fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;

          // Set the content based on the file type
          fileContent.innerHTML = getAttachmentContent(type, fileName);

          // Show the modal
          const modal = new bootstrap.Modal(fileViewerModal);
          modal.show();

          console.log(`Viewing ${fileName} directly`);
        }

        // Helper function to get the appropriate icon for the file type
        function getFileTypeIcon(type) {
          if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
          if (type === 'document') return 'bi-file-earmark-text text-primary';
          if (type === 'spreadsheet') return 'bi-file-earmark-spreadsheet text-success';
          if (type === 'image') return 'bi-file-earmark-image text-info';
          return 'bi-file-earmark text-secondary';
        }

        // Helper function to generate content for the attachment viewer
        function getAttachmentContent(type, fileName) {
          // Generate simulated content based on file type
          if (type === 'pdf') {
            return `<div class="text-center">
                      <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                      <h4 class="mt-3">${fileName}</h4>
                      <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                        <h5>Document Preview</h5>
                        <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                        <p>The document contains information related to asset performance management.</p>
                      </div>
                    </div>`;
          } else if (type === 'document') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${fileName}</h4>
                      <hr>
                      <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                      <p>The document contains information related to asset performance management.</p>
                      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                    </div>`;
          } else if (type === 'spreadsheet') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${fileName}</h4>
                      <hr>
                      <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                          <thead>
                            <tr>
                              <th>Asset</th>
                              <th>Status</th>
                              <th>Last Maintenance</th>
                              <th>Next Maintenance</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Production Machine A</td>
                              <td>Operational</td>
                              <td>2025-04-15</td>
                              <td>2025-05-15</td>
                            </tr>
                            <tr>
                              <td>Forklift B</td>
                              <td>Maintenance</td>
                              <td>2025-03-10</td>
                              <td>2025-04-30</td>
                            </tr>
                            <tr>
                              <td>Conveyor System C</td>
                              <td>Operational</td>
                              <td>2025-04-01</td>
                              <td>2025-05-01</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>`;
          } else if (type === 'image') {
            return `<div class="text-center">
                      <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                        <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                        <h5 class="mt-3">${fileName}</h5>
                        <p>This is an image viewer. In a real application, the image would be displayed here.</p>
                      </div>
                    </div>`;
          }

          return `<div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
                  </div>`;
        }

        // Function for downloading attachments
        function downloadAttachment(fileName) {
          downloadFile(fileName);
        }

        // Function for downloading files
        function downloadFile(fileName) {
          alert(`Downloading ${fileName}...`);
          // In a real application, this would trigger a download

          // Create a temporary link element to simulate download
          const link = document.createElement('a');
          link.href = '#';
          link.download = fileName;

          // Append to body, click, and remove
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }

        // Function for sharing files
        function shareFile(fileName) {
          const email = prompt(`Enter email address to share ${fileName} with:`);
          if (email) {
            alert(`${fileName} has been shared with ${email}.`);
            // In a real application, this would share the file with the specified email
          }
        }

        // Function for deleting attachments
        function deleteAttachment(fileName) {
          if (confirm(`Are you sure you want to delete ${fileName}?`)) {
            alert(`${fileName} has been deleted.`);
            // In a real application, this would delete the file
          }
        }
        // Initialize the page
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize sidebar toggle functionality
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    sidebar.classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 768) {
                    const isClickInsideSidebar = sidebar.contains(event.target);
                    const isClickOnToggle = sidebarToggle && sidebarToggle.contains(event.target);

                    if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768 && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });

            // Fetch AI insights
            fetchAIInsights();

            // Add event listener for refresh insights button
            document.getElementById('refresh-insights-btn').addEventListener('click', fetchAIInsights);

            // Add event listeners for expand/collapse all insights
            document.getElementById('expand-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.show();
                });
            });

            document.getElementById('collapse-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.hide();
                });
            });

            // Add event listener for export insights button
            document.getElementById('export-insights').addEventListener('click', function() {
                if (!window.aiInsightsData) return;

                const dataStr = JSON.stringify(window.aiInsightsData, null, 2);
                const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

                const exportFileDefaultName = 'asset-insights-' + new Date().toISOString().split('T')[0] + '.json';

                const linkElement = document.createElement('a');
                linkElement.setAttribute('href', dataUri);
                linkElement.setAttribute('download', exportFileDefaultName);
                linkElement.click();
            });
        });

        // Function to fetch AI insights from the Integration Hub
        function fetchAIInsights() {
            const insightsContainer = document.getElementById('ai-insights-container');
            insightsContainer.innerHTML = '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

            fetch('http://localhost:8000/api/ai-analytics/maintenance-prediction')
                .then(response => response.json())
                .then(data => {
                    console.log('AI insights:', data);

                    // Store insights data globally for export
                    window.aiInsightsData = data;

                    // Clear the container
                    insightsContainer.innerHTML = '';

                    // Generate sample insights if the API doesn't return the expected data structure
                    if (!data.insights) {
                        generateSampleInsights();
                        return;
                    }

                    // Create insights sections
                    createPredictiveMaintenanceSection(data, insightsContainer);
                    createPerformanceOptimizationSection(data, insightsContainer);
                    createRecommendationsSection(data, insightsContainer);

                    // Add event listeners for interactive elements
                    addInteractiveEventListeners(data);
                })
                .catch(error => {
                    console.error('Error fetching AI insights:', error);
                    generateSampleInsights();
                });
        }

        // Function to generate sample insights when the API is not available
        function generateSampleInsights() {
            const sampleData = {
                insights: [
                    {
                        type: 'predictive_maintenance',
                        assets: [
                            {
                                id: 1,
                                name: 'Production Machine A',
                                failureProbability: 0.15,
                                predictedFailureDate: '2025-06-10',
                                maintenanceRecommendation: 'Schedule preventive maintenance within 2 weeks',
                                estimatedDowntime: '4 hours',
                                estimatedCost: 2500
                            },
                            {
                                id: 3,
                                name: 'Conveyor System C',
                                failureProbability: 0.35,
                                predictedFailureDate: '2025-05-15',
                                maintenanceRecommendation: 'Immediate inspection required, signs of belt wear detected',
                                estimatedDowntime: '8 hours',
                                estimatedCost: 5000
                            }
                        ]
                    },
                    {
                        type: 'performance_optimization',
                        assets: [
                            {
                                id: 5,
                                name: 'Robotic Arm E',
                                currentEfficiency: 82,
                                potentialEfficiency: 95,
                                optimizationRecommendation: 'Recalibrate motion parameters and update firmware',
                                estimatedProductivityGain: '15%'
                            }
                        ]
                    }
                ],
                recommendations: [
                    'Schedule maintenance for Conveyor System C before May 15 to avoid unplanned downtime',
                    'Implement regular vibration analysis for Production Machine A',
                    'Optimize Robotic Arm E parameters to improve production efficiency by up to 15%'
                ],
                timestamp: new Date().toISOString()
            };

            const insightsContainer = document.getElementById('ai-insights-container');
            insightsContainer.innerHTML = '';

            // Store insights data globally for export
            window.aiInsightsData = sampleData;

            // Create insights sections
            createPredictiveMaintenanceSection(sampleData, insightsContainer);
            createPerformanceOptimizationSection(sampleData, insightsContainer);
            createRecommendationsSection(sampleData, insightsContainer);

            // Add event listeners for interactive elements
            addInteractiveEventListeners(sampleData);
        }

        // Function to create the Predictive Maintenance section
        function createPredictiveMaintenanceSection(data, container) {
            const predictiveMaintenance = data.insights.find(insight => insight.type === 'predictive_maintenance');
            if (!predictiveMaintenance || !predictiveMaintenance.assets || predictiveMaintenance.assets.length === 0) return;

            const section = document.createElement('div');
            section.className = 'mb-4';
            section.innerHTML = `
                <h5 class="mb-3">
                    <a href="#predictiveMaintenanceInsights" class="text-decoration-none text-dark" data-bs-toggle="collapse">
                        <i class="bi bi-tools me-2"></i>Predictive Maintenance
                        <i class="bi bi-chevron-down ms-2 small"></i>
                    </a>
                </h5>
                <div class="collapse show" id="predictiveMaintenanceInsights">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Asset</th>
                                            <th>Failure Probability</th>
                                            <th>Predicted Failure</th>
                                            <th>Estimated Impact</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${predictiveMaintenance.assets.map(asset => `
                                            <tr>
                                                <td>${asset.name}</td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar ${asset.failureProbability > 0.3 ? 'bg-danger' : asset.failureProbability > 0.15 ? 'bg-warning' : 'bg-success'}"
                                                            role="progressbar"
                                                            style="width: ${asset.failureProbability * 100}%;"
                                                            aria-valuenow="${asset.failureProbability * 100}"
                                                            aria-valuemin="0"
                                                            aria-valuemax="100">
                                                            ${Math.round(asset.failureProbability * 100)}%
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>${asset.predictedFailureDate}</td>
                                                <td>Downtime: ${asset.estimatedDowntime}<br>Cost: $${asset.estimatedCost.toLocaleString()}</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary schedule-maintenance-btn"
                                                        data-asset-id="${asset.id}"
                                                        data-asset-name="${asset.name}">
                                                        Schedule Maintenance
                                                    </button>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(section);
        }

        // Function to create the Performance Optimization section
        function createPerformanceOptimizationSection(data, container) {
            const performanceOptimization = data.insights.find(insight => insight.type === 'performance_optimization');
            if (!performanceOptimization || !performanceOptimization.assets || performanceOptimization.assets.length === 0) return;

            const section = document.createElement('div');
            section.className = 'mb-4';
            section.innerHTML = `
                <h5 class="mb-3">
                    <a href="#performanceOptimizationInsights" class="text-decoration-none text-dark" data-bs-toggle="collapse">
                        <i class="bi bi-graph-up me-2"></i>Performance Optimization
                        <i class="bi bi-chevron-down ms-2 small"></i>
                    </a>
                </h5>
                <div class="collapse show" id="performanceOptimizationInsights">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                ${performanceOptimization.assets.map(asset => `
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100">
                                            <div class="card-header">
                                                <h6 class="mb-0">${asset.name}</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div>Current Efficiency:</div>
                                                    <div class="fw-bold">${asset.currentEfficiency}%</div>
                                                </div>
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div>Potential Efficiency:</div>
                                                    <div class="fw-bold text-success">${asset.potentialEfficiency}%</div>
                                                </div>
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div>Productivity Gain:</div>
                                                    <div class="fw-bold text-success">${asset.estimatedProductivityGain}</div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="fw-bold mb-2">Recommendation:</div>
                                                    <div>${asset.optimizationRecommendation}</div>
                                                </div>
                                                <button class="btn btn-primary optimize-asset-btn"
                                                    data-asset-id="${asset.id}"
                                                    data-asset-name="${asset.name}">
                                                    Implement Optimization
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(section);
        }

        // Function to create the Recommendations section
        function createRecommendationsSection(data, container) {
            if (!data.recommendations || data.recommendations.length === 0) return;

            const section = document.createElement('div');
            section.className = 'mb-4';
            section.innerHTML = `
                <h5 class="mb-3">
                    <a href="#recommendationsSection" class="text-decoration-none text-dark" data-bs-toggle="collapse">
                        <i class="bi bi-lightbulb me-2"></i>Recommendations
                        <i class="bi bi-chevron-down ms-2 small"></i>
                    </a>
                </h5>
                <div class="collapse show" id="recommendationsSection">
                    <div class="card">
                        <div class="card-body">
                            <ul class="list-group">
                                ${data.recommendations.map((recommendation, index) => `
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="badge bg-primary rounded-pill me-2">${index + 1}</span>
                                            ${recommendation}
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary create-action-btn"
                                            data-recommendation-id="${index}"
                                            data-recommendation-text="${recommendation}">
                                            Create Action Plan
                                        </button>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(section);
        }

        // Function to add interactive event listeners to the AI insights
        function addInteractiveEventListeners(data) {
            // Add event listeners for schedule maintenance buttons
            document.querySelectorAll('.schedule-maintenance-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const assetId = this.dataset.assetId;
                    const assetName = this.dataset.assetName;

                    // Find the asset in the data
                    let asset;
                    const predictiveMaintenance = data.insights.find(insight => insight.type === 'predictive_maintenance');
                    if (predictiveMaintenance && predictiveMaintenance.assets) {
                        asset = predictiveMaintenance.assets.find(a => a.id == assetId);
                    }

                    if (!asset) return;

                    // Create modal for scheduling maintenance
                    const modalId = `maintenanceModal-${assetId}`;
                    const modalHtml = `
                        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="${modalId}Label">Schedule Maintenance for ${assetName}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="maintenanceDate" class="form-label">Maintenance Date</label>
                                                    <input type="date" class="form-control" id="maintenanceDate" value="${asset.predictedFailureDate}">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="maintenanceType" class="form-label">Maintenance Type</label>
                                                    <select class="form-select" id="maintenanceType">
                                                        <option value="preventive">Preventive Maintenance</option>
                                                        <option value="corrective">Corrective Maintenance</option>
                                                        <option value="predictive">Predictive Maintenance</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="maintenanceTeam" class="form-label">Maintenance Team</label>
                                                    <select class="form-select" id="maintenanceTeam">
                                                        <option value="internal">Internal Team</option>
                                                        <option value="external">External Contractor</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card h-100">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Asset Information</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>Failure Probability:</strong> ${Math.round(asset.failureProbability * 100)}%</p>
                                                        <p><strong>Predicted Failure Date:</strong> ${asset.predictedFailureDate}</p>
                                                        <p><strong>Estimated Downtime:</strong> ${asset.estimatedDowntime}</p>
                                                        <p><strong>Estimated Cost:</strong> $${asset.estimatedCost.toLocaleString()}</p>
                                                        <p><strong>Recommendation:</strong> ${asset.maintenanceRecommendation}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="maintenanceNotes" class="form-label">Maintenance Notes</label>
                                            <textarea class="form-control" id="maintenanceNotes" rows="3" placeholder="Enter maintenance details and instructions...">${asset.maintenanceRecommendation}</textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary confirm-maintenance-btn">Schedule Maintenance</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add modal to the document if it doesn't exist
                    if (!document.getElementById(modalId)) {
                        document.body.insertAdjacentHTML('beforeend', modalHtml);
                    }

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById(modalId));
                    modal.show();

                    // Add event listener for confirm button
                    document.querySelector(`#${modalId} .confirm-maintenance-btn`).addEventListener('click', function() {
                        // Here you would typically send the data to the server
                        alert(`Maintenance scheduled for ${assetName} on ${document.getElementById('maintenanceDate').value}`);
                        modal.hide();
                    });
                });
            });

            // Add event listeners for optimize asset buttons
            document.querySelectorAll('.optimize-asset-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const assetId = this.dataset.assetId;
                    const assetName = this.dataset.assetName;

                    // Find the asset in the data
                    let asset;
                    const performanceOptimization = data.insights.find(insight => insight.type === 'performance_optimization');
                    if (performanceOptimization && performanceOptimization.assets) {
                        asset = performanceOptimization.assets.find(a => a.id == assetId);
                    }

                    if (!asset) return;

                    // Create modal for optimization plan
                    const modalId = `optimizationModal-${assetId}`;
                    const modalHtml = `
                        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="${modalId}Label">Optimization Plan for ${assetName}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="implementationDate" class="form-label">Implementation Date</label>
                                                    <input type="date" class="form-control" id="implementationDate" value="${new Date().toISOString().split('T')[0]}">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="implementationTeam" class="form-label">Implementation Team</label>
                                                    <select class="form-select" id="implementationTeam">
                                                        <option value="engineering">Engineering Team</option>
                                                        <option value="maintenance">Maintenance Team</option>
                                                        <option value="external">External Specialist</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="downtimeRequired" class="form-label">Downtime Required</label>
                                                    <select class="form-select" id="downtimeRequired">
                                                        <option value="none">None</option>
                                                        <option value="minimal">Minimal (1-2 hours)</option>
                                                        <option value="partial">Partial Day (4-6 hours)</option>
                                                        <option value="full">Full Day</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card h-100">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Optimization Details</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>Current Efficiency:</strong> ${asset.currentEfficiency}%</p>
                                                        <p><strong>Potential Efficiency:</strong> ${asset.potentialEfficiency}%</p>
                                                        <p><strong>Productivity Gain:</strong> ${asset.estimatedProductivityGain}</p>
                                                        <p><strong>Recommendation:</strong> ${asset.optimizationRecommendation}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="implementationSteps" class="form-label">Implementation Steps</label>
                                            <textarea class="form-control" id="implementationSteps" rows="3" placeholder="Enter detailed implementation steps...">1. Prepare equipment for recalibration
2. Update firmware to latest version
3. Recalibrate motion parameters
4. Test and validate performance
5. Document changes and monitor results</textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary confirm-optimization-btn">Implement Optimization</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add modal to the document if it doesn't exist
                    if (!document.getElementById(modalId)) {
                        document.body.insertAdjacentHTML('beforeend', modalHtml);
                    }

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById(modalId));
                    modal.show();

                    // Add event listener for confirm button
                    document.querySelector(`#${modalId} .confirm-optimization-btn`).addEventListener('click', function() {
                        // Here you would typically send the data to the server
                        alert(`Optimization plan created for ${assetName}. Implementation scheduled for ${document.getElementById('implementationDate').value}`);
                        modal.hide();
                    });
                });
            });

            // Add event listeners for create action plan buttons
            document.querySelectorAll('.create-action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const recommendationId = this.dataset.recommendationId;
                    const recommendationText = this.dataset.recommendationText;

                    // Create modal for action plan
                    const modalId = `actionPlanModal-${recommendationId}`;
                    const modalHtml = `
                        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="${modalId}Label">Create Action Plan</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label class="form-label">Recommendation</label>
                                            <p>${recommendationText}</p>
                                        </div>
                                        <div class="mb-3">
                                            <label for="actionTitle" class="form-label">Action Title</label>
                                            <input type="text" class="form-control" id="actionTitle" value="Action Plan for ${recommendationText.substring(0, 30)}...">
                                        </div>
                                        <div class="mb-3">
                                            <label for="assignee" class="form-label">Assignee</label>
                                            <select class="form-select" id="assignee">
                                                <option value="maintenance">Maintenance Team</option>
                                                <option value="engineering">Engineering Team</option>
                                                <option value="operations">Operations Team</option>
                                                <option value="management">Management</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="dueDate" class="form-label">Due Date</label>
                                            <input type="date" class="form-control" id="dueDate" value="${new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">Priority</label>
                                            <select class="form-select" id="priority">
                                                <option value="low">Low</option>
                                                <option value="medium" selected>Medium</option>
                                                <option value="high">High</option>
                                                <option value="critical">Critical</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="actionDescription" class="form-label">Description</label>
                                            <textarea class="form-control" id="actionDescription" rows="3" placeholder="Enter action plan details...">${recommendationText}</textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary confirm-action-btn">Create Action Plan</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add modal to the document if it doesn't exist
                    if (!document.getElementById(modalId)) {
                        document.body.insertAdjacentHTML('beforeend', modalHtml);
                    }

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById(modalId));
                    modal.show();

                    // Add event listener for confirm button
                    document.querySelector(`#${modalId} .confirm-action-btn`).addEventListener('click', function() {
                        // Here you would typically send the data to the server
                        alert(`Action plan "${document.getElementById('actionTitle').value}" created and assigned to ${document.getElementById('assignee').value}`);
                        modal.hide();
                    });
                });
            });

            // Add event listener for export insights button
            document.getElementById('export-insights').addEventListener('click', function() {
                if (!window.aiInsightsData) return;

                const dataStr = JSON.stringify(window.aiInsightsData, null, 2);
                const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

                const exportFileDefaultName = 'asset-insights-' + new Date().toISOString().split('T')[0] + '.json';

                const linkElement = document.createElement('a');
                linkElement.setAttribute('href', dataUri);
                linkElement.setAttribute('download', exportFileDefaultName);
                linkElement.click();
            });

            // Add event listeners for expand/collapse all insights
            document.getElementById('expand-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.show();
                });
            });

            document.getElementById('collapse-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.hide();
                });
            });
        }

        // Attachments Handler
        const AttachmentsHandler = {
            init() {
                // Search functionality
                const attachmentSearch = document.getElementById('attachment-search');
                if (attachmentSearch) {
                    attachmentSearch.addEventListener('input', this.searchAttachments.bind(this));
                }

                // Sort functionality
                document.querySelectorAll('#all-attachments-content .dropdown-item[data-sort]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const sortBy = e.currentTarget.getAttribute('data-sort');
                        this.sortAttachments(sortBy);
                    });
                });

                // Filter functionality
                document.querySelectorAll('#all-attachments-content .dropdown-item[data-filter]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const filter = e.currentTarget.getAttribute('data-filter');
                        this.filterAttachments(filter);
                    });
                });

                // Upload button
                const uploadAttachmentBtn = document.getElementById('upload-attachment-btn');
                if (uploadAttachmentBtn) {
                    uploadAttachmentBtn.addEventListener('click', this.uploadAttachment.bind(this));
                }
            },

            searchAttachments(event) {
                const searchTerm = event.target.value.toLowerCase();
                const attachmentRows = document.querySelectorAll('#all-attachments-content tbody tr');

                attachmentRows.forEach(row => {
                    const fileName = row.querySelector('.d-flex span').textContent.toLowerCase();
                    if (fileName.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            },

            sortAttachments(sortBy) {
                const attachmentsList = document.querySelector('#all-attachments-content tbody');
                if (!attachmentsList) return;

                const rows = Array.from(attachmentsList.querySelectorAll('tr'));

                rows.sort((a, b) => {
                    let aValue, bValue;

                    if (sortBy === 'name') {
                        aValue = a.querySelector('.d-flex span').textContent;
                        bValue = b.querySelector('.d-flex span').textContent;
                    } else if (sortBy === 'date') {
                        aValue = a.querySelectorAll('td')[3].textContent;
                        bValue = b.querySelectorAll('td')[3].textContent;
                    } else if (sortBy === 'size') {
                        aValue = this.parseFileSize(a.querySelectorAll('td')[2].textContent);
                        bValue = this.parseFileSize(b.querySelectorAll('td')[2].textContent);
                        return aValue - bValue;
                    } else if (sortBy === 'type') {
                        aValue = a.querySelectorAll('td')[1].textContent;
                        bValue = b.querySelectorAll('td')[1].textContent;
                    }

                    return aValue.localeCompare(bValue);
                });

                // Clear the table
                while (attachmentsList.firstChild) {
                    attachmentsList.removeChild(attachmentsList.firstChild);
                }

                // Add sorted rows
                rows.forEach(row => {
                    attachmentsList.appendChild(row);
                });
            },

            parseFileSize(sizeStr) {
                const size = parseFloat(sizeStr);
                if (sizeStr.includes('KB')) {
                    return size * 1024;
                } else if (sizeStr.includes('MB')) {
                    return size * 1024 * 1024;
                } else if (sizeStr.includes('GB')) {
                    return size * 1024 * 1024 * 1024;
                }
                return size;
            },

            filterAttachments(filter) {
                const attachmentRows = document.querySelectorAll('#all-attachments-content tbody tr');

                attachmentRows.forEach(row => {
                    const fileType = row.querySelectorAll('td')[1].textContent;

                    if (filter === 'all') {
                        row.style.display = '';
                    } else if (filter === 'documents' && (fileType === 'Document')) {
                        row.style.display = '';
                    } else if (filter === 'images' && (fileType === 'Image')) {
                        row.style.display = '';
                    } else if (filter === 'spreadsheets' && (fileType === 'Spreadsheet')) {
                        row.style.display = '';
                    } else if (filter === 'pdfs' && (fileType === 'PDF')) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            },

            uploadAttachment() {
                const fileInput = document.getElementById('attachment-file');
                const nameInput = document.getElementById('attachment-name');
                const assetSelect = document.getElementById('attachment-asset');
                const descriptionInput = document.getElementById('attachment-description');
                const notifyCheckbox = document.getElementById('attachment-notify');

                if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                    alert('Please select a file to upload');
                    return;
                }

                const file = fileInput.files[0];
                const fileName = nameInput.value || file.name;
                const asset = assetSelect.value;
                const description = descriptionInput.value;
                const notify = notifyCheckbox.checked;

                // Show loading state
                const uploadBtn = document.getElementById('upload-attachment-btn');
                const originalText = uploadBtn.innerHTML;
                uploadBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Uploading...';
                uploadBtn.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    // Reset button state
                    uploadBtn.innerHTML = originalText;
                    uploadBtn.disabled = false;

                    // Show success message
                    alert(`File "${fileName}" uploaded successfully!`);

                    // Reset form
                    fileInput.value = '';
                    nameInput.value = '';
                    descriptionInput.value = '';
                    notifyCheckbox.checked = false;

                    // Switch to All Attachments tab
                    const allAttachmentsTab = document.getElementById('all-attachments-tab');
                    if (allAttachmentsTab) {
                        allAttachmentsTab.click();
                    }
                }, 2000);
            }
        };

        // Initialize the page
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Google Integration Components
            GoogleMapsHandler.init();
            GoogleSheetsHandler.init();
            GoogleCalendarHandler.init();
            GoogleGmailHandler.init();
            GoogleDriveHandler.init();
            GoogleDocsHandler.init();

            // Initialize Attachments Handler
            AttachmentsHandler.init();

            // Fetch AI insights
            fetchAIInsights();

            // Add event listener for refresh insights button
            document.getElementById('refresh-insights-btn').addEventListener('click', fetchAIInsights);
        });
    </script>

    <script>
        // Function to open specific Google items
        function openGoogleItem(app, type, itemId) {
          // For general app buttons (without specific item), show the modal
          if (app === 'drive' && !type && !itemId) {
            const driveModal = new bootstrap.Modal(document.getElementById('driveModal'));
            driveModal.show();
            return;
          } else if (app === 'docs' && !type && !itemId) {
            const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
            docsModal.show();
            return;
          } else if (app === 'sheets' && !type && !itemId) {
            const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
            sheetsModal.show();
            return;
          } else if (app === 'attachments' && !type && !itemId) {
            const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
            attachmentsModal.show();
            return;
          }

          // For specific items, directly open the file in a viewer
          try {
            // Create a simulated file viewer
            const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

            // Set the file information
            const fileTitle = document.getElementById('fileViewerTitle');
            const fileContent = document.getElementById('fileViewerContent');

            // Format the item ID to make it more readable
            const readableId = itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            // Set the title based on the app and type
            fileTitle.innerHTML = `<i class="bi ${getFileIcon(app, type)}"></i> ${readableId}`;

            // Set the content based on the file type
            fileContent.innerHTML = getFileContent(app, type, itemId);

            // Show the modal
            const modal = new bootstrap.Modal(fileViewerModal);
            modal.show();
          } catch (error) {
            console.error('Error opening file:', error);
            alert('Could not open the file. Please try again later.');
          }
        }

        // Helper function to create a file viewer modal if it doesn't exist
        function createFileViewerModal() {
          const modal = document.createElement('div');
          modal.className = 'modal fade';
          modal.id = 'fileViewerModal';
          modal.tabIndex = '-1';
          modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
          modal.setAttribute('aria-hidden', 'true');

          modal.innerHTML = `
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                  <h5 class="modal-title" id="fileViewerTitle"></h5>
                  <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">Download</button>
                  <button type="button" class="btn btn-success" onclick="shareCurrentFile()">Share</button>
                  <button type="button" class="btn btn-danger" onclick="deleteCurrentFile()">Delete</button>
                </div>
              </div>
            </div>
          `;

          document.body.appendChild(modal);
          return modal;
        }

        // Helper function to get the appropriate icon for the file type
        function getFileIcon(app, type) {
          if (app === 'drive') {
            if (type === 'folder') return 'bi-folder-fill text-primary';
            if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
            if (type === 'image') return 'bi-file-earmark-image text-info';
            if (type === 'document') return 'bi-file-earmark-text text-primary';
            return 'bi-file-earmark text-secondary';
          } else if (app === 'docs') {
            return 'bi-file-earmark-text text-primary';
          } else if (app === 'sheets') {
            return 'bi-file-earmark-spreadsheet text-success';
          } else if (app === 'calendar') {
            return 'bi-calendar-event text-primary';
          }
          return 'bi-file-earmark text-secondary';
        }

        // Helper function to generate content for the file viewer
        function getFileContent(app, type, itemId) {
          // Generate simulated content based on file type
          if (app === 'drive') {
            if (type === 'folder') {
              return `<div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> This is a folder view. In a real application, this would show the contents of the folder.
                      </div>
                      <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-text me-2"></i> Document 1.docx
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-spreadsheet me-2"></i> Spreadsheet 1.xlsx
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-pdf me-2"></i> Report.pdf
                        </a>
                      </div>`;
            } else if (type === 'pdf') {
              return `<div class="text-center">
                        <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                        <h4 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <div class="alert alert-info mt-3">
                          <i class="bi bi-info-circle"></i> This is a PDF viewer. In a real application, the PDF would be displayed here.
                        </div>
                        <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                          <h5>Document Preview</h5>
                          <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                          <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                        </div>
                      </div>`;
            } else if (type === 'document') {
              return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <hr>
                        <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                        <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                      </div>`;
            } else if (type === 'image') {
              return `<div class="text-center">
                        <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                          <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                          <h5 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h5>
                          <p>This is an image viewer. In a real application, the image would be displayed here.</p>
                        </div>
                      </div>`;
            }
          } else if (app === 'docs') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                      <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                    </div>`;
          } else if (app === 'sheets') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                          <thead>
                            <tr>
                              <th>Asset</th>
                              <th>Status</th>
                              <th>Last Maintenance</th>
                              <th>Next Maintenance</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Production Machine A</td>
                              <td>Operational</td>
                              <td>2025-04-15</td>
                              <td>2025-05-15</td>
                            </tr>
                            <tr>
                              <td>Forklift B</td>
                              <td>Maintenance</td>
                              <td>2025-03-10</td>
                              <td>2025-04-30</td>
                            </tr>
                            <tr>
                              <td>Conveyor System C</td>
                              <td>Operational</td>
                              <td>2025-04-01</td>
                              <td>2025-05-01</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>`;
          } else if (app === 'calendar') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <div class="row">
                        <div class="col-md-6">
                          <p><strong>Date:</strong> May 15, 2025</p>
                          <p><strong>Time:</strong> 10:00 AM - 11:30 AM</p>
                          <p><strong>Location:</strong> Conference Room A</p>
                        </div>
                        <div class="col-md-6">
                          <p><strong>Organizer:</strong> John Doe</p>
                          <p><strong>Attendees:</strong> 5</p>
                          <p><strong>Status:</strong> Confirmed</p>
                        </div>
                      </div>
                      <div class="mt-3">
                        <h5>Description</h5>
                        <p>This is a calendar event viewer. In a real application, the event details would be displayed here.</p>
                      </div>
                    </div>`;
          }

          return `<div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
                  </div>`;
        }

        // Function to download the current file
        function downloadCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          alert(`Downloading ${fileTitle}...`);
          // In a real application, this would trigger a download
        }

        // Function to share the current file
        function shareCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          const email = prompt(`Enter email address to share ${fileTitle} with:`);
          if (email) {
            alert(`${fileTitle} has been shared with ${email}.`);
            // In a real application, this would share the file with the specified email
          }
        }

        // Function to delete the current file
        function deleteCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          const confirmDelete = confirm(`Are you sure you want to delete ${fileTitle}?`);
          if (confirmDelete) {
            alert(`${fileTitle} has been deleted.`);
            // In a real application, this would delete the file
            // Close the modal after deletion
            const modal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
            if (modal) {
              modal.hide();
            }
          }
        }

        // Function to download a file
        function downloadFile(fileName) {
          alert(`Downloading ${fileName}...`);
          // In a real application, this would trigger a download
        }

        // Function to share a file
        function shareFile(fileName) {
          const email = prompt(`Enter email address to share ${fileName} with:`);
          if (email) {
            alert(`${fileName} has been shared with ${email}.`);
            // In a real application, this would share the file with the specified email
          }
        }

        // Function to open an email
        function openEmail(emailId) {
          // Get the email tab and show it
          const readEmailTab = document.getElementById('read-email-tab');
          bootstrap.Tab.getInstance(readEmailTab) || new bootstrap.Tab(readEmailTab);
          readEmailTab.click();

          // Set email content based on the email ID
          const emailData = getEmailData(emailId);

          // Update the email view with the data
          document.getElementById('email-subject').textContent = emailData.subject;
          document.getElementById('email-sender').textContent = emailData.sender;
          document.getElementById('email-sender-avatar').textContent = getInitials(emailData.sender);
          document.getElementById('email-recipients').textContent = `To: ${emailData.recipients}`;
          document.getElementById('email-time').textContent = emailData.time;
          document.getElementById('email-content').innerHTML = emailData.content;

          // Store the current email ID for reply functionality
          document.body.dataset.currentEmailId = emailId;

          // Update attachments if any
          const attachmentsContainer = document.getElementById('email-attachments');
          if (emailData.attachments && emailData.attachments.length > 0) {
            attachmentsContainer.style.display = 'block';
            const attachmentsList = attachmentsContainer.querySelector('.list-group');
            attachmentsList.innerHTML = '';

            emailData.attachments.forEach(attachment => {
              const attachmentItem = document.createElement('div');
              attachmentItem.className = 'list-group-item d-flex justify-content-between align-items-center';
              attachmentItem.innerHTML = `
                <div class="d-flex align-items-center">
                  <i class="bi ${getFileTypeIcon(attachment.type)} me-2"></i>
                  <span>${attachment.name}</span>
                  <small class="text-muted ms-2">(${attachment.size})</small>
                </div>
                <div class="btn-group">
                  <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment('${attachment.type}', '${attachment.name}')">
                    <i class="bi bi-eye"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-success" onclick="downloadFile('${attachment.name}')">
                    <i class="bi bi-download"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-secondary" onclick="shareFile('${attachment.name}')">
                    <i class="bi bi-share"></i>
                  </button>
                </div>
              `;
              attachmentsList.appendChild(attachmentItem);
            });
          } else {
            attachmentsContainer.style.display = 'none';
          }
        }

        // Function to get email data based on ID
        function getEmailData(emailId) {
          // This would typically come from an API or database
          const emailsData = {
            'asset-maintenance-schedule-updated': {
              subject: 'Asset maintenance schedule updated',
              sender: 'Maintenance Team',
              recipients: 'me',
              time: '15 minutes ago',
              content: `<p>Hello,</p>
                        <p>The maintenance schedule for Q2 2025 has been updated with new equipment. Please review the attached schedule and let us know if you have any questions or concerns.</p>
                        <p>Key updates include:</p>
                        <ul>
                          <li>Added new Production Machine A to the schedule</li>
                          <li>Adjusted maintenance frequency for Conveyor System C</li>
                          <li>Updated maintenance procedures for Forklift B</li>
                        </ul>
                        <p>Best regards,<br>Maintenance Team</p>`,
              attachments: [
                { name: 'Maintenance_Schedule_Q2_2025.xlsx', type: 'spreadsheet', size: '1.8 MB' }
              ]
            },
            'equipment-inspection-report': {
              subject: 'Equipment inspection report',
              sender: 'John Davis',
              recipients: 'me',
              time: '2 hours ago',
              content: `<p>Hi there,</p>
                        <p>Please find attached the inspection report for the conveyor system. The inspection was completed yesterday and there are a few issues that need to be addressed.</p>
                        <p>The main concerns are:</p>
                        <ol>
                          <li>Excessive wear on the main belt</li>
                          <li>Misalignment of the drive pulley</li>
                          <li>Several rollers need replacement</li>
                        </ol>
                        <p>I recommend scheduling maintenance within the next two weeks to prevent any downtime.</p>
                        <p>Regards,<br>John Davis</p>`,
              attachments: [
                { name: 'Conveyor_System_Inspection_Report.pdf', type: 'pdf', size: '2.4 MB' },
                { name: 'Inspection_Photos.zip', type: 'archive', size: '5.7 MB' }
              ]
            },
            'maintenance-parts-order-confirmation': {
              subject: 'Maintenance parts order confirmation',
              sender: 'Sarah Miller',
              recipients: 'me',
              time: '4 hours ago',
              content: `<p>Hello,</p>
                        <p>This is to confirm that the parts for the scheduled maintenance have been ordered. The order details are as follows:</p>
                        <table class="table table-bordered mt-3 mb-3">
                          <thead>
                            <tr>
                              <th>Part Number</th>
                              <th>Description</th>
                              <th>Quantity</th>
                              <th>Expected Delivery</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>CV-2025-A</td>
                              <td>Conveyor Belt (50m)</td>
                              <td>1</td>
                              <td>May 5, 2025</td>
                            </tr>
                            <tr>
                              <td>RL-103</td>
                              <td>Roller Assembly</td>
                              <td>12</td>
                              <td>May 3, 2025</td>
                            </tr>
                            <tr>
                              <td>DP-450</td>
                              <td>Drive Pulley</td>
                              <td>1</td>
                              <td>May 7, 2025</td>
                            </tr>
                          </tbody>
                        </table>
                        <p>Please let me know if you need any changes to this order.</p>
                        <p>Best regards,<br>Sarah Miller<br>Procurement Department</p>`,
              attachments: [
                { name: 'Parts_Order_Invoice.pdf', type: 'pdf', size: '1.2 MB' }
              ]
            }
          };

          return emailsData[emailId] || {
            subject: 'Unknown Email',
            sender: 'Unknown',
            recipients: 'me',
            time: 'Unknown',
            content: '<p>Email content not found.</p>',
            attachments: []
          };
        }

        // Function to get initials from a name
        function getInitials(name) {
          return name.split(' ').map(part => part.charAt(0)).join('');
        }

        // Function to reply to the current email
        function replyToEmail() {
          // Get the reply tab and show it
          const replyEmailTab = document.getElementById('reply-email-tab');
          bootstrap.Tab.getInstance(replyEmailTab) || new bootstrap.Tab(replyEmailTab);
          replyEmailTab.click();

          // Get the current email ID
          const emailId = document.body.dataset.currentEmailId;
          if (!emailId) return;

          // Get the email data
          const emailData = getEmailData(emailId);

          // Update the reply form
          document.getElementById('reply-email-subject').textContent = `Re: ${emailData.subject}`;
          document.getElementById('reply-email-recipients').innerHTML = `To: <span class="fw-bold">${emailData.sender}</span>`;
          document.getElementById('original-email-date').textContent = emailData.time;
          document.getElementById('original-email-sender').textContent = emailData.sender;
          document.getElementById('original-email-content').innerHTML = emailData.content;

          // Focus on the reply textarea
          document.getElementById('reply-email-content-textarea').focus();
        }

        // Function to send a reply
        function sendReply() {
          const replyContent = document.getElementById('reply-email-content-textarea').value.trim();
          if (!replyContent) {
            alert('Please enter a reply before sending.');
            return;
          }

          // Get the current email ID
          const emailId = document.body.dataset.currentEmailId;
          if (!emailId) return;

          // Get the email data
          const emailData = getEmailData(emailId);

          // In a real application, this would send the reply
          alert(`Reply sent to ${emailData.sender}`);

          // Clear the reply form
          document.getElementById('reply-email-content-textarea').value = '';

          // Go back to the inbox
          const inboxTab = document.getElementById('inbox-tab');
          bootstrap.Tab.getInstance(inboxTab) || new bootstrap.Tab(inboxTab);
          inboxTab.click();
        }

        // Function to view attachments
        function viewAttachment(type, fileName) {
          // Create a file viewer modal if it doesn't exist
          const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

          // Set the file information
          const fileTitle = document.getElementById('fileViewerTitle');
          const fileContent = document.getElementById('fileViewerContent');

          // Set the title based on the file type and name
          fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;

          // Set the content based on the file type
          fileContent.innerHTML = getAttachmentContent(type, fileName);

          // Show the modal
          const modal = new bootstrap.Modal(fileViewerModal);
          modal.show();

          console.log(`Viewing ${fileName} directly`);
        }

        // Helper function to get the appropriate icon for the file type
        function getFileTypeIcon(type) {
          if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
          if (type === 'document') return 'bi-file-earmark-text text-primary';
          if (type === 'spreadsheet') return 'bi-file-earmark-spreadsheet text-success';
          if (type === 'image') return 'bi-file-earmark-image text-info';
          return 'bi-file-earmark text-secondary';
        }

        // Helper function to generate content for the attachment viewer
        function getAttachmentContent(type, fileName) {
          // Generate simulated content based on file type
          if (type === 'pdf') {
            return `<div class="text-center">
                      <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                      <h4 class="mt-3">${fileName}</h4>
                      <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                        <h5>Document Preview</h5>
                        <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                        <p>The document contains information related to asset performance management.</p>
                      </div>
                    </div>`;
          } else if (type === 'document') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${fileName}</h4>
                      <hr>
                      <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                      <p>The document contains information related to asset performance management.</p>
                      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                    </div>`;
          } else if (type === 'spreadsheet') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${fileName}</h4>
                      <hr>
                      <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                          <thead>
                            <tr>
                              <th>Asset</th>
                              <th>Status</th>
                              <th>Last Maintenance</th>
                              <th>Next Maintenance</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Production Machine A</td>
                              <td>Operational</td>
                              <td>2025-04-15</td>
                              <td>2025-05-15</td>
                            </tr>
                            <tr>
                              <td>Forklift B</td>
                              <td>Maintenance</td>
                              <td>2025-03-10</td>
                              <td>2025-04-30</td>
                            </tr>
                            <tr>
                              <td>Conveyor System C</td>
                              <td>Operational</td>
                              <td>2025-04-01</td>
                              <td>2025-05-01</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>`;
          } else if (type === 'image') {
            return `<div class="text-center">
                      <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                        <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                        <h5 class="mt-3">${fileName}</h5>
                        <p>This is an image viewer. In a real application, the image would be displayed here.</p>
                      </div>
                    </div>`;
          }

          return `<div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
                  </div>`;
        }

        // Function for downloading attachments
        function downloadAttachment(fileName) {
          alert(`Downloading ${fileName}...`);
          // In a real application, this would trigger a download
        }

        // Function for deleting attachments
        function deleteAttachment(fileName) {
          if (confirm(`Are you sure you want to delete ${fileName}?`)) {
            alert(`${fileName} has been deleted.`);
            // In a real application, this would delete the file
          }
        }
    </script>
</body>
</html>
