<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Modal Functionality Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .console-output {
            background: #212529;
            color: #fff;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6">
                <h1>Gmail Modal Test</h1>
                
                <div class="test-section">
                    <h3>Basic Tests</h3>
                    <button class="btn btn-primary me-2" onclick="testGmailButton()">Test Open Gmail Button</button>
                    <button class="btn btn-secondary me-2" onclick="testDropdownElements()">Test Dropdown Elements</button>
                    <button class="btn btn-info me-2" onclick="testFunctions()">Test Functions</button>
                    <button class="btn btn-success" onclick="openGmailModal()">Open Gmail Modal</button>
                </div>
                
                <div class="test-section">
                    <h3>Dropdown Tests</h3>
                    <button class="btn btn-outline-primary me-2" onclick="testBootstrapDropdowns()">Test Bootstrap Dropdowns</button>
                    <button class="btn btn-outline-secondary me-2" onclick="testManualDropdowns()">Test Manual Dropdowns</button>
                    <button class="btn btn-outline-info" onclick="debugDropdowns()">Debug Dropdowns</button>
                </div>
                
                <div class="test-section">
                    <h3>Email Functions Tests</h3>
                    <button class="btn btn-outline-success me-2" onclick="testSearchFunction()">Test Search</button>
                    <button class="btn btn-outline-warning me-2" onclick="testSortFunction()">Test Sort</button>
                    <button class="btn btn-outline-danger me-2" onclick="testFilterFunction()">Test Filter</button>
                    <button class="btn btn-outline-dark" onclick="testEmailOpen()">Test Email Open</button>
                </div>
                
                <div id="test-results"></div>
            </div>
            
            <div class="col-md-6">
                <h3>Console Output</h3>
                <div id="console-output" class="console-output"></div>
                <button class="btn btn-sm btn-secondary mt-2" onclick="clearConsole()">Clear Console</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Override console.log to show in page
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.innerHTML = `<span style="color: #6c757d;">[${timestamp}]</span> <span style="color: ${type === 'error' ? '#dc3545' : type === 'warn' ? '#ffc107' : '#28a745'};">[${type.toUpperCase()}]</span> ${message}`;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        function clearConsole() {
            document.getElementById('console-output').innerHTML = '';
        }
        
        function addTestResult(message, type = 'success') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result test-${type}`;
            div.textContent = message;
            results.appendChild(div);
        }
        
        function testGmailButton() {
            console.log('Testing Gmail button...');
            const button = document.getElementById('mrp-open-gmail-btn');
            if (button) {
                addTestResult('✓ Gmail button found', 'success');
                console.log('Gmail button found:', button);
            } else {
                addTestResult('✗ Gmail button not found', 'error');
                console.error('Gmail button not found');
            }
        }
        
        function testDropdownElements() {
            console.log('Testing dropdown elements...');
            
            const modal = document.getElementById('mrp-gmailModal');
            if (!modal) {
                addTestResult('✗ Gmail modal not found', 'error');
                return;
            }
            
            const sortDropdown = document.getElementById('sort-dropdown');
            const filterDropdown = document.getElementById('filter-dropdown');
            
            if (sortDropdown && filterDropdown) {
                addTestResult('✓ Dropdown elements found', 'success');
                console.log('Sort dropdown:', sortDropdown);
                console.log('Filter dropdown:', filterDropdown);
            } else {
                addTestResult('✗ Some dropdown elements missing', 'error');
                console.error('Missing dropdowns - Sort:', !!sortDropdown, 'Filter:', !!filterDropdown);
            }
        }
        
        function testFunctions() {
            console.log('Testing functions availability...');
            
            const functions = ['searchEmails', 'sortEmails', 'filterEmails', 'showAllEmails', 'openEmail'];
            let allFound = true;
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    console.log(`✓ ${funcName} function found`);
                } else {
                    console.error(`✗ ${funcName} function not found`);
                    allFound = false;
                }
            });
            
            if (allFound) {
                addTestResult('✓ All essential functions found', 'success');
            } else {
                addTestResult('✗ Some functions missing', 'error');
            }
        }
        
        function openGmailModal() {
            console.log('Opening Gmail modal...');
            const modal = document.getElementById('mrp-gmailModal');
            if (modal) {
                try {
                    const bootstrapModal = new bootstrap.Modal(modal);
                    bootstrapModal.show();
                    addTestResult('✓ Gmail modal opened', 'success');
                } catch (error) {
                    addTestResult('✗ Error opening modal: ' + error.message, 'error');
                }
            } else {
                addTestResult('✗ Gmail modal element not found', 'error');
            }
        }
        
        function testBootstrapDropdowns() {
            console.log('Testing Bootstrap dropdowns...');
            const modal = document.getElementById('mrp-gmailModal');
            if (!modal) {
                addTestResult('✗ Modal not found for dropdown test', 'error');
                return;
            }
            
            const dropdowns = modal.querySelectorAll('[data-bs-toggle="dropdown"]');
            console.log('Found', dropdowns.length, 'Bootstrap dropdowns');
            
            if (dropdowns.length > 0) {
                dropdowns.forEach((dropdown, index) => {
                    try {
                        const bsDropdown = new bootstrap.Dropdown(dropdown);
                        console.log(`Bootstrap dropdown ${index + 1} initialized:`, dropdown.id);
                    } catch (error) {
                        console.error(`Error initializing dropdown ${index + 1}:`, error);
                    }
                });
                addTestResult(`✓ Found ${dropdowns.length} Bootstrap dropdowns`, 'success');
            } else {
                addTestResult('⚠ No Bootstrap dropdowns found', 'warning');
            }
        }
        
        function testManualDropdowns() {
            console.log('Testing manual dropdown functionality...');
            if (typeof window.gmailDropdownDebug === 'object') {
                addTestResult('✓ Manual dropdown debug functions available', 'success');
                try {
                    window.gmailDropdownDebug.setupGmailDropdowns();
                    addTestResult('✓ Manual dropdown setup executed', 'success');
                } catch (error) {
                    addTestResult('✗ Error in manual dropdown setup: ' + error.message, 'error');
                }
            } else {
                addTestResult('✗ Manual dropdown debug functions not available', 'error');
            }
        }
        
        function debugDropdowns() {
            console.log('Running dropdown debug...');
            if (typeof window.debugDropdownState === 'function') {
                window.debugDropdownState();
                addTestResult('✓ Dropdown debug executed - check console', 'success');
            } else {
                addTestResult('✗ Debug function not available', 'error');
            }
        }
        
        function testSearchFunction() {
            console.log('Testing search function...');
            if (typeof searchEmails === 'function') {
                try {
                    searchEmails('inventory');
                    addTestResult('✓ Search function executed', 'success');
                } catch (error) {
                    addTestResult('✗ Search function error: ' + error.message, 'error');
                }
            } else {
                addTestResult('✗ Search function not found', 'error');
            }
        }
        
        function testSortFunction() {
            console.log('Testing sort function...');
            if (typeof sortEmails === 'function') {
                try {
                    sortEmails('sender-asc');
                    addTestResult('✓ Sort function executed', 'success');
                } catch (error) {
                    addTestResult('✗ Sort function error: ' + error.message, 'error');
                }
            } else {
                addTestResult('✗ Sort function not found', 'error');
            }
        }
        
        function testFilterFunction() {
            console.log('Testing filter function...');
            if (typeof filterEmails === 'function') {
                try {
                    filterEmails('unread');
                    addTestResult('✓ Filter function executed', 'success');
                } catch (error) {
                    addTestResult('✗ Filter function error: ' + error.message, 'error');
                }
            } else {
                addTestResult('✗ Filter function not found', 'error');
            }
        }
        
        function testEmailOpen() {
            console.log('Testing email open function...');
            if (typeof openEmail === 'function') {
                try {
                    openEmail('test-sender', 'Test Subject');
                    addTestResult('✓ Email open function executed', 'success');
                } catch (error) {
                    addTestResult('✗ Email open function error: ' + error.message, 'error');
                }
            } else {
                addTestResult('✗ Email open function not found', 'error');
            }
        }
        
        // Run initial tests
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
            if (typeof bootstrap !== 'undefined') {
                console.log('Bootstrap version:', bootstrap.Tooltip?.VERSION || 'Unknown');
            }
            
            // Wait a bit then run basic tests
            setTimeout(() => {
                console.log('Running automatic tests...');
                testGmailButton();
                testDropdownElements();
                testFunctions();
            }, 1000);
        });
    </script>
</body>
</html>
