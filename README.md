# ISA Suite

A comprehensive enterprise application suite built with TypeScript, React, and Node.js.

## Overview

ISA Suite is a collection of integrated enterprise applications designed to streamline business operations. The suite includes:

- **Business Management System (BMS)**: Core business operations, financial tracking, and reporting
- **Customer Relationship Management (CRM)**: Customer and sales management
- **Integration Hub**: Central connection point for all systems

## Prerequisites

- Node.js >= 18.0.0 (Portable version included in the USB drive)

## Quick Start

1. Run the all-in-one script by double-clicking on `start-and-open.bat` to start the applications and open them in your browser.

   OR

2. Run the setup script by double-clicking on `start.bat` or running:

   ```
   powershell -ExecutionPolicy Bypass -File "C:\ISASUITE\setup-and-run.ps1"
   ```
   > **Note:** Always run `setup-and-run.ps1` with PowerShell. Do not open it with Notepad.

3. Select the application mode:

   - **development**: For development with mock data and debugging
   - **production**: For production use with real data
   - **sandbox**: For training and testing with mock data
   - **demo**: For demonstration with pre-populated demo data

4. Open the applications in your browser by double-clicking on `open-apps.bat` or manually navigating to:
   - Integration Hub: http://localhost:8000
   - Business Management System: http://localhost:3001
   - Materials Requirements Planning: http://localhost:3002
   - Customer Relationship Management: http://localhost:3003
   - Warehouse Management System: http://localhost:3004
   - Advanced Planning and Scheduling: http://localhost:3005
   - Asset Performance Management: http://localhost:3006
   - PMS: http://localhost:3007
   - SCM: http://localhost:3008
   - TM: http://localhost:3009

## Creating Desktop Shortcuts

Run the following script to create desktop shortcuts for easy access:

```
powershell -ExecutionPolicy Bypass -File "D:\ISASUITE\create-shortcuts.ps1"
```

## Application Modes

### Development Mode

- Uses mock data
- Enables debugging features
- Suitable for development and testing

### Production Mode

- Uses real data
- Disables debugging features
- Optimized for performance

### Sandbox Mode

- Uses mock data
- Enables training features
- Includes gamification elements
- Safe environment for learning

### Demo Mode

- Uses pre-populated demo data
- Disables certain features
- Suitable for demonstrations

## Project Structure

```
ISASUITE/
├── apps/                  # Application packages
│   ├── BMS/               # Business Management System
│   ├── CRM/               # Customer Relationship Management
│   ├── MRP/               # Materials Requirements Planning
│   ├── WMS/               # Warehouse Management System
│   ├── APS/               # Advanced Planning and Scheduling
│   ├── APM/               # Asset Performance Management
│   ├── PMS/               # Project Management System
│   ├── SCM/               # Supply Chain Management
│   ├── TM/                # Transport Management
│   └── hub/               # Integration Hub
├── SharedFeatures/        # Shared features and utilities
│   ├── auth/              # Authentication utilities
│   ├── logger/            # Logging utilities
│   ├── integrations/      # External service integrations
│   └── utils/             # Utility functions
├── setup-and-run.ps1      # Setup and run script
├── start.bat              # Batch file to start the setup script
├── open-apps.bat          # Batch file to open applications in browser
├── start-and-open.bat     # All-in-one batch file to start and open applications
└── create-shortcuts.ps1   # Create desktop shortcuts script
```

> **Note:** Use `pnpm` for dependency management. The workspace uses a portable Node.js and pnpm setup.

## License

ISC
