{"name": "bms", "version": "1.0.0", "description": "Business Management System", "main": "electron.js", "scripts": {"start": "node index.js production", "dev": "nodemon index.js development", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{js,jsx,json,md}\"", "build": "webpack --mode production", "build:dev": "webpack --mode development", "client:dev": "webpack serve --mode development --open", "check:deps": "node scripts/check-dependencies.js", "update:deps": "npm update", "audit": "npm audit", "audit:fix": "npm audit fix", "electron-dev": "cross-env BROWSER=none pnpm run dev", "electron-pack": "pnpm run build && electron-builder build --win --publish never", "electron-dist": "electron-builder --win --publish never", "deploy:dev": "node scripts/deploy.js development", "deploy:staging": "node scripts/deploy.js staging", "deploy:prod": "node scripts/deploy.js production", "health": "node scripts/health-check.js"}, "keywords": ["business", "management", "erp"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-health-check": "^0.1.0", "express-pino-logger": "^7.0.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "pino-http": "^10.4.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@testing-library/jest-dom": "^6.6.3", "babel-loader": "^10.0.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "electron": "^30.0.0", "electron-builder": "^24.13.3", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "prettier": "^3.5.3", "style-loader": "^4.0.0", "wait-on": "^7.2.0", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}, "build": {"appId": "com.isa.bms", "productName": "ISA Business Management System", "files": ["build/**/*", "electron.js", "preload.js", "manifest.json", "package.json"], "directories": {"buildResources": "public"}, "win": {"target": ["nsis"], "icon": "public/icons/icon-512x512.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}