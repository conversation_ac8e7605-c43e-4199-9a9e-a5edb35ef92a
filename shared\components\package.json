{"name": "@isa-suite/shared-components", "version": "1.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.tsx", "test": "jest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@mui/material": "^5.15.10", "@mui/icons-material": "^5.15.10", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "typescript": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@testing-library/react": "^14.2.1"}}