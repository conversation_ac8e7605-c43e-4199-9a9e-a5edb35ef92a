// Application Modes Module

// Define application modes
const APP_MODES = {
  PRODUCTION: 'production',
  SANDBOX: 'sandbox',
  DEMO: 'demo',
  DEVELOPMENT: 'development',
};

/**
 * Initialize application mode
 */
function initializeMode(mode) {
  // You can add mode-specific config here
  return { mode };
}

/**
 * Create middleware for handling application mode
 */
function createModeMiddleware(mode) {
  return (req, res, next) => {
    req.appMode = mode;
    next();
  };
}

module.exports = {
  APP_MODES,
  initializeMode,
  createModeMiddleware,
};
