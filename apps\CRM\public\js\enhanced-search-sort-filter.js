/**
 * Enhanced Search, Sort, and Filter Functionality for CRM Application
 * Implements the BMS-style search, sort, and filter functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced search, sort, and filter functionality loaded for CRM');

    // Initialize enhanced search, sort, and filter for customers table
    initEnhancedCustomersControls();

    // Initialize enhanced search, sort, and filter for deals table
    initEnhancedDealsControls();

    // Add toast container if it doesn't exist
    if (!document.getElementById('toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
});

/**
 * Initialize enhanced controls for the customers table
 */
function initEnhancedCustomersControls() {
    const customersTable = document.getElementById('customersTable');
    if (!customersTable) {
        console.log('Customers table not found, skipping enhanced controls');
        return;
    }

    // Find the search input and controls container
    const searchInput = document.getElementById('customersSearchInput');
    const controlsContainer = searchInput ? searchInput.closest('.row') : null;

    if (!controlsContainer) {
        console.log('Controls container not found, skipping enhanced controls');
        return;
    }

    // Update the search input with icon and styling
    if (searchInput) {
        const searchInputGroup = searchInput.closest('.input-group');
        if (searchInputGroup) {
            searchInputGroup.innerHTML = `
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" id="customersSearchInput" placeholder="Search customers...">
                <button class="btn btn-outline-secondary" type="button" id="clearCustomersSearch">
                    <i class="bi bi-x"></i>
                </button>
            `;

            // Add event listener for the clear button
            document.getElementById('clearCustomersSearch').addEventListener('click', function() {
                document.getElementById('customersSearchInput').value = '';
                document.getElementById('customersSearchInput').dispatchEvent(new Event('input'));
            });
        }
    }

    // Update the filter controls
    const filterControls = controlsContainer.querySelector('.d-flex');
    if (filterControls) {
        // Add sort dropdown if it doesn't exist
        if (!document.getElementById('customersSortDropdown')) {
            const sortDropdown = document.createElement('div');
            sortDropdown.className = 'dropdown me-2';
            sortDropdown.innerHTML = `
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="customersSortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-sort-down"></i> Sort
                </button>
                <ul class="dropdown-menu" aria-labelledby="customersSortDropdown">
                    <li><a class="dropdown-item sort-option" href="#" data-sort="id-asc">ID (A-Z)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="id-desc">ID (Z-A)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="company-asc">Company (A-Z)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="company-desc">Company (Z-A)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="contact-asc">Contact (A-Z)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="contact-desc">Contact (Z-A)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="date-asc">Last Contact (Oldest)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="date-desc">Last Contact (Newest)</a></li>
                </ul>
            `;

            // Insert before the first select element
            const firstSelect = filterControls.querySelector('select');
            if (firstSelect) {
                filterControls.insertBefore(sortDropdown, firstSelect.parentNode);
            } else {
                filterControls.prepend(sortDropdown);
            }

            // Add event listeners for sort options
            document.querySelectorAll('#customersSortDropdown + .dropdown-menu .sort-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sortValue = this.getAttribute('data-sort');
                    const [column, direction] = sortValue.split('-');

                    // Find the column index
                    let columnIndex = 0;
                    switch (column) {
                        case 'id': columnIndex = 0; break;
                        case 'company': columnIndex = 1; break;
                        case 'contact': columnIndex = 2; break;
                        case 'date': columnIndex = 6; break;
                        default: columnIndex = 0;
                    }

                    // Sort the table
                    sortTable(customersTable, columnIndex, direction);

                    // Update dropdown button text
                    document.getElementById('customersSortDropdown').innerHTML = `
                        <i class="bi bi-sort-${direction === 'asc' ? 'up-alt' : 'down-alt'}"></i> ${this.textContent}
                    `;
                });
            });
        }

        // Update the refresh button
        const refreshBtn = document.getElementById('refreshCustomersBtn');
        if (refreshBtn) {
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
            refreshBtn.className = 'btn btn-outline-primary';
        }
    }

    // Add enhanced search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase();
            const rows = customersTable.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });

            // Show a message if no results
            let noResultsRow = customersTable.querySelector('.no-results-row');
            if (searchText && Array.from(rows).every(row => row.style.display === 'none')) {
                if (!noResultsRow) {
                    noResultsRow = document.createElement('tr');
                    noResultsRow.className = 'no-results-row';
                    noResultsRow.innerHTML = `<td colspan="${customersTable.querySelectorAll('thead th').length}" class="text-center py-3">No matching customers found</td>`;
                    customersTable.querySelector('tbody').appendChild(noResultsRow);
                }
                noResultsRow.style.display = '';
            } else if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        });
    }
}

/**
 * Initialize enhanced controls for the deals table
 */
function initEnhancedDealsControls() {
    const dealsTable = document.getElementById('dealsTable');
    if (!dealsTable) {
        console.log('Deals table not found, skipping enhanced controls');
        return;
    }

    // Find the search input and controls container
    const searchInput = document.getElementById('dealsSearchInput');
    const controlsContainer = searchInput ? searchInput.closest('.row') : null;

    if (!controlsContainer) {
        console.log('Controls container not found, skipping enhanced controls');
        return;
    }

    // Update the search input with icon and styling
    if (searchInput) {
        const searchInputGroup = searchInput.closest('.input-group');
        if (searchInputGroup) {
            searchInputGroup.innerHTML = `
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" id="dealsSearchInput" placeholder="Search deals...">
                <button class="btn btn-outline-secondary" type="button" id="clearDealsSearch">
                    <i class="bi bi-x"></i>
                </button>
            `;

            // Add event listener for the clear button
            document.getElementById('clearDealsSearch').addEventListener('click', function() {
                document.getElementById('dealsSearchInput').value = '';
                document.getElementById('dealsSearchInput').dispatchEvent(new Event('input'));
            });
        }
    }

    // Update the filter controls
    const filterControls = controlsContainer.querySelector('.d-flex');
    if (filterControls) {
        // Add sort dropdown if it doesn't exist
        if (!document.getElementById('dealsSortDropdown')) {
            const sortDropdown = document.createElement('div');
            sortDropdown.className = 'dropdown me-2';
            sortDropdown.innerHTML = `
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="dealsSortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-sort-down"></i> Sort
                </button>
                <ul class="dropdown-menu" aria-labelledby="dealsSortDropdown">
                    <li><a class="dropdown-item sort-option" href="#" data-sort="id-asc">ID (A-Z)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="id-desc">ID (Z-A)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="name-asc">Deal Name (A-Z)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="name-desc">Deal Name (Z-A)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="value-asc">Value (Low to High)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="value-desc">Value (High to Low)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="date-asc">Closing Date (Earliest)</a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="date-desc">Closing Date (Latest)</a></li>
                </ul>
            `;

            // Insert before the first select element
            const firstSelect = filterControls.querySelector('select');
            if (firstSelect) {
                filterControls.insertBefore(sortDropdown, firstSelect.parentNode);
            } else {
                filterControls.prepend(sortDropdown);
            }

            // Add event listeners for sort options
            document.querySelectorAll('#dealsSortDropdown + .dropdown-menu .sort-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sortValue = this.getAttribute('data-sort');
                    const [column, direction] = sortValue.split('-');

                    // Find the column index
                    let columnIndex = 0;
                    switch (column) {
                        case 'id': columnIndex = 0; break;
                        case 'name': columnIndex = 1; break;
                        case 'value': columnIndex = 3; break;
                        case 'date': columnIndex = 6; break;
                        default: columnIndex = 0;
                    }

                    // Sort the table
                    sortTable(dealsTable, columnIndex, direction);

                    // Update dropdown button text
                    document.getElementById('dealsSortDropdown').innerHTML = `
                        <i class="bi bi-sort-${direction === 'asc' ? 'up-alt' : 'down-alt'}"></i> ${this.textContent}
                    `;
                });
            });
        }

        // Update the refresh button
        const refreshBtn = document.getElementById('refreshDealsBtn');
        if (refreshBtn) {
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
            refreshBtn.className = 'btn btn-outline-primary';
        }
    }

    // Add enhanced search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase();
            const rows = dealsTable.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });

            // Show a message if no results
            let noResultsRow = dealsTable.querySelector('.no-results-row');
            if (searchText && Array.from(rows).every(row => row.style.display === 'none')) {
                if (!noResultsRow) {
                    noResultsRow = document.createElement('tr');
                    noResultsRow.className = 'no-results-row';
                    noResultsRow.innerHTML = `<td colspan="${dealsTable.querySelectorAll('thead th').length}" class="text-center py-3">No matching deals found</td>`;
                    dealsTable.querySelector('tbody').appendChild(noResultsRow);
                }
                noResultsRow.style.display = '';
            } else if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        });
    }
}

/**
 * Sort a table by the specified column and direction
 * @param {HTMLElement} table - The table element
 * @param {number} columnIndex - The index of the column to sort by
 * @param {string} direction - The sort direction ('asc' or 'desc')
 */
function sortTable(table, columnIndex, direction) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr:not(.no-results-row)'));

    // Sort the rows
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();

        // Check if the values are numbers
        const aNum = parseFloat(aValue.replace(/[^0-9.-]+/g, ''));
        const bNum = parseFloat(bValue.replace(/[^0-9.-]+/g, ''));

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return direction === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // Check if the values are dates
        const aDate = new Date(aValue);
        const bDate = new Date(bValue);

        if (!isNaN(aDate) && !isNaN(bDate)) {
            return direction === 'asc' ? aDate - bDate : bDate - aDate;
        }

        // Otherwise sort as strings
        return direction === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
    });

    // Remove all rows
    rows.forEach(row => row.remove());

    // Add sorted rows
    rows.forEach(row => tbody.appendChild(row));

    // Show toast notification
    showToast(`Table sorted by ${table.querySelector('th:nth-child(' + (columnIndex + 1) + ')').textContent} (${direction === 'asc' ? 'ascending' : 'descending'})`);
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of toast (success, danger, warning, info)
 */
function showToast(message, type = 'success') {
    // Use the ISADataUtils.showToast function if available
    if (window.ISADataUtils && typeof window.ISADataUtils.showToast === 'function') {
        window.ISADataUtils.showToast(message, type);
        return;
    }

    // Otherwise create a toast manually
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) return;

    const toastId = 'toast-' + Date.now();

    // Determine icon and title based on type
    let icon, title;
    switch (type) {
        case 'success':
            icon = 'check-circle-fill text-success';
            title = 'Success';
            break;
        case 'danger':
        case 'error':
            icon = 'exclamation-circle-fill text-danger';
            title = 'Error';
            break;
        case 'warning':
            icon = 'exclamation-triangle-fill text-warning';
            title = 'Warning';
            break;
        case 'info':
            icon = 'info-circle-fill text-info';
            title = 'Information';
            break;
        default:
            icon = 'bell-fill text-primary';
            title = 'Notification';
    }

    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi bi-${icon} me-2"></i>
                <strong class="me-auto">${title}</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
    toast.show();

    toastElement.addEventListener('hidden.bs.toast', function () {
        toastElement.remove();
    });
}
