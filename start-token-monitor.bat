@echo off
echo ===================================
echo    ISA Suite - Google Token Monitor
echo ===================================
echo.
echo Starting Google token monitor in the background...
echo.
echo This script will monitor the Google token and refresh it automatically
echo before it expires. This ensures that Google integration features
echo continue to work without interruption.
echo.
echo The monitor will run in the background. You can close this window
echo after the monitor starts.
echo.
echo Press any key to start the monitor...
pause > nul

start /b cmd /c "node google-token-monitor.js > logs\google-token-monitor.log 2>&1"

echo.
echo Google token monitor started in the background.
echo Logs are being written to logs\google-token-monitor.log
echo.
echo You can close this window now.
echo.
pause
