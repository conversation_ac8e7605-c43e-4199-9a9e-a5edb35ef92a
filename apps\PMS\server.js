const express = require('express');
// const cors = require('cors');
const google = require('../../SharedFeatures/integrations/google');

const app = express();
const port = 3007;

// Configure middleware
// app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));

// Initialize Google API
google.initGoogleAPI().catch((err) => {
  console.error('Failed to initialize Google API', err);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Default route
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// Start server
app.listen(port, () => {
  console.log(`Project Management System running on http://localhost:${port}`);
  console.log('Connected to IntegrationHub');
});
