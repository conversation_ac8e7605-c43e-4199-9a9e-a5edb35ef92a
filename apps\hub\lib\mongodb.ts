import mongoose, { Collection, Document, UpdateWriteOpResult } from 'mongoose';
import fetch, { Response } from 'node-fetch';
import Redis from 'redis';
import { AppStatusCache } from './mongodb';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/isasuite';

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env');
}

interface GlobalWithMongoose {
  mongoose: any;
}

let cached = (global as unknown as GlobalWithMongoose).mongoose;

if (!cached) {
  cached = (global as unknown as GlobalWithMongoose).mongoose = { conn: null, promise: null };
}

export async function connectToDatabase(): Promise<{ conn: any; promise: any }> {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
    };

    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      return mongoose;
    });
  }

  try {
    cached.conn = await cached.promise;
  } catch (e) {
    cached.promise = null;
    throw e;
  }

  return cached.conn;
}
export interface AppStatusCache {
  status: 'online' | 'offline' | 'maintenance';
  lastChecked: string;
}
const redisClient = Redis.createClient({ url: process.env.REDIS_URL || 'redis://localhost:6379' });
/**
 * Updates the status of an application in the database and caches the result in Redis.
 *
 * @param {Object} params - The parameters for updating the application status.
 * @param {string} params.appName - The name of the application whose status is being updated.
 * @param {'online' | 'offline' | 'maintenance'} params.status - The new status of the application.
 * @returns {Promise<void>} A promise that resolves when the status update is complete.
 *
 * @throws Will log an error if the database or Redis operation fails.
 *
 * @remarks
 * - The function updates the `app_status` collection in the database.
 * - If the application does not exist in the collection, a new document is created (upsert).
 * - The status is also cached in Redis with a TTL of 60 seconds.
 */

export async function updateAppStatus({
  appName,
  status,
}: {
  appName: string;
  status: 'online' | 'offline' | 'maintenance';
}): Promise<void> {
  try {
    const connection = await connectToDatabase();
    const collection: Collection<Document> = connection.conn.collection('app_status');
    const result: UpdateWriteOpResult = await collection.updateOne(
      { name: appName },
      {
        $set: {
          status,
          lastChecked: new Date().toISOString(),
        },
      },
      { upsert: true },
    );

    // Update Redis cache
    const key = `app_status:${appName}`;
    redisClient.setex(key, 60, JSON.stringify({ status, lastChecked: new Date().toISOString() }));
  } catch (error) {
    console.error(`Error updating status for ${appName}:`, error);
  }
}

export async function checkAppStatus({
  appName,
  url,
}: {
  appName: string;
  url: string;
}): Promise<'online' | 'offline' | 'maintenance'> {
  try {
    // Check Redis cache first
    const cachedData = (await new Promise((resolve) => {
      redisClient.get(`app_status:${appName}`, (err: Error | null, data: string | null) => {
        if (err || !data) return resolve(null);
        resolve(JSON.parse(data) as AppStatusCache);
      });
    })) as AppStatusCache | null;

    if (cachedData) {
      const lastCheckedDate = new Date(cachedData.lastChecked);
      const now = Date.now();
      const timeDiff = now - lastCheckedDate.getTime();

      if (timeDiff < 60000 && cachedData.status) {
        return cachedData.status;
      }

      const freshCachedData = (await new Promise((resolve) => {
        redisClient.get(`app_status:${appName}`, (err: Error | null, data: string | null) => {
          resolve(err || !data ? null : (JSON.parse(data) as AppStatusCache));
        });
      })) as AppStatusCache | null;
      return freshCachedData?.status ?? 'offline';
    }

    // If no cache or expired, check the app
    const response: Response = await fetch(url, { timeout: 5000 });
    const status = response.ok ? 'online' : 'maintenance';
    await updateAppStatus({ appName, status });
    return status;
  } catch (error) {
    console.error(`Error checking status for ${appName}:`, error);
    await updateAppStatus({ appName, status: 'offline' });
    return 'offline';
  }
}
/**
 * Monitors the status of all applications by checking their availability.
 *
 * This function retrieves a list of applications with their respective names and URLs,
 * then asynchronously checks the status of each application using the `checkAppStatus` function.
 *
 * The URLs for the applications are expected to be provided via environment variables:
 * - `BI_URL` for Business Intelligence
 * - `BMS_URL` for Business Management
 * - `CRM_URL` for Customer Relationship
 * - `PMS_URL` for Project Management
 * - `TMS_URL` for Task Management
 * - `SCM_URL` for Supply Chain
 * - `PDM_URL` for Product Data
 *
 * @returns A promise that resolves when the status of all applications has been checked.
 */

export async function monitorAllApps(): Promise<void> {
  const apps = [
    { name: 'Business Intelligence', url: process.env.BI_URL },
    { name: 'Business Management', url: process.env.BMS_URL },
    { name: 'Customer Relationship', url: process.env.CRM_URL },
    { name: 'Project Management', url: process.env.PMS_URL },
    { name: 'Task Management', url: process.env.TMS_URL },
    { name: 'Supply Chain', url: process.env.SCM_URL },
    { name: 'Product Data', url: process.env.PDM_URL },
  ];

  await Promise.all(apps.map((app) => checkAppStatus({ appName: app.name, url: app.url })));
}
redisClient.on('error', (err: any) => {
  console.error('Redis error:', err);
});
redisClient.connect().then(() => {
  console.log('Connected to Redis');
});
