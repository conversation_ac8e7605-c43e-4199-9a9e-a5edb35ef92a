<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Integration Test - All ISA Suite Applications</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-app-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-result.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-result.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-result.info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .app-header { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; }
        .app-color-indicator { width: 20px; height: 20px; border-radius: 50%; }
        .console-output { 
            background-color: #f8f9fa; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            padding: 15px; 
            font-family: monospace; 
            font-size: 12px; 
            max-height: 300px; 
            overflow-y: auto; 
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">
            <i class="bi bi-envelope-check"></i>
            Gmail Integration Test - All ISA Suite Applications
        </h1>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i>
                            System Status & Global Test Controls
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="system-status" class="mb-3"></div>
                        <div class="d-flex gap-2 mb-3">
                            <button id="run-all-tests" class="btn btn-success">
                                <i class="bi bi-play-circle"></i> Run All Tests
                            </button>
                            <button id="clear-results" class="btn btn-secondary">
                                <i class="bi bi-trash"></i> Clear Results
                            </button>
                            <button id="download-report" class="btn btn-info">
                                <i class="bi bi-download"></i> Download Report
                            </button>
                        </div>
                        <div id="global-results" class="console-output"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <h3>Individual Application Tests</h3>
                <div id="app-tests-container"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="./SharedFeatures/ui/gmail-integration.js"></script>
    
    <script>
        // Application configurations
        const applications = [
            { name: 'APM', prefix: 'apm', color: '#3498db', description: 'Asset Performance Management' },
            { name: 'APS', prefix: 'aps', color: '#1abc9c', description: 'Advanced Planning & Scheduling' },
            { name: 'BMS', prefix: 'bms', color: '#00acc1', description: 'Business Management System' },
            { name: 'CRM', prefix: 'crm', color: '#e67e22', description: 'Customer Relationship Management' },
            { name: 'MRP', prefix: 'mrp', color: '#9c27b0', description: 'Material Requirements Planning' },
            { name: 'PMS', prefix: 'pms', color: '#e91e63', description: 'Project Management System' },
            { name: 'SCM', prefix: 'scm', color: '#6a3de8', description: 'Supply Chain Management' },
            { name: 'TM', prefix: 'tm', color: '#6c5ce7', description: 'Task Management' },
            { name: 'WMS', prefix: 'wms', color: '#2ecc71', description: 'Warehouse Management System' }
        ];

        let testResults = {};
        let globalLog = [];

        // Initialize the test interface
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            createAppTestCards();
            setupEventListeners();
        });

        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            let status = '<h6>System Dependencies:</h6>';
            
            // Check if Gmail integration is loaded
            if (typeof GmailIntegration !== 'undefined') {
                status += '<div class="test-result success">✅ GmailIntegration class is loaded</div>';
            } else {
                status += '<div class="test-result error">❌ GmailIntegration class is NOT loaded</div>';
            }
            
            // Check if initializeGmail function is available
            if (typeof initializeGmail !== 'undefined') {
                status += '<div class="test-result success">✅ initializeGmail function is available</div>';
            } else {
                status += '<div class="test-result error">❌ initializeGmail function is NOT available</div>';
            }
            
            // Check Bootstrap
            if (typeof bootstrap !== 'undefined') {
                status += '<div class="test-result success">✅ Bootstrap is loaded</div>';
            } else {
                status += '<div class="test-result error">❌ Bootstrap is NOT loaded</div>';
            }
            
            statusDiv.innerHTML = status;
        }

        function createAppTestCards() {
            const container = document.getElementById('app-tests-container');
            
            applications.forEach(app => {
                const card = document.createElement('div');
                card.className = 'test-app-card';
                card.innerHTML = `
                    <div class="app-header">
                        <div class="app-color-indicator" style="background-color: ${app.color}"></div>
                        <h5>${app.name}</h5>
                        <span class="badge bg-secondary">${app.description}</span>
                        <button class="btn btn-sm btn-primary ms-auto" onclick="testSingleApp('${app.prefix}')">
                            <i class="bi bi-play"></i> Test
                        </button>
                    </div>
                    <div id="results-${app.prefix}" class="test-results"></div>
                    <div class="mt-3">
                        <button id="gmail-btn-${app.prefix}" class="btn btn-outline-primary" onclick="openGmailForApp('${app.prefix}')" style="display: none;">
                            <i class="bi bi-envelope"></i> Test Gmail Modal
                        </button>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        function setupEventListeners() {
            document.getElementById('run-all-tests').addEventListener('click', runAllTests);
            document.getElementById('clear-results').addEventListener('click', clearAllResults);
            document.getElementById('download-report').addEventListener('click', downloadReport);
        }

        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            globalLog.push(`[${timestamp}] ${message}`);
            
            const globalResults = document.getElementById('global-results');
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#721c24' : type === 'success' ? '#155724' : '#0c5460';
            globalResults.appendChild(logEntry);
            globalResults.scrollTop = globalResults.scrollHeight;
        }

        function testSingleApp(appPrefix) {
            const app = applications.find(a => a.prefix === appPrefix);
            if (!app) return;

            logMessage(`Testing ${app.name} application...`);
            
            const resultsDiv = document.getElementById(`results-${appPrefix}`);
            resultsDiv.innerHTML = '<div class="test-result info">🔄 Running tests...</div>';
            
            try {
                // Test configuration
                const config = {
                    appName: app.name,
                    appPrefix: app.prefix,
                    appColor: app.color,
                    modalId: `${app.prefix}-gmailModal`,
                    triggerId: `gmail-btn-${app.prefix}`,
                    debug: true
                };
                
                // Test initialization
                if (typeof initializeGmail === 'function') {
                    const gmail = initializeGmail(config);
                    
                    if (gmail) {
                        resultsDiv.innerHTML = `
                            <div class="test-result success">✅ Gmail integration initialized successfully</div>
                            <div class="test-result info">📧 Modal ID: ${config.modalId}</div>
                            <div class="test-result info">🎨 Theme Color: ${app.color}</div>
                        `;
                        
                        // Show the test button
                        document.getElementById(`gmail-btn-${appPrefix}`).style.display = 'inline-block';
                        testResults[appPrefix] = { success: true, config: config };
                        logMessage(`${app.name}: Successfully initialized`, 'success');
                    } else {
                        resultsDiv.innerHTML = '<div class="test-result error">❌ Gmail integration failed to initialize</div>';
                        testResults[appPrefix] = { success: false, error: 'Initialization failed' };
                        logMessage(`${app.name}: Initialization failed`, 'error');
                    }
                } else {
                    resultsDiv.innerHTML = '<div class="test-result error">❌ initializeGmail function not available</div>';
                    testResults[appPrefix] = { success: false, error: 'Function not available' };
                    logMessage(`${app.name}: initializeGmail function not available`, 'error');
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ Error: ${error.message}</div>`;
                testResults[appPrefix] = { success: false, error: error.message };
                logMessage(`${app.name}: Error - ${error.message}`, 'error');
            }
        }

        function openGmailForApp(appPrefix) {
            const modalId = `${appPrefix}-gmailModal`;
            const modal = document.getElementById(modalId);
            
            if (modal) {
                try {
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                    logMessage(`${appPrefix.toUpperCase()}: Gmail modal opened successfully`, 'success');
                } catch (error) {
                    logMessage(`${appPrefix.toUpperCase()}: Error opening modal - ${error.message}`, 'error');
                }
            } else {
                logMessage(`${appPrefix.toUpperCase()}: Modal ${modalId} not found in DOM`, 'error');
            }
        }

        function runAllTests() {
            logMessage('Starting comprehensive Gmail integration tests...', 'info');
            clearAllResults();
            
            applications.forEach((app, index) => {
                setTimeout(() => {
                    testSingleApp(app.prefix);
                    
                    // Show summary after all tests complete
                    if (index === applications.length - 1) {
                        setTimeout(showTestSummary, 1000);
                    }
                }, index * 500);
            });
        }

        function showTestSummary() {
            const passed = Object.values(testResults).filter(r => r.success).length;
            const total = Object.keys(testResults).length;
            
            logMessage(`\n📊 Test Summary: ${passed}/${total} applications passed`, passed === total ? 'success' : 'error');
            logMessage(`Success Rate: ${((passed/total) * 100).toFixed(1)}%`, 'info');
            
            if (passed === total) {
                logMessage('🎉 All applications are using the enhanced Gmail integration correctly!', 'success');
            } else {
                logMessage('⚠️ Some applications need attention', 'error');
            }
        }

        function clearAllResults() {
            applications.forEach(app => {
                const resultsDiv = document.getElementById(`results-${app.prefix}`);
                if (resultsDiv) resultsDiv.innerHTML = '';
                
                const testBtn = document.getElementById(`gmail-btn-${app.prefix}`);
                if (testBtn) testBtn.style.display = 'none';
            });
            
            document.getElementById('global-results').innerHTML = '';
            testResults = {};
            globalLog = [];
        }

        function downloadReport() {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    totalApps: applications.length,
                    passedTests: Object.values(testResults).filter(r => r.success).length,
                    failedTests: Object.values(testResults).filter(r => !r.success).length
                },
                applications: applications.map(app => ({
                    name: app.name,
                    prefix: app.prefix,
                    color: app.color,
                    testResult: testResults[app.prefix] || { success: false, error: 'Not tested' }
                })),
                log: globalLog
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `gmail-integration-test-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
