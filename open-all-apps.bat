@echo off
setlocal enabledelayedexpansion

REM Set colors for console output
set "GREEN=0A"
set "YELLOW=0E"
set "RED=0C"
set "WHITE=0F"
set "CYAN=0B"

echo ===================================
color %CYAN%
echo    ISA Suite - Open All Applications
color %WHITE%
echo ===================================
echo.
echo This script will open all ISA Suite applications in your default browser.
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause > nul

REM Define application information - name|port
set "apps[1]=Integration Hub|8000"
set "apps[2]=Business Management System|3001"
set "apps[3]=Materials Requirements Planning|3002"
set "apps[4]=Customer Relationship Management|3003"
set "apps[5]=Warehouse Management System|3004"
set "apps[6]=Advanced Planning and Scheduling|3005"
set "apps[7]=Asset Performance Management|3006"
set "apps[8]=Project Management System|3007"
set "apps[9]=Supply Chain Management|3008"
set "apps[10]=Task Management System|3009"

REM Open each application in the browser
for /L %%i in (1,1,10) do (
    for /F "tokens=1-2 delims=|" %%a in ("!apps[%%i]!") do (
        echo Opening %%a (http://localhost:%%b)...
        start "" http://localhost:%%b

        REM Small delay between opening apps
        timeout /t 1 > nul
    )
)

echo.
echo All applications have been opened in your browser.
echo.
pause
