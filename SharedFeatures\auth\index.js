// Authentication Module

const jwt = require('jsonwebtoken');

// Secret key for JWT (in a real app, this would be in environment variables)
const JWT_SECRET = 'your-secret-key';

/**
 * Authenticate middleware
 */
function authenticate(req, res, next) {
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ status: 'error', message: 'Authentication required' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ status: 'error', message: 'Invalid token' });
  }
}

/**
 * Generate JWT token
 */
function generateToken(user) {
  return jwt.sign(user, JWT_SECRET, { expiresIn: '1d' });
}

/**
 * Verify JWT token
 */
function verifyToken(token) {
  return jwt.verify(token, JWT_SECRET);
}

/**
 * Login function
 */
function login(username, password) {
  // In a real app, this would check against a database
  if (username === 'admin' && password === 'password') {
    const user = { id: 1, username: 'admin', role: 'admin' };
    const token = generateToken(user);
    return { user, token };
  }

  if (username === 'user' && password === 'password') {
    const user = { id: 2, username: 'user', role: 'user' };
    const token = generateToken(user);
    return { user, token };
  }

  return null;
}

module.exports = {
  authenticate,
  generateToken,
  verifyToken,
  login,
};
