const express = require('express');
const path = require('path');
const cors = require('cors');

// Try to load shared features
let sharedFeatures;
try {
  sharedFeatures = require('../../SharedFeatures');
  console.log('Shared features loaded successfully');
} catch (error) {
  console.log('Shared features not available, continuing without them');
}

// Create Express app
const app = express();

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Sample data for the dashboard
const dashboardData = {
  metrics: {
    revenue: 125000,
    expenses: 78000,
    profit: 47000,
    employees: 42
  },
  financialData: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    revenue: [95000, 110000, 105000, 115000, 120000, 125000],
    expenses: [70000, 75000, 72000, 80000, 82000, 78000]
  },
  budgetAllocation: {
    labels: ['Operations', 'Marketing', 'R&D', 'HR', 'IT', 'Admin'],
    data: [35, 20, 15, 10, 12, 8]
  },
  transactions: [
    { id: 1001, date: '2025-04-28', description: 'Client Payment - ABC Corp', category: 'Income', amount: 12500, status: 'Completed' },
    { id: 1002, date: '2025-04-27', description: 'Office Supplies', category: 'Expense', amount: -850.75, status: 'Completed' },
    { id: 1003, date: '2025-04-26', description: 'Monthly Rent', category: 'Expense', amount: -3500, status: 'Completed' },
    { id: 1004, date: '2025-04-25', description: 'Client Payment - XYZ Ltd', category: 'Income', amount: 8750, status: 'Completed' },
    { id: 1005, date: '2025-04-25', description: 'Employee Salaries', category: 'Expense', amount: -32450, status: 'Pending' }
  ],
  notifications: [
    { id: 1, title: 'Invoice Payment Overdue', message: 'Invoice #INV-2025-042 for client DEF Industries is 15 days overdue.', timestamp: '2025-04-28 09:15:22', priority: 'High' },
    { id: 2, title: 'Expense Approval Required', message: 'New expense report submitted by John Smith requires your approval.', timestamp: '2025-04-27 14:30:45', priority: 'Medium' },
    { id: 3, title: 'New Client Added', message: 'GHI Corporation has been added as a new client by Sarah Johnson.', timestamp: '2025-04-26 11:20:33', priority: 'Low' }
  ],
  events: [
    { id: 1, title: 'Board Meeting', date: 'Tomorrow, 10:00 AM', description: 'Quarterly board meeting to discuss financial performance.' },
    { id: 2, title: 'Client Presentation', date: 'May 2, 2:00 PM', description: 'Presentation of new product features to ABC Corp.' },
    { id: 3, title: 'Tax Filing Deadline', date: 'May 15', description: 'Deadline for quarterly tax filing.' }
  ]
};

// Define routes
app.get('/', (req, res) => {
  res.sendFile('dashboard.html', { root: __dirname + '/public' });
});

// Legacy route for the old index.html
app.get('/old', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// API endpoint for dashboard data
app.get('/api/dashboard', (req, res) => {
  res.json({ status: 'success', data: dashboardData });
});

// API endpoint for business data
app.get('/api/business-data', (req, res) => {
  res.json({
    status: 'success',
    data: {
      financials: {
        revenue: 125000,
        expenses: 78000,
        profit: 47000,
        margin: 37.6
      },
      operations: {
        employees: 42,
        departments: 6,
        locations: 3
      }
    }
  });
});

// API endpoint for application mode
app.get('/api/app-mode', (req, res) => {
  const modes = ['production', 'sandbox', 'demo'];
  const currentMode = modes[0]; // Default to production

  res.json({ status: 'success', data: { mode: currentMode } });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Start server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Business Management System running on http://localhost:${PORT}`);
});
