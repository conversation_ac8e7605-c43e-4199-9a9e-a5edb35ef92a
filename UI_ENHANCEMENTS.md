# ISA Suite - UI Enhancements

This document explains the UI enhancements that have been applied to the ISA Suite applications.

## Enhanced Applications

The following applications have been enhanced with consistent UI elements:

1. **Project Management System (PMS)**:
   - Updated color scheme to use purple as the primary color
   - Enhanced sidebar and top banner styling
   - Made dashboard cards more interactive with hover effects
   - Updated all buttons to use the primary color
   - Added consistent styling for all UI elements

2. **Warehouse Management System (WMS)**:
   - Updated color scheme to use green as the primary color
   - Enhanced sidebar and top banner styling
   - Made dashboard cards more interactive with hover effects
   - Updated all buttons to use the primary color
   - Added consistent styling for all UI elements

## Color Schemes

Each application in the ISA Suite now uses a consistent color scheme:

- **Integration Hub**: Blue (#3498db)
- **Business Management System (BMS)**: Orange (#e67e22)
- **Materials Requirements Planning (MRP)**: Teal (#1abc9c)
- **Customer Relationship Management (CRM)**: Red (#e74c3c)
- **Warehouse Management System (WMS)**: Green (#2ecc71)
- **Advanced Planning and Scheduling (APS)**: Yellow (#f1c40f)
- **Asset Performance Management (APM)**: Blue-Gray (#34495e)
- **Project Management System (PMS)**: Purple (#9b59b6)
- **Supply Chain Management (SCM)**: Light Blue (#3498db)
- **Task Management System (TM)**: Dark Blue (#2980b9)

## UI Components

All applications now have the following consistent UI components:

1. **Top Banner**:
   - Fixed position at the top of the page
   - Uses the application's primary color
   - Includes the application name and notification icons
   - Responsive design for mobile devices

2. **Sidebar**:
   - Fixed position on the left side of the page
   - Uses the application's primary color
   - Includes navigation links with icons
   - Collapsible on mobile devices

3. **Dashboard Cards**:
   - Interactive cards with hover effects
   - Consistent styling across all applications
   - Uses the application's primary color for key elements

4. **Buttons**:
   - Primary buttons use the application's primary color
   - Consistent styling for all button types
   - Interactive hover effects

5. **AI Insights Section**:
   - Consistent styling across all applications
   - Interactive elements for expanding/collapsing sections
   - Refresh and export functionality

## How to Use

### Starting Applications
1. Run `single-window-launcher.bat`
2. Select the desired mode (Production, Sandbox/Training, or Demo)
3. All applications will start in the background without opening individual terminal windows
4. The Integration Hub will open in your browser automatically

### Checking Application Status
1. Run `check-status.bat`
2. The script will display the status of all applications (online or offline)

### Opening Applications in Browser
1. Run `open-apps.bat`
2. All applications will open in your default browser

### Stopping Applications
1. Press any key in the launcher window to stop all applications
   OR
2. Run `stop-apps.bat` to stop all applications

## Application URLs

Once the applications are running, you can access them through the Integration Hub or directly using the following URLs:

- Integration Hub: http://localhost:8000
- Business Management System: http://localhost:3001
- Materials Requirements Planning: http://localhost:3002
- Customer Relationship Management: http://localhost:3003
- Warehouse Management System: http://localhost:3004
- Advanced Planning and Scheduling: http://localhost:3005
- Asset Performance Management: http://localhost:3006
- Project Management System: http://localhost:3007
- Supply Chain Management: http://localhost:3008
- Task Management System: http://localhost:3009

## Troubleshooting

If you encounter issues with the UI:

1. Clear your browser cache
2. Try accessing the application in a different browser
3. Check the browser console for any JavaScript errors
4. Make sure all applications are running correctly (use `check-status.bat`)
5. Check the log files in the `logs` directory for any server-side errors
