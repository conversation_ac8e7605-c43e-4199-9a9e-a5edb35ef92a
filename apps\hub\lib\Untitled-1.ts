import { connectToDatabase } from './lib/mongodb';
import Redis from 'redis';
import { Collection, Document } from 'mongoose';
import fetch from 'node-fetch';

// Create a Redis client
const redisClient = Redis.createClient({ url: process.env.REDIS_URL });

interface AppStatusCache {
  // Define AppStatusCache interface
  status: 'online' | 'offline' | 'maintenance';
  lastChecked: string;
}

export async function updateAppStatus(
  appName: string,
  status: 'online' | 'offline' | 'maintenance',
) {
  try {
    const connection = await connectToDatabase();
    interface AppStatusDocument {
      name: string;
      status: 'online' | 'offline' | 'maintenance';
      lastChecked: string;
    }
    const collection: Collection<AppStatusDocument> = connection.conn.collection('app_status');
    await collection.updateOne(
      { name: appName },
      {
        $set: {
          status,
          lastChecked: new Date().toISOString(),
        },
      },
      { upsert: true },
    );

    // Update Redis cache
    const key = `app_status:${appName}`;
    redisClient.setex(key, 60, JSON.stringify({ status, lastChecked: new Date().toISOString() }));
  } catch (error) {
    console.error(`Error updating status for ${appName}:`, error);
  }
}

export async function checkAppStatus(
  appName: string,
  url: string,
): Promise<'online' | 'offline' | 'maintenance'> {
  try {
    // Check Redis cache first
    const cachedStatus = (await new Promise((resolve) => {
      redisClient.get(`app_status:${appName}`, (err: Error | null, data: string | null) => {
        if (err || !data) {
          return resolve(null);
        }
        const parsedData = JSON.parse(data) as AppStatusCache;
        return resolve(parsedData);
      });
    })) as AppStatusCache | null;

    if (cachedStatus && Date.now() - new Date(cachedStatus.lastChecked).getTime() < 60000) {
      return cachedStatus.status as 'online' | 'offline' | 'maintenance';
    }

    // If no cache or expired, check the app
    const response = await fetch(url, { timeout: 5000 });
    const status = response.ok ? 'online' : 'maintenance';
    await updateAppStatus(appName, status);
    return status;
  } catch (error) {
    console.error(`Error checking status for ${appName}:`, error);
    await updateAppStatus(appName, 'offline');
    return 'offline';
  }
}

export async function monitorAllApps() {
  const apps = [
    { name: 'Business Intelligence', url: process.env.BI_URL },
    { name: 'Business Management', url: process.env.BMS_URL },
    { name: 'Customer Relationship', url: process.env.CRM_URL },
    { name: 'Project Management', url: process.env.PMS_URL },
    { name: 'Task Management', url: process.env.TMS_URL },
    { name: 'Supply Chain', url: process.env.SCM_URL },
    { name: 'Product Data', url: process.env.PDM_URL },
  ];

  await Promise.all(apps.map((app) => checkAppStatus(app.name, app.url)));
}

redisClient.on('error', (err: any) => {
  console.error('Redis error:', err);
});
redisClient.connect().then(() => {
  console.log('Connected to Redis');
});
