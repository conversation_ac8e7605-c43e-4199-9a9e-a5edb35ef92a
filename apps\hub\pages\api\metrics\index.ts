import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/mongodb';
import Redis from 'redis';

const redisClient = Redis.createClient({ url: process.env.REDIS_URL });

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { db } = await connectToDatabase();

    // Get metrics from Redis cache
    const metrics = await new Promise((resolve, reject) => {
      redisClient.get('system_metrics', (err, data) => {
        if (err) reject(err);
        resolve(data ? JSON.parse(data) : null);
      });
    });

    if (metrics) {
      return res.status(200).json(metrics);
    }

    // If no cached metrics, calculate new ones
    const [transactions, users] = await Promise.all([
      db.collection('transactions').countDocuments(),
      db.collection('sessions').countDocuments({ expires: { $gt: new Date() } }),
    ]);

    const successRate = await db
      .collection('transactions')
      .aggregate([
        {
          $group: {
            _id: null,
            successRate: { $avg: { $cond: ['$success', 1, 0] } },
          },
        },
      ])
      .toArray();

    const responseTime = await db
      .collection('transactions')
      .aggregate([
        {
          $group: {
            _id: null,
            avgResponseTime: { $avg: '$responseTime' },
          },
        },
      ])
      .toArray();

    const newMetrics = {
      totalTransactions: transactions,
      successRate: (successRate[0]?.successRate || 0) * 100,
      averageResponseTime: Math.round(responseTime[0]?.avgResponseTime || 0),
      activeUsers: users,
    };

    // Cache the metrics for 30 seconds
    redisClient.setex('system_metrics', 30, JSON.stringify(newMetrics));

    res.status(200).json(newMetrics);
  } catch (error) {
    console.error('Error fetching metrics:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
