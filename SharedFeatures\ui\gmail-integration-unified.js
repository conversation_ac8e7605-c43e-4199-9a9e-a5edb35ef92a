/**
 * Unified Gmail Integration for ISA Suite
 *
 * This is the single, authoritative Gmail integration system that replaces
 * all previous conflicting implementations. It provides:
 * - Working Gmail modal with email viewing
 * - Reply and forward functionality
 * - Attachment support
 * - Search, sort, and filter capabilities
 * - Consistent behavior across all applications
 */

class UnifiedGmailIntegration {
    constructor(config = {}) {
        this.config = {
            appName: config.appName || 'ISA Suite',
            appPrefix: config.appPrefix || 'app',
            appColor: config.appColor || '#007bff',
            modalId: config.modalId || 'gmailModal',
            triggerId: config.triggerId || 'gmail-link',
            debug: config.debug || false,
            ...config
        };

        this.initialized = false;
        this.currentSort = 'date-desc';
        this.currentFilter = 'all';
        this.searchTerm = '';
        this.emails = this.generateSampleEmails();
    }

    /**
     * Initialize the Gmail integration
     */
    init() {
        if (this.initialized) {
            this.log('Gmail integration already initialized');
            return;
        }

        this.log('Initializing Unified Gmail integration');

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    /**
     * Setup the Gmail integration
     */
    setup() {
        try {
            // Create the modal if it doesn't exist
            this.createGmailModal();

            // Setup event listeners
            this.setupEventListeners();

            // Setup sidebar link
            this.setupSidebarLink();

            // Setup dashboard buttons
            this.setupDashboardButtons();

            this.initialized = true;
            this.log('Unified Gmail integration initialized successfully');

            // Make globally available
            window.unifiedGmail = this;

        } catch (error) {
            console.error('Error setting up Gmail integration:', error);
        }
    }

    /**
     * Create the Gmail modal
     */
    createGmailModal() {
        // Remove any existing modal
        const existingModal = document.getElementById(this.config.modalId);
        if (existingModal) {
            existingModal.remove();
        }

        const modalHtml = `
        <div class="modal fade" id="${this.config.modalId}" tabindex="-1" aria-labelledby="${this.config.modalId}Label" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: ${this.config.appColor}; color: white;">
                        <h5 class="modal-title" id="${this.config.modalId}Label">
                            <i class="bi bi-envelope-fill me-2"></i>Gmail - ${this.config.appName}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <!-- Gmail Tabs -->
                        <ul class="nav nav-tabs" id="${this.config.appPrefix}-gmail-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="${this.config.appPrefix}-inbox-tab" data-bs-toggle="tab"
                                        data-bs-target="#${this.config.appPrefix}-inbox" type="button" role="tab">
                                    <i class="bi bi-inbox me-2"></i>Inbox
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="${this.config.appPrefix}-compose-tab" data-bs-toggle="tab"
                                        data-bs-target="#${this.config.appPrefix}-compose" type="button" role="tab">
                                    <i class="bi bi-pencil me-2"></i>Compose
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="${this.config.appPrefix}-read-tab" data-bs-toggle="tab"
                                        data-bs-target="#${this.config.appPrefix}-read" type="button" role="tab">
                                    <i class="bi bi-envelope-open me-2"></i>Read
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="${this.config.appPrefix}-gmail-tab-content">
                            <!-- Inbox Tab -->
                            <div class="tab-pane fade show active" id="${this.config.appPrefix}-inbox" role="tabpanel">
                                <div class="p-3">
                                    <!-- Search and Controls -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                                <input type="text" class="form-control" id="${this.config.appPrefix}-email-search"
                                                       placeholder="Search emails...">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="${this.config.appPrefix}-email-sort" class="form-label visually-hidden">Sort emails</label>
                                            <select class="form-select" id="${this.config.appPrefix}-email-sort" title="Sort emails" aria-label="Sort emails">
                                                <option value="date-desc">Newest First</option>
                                                <option value="date-asc">Oldest First</option>
                                                <option value="sender">By Sender</option>
                                                <option value="subject">By Subject</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="${this.config.appPrefix}-email-filter" class="form-label visually-hidden">Filter emails</label>
                                            <select class="form-select" id="${this.config.appPrefix}-email-filter" title="Filter emails" aria-label="Filter emails">
                                                <option value="all">All Emails</option>
                                                <option value="unread">Unread</option>
                                                <option value="important">Important</option>
                                                <option value="starred">Starred</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Email List -->
                                    <div id="${this.config.appPrefix}-email-list" class="list-group">
                                        <!-- Emails will be populated here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Compose Tab -->
                            <div class="tab-pane fade" id="${this.config.appPrefix}-compose" role="tabpanel">
                                <div class="p-3">
                                    <form id="${this.config.appPrefix}-compose-form">
                                        <div class="mb-3">
                                            <label for="${this.config.appPrefix}-email-to" class="form-label">To:</label>
                                            <input type="email" class="form-control" id="${this.config.appPrefix}-email-to" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="${this.config.appPrefix}-email-subject" class="form-label">Subject:</label>
                                            <input type="text" class="form-control" id="${this.config.appPrefix}-email-subject" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="${this.config.appPrefix}-email-body" class="form-label">Message:</label>
                                            <textarea class="form-control" id="${this.config.appPrefix}-email-body" rows="10" required></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="${this.config.appPrefix}-email-attachments" class="form-label">Attachments:</label>
                                            <input type="file" class="form-control" id="${this.config.appPrefix}-email-attachments" multiple>
                                            <div class="form-text">You can attach multiple files. Supported formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, JPG, PNG, GIF</div>
                                            <div id="${this.config.appPrefix}-attachment-preview" class="mt-2"></div>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-send me-2"></i>Send
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" id="${this.config.appPrefix}-save-draft">
                                                <i class="bi bi-save me-2"></i>Save Draft
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" id="${this.config.appPrefix}-clear-compose">
                                                <i class="bi bi-trash me-2"></i>Clear
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Read Tab -->
                            <div class="tab-pane fade" id="${this.config.appPrefix}-read" role="tabpanel">
                                <div class="p-3" id="${this.config.appPrefix}-read-content">
                                    <div class="text-center text-muted">
                                        <i class="bi bi-envelope-open display-4"></i>
                                        <p class="mt-3">Select an email to read</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <a href="https://mail.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Gmail</a>
                    </div>
                </div>
            </div>
        </div>`;

        // Add modal to document
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Populate inbox
        this.populateInbox();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById(`${this.config.appPrefix}-email-search`);
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchEmails(e.target.value);
            });
        }

        // Sort functionality
        const sortSelect = document.getElementById(`${this.config.appPrefix}-email-sort`);
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortEmails(e.target.value);
            });
        }

        // Filter functionality
        const filterSelect = document.getElementById(`${this.config.appPrefix}-email-filter`);
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                this.filterEmails(e.target.value);
            });
        }

        // Compose form
        const composeForm = document.getElementById(`${this.config.appPrefix}-compose-form`);
        if (composeForm) {
            composeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.sendEmail();
            });
        }

        // Save draft button
        const saveDraftBtn = document.getElementById(`${this.config.appPrefix}-save-draft`);
        if (saveDraftBtn) {
            saveDraftBtn.addEventListener('click', () => this.saveDraft());
        }

        // Clear compose button
        const clearBtn = document.getElementById(`${this.config.appPrefix}-clear-compose`);
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearCompose());
        }

        // Attachment preview functionality
        const attachmentInput = document.getElementById(`${this.config.appPrefix}-email-attachments`);
        if (attachmentInput) {
            attachmentInput.addEventListener('change', (e) => this.previewAttachments(e));
        }
    }

    /**
     * Setup sidebar Gmail link
     */
    setupSidebarLink() {
        const gmailLink = document.getElementById(this.config.triggerId);
        if (gmailLink) {
            // Remove any existing event listeners
            const newLink = gmailLink.cloneNode(true);
            gmailLink.parentNode.replaceChild(newLink, gmailLink);

            // Add our event listener
            newLink.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.openGmailModal();
            });

            this.log('Sidebar Gmail link configured');
        }
    }

    /**
     * Setup dashboard Gmail buttons
     */
    setupDashboardButtons() {
        // Look for various Gmail button selectors
        const selectors = [
            '#open-gmail-btn',
            '.open-gmail',
            '[data-gmail="open"]',
            '.gmail-button'
        ];

        selectors.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(button => {
                // Remove existing listeners
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);

                // Add our listener
                newButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.openGmailModal();
                });
            });
        });
    }

    /**
     * Open Gmail modal
     */
    openGmailModal() {
        this.log('Opening Gmail modal');

        const modal = document.getElementById(this.config.modalId);
        if (modal) {
            try {
                const bootstrapModal = new bootstrap.Modal(modal);
                bootstrapModal.show();

                // Refresh inbox when opened
                this.populateInbox();

            } catch (error) {
                console.error('Error opening Gmail modal:', error);
                // Fallback: show modal manually
                modal.style.display = 'block';
                modal.classList.add('show');
                document.body.classList.add('modal-open');
            }
        }
    }

    /**
     * Generate sample emails
     */
    generateSampleEmails() {
        return [
            {
                id: 1,
                sender: '<EMAIL>',
                senderName: 'John Davis',
                subject: 'Meeting follow-up: Product demo feedback',
                preview: 'Thank you for the product demonstration yesterday. Our team was impressed...',
                date: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
                read: false,
                important: true,
                starred: false,
                attachments: [
                    { name: 'Product_Demo_Feedback.pdf', size: '245 KB', type: 'application/pdf' },
                    { name: 'Requirements_List.docx', size: '89 KB', type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }
                ],
                body: `Hello,

Thank you for the product demonstration yesterday. Our team was impressed with the features and capabilities of your CRM system.

We particularly liked the AI-powered analytics and the integration capabilities with our existing tools. The user interface is also very intuitive and should require minimal training for our team.

We have a few questions about customization options and pricing tiers that we'd like to discuss further. Would you be available for a follow-up call next week?

Please find attached our detailed feedback and requirements list for your review.

Best regards,
John Davis
Product Manager
ABC Corporation`
            },
            {
                id: 2,
                sender: '<EMAIL>',
                senderName: 'Sarah Miller',
                subject: 'Contract renewal discussion',
                preview: 'I would like to schedule a call to discuss the upcoming contract renewal...',
                date: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
                read: false,
                important: false,
                starred: true,
                attachments: [
                    { name: 'Current_Contract.pdf', size: '156 KB', type: 'application/pdf' }
                ],
                body: `Hi there,

I'd like to schedule a call to discuss the upcoming contract renewal for our enterprise subscription.

Our current contract expires in 45 days, and we're interested in exploring some of the new features you've added since our last renewal. We're also considering expanding the number of user licenses.

Could you please provide some available times next week for a discussion? Also, it would be helpful if you could send over the current pricing structure for enterprise plans.

I've attached our current contract for reference.

Thank you,
Sarah Miller
Operations Director
XYZ Company`
            },
            {
                id: 3,
                sender: '<EMAIL>',
                senderName: 'Alex Chen',
                subject: 'New customer inquiry about enterprise plan',
                preview: 'We are interested in learning more about your enterprise plan options...',
                date: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
                read: true,
                important: false,
                starred: false,
                body: `Hello,

We're interested in learning more about your enterprise plan options. Our company, Tech Innovations, is growing rapidly and we need a robust CRM solution that can scale with us.

Currently, we have about 50 employees who would need access to the system, primarily in sales, marketing, and customer support roles. We're particularly interested in:

• Advanced reporting and analytics capabilities
• Integration with our existing tech stack (Slack, Google Workspace, Jira)
• Customizable workflows and automation
• Mobile access for our field sales team

Could you provide information about your enterprise offerings and possibly schedule a demo for our team?

Best regards,
Alex Chen
Director of Operations
Tech Innovations`
            }
        ];
    }

    /**
     * Populate inbox with emails
     */
    populateInbox() {
        const emailList = document.getElementById(`${this.config.appPrefix}-email-list`);
        if (!emailList) return;

        let filteredEmails = [...this.emails];

        // Apply current filter
        if (this.currentFilter !== 'all') {
            filteredEmails = filteredEmails.filter(email => {
                switch (this.currentFilter) {
                    case 'unread': return !email.read;
                    case 'important': return email.important;
                    case 'starred': return email.starred;
                    default: return true;
                }
            });
        }

        // Apply search
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filteredEmails = filteredEmails.filter(email =>
                email.senderName.toLowerCase().includes(term) ||
                email.subject.toLowerCase().includes(term) ||
                email.preview.toLowerCase().includes(term)
            );
        }

        // Apply sort
        filteredEmails.sort((a, b) => {
            switch (this.currentSort) {
                case 'date-asc': return a.date - b.date;
                case 'date-desc': return b.date - a.date;
                case 'sender': return a.senderName.localeCompare(b.senderName);
                case 'subject': return a.subject.localeCompare(b.subject);
                default: return b.date - a.date;
            }
        });

        // Generate HTML
        emailList.innerHTML = filteredEmails.map(email => `
            <div class="list-group-item list-group-item-action email-item ${!email.read ? 'fw-bold' : ''}"
                 data-email-id="${email.id}" style="cursor: pointer;">
                <div class="d-flex w-100 justify-content-between">
                    <div class="d-flex align-items-center">
                        ${email.important ? '<i class="bi bi-exclamation-circle text-warning me-2"></i>' : ''}
                        ${email.starred ? '<i class="bi bi-star-fill text-warning me-2"></i>' : ''}
                        <div>
                            <h6 class="mb-1">${email.senderName}</h6>
                            <p class="mb-1">${email.subject}</p>
                            <small class="text-muted">${email.preview}</small>
                        </div>
                    </div>
                    <small class="text-muted">${this.formatDate(email.date)}</small>
                </div>
            </div>
        `).join('');

        // Add click listeners to email items
        emailList.querySelectorAll('.email-item').forEach(item => {
            item.addEventListener('click', () => {
                const emailId = parseInt(item.getAttribute('data-email-id'));
                this.openEmail(emailId);
            });
        });
    }

    /**
     * Open an email for reading
     */
    openEmail(emailId) {
        const email = this.emails.find(e => e.id === emailId);
        if (!email) return;

        // Mark as read
        email.read = true;

        // Switch to read tab
        const readTab = document.getElementById(`${this.config.appPrefix}-read-tab`);
        if (readTab) {
            const tabTrigger = new bootstrap.Tab(readTab);
            tabTrigger.show();
        }

        // Populate read content
        const readContent = document.getElementById(`${this.config.appPrefix}-read-content`);
        if (readContent) {
            readContent.innerHTML = `
                <div class="email-header mb-4">
                    <h5>${email.subject}</h5>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <strong>From:</strong> ${email.senderName} &lt;${email.sender}&gt;
                        </div>
                        <div class="text-muted">
                            ${this.formatDate(email.date)}
                        </div>
                    </div>
                    <div class="d-flex gap-2 mb-3">
                        <button class="btn btn-primary btn-sm" onclick="unifiedGmail.replyToEmail(${email.id})">
                            <i class="bi bi-reply me-1"></i>Reply
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="unifiedGmail.forwardEmail(${email.id})">
                            <i class="bi bi-arrow-right me-1"></i>Forward
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="unifiedGmail.deleteEmail(${email.id})">
                            <i class="bi bi-trash me-1"></i>Delete
                        </button>
                    </div>
                </div>
                <div class="email-body">
                    <div style="white-space: pre-wrap; line-height: 1.6;">${email.body}</div>
                    ${email.attachments && email.attachments.length > 0 ? `
                        <div class="email-attachments mt-4">
                            <h6><i class="bi bi-paperclip me-2"></i>Attachments (${email.attachments.length})</h6>
                            <div class="list-group">
                                ${email.attachments.map(attachment => `
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark me-2"></i>
                                            <div>
                                                <div class="fw-medium">${attachment.name}</div>
                                                <small class="text-muted">${attachment.size}</small>
                                            </div>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="unifiedGmail.viewAttachment('${attachment.name}')">
                                                <i class="bi bi-eye"></i> View
                                            </button>
                                            <button class="btn btn-outline-success" onclick="unifiedGmail.downloadAttachment('${attachment.name}')">
                                                <i class="bi bi-download"></i> Download
                                            </button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // Refresh inbox to update read status
        this.populateInbox();
    }

    /**
     * Reply to an email
     */
    replyToEmail(emailId) {
        const email = this.emails.find(e => e.id === emailId);
        if (!email) return;

        // Switch to compose tab
        const composeTab = document.getElementById(`${this.config.appPrefix}-compose-tab`);
        if (composeTab) {
            const tabTrigger = new bootstrap.Tab(composeTab);
            tabTrigger.show();
        }

        // Pre-fill compose form
        const toField = document.getElementById(`${this.config.appPrefix}-email-to`);
        const subjectField = document.getElementById(`${this.config.appPrefix}-email-subject`);
        const bodyField = document.getElementById(`${this.config.appPrefix}-email-body`);

        if (toField) toField.value = email.sender;
        if (subjectField) subjectField.value = `Re: ${email.subject}`;
        if (bodyField) {
            bodyField.value = `\n\n-------- Original Message --------\nFrom: ${email.senderName} <${email.sender}>\nSubject: ${email.subject}\nDate: ${this.formatDate(email.date)}\n\n${email.body}`;
            bodyField.focus();
        }

        this.showToast('Reply started', 'info');
    }

    /**
     * Forward an email
     */
    forwardEmail(emailId) {
        const email = this.emails.find(e => e.id === emailId);
        if (!email) return;

        // Switch to compose tab
        const composeTab = document.getElementById(`${this.config.appPrefix}-compose-tab`);
        if (composeTab) {
            const tabTrigger = new bootstrap.Tab(composeTab);
            tabTrigger.show();
        }

        // Pre-fill compose form
        const subjectField = document.getElementById(`${this.config.appPrefix}-email-subject`);
        const bodyField = document.getElementById(`${this.config.appPrefix}-email-body`);

        if (subjectField) subjectField.value = `Fwd: ${email.subject}`;
        if (bodyField) {
            bodyField.value = `\n\n-------- Forwarded Message --------\nFrom: ${email.senderName} <${email.sender}>\nSubject: ${email.subject}\nDate: ${this.formatDate(email.date)}\n\n${email.body}`;

            // Focus on the To field for forwarding
            const toField = document.getElementById(`${this.config.appPrefix}-email-to`);
            if (toField) toField.focus();
        }

        this.showToast('Forward started', 'info');
    }

    /**
     * Delete an email
     */
    deleteEmail(emailId) {
        if (confirm('Are you sure you want to delete this email?')) {
            this.emails = this.emails.filter(e => e.id !== emailId);
            this.populateInbox();

            // If we're currently viewing this email, go back to inbox
            const readContent = document.getElementById(`${this.config.appPrefix}-read-content`);
            if (readContent && readContent.innerHTML.includes(`deleteEmail(${emailId})`)) {
                const inboxTab = document.getElementById(`${this.config.appPrefix}-inbox-tab`);
                if (inboxTab) {
                    const tabTrigger = new bootstrap.Tab(inboxTab);
                    tabTrigger.show();
                }
            }

            this.showToast('Email deleted', 'success');
        }
    }

    /**
     * Search emails
     */
    searchEmails(term) {
        this.searchTerm = term;
        this.populateInbox();
    }

    /**
     * Sort emails
     */
    sortEmails(sortBy) {
        this.currentSort = sortBy;
        this.populateInbox();
    }

    /**
     * Filter emails
     */
    filterEmails(filter) {
        this.currentFilter = filter;
        this.populateInbox();
    }

    /**
     * Send email
     */
    sendEmail() {
        const toField = document.getElementById(`${this.config.appPrefix}-email-to`);
        const subjectField = document.getElementById(`${this.config.appPrefix}-email-subject`);
        const bodyField = document.getElementById(`${this.config.appPrefix}-email-body`);

        if (!toField.value || !subjectField.value || !bodyField.value) {
            this.showToast('Please fill in all required fields', 'error');
            return;
        }

        // Simulate sending email
        this.showToast('Email sent successfully!', 'success');

        // Clear form
        this.clearCompose();

        // Switch back to inbox
        const inboxTab = document.getElementById(`${this.config.appPrefix}-inbox-tab`);
        if (inboxTab) {
            const tabTrigger = new bootstrap.Tab(inboxTab);
            tabTrigger.show();
        }
    }

    /**
     * Save draft
     */
    saveDraft() {
        this.showToast('Draft saved', 'success');
    }

    /**
     * Clear compose form
     */
    clearCompose() {
        const toField = document.getElementById(`${this.config.appPrefix}-email-to`);
        const subjectField = document.getElementById(`${this.config.appPrefix}-email-subject`);
        const bodyField = document.getElementById(`${this.config.appPrefix}-email-body`);
        const attachmentsField = document.getElementById(`${this.config.appPrefix}-email-attachments`);

        if (toField) toField.value = '';
        if (subjectField) subjectField.value = '';
        if (bodyField) bodyField.value = '';
        if (attachmentsField) attachmentsField.value = '';
    }

    /**
     * Format date for display
     */
    formatDate(date) {
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) { // Less than 1 minute
            return 'Just now';
        } else if (diff < 3600000) { // Less than 1 hour
            const minutes = Math.floor(diff / 60000);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diff < 86400000) { // Less than 1 day
            const hours = Math.floor(diff / 3600000);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else {
            return date.toLocaleDateString();
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'success') {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        // Create toast
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi bi-${type === 'success' ? 'check-circle-fill text-success' :
                                     type === 'error' ? 'exclamation-circle-fill text-danger' :
                                     'info-circle-fill text-info'} me-2"></i>
                    <strong class="me-auto">${type === 'success' ? 'Success' :
                                              type === 'error' ? 'Error' : 'Info'}</strong>
                    <small>Just now</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Show toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
        toast.show();

        // Remove from DOM after hiding
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }

    /**
     * Preview attachments when files are selected
     */
    previewAttachments(event) {
        const files = event.target.files;
        const previewContainer = document.getElementById(`${this.config.appPrefix}-attachment-preview`);

        if (!previewContainer) return;

        if (files.length === 0) {
            previewContainer.innerHTML = '';
            return;
        }

        let previewHtml = '<div class="mt-2"><h6>Selected Files:</h6><div class="list-group">';

        Array.from(files).forEach((file, index) => {
            const fileSize = this.formatFileSize(file.size);
            previewHtml += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-file-earmark me-2"></i>
                        <div>
                            <div class="fw-medium">${file.name}</div>
                            <small class="text-muted">${fileSize}</small>
                        </div>
                    </div>
                    <button class="btn btn-outline-danger btn-sm" onclick="unifiedGmail.removeAttachment(${index})">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;
        });

        previewHtml += '</div></div>';
        previewContainer.innerHTML = previewHtml;
    }

    /**
     * Remove an attachment from the preview
     */
    removeAttachment(index) {
        const attachmentInput = document.getElementById(`${this.config.appPrefix}-email-attachments`);
        if (!attachmentInput) return;

        const dt = new DataTransfer();
        const files = attachmentInput.files;

        for (let i = 0; i < files.length; i++) {
            if (i !== index) {
                dt.items.add(files[i]);
            }
        }

        attachmentInput.files = dt.files;
        this.previewAttachments({ target: attachmentInput });
    }

    /**
     * View an attachment
     */
    viewAttachment(filename) {
        // Determine file type from extension
        let fileType = 'document';
        if (filename.toLowerCase().includes('.pdf')) {
            fileType = 'pdf';
        } else if (filename.toLowerCase().includes('.xlsx') || filename.toLowerCase().includes('.xls') || filename.toLowerCase().includes('spreadsheet')) {
            fileType = 'spreadsheet';
        } else if (filename.toLowerCase().includes('.docx') || filename.toLowerCase().includes('.doc')) {
            fileType = 'document';
        }

        // Try to use the global viewAttachment function if available
        if (typeof window.viewAttachment === 'function') {
            window.viewAttachment(fileType, filename);
        } else if (typeof window.simulateOpenFile === 'function') {
            window.simulateOpenFile(filename);
        } else {
            // Fallback: create a simple file viewer modal
            this.createSimpleFileViewer(filename, fileType);
        }
    }

    /**
     * Download an attachment
     */
    downloadAttachment(filename) {
        // Try to use the global downloadAttachment function if available
        if (typeof window.downloadAttachment === 'function') {
            window.downloadAttachment(filename);
        } else {
            // Simulate download without toast messages
            console.log(`Downloading ${filename}...`);
        }
    }

    /**
     * Create a simple file viewer modal as fallback
     */
    createSimpleFileViewer(filename, fileType) {
        const modalId = 'simple-file-viewer-' + Date.now();

        // Determine icon and content based on file type
        let icon = 'bi-file-earmark';
        let iconColor = '';
        let content = `<p>File content for ${filename} would be displayed here.</p>`;

        if (fileType === 'pdf') {
            icon = 'bi-file-earmark-pdf';
            iconColor = 'text-danger';
            content = `
                <div class="text-center p-4">
                    <h5>PDF Document</h5>
                    <p>This PDF document contains important information.</p>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        PDF content would be rendered here in a real implementation.
                    </div>
                </div>
            `;
        } else if (fileType === 'spreadsheet') {
            icon = 'bi-file-earmark-spreadsheet';
            iconColor = 'text-success';
            content = `
                <div class="text-center p-4">
                    <h5>Spreadsheet Document</h5>
                    <table class="table table-bordered table-sm">
                        <thead>
                            <tr><th>Column A</th><th>Column B</th><th>Column C</th></tr>
                        </thead>
                        <tbody>
                            <tr><td>Data 1</td><td>Data 2</td><td>Data 3</td></tr>
                            <tr><td>Data 4</td><td>Data 5</td><td>Data 6</td></tr>
                        </tbody>
                    </table>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Spreadsheet content would be rendered here in a real implementation.
                    </div>
                </div>
            `;
        } else {
            icon = 'bi-file-earmark-text';
            iconColor = 'text-primary';
            content = `
                <div class="text-center p-4">
                    <h5>Document</h5>
                    <p>This document contains important information and content.</p>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Document content would be rendered here in a real implementation.
                    </div>
                </div>
            `;
        }

        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="bi ${icon} ${iconColor} me-2"></i>${filename}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-success" onclick="unifiedGmail.downloadAttachment('${filename}')">
                                <i class="bi bi-download"></i> Download
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to document
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modalElement = document.getElementById(modalId);
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Clean up modal after it's hidden
        modalElement.addEventListener('hidden.bs.modal', function() {
            modalElement.remove();
        });
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Log messages (if debug is enabled)
     */
    log(message, ...args) {
        if (this.config.debug) {
            console.log(`[UnifiedGmail:${this.config.appName}]`, message, ...args);
        }
    }
}

/**
 * Initialize Unified Gmail Integration
 * @param {Object} config - Configuration options
 * @returns {UnifiedGmailIntegration} The Gmail integration instance
 */
function initializeUnifiedGmail(config = {}) {
    console.log('Initializing Unified Gmail Integration for', config.appName || 'application');

    try {
        const gmail = new UnifiedGmailIntegration(config);
        gmail.init();

        console.log('Unified Gmail Integration initialized successfully for', config.appName || 'application');
        return gmail;
    } catch (error) {
        console.error('Error initializing Unified Gmail Integration:', error);
        return null;
    }
}

// Make globally available
window.UnifiedGmailIntegration = UnifiedGmailIntegration;
window.initializeUnifiedGmail = initializeUnifiedGmail;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UnifiedGmailIntegration, initializeUnifiedGmail };
}
