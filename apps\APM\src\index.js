import './styles.css';

console.log('Webpack configuration test successful!');

// Test React integration
import React from 'react';
import ReactDOM from 'react-dom';

const App = () => {
  return (
    <div className="test-container">
      <h1>Webpack Test</h1>
      <p>If you can see this, webpack is working correctly!</p>
    </div>
  );
};

ReactDOM.render(<App />, document.getElementById('root'));
