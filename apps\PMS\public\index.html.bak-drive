<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --app-primary-color: #e91e63; /* Pink for PMS */
            --app-primary-dark: #c2185b;
            --app-primary-light: rgba(233, 30, 99, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            padding-top: 56px; /* Height of navbar */
        }

        /* Sidebar styles */
        .sidebar {
            background-color: var(--app-primary-color);
            color: white;
            height: calc(100vh - 56px);
            position: fixed;
            top: 56px; /* Height of navbar */
            padding-top: 20px;
            width: 250px;
            z-index: 100;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto; /* Add scrollbar when content overflows */
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--app-primary-dark);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar .nav-link {
            color: #f8f9fa;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-heading {
            padding: 0.75rem 1.25rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.1rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Main content area */
        .main-content {
            margin-left: 250px;
            padding: 20px;
            transition: margin-left 0.3s;
        }

        /* Dashboard cards */
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        .card-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        /* Navbar */
        .navbar {
            background-color: var(--app-primary-color) !important;
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
        }

        /* Toggle sidebar button */
        #sidebarToggle {
            cursor: pointer;
            background: transparent;
            border: none;
            color: white;
        }

        /* Progress bar */
        .progress-bar-custom {
            height: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--app-primary-color);
        }

        /* For mobile view */
        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: -250px;
                height: calc(100vh - 56px);
                transition: left 0.3s;
                z-index: 1030;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0;
                padding: 15px;
            }

            .navbar {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-dark fixed-top">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <button id="sidebarToggle" class="d-md-none me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-list fs-4"></i>
                </button>
                <a class="navbar-brand" href="/">PMS System</a>
            </div>
            <div class="d-flex">
                <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3" style="background: transparent; border: 1px solid white; color: white; padding: 5px 10px;">
                    <i class="bi bi-box-arrow-left me-1"></i> Back to Hub
                </a>
                <button class="btn position-relative me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-bell fs-5"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        2
                    </span>
                </button>
                <button class="btn" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-person-circle fs-5"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="pt-2">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="/">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/projects">
                        <i class="bi bi-kanban"></i>
                        Projects
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/tasks">
                        <i class="bi bi-check2-square"></i>
                        Tasks
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/team">
                        <i class="bi bi-people"></i>
                        Team
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/calendar">
                        <i class="bi bi-calendar-week"></i>
                        Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="bi bi-file-earmark-text"></i>
                        Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#attachments" data-bs-toggle="modal" data-bs-target="#attachmentsModal">
                        <i class="bi bi-paperclip"></i>
                        Attachments
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/settings">
                        <i class="bi bi-gear-fill"></i>
                        Settings
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                <span>Integrations</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal">
                        <i class="bi bi-calendar3"></i>
                        Google Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal">
                        <i class="bi bi-folder"></i>
                        Google Drive
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal">
                        <i class="bi bi-file-earmark-text"></i>
                        Google Docs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                        <i class="bi bi-file-earmark-spreadsheet"></i>
                        Google Sheets
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-maps" data-bs-toggle="modal" data-bs-target="#mapsModal">
                        <i class="bi bi-geo-alt"></i>
                        Google Maps
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#gmail" data-bs-toggle="modal" data-bs-target="#gmailModal">
                        <i class="bi bi-envelope"></i>
                        Gmail
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#attachments" data-bs-toggle="modal" data-bs-target="#attachmentsModal">
                        <i class="bi bi-paperclip"></i>
                        Attachments
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();">
                        <i class="bi bi-box-arrow-left"></i>
                        Back to Hub
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">Project Management Dashboard</h1>
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-primary">Share</button>
                    <button type="button" class="btn btn-sm btn-outline-primary">Export</button>
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle">
                    <i class="bi bi-calendar"></i>
                    This month
                </button>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="row">
            <div class="col-md-4">
                <div class="card dashboard-card text-white" style="background-color: var(--app-primary-color);">
                    <div class="card-body text-center">
                        <i class="bi bi-kanban card-icon"></i>
                        <h5 class="card-title">Active Projects</h5>
                        <h2 class="card-text">3</h2>
                        <p class="card-text">In progress</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card dashboard-card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-graph-up card-icon"></i>
                        <h5 class="card-title">Average Progress</h5>
                        <h2 class="card-text">68%</h2>
                        <p class="card-text">Across all projects</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card dashboard-card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-cash-stack card-icon"></i>
                        <h5 class="card-title">Total Budget</h5>
                        <h2 class="card-text">$1.2M</h2>
                        <p class="card-text">Allocated resources</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Overview -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5>Project Overview</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Project</th>
                                    <th>Manager</th>
                                    <th>Timeline</th>
                                    <th>Budget</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ERP System Implementation</td>
                                    <td>Sarah Wilson</td>
                                    <td>2024-01-15 - 2024-06-30</td>
                                    <td>$350,000 / $500,000</td>
                                    <td><span class="badge bg-warning">In Progress</span></td>
                                    <td>
                                        <div class="progress-bar-custom">
                                            <div class="progress-fill" style="width: 70%;"></div>
                                        </div>
                                        <div style="text-align: right; font-size: 12px; color: #6c757d;">70%</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Mobile App Development</td>
                                    <td>David Chen</td>
                                    <td>2024-01-01 - 2024-08-15</td>
                                    <td>$120,000 / $300,000</td>
                                    <td><span class="badge bg-success">On Track</span></td>
                                    <td>
                                        <div class="progress-bar-custom">
                                            <div class="progress-fill" style="width: 40%;"></div>
                                        </div>
                                        <div style="text-align: right; font-size: 12px; color: #6c757d;">40%</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Cloud Migration</td>
                                    <td>Emily Brown</td>
                                    <td>2024-02-01 - 2024-07-15</td>
                                    <td>$380,000 / $400,000</td>
                                    <td><span class="badge bg-danger">At Risk</span></td>
                                    <td>
                                        <div class="progress-bar-custom">
                                            <div class="progress-fill" style="width: 95%;"></div>
                                        </div>
                                        <div style="text-align: right; font-size: 12px; color: #6c757d;">95%</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Milestones -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5>Project Milestones</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Project</th>
                                    <th>Milestone</th>
                                    <th>Due Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ERP System Implementation</td>
                                    <td>Core Modules Development</td>
                                    <td>2024-03-30</td>
                                    <td><span class="badge bg-success">Completed</span></td>
                                </tr>
                                <tr>
                                    <td>ERP System Implementation</td>
                                    <td>Integration Testing</td>
                                    <td>2024-05-15</td>
                                    <td><span class="badge bg-warning">In Progress</span></td>
                                </tr>
                                <tr>
                                    <td>Mobile App Development</td>
                                    <td>UI/UX Design</td>
                                    <td>2024-03-15</td>
                                    <td><span class="badge bg-success">Completed</span></td>
                                </tr>
                                <tr>
                                    <td>Cloud Migration</td>
                                    <td>Data Migration</td>
                                    <td>2024-05-15</td>
                                    <td><span class="badge bg-danger">At Risk</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Google Integrations -->
        <div class="row mt-4">
            <div class="col-12">
                <h4 class="mb-3">Google Integrations</h4>
            </div>

            <!-- Google Gmail -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-gmail-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-envelope"></i> Gmail</h5>
                        <div class="component-actions">
                            <button id="refresh-gmail" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="compose-email" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#gmailModal"><i class="bi bi-pencil"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">SC</div>
                                        <div>
                                            <div class="fw-bold">Sarah Chen</div>
                                            <div class="small text-truncate" style="max-width: 200px;">Project update: New timeline for Phase 2</div>
                                        </div>
                                    </div>
                                </div>
                                <span class="badge bg-primary rounded-pill">10m</span>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">JD</div>
                                        <div>
                                            <div class="fw-bold">John Davis</div>
                                            <div class="small text-truncate" style="max-width: 200px;">Meeting notes from yesterday's client call</div>
                                        </div>
                                    </div>
                                </div>
                                <span class="badge bg-primary rounded-pill">1h</span>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">RM</div>
                                        <div>
                                            <div class="fw-bold">Rachel Miller</div>
                                            <div class="small text-truncate" style="max-width: 200px;">Budget approval for new resources</div>
                                        </div>
                                    </div>
                                </div>
                                <span class="badge bg-primary rounded-pill">3h</span>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#gmailModal">
                                <i class="bi bi-envelope me-2"></i>Open Gmail
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Drive -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-drive-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-folder"></i> Google Drive</h5>
                        <div class="component-actions">
                            <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="upload-file" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-upload"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" onclick="openGoogleItem('drive', 'folder', 'project-documentation')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                    <span>Project Documentation</span>
                                </div>
                                <span class="badge bg-secondary rounded-pill">12 files</span>
                            </a>
                            <a href="#" onclick="openGoogleItem('drive', 'folder', 'client-presentations')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                    <span>Client Presentations</span>
                                </div>
                                <span class="badge bg-secondary rounded-pill">5 files</span>
                            </a>
                            <a href="#" onclick="openGoogleItem('drive', 'pdf', 'project-proposal-final')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                    <span>Project_Proposal_Final.pdf</span>
                                </div>
                                <span class="badge bg-primary rounded-pill">Yesterday</span>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
                                <i class="bi bi-folder me-2"></i>Open Drive
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Sheets -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-sheets-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                        <div class="component-actions">
                            <button id="refresh-sheets" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="create-new-sheet" class="btn btn-sm btn-outline-success"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'project-timeline-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                    <span>Project_Timeline_2025.xlsx</span>
                                </div>
                                <span class="badge bg-primary rounded-pill">2 days ago</span>
                            </a>
                            <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'budget-tracking-q2-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                    <span>Budget_Tracking_Q2_2025.xlsx</span>
                                </div>
                                <span class="badge bg-primary rounded-pill">1 week ago</span>
                            </a>
                            <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'resource-allocation-2025')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                    <span>Resource_Allocation_2025.xlsx</span>
                                </div>
                                <span class="badge bg-primary rounded-pill">2 weeks ago</span>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                                <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Docs -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-docs-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                        <div class="component-actions">
                            <button id="refresh-docs" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="create-new-doc" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#docsModal"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" onclick="openGoogleItem('docs', 'document', 'project-requirements-document')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                    <span>Project Requirements Document</span>
                                </div>
                                <span class="badge bg-primary rounded-pill">Today</span>
                            </a>
                            <a href="#" onclick="openGoogleItem('docs', 'document', 'meeting-minutes-client-call')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                    <span>Meeting Minutes - Client Call</span>
                                </div>
                                <span class="badge bg-primary rounded-pill">Yesterday</span>
                            </a>
                            <a href="#" onclick="openGoogleItem('docs', 'document', 'project-proposal-draft')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                    <span>Project Proposal Draft</span>
                                </div>
                                <span class="badge bg-primary rounded-pill">3 days ago</span>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#docsModal">
                                <i class="bi bi-file-earmark-text me-2"></i>View All Documents
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Calendar -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-calendar-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                        <div class="component-actions">
                            <button id="refresh-calendar" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="create-new-event" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#calendarModal"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <a href="#" onclick="openGoogleItem('calendar', 'event', 'project-kickoff-meeting')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="fw-bold">Project Kickoff Meeting</div>
                                    <div class="small text-muted">
                                        <i class="bi bi-clock me-1"></i>Today, 10:00 AM - 11:30 AM
                                    </div>
                                </div>
                                <span class="badge bg-warning rounded-pill">Today</span>
                            </a>
                            <a href="#" onclick="openGoogleItem('calendar', 'event', 'client-demo')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="fw-bold">Client Demo</div>
                                    <div class="small text-muted">
                                        <i class="bi bi-clock me-1"></i>Tomorrow, 2:00 PM - 3:00 PM
                                    </div>
                                </div>
                                <span class="badge bg-info rounded-pill">Tomorrow</span>
                            </a>
                            <a href="#" onclick="openGoogleItem('calendar', 'event', 'sprint-planning')" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="fw-bold">Sprint Planning</div>
                                    <div class="small text-muted">
                                        <i class="bi bi-clock me-1"></i>May 15, 9:00 AM - 10:30 AM
                                    </div>
                                </div>
                                <span class="badge bg-secondary rounded-pill">Next Week</span>
                            </a>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#calendarModal">
                                <i class="bi bi-calendar3 me-2"></i>View Full Calendar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Attachments Modal -->
    <div class="modal fade" id="attachmentsModal" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="attachmentsModalLabel"><i class="bi bi-paperclip"></i> Attachments</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="attachmentsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content" type="button" role="tab" aria-controls="files-content" aria-selected="true">Files</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button" role="tab" aria-controls="upload-content" aria-selected="false">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-tab" data-bs-toggle="tab" data-bs-target="#shared-content" type="button" role="tab" aria-controls="shared-content" aria-selected="false">Shared with me</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="attachmentsTabContent">
                        <!-- Files Tab -->
                        <div class="tab-pane fade show active" id="files-content" role="tabpanel" aria-labelledby="files-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="file-search" class="form-control" placeholder="Search files...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-sort-down"></i> Sort by
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="date">Date</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="size">Size</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="type">Type</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>Size</th>
                                            <th>Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="files-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2 fs-5"></i>
                                                    <span>Project_Proposal_2025.pdf</span>
                                                </div>
                                            </td>
                                            <td>PDF Document</td>
                                            <td>3.2 MB</td>
                                            <td>2025-04-15</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Project_Proposal_2025.pdf')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Proposal_2025.pdf')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Proposal_2025.pdf')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Proposal_2025.pdf')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-excel text-success me-2 fs-5"></i>
                                                    <span>Project_Timeline_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>Excel Spreadsheet</td>
                                            <td>1.5 MB</td>
                                            <td>2025-04-10</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Project_Timeline_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Timeline_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Timeline_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Timeline_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-word text-primary me-2 fs-5"></i>
                                                    <span>Requirements_Document.docx</span>
                                                </div>
                                            </td>
                                            <td>Word Document</td>
                                            <td>950 KB</td>
                                            <td>2025-04-05</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('document', 'Requirements_Document.docx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Requirements_Document.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Requirements_Document.docx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Requirements_Document.docx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-image text-info me-2 fs-5"></i>
                                                    <span>Project_Diagram.png</span>
                                                </div>
                                            </td>
                                            <td>Image</td>
                                            <td>1.8 MB</td>
                                            <td>2025-03-28</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('image', 'Project_Diagram.png')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Diagram.png')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Diagram.png')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Diagram.png')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-zip text-warning me-2 fs-5"></i>
                                                    <span>Project_Assets.zip</span>
                                                </div>
                                            </td>
                                            <td>Archive</td>
                                            <td>7.2 MB</td>
                                            <td>2025-03-20</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('archive', 'Project_Assets.zip')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Assets.zip')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Assets.zip')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Assets.zip')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <nav aria-label="Files pagination">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>

                        <!-- Upload Tab -->
                        <div class="tab-pane fade" id="upload-content" role="tabpanel" aria-labelledby="upload-tab">
                            <div class="row">
                                <div class="col-md-7">
                                    <div class="upload-area p-5 mb-3 text-center" id="dropzone" style="border: 2px dashed #ccc; border-radius: 5px; background-color: #f8f9fa;">
                                        <i class="bi bi-cloud-arrow-up fs-1 text-muted mb-3"></i>
                                        <h5>Drag & Drop Files Here</h5>
                                        <p class="text-muted">or</p>
                                        <label for="file-upload" class="btn btn-primary">
                                            <i class="bi bi-folder-plus me-2"></i>Browse Files
                                        </label>
                                        <input id="file-upload" type="file" multiple style="display: none;">
                                        <p class="text-muted mt-3 small">Maximum file size: 50MB</p>
                                    </div>
                                    <div class="progress mb-3" style="display: none;">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Upload Options</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="file-category" class="form-label">Project</label>
                                                <select class="form-select" id="file-category">
                                                    <option value="">-- Select Project --</option>
                                                    <option value="erp">ERP System Implementation</option>
                                                    <option value="mobile">Mobile App Development</option>
                                                    <option value="cloud">Cloud Migration</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="file-tags" class="form-label">Tags (comma separated)</label>
                                                <input type="text" class="form-control" id="file-tags" placeholder="e.g., documentation, design, requirements">
                                            </div>
                                            <div class="mb-3">
                                                <label for="file-description" class="form-label">Description</label>
                                                <textarea class="form-control" id="file-description" rows="3" placeholder="Add a description for your files..."></textarea>
                                            </div>
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="file-share">
                                                <label class="form-check-label" for="file-share">
                                                    Share with team members
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="upload-list" class="mt-3">
                                <!-- Files will be added here dynamically -->
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                                <button class="btn btn-secondary me-md-2" type="button" id="clear-uploads">Clear All</button>
                                <button class="btn btn-primary" type="button" id="start-upload" disabled>Upload Files</button>
                            </div>
                        </div>

                        <!-- Shared Tab -->
                        <div class="tab-pane fade" id="shared-content" role="tabpanel" aria-labelledby="shared-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="shared-search" class="form-control" placeholder="Search shared files...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="other">Other</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Shared By</th>
                                            <th>Project</th>
                                            <th>Date Shared</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="shared-files-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2 fs-5"></i>
                                                    <span>Project_Scope_Document.pdf</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="User Avatar">
                                                    <span>Sarah Wilson</span>
                                                </div>
                                            </td>
                                            <td>ERP System Implementation</td>
                                            <td>2025-04-18</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('pdf', 'Project_Scope_Document.pdf')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Scope_Document.pdf')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Scope_Document.pdf')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Scope_Document.pdf')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-slides text-warning me-2 fs-5"></i>
                                                    <span>Project_Presentation.pptx</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="User Avatar">
                                                    <span>David Chen</span>
                                                </div>
                                            </td>
                                            <td>Mobile App Development</td>
                                            <td>2025-04-12</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('presentation', 'Project_Presentation.pptx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Presentation.pptx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Presentation.pptx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Presentation.pptx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-excel text-success me-2 fs-5"></i>
                                                    <span>Migration_Plan.xlsx</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="User Avatar">
                                                    <span>Emily Brown</span>
                                                </div>
                                            </td>
                                            <td>Cloud Migration</td>
                                            <td>2025-04-05</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Migration_Plan.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Migration_Plan.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Migration_Plan.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Migration_Plan.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Maps Modal -->
    <div class="modal fade" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="mapsModalLabel"><i class="bi bi-geo-alt"></i> Google Maps</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="mapsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="map-tab" data-bs-toggle="tab" data-bs-target="#map-content" type="button" role="tab" aria-controls="map-content" aria-selected="true">Map</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="directions-tab" data-bs-toggle="tab" data-bs-target="#directions-content" type="button" role="tab" aria-controls="directions-content" aria-selected="false">Directions</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="places-tab" data-bs-toggle="tab" data-bs-target="#places-content" type="button" role="tab" aria-controls="places-content" aria-selected="false">Places</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="mapsTabContent">
                        <!-- Map Tab -->
                        <div class="tab-pane fade show active" id="map-content" role="tabpanel" aria-labelledby="map-tab">
                            <div class="mb-3">
                                <div class="input-group">
                                    <input type="text" id="modal-location-search" class="form-control" placeholder="Search for a location...">
                                    <button class="btn btn-outline-primary" type="button" id="modal-search-location">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="modal-map-container" style="height: 400px; background-color: #f8f9fa; border-radius: 5px;">
                                <div class="d-flex justify-content-center align-items-center h-100">
                                    <div class="text-center">
                                        <i class="bi bi-geo-alt fs-1 text-muted mb-3"></i>
                                        <h5>Search for a location to display the map</h5>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Directions Tab -->
                        <div class="tab-pane fade" id="directions-content" role="tabpanel" aria-labelledby="directions-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="directions-from" class="form-label">From</label>
                                    <input type="text" class="form-control" id="directions-from" placeholder="Starting point">
                                </div>
                                <div class="col-md-6">
                                    <label for="directions-to" class="form-label">To</label>
                                    <input type="text" class="form-control" id="directions-to" placeholder="Destination">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Travel Mode</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-driving" checked>
                                    <label class="btn btn-outline-primary" for="travel-driving"><i class="bi bi-car-front"></i> Driving</label>

                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-transit">
                                    <label class="btn btn-outline-primary" for="travel-transit"><i class="bi bi-train-front"></i> Transit</label>

                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-walking">
                                    <label class="btn btn-outline-primary" for="travel-walking"><i class="bi bi-person-walking"></i> Walking</label>

                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-cycling">
                                    <label class="btn btn-outline-primary" for="travel-cycling"><i class="bi bi-bicycle"></i> Cycling</label>
                                </div>
                            </div>
                            <div class="d-grid">
                                <button class="btn btn-primary" type="button" id="get-directions">
                                    <i class="bi bi-signpost-2"></i> Get Directions
                                </button>
                            </div>
                            <div id="directions-result" class="mt-3" style="height: 300px; overflow-y: auto;">
                                <!-- Directions will be displayed here -->
                            </div>
                        </div>

                        <!-- Places Tab -->
                        <div class="tab-pane fade" id="places-content" role="tabpanel" aria-labelledby="places-tab">
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label for="places-search" class="form-label">Search for places</label>
                                    <input type="text" class="form-control" id="places-search" placeholder="e.g., restaurants, hotels, etc.">
                                </div>
                                <div class="col-md-4">
                                    <label for="places-radius" class="form-label">Radius (km)</label>
                                    <select class="form-select" id="places-radius">
                                        <option value="1">1 km</option>
                                        <option value="2">2 km</option>
                                        <option value="5" selected>5 km</option>
                                        <option value="10">10 km</option>
                                        <option value="20">20 km</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Place Type</label>
                                <div class="d-flex flex-wrap gap-2">
                                    <button class="btn btn-outline-primary btn-sm place-type-btn" data-type="restaurant">
                                        <i class="bi bi-cup-hot"></i> Restaurants
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm place-type-btn" data-type="hotel">
                                        <i class="bi bi-building"></i> Hotels
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm place-type-btn" data-type="cafe">
                                        <i class="bi bi-cup"></i> Cafes
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm place-type-btn" data-type="store">
                                        <i class="bi bi-shop"></i> Stores
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm place-type-btn" data-type="gas_station">
                                        <i class="bi bi-fuel-pump"></i> Gas Stations
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm place-type-btn" data-type="bank">
                                        <i class="bi bi-bank"></i> Banks
                                    </button>
                                </div>
                            </div>
                            <div class="d-grid">
                                <button class="btn btn-primary" type="button" id="search-places">
                                    <i class="bi bi-search"></i> Search Places
                                </button>
                            </div>
                            <div id="places-results" class="mt-3" style="height: 300px; overflow-y: auto;">
                                <!-- Places results will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Sheets Modal -->
    <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="sheetsModalLabel"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="sheetsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="sheets-tab" data-bs-toggle="tab" data-bs-target="#sheets-content" type="button" role="tab" aria-controls="sheets-content" aria-selected="true">My Sheets</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create-content" type="button" role="tab" aria-controls="create-content" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import-content" type="button" role="tab" aria-controls="import-content" aria-selected="false">Import</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="sheetsTabContent">
                        <!-- My Sheets Tab -->
                        <div class="tab-pane fade show active" id="sheets-content" role="tabpanel" aria-labelledby="sheets-tab">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" placeholder="Search sheets..." id="sheets-search">
                                <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sheets-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Project_Timeline_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'project-timeline-2025')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Timeline_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Timeline_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Project_Timeline_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Budget_Tracking_Q2_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'budget-tracking-q2-2025')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Budget_Tracking_Q2_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Budget_Tracking_Q2_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Budget_Tracking_Q2_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Resource_Allocation_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>2 weeks ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'resource-allocation-2025')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Resource_Allocation_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Resource_Allocation_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Resource_Allocation_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Create New Tab -->
                        <div class="tab-pane fade" id="create-content" role="tabpanel" aria-labelledby="create-tab">
                            <form id="create-sheet-form">
                                <div class="mb-3">
                                    <label for="sheet-title" class="form-label">Sheet Title</label>
                                    <input type="text" class="form-control" id="sheet-title" placeholder="Enter a title for your sheet">
                                </div>
                                <div class="mb-3">
                                    <label for="sheet-template" class="form-label">Template</label>
                                    <select class="form-select" id="sheet-template">
                                        <option value="blank">Blank</option>
                                        <option value="project">Project Timeline</option>
                                        <option value="budget">Budget Tracker</option>
                                        <option value="resources">Resource Allocation</option>
                                        <option value="tasks">Task Tracker</option>
                                    </select>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="create-sheet-btn">
                                        <i class="bi bi-plus-circle me-2"></i>Create Sheet
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Import Tab -->
                        <div class="tab-pane fade" id="import-content" role="tabpanel" aria-labelledby="import-tab">
                            <form id="import-sheet-form">
                                <div class="mb-3">
                                    <label for="import-title" class="form-label">Sheet Title</label>
                                    <input type="text" class="form-control" id="import-title" placeholder="Enter a title for your imported sheet">
                                </div>
                                <div class="mb-3">
                                    <label for="import-file" class="form-label">File to Import</label>
                                    <input class="form-control" type="file" id="import-file" accept=".xlsx,.xls,.csv,.ods">
                                    <div class="form-text">Supported formats: Excel (.xlsx, .xls), CSV, OpenDocument Spreadsheet (.ods)</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="import-first-row">
                                        <label class="form-check-label" for="import-first-row">
                                            First row contains column headers
                                        </label>
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="import-sheet-btn">
                                        <i class="bi bi-upload me-2"></i>Import Sheet
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Gmail Modal -->
    <div class="modal fade" id="gmailModal" tabindex="-1" aria-labelledby="gmailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="gmailModalLabel"><i class="bi bi-envelope"></i> Gmail</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="gmailTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox-content" type="button" role="tab" aria-controls="inbox-content" aria-selected="true">Inbox</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="read-tab" data-bs-toggle="tab" data-bs-target="#read-content" type="button" role="tab" aria-controls="read-content" aria-selected="false">Read</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose-content" type="button" role="tab" aria-controls="compose-content" aria-selected="false">Compose</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent-content" type="button" role="tab" aria-controls="sent-content" aria-selected="false">Sent</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="gmailTabContent">
                        <!-- Inbox Tab -->
                        <div class="tab-pane fade show active" id="inbox-content" role="tabpanel" aria-labelledby="inbox-tab">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" placeholder="Search emails..." id="email-search">
                                <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                            </div>
                            <div class="list-group">
                                <a href="#" onclick="showEmail('sarah-chen', 'Project update: New timeline for Phase 2', 'Hi team, I\'ve updated the timeline for Phase 2 of the project. Please review the attached document and let me know if you have any questions.', '10 minutes ago')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Sarah Chen</h6>
                                        <small>10 minutes ago</small>
                                    </div>
                                    <p class="mb-1 fw-bold">Project update: New timeline for Phase 2</p>
                                    <small class="text-muted">Hi team, I've updated the timeline for Phase 2 of the project. Please review the attached document and let me know if you have any questions.</small>
                                </a>
                                <a href="#" onclick="showEmail('john-davis', 'Meeting notes from yesterday\'s client call', 'Hello everyone, I\'ve attached the meeting notes from yesterday\'s client call. Please review and let me know if I missed anything important.', '1 hour ago')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">John Davis</h6>
                                        <small>1 hour ago</small>
                                    </div>
                                    <p class="mb-1 fw-bold">Meeting notes from yesterday's client call</p>
                                    <small class="text-muted">Hello everyone, I've attached the meeting notes from yesterday's client call. Please review and let me know if I missed anything important.</small>
                                </a>
                                <a href="#" onclick="showEmail('rachel-miller', 'Budget approval for new resources', 'Good news! The budget for the additional resources has been approved. We can start the hiring process next week.', '3 hours ago')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Rachel Miller</h6>
                                        <small>3 hours ago</small>
                                    </div>
                                    <p class="mb-1 fw-bold">Budget approval for new resources</p>
                                    <small class="text-muted">Good news! The budget for the additional resources has been approved. We can start the hiring process next week.</small>
                                </a>
                                <a href="#" onclick="showEmail('michael-johnson', 'Weekly status report', 'Please find attached the weekly status report for the project. Let me know if you need any clarification.', 'Yesterday')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Michael Johnson</h6>
                                        <small>Yesterday</small>
                                    </div>
                                    <p class="mb-1 fw-bold">Weekly status report</p>
                                    <small class="text-muted">Please find attached the weekly status report for the project. Let me know if you need any clarification.</small>
                                </a>
                                <a href="#" onclick="showEmail('emily-rodriguez', 'Client feedback on prototype', 'The client has provided feedback on the prototype. Overall, they\'re happy with the direction but have a few suggestions for improvements.', '2 days ago')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Emily Rodriguez</h6>
                                        <small>2 days ago</small>
                                    </div>
                                    <p class="mb-1 fw-bold">Client feedback on prototype</p>
                                    <small class="text-muted">The client has provided feedback on the prototype. Overall, they're happy with the direction but have a few suggestions for improvements.</small>
                                </a>
                            </div>
                            <nav aria-label="Email pagination" class="mt-3">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>

                        <!-- Read Tab -->
                        <div class="tab-pane fade" id="read-content" role="tabpanel" aria-labelledby="read-tab">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0">Project update: New timeline for Phase 2</h5>
                                        <div class="text-muted small">From: Sarah Chen &lt;<EMAIL>&gt;</div>
                                        <div class="text-muted small">To: Project Team &lt;<EMAIL>&gt;</div>
                                    </div>
                                    <div class="text-muted">10 minutes ago</div>
                                </div>
                                <div class="card-body">
                                    <p>Hi team,</p>
                                    <p>I've updated the timeline for Phase 2 of the project. Please review the attached document and let me know if you have any questions.</p>
                                    <p>The key changes include:</p>
                                    <ul>
                                        <li>Extended the design phase by one week</li>
                                        <li>Adjusted the development sprint schedule</li>
                                        <li>Added a buffer period before the client demo</li>
                                        <li>Updated resource allocations for the testing phase</li>
                                    </ul>
                                    <p>These changes have been approved by the client and should give us more flexibility to deliver a high-quality product.</p>
                                    <p>Let me know if you have any concerns or questions.</p>
                                    <p>Best regards,<br>Sarah</p>
                                    <hr>
                                    <div class="mt-3">
                                        <h6>Attachments</h6>
                                        <div class="list-group">
                                            <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                                <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                                <div>
                                                    <h6 class="mb-1">Project_Timeline_Phase2_Updated.xlsx</h6>
                                                    <small>1.2 MB</small>
                                                </div>
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                                <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                                <div>
                                                    <h6 class="mb-1">Client_Approval_Document.pdf</h6>
                                                    <small>850 KB</small>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-outline-primary"><i class="bi bi-reply me-2"></i>Reply</button>
                                        <button type="button" class="btn btn-outline-primary"><i class="bi bi-reply-all me-2"></i>Reply All</button>
                                        <button type="button" class="btn btn-outline-primary"><i class="bi bi-forward me-2"></i>Forward</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Compose Tab -->
                        <div class="tab-pane fade" id="compose-content" role="tabpanel" aria-labelledby="compose-tab">
                            <form id="compose-email-form">
                                <div class="mb-3">
                                    <label for="email-to" class="form-label">To</label>
                                    <input type="email" class="form-control" id="email-to" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label for="email-cc" class="form-label">CC</label>
                                    <input type="email" class="form-control" id="email-cc" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label for="email-subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="email-subject" placeholder="Email subject">
                                </div>
                                <div class="mb-3">
                                    <label for="email-body" class="form-label">Message</label>
                                    <textarea class="form-control" id="email-body" rows="10" placeholder="Type your message here..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="email-attachments" class="form-label">Attachments</label>
                                    <input class="form-control" type="file" id="email-attachments" multiple>
                                    <div id="attachment-list" class="mt-2">
                                        <!-- Attachments will be listed here -->
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" id="save-draft">
                                        <i class="bi bi-save me-2"></i>Save Draft
                                    </button>
                                    <button type="button" class="btn btn-primary" id="send-email">
                                        <i class="bi bi-send me-2"></i>Send
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Sent Tab -->
                        <div class="tab-pane fade" id="sent-content" role="tabpanel" aria-labelledby="sent-tab">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" placeholder="Search sent emails..." id="sent-email-search">
                                <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                            </div>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">To: Project Team</h6>
                                        <small>Yesterday</small>
                                    </div>
                                    <p class="mb-1 fw-bold">Weekly team meeting agenda</p>
                                    <small class="text-muted">Here's the agenda for our weekly team meeting tomorrow. Please review and come prepared to discuss your updates.</small>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">To: Client Stakeholders</h6>
                                        <small>3 days ago</small>
                                    </div>
                                    <p class="mb-1 fw-bold">Project status update</p>
                                    <small class="text-muted">Please find attached the latest project status report. We're on track to meet the next milestone.</small>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">To: Development Team</h6>
                                        <small>1 week ago</small>
                                    </div>
                                    <p class="mb-1 fw-bold">Code review process update</p>
                                    <small class="text-muted">We're updating our code review process to improve efficiency. Please see the attached document for details.</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Calendar Modal -->
    <div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="calendarModalLabel"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="calendarTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="events-tab" data-bs-toggle="tab" data-bs-target="#events-content" type="button" role="tab" aria-controls="events-content" aria-selected="true">Events</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-event-tab" data-bs-toggle="tab" data-bs-target="#create-event-content" type="button" role="tab" aria-controls="create-event-content" aria-selected="false">Create Event</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="calendar-settings-tab" data-bs-toggle="tab" data-bs-target="#calendar-settings-content" type="button" role="tab" aria-controls="calendar-settings-content" aria-selected="false">Settings</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="calendarTabContent">
                        <!-- Events Tab -->
                        <div class="tab-pane fade show active" id="events-content" role="tabpanel" aria-labelledby="events-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="event-search" class="form-control" placeholder="Search events...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Events</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="today">Today</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="week">This Week</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="month">This Month</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'project-kickoff-meeting')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Project Kickoff Meeting</h6>
                                        <small class="text-warning">Today</small>
                                    </div>
                                    <p class="mb-1">10:00 AM - 11:30 AM</p>
                                    <small>Initial kickoff meeting for the new project with all team members</small>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'client-demo')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Client Demo</h6>
                                        <small class="text-info">Tomorrow</small>
                                    </div>
                                    <p class="mb-1">2:00 PM - 3:00 PM</p>
                                    <small>Demonstration of the current progress to the client</small>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'sprint-planning')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Sprint Planning</h6>
                                        <small class="text-secondary">Next Week</small>
                                    </div>
                                    <p class="mb-1">May 15, 9:00 AM - 10:30 AM</p>
                                    <small>Planning session for the next sprint with the development team</small>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'project-review')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Project Review</h6>
                                        <small class="text-secondary">Next Week</small>
                                    </div>
                                    <p class="mb-1">May 17, 1:00 PM - 2:30 PM</p>
                                    <small>Review of the project progress with stakeholders</small>
                                </a>
                                <a href="#" onclick="openGoogleItem('calendar', 'event', 'team-building')" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Team Building Event</h6>
                                        <small class="text-secondary">Next Month</small>
                                    </div>
                                    <p class="mb-1">June 5, 3:00 PM - 6:00 PM</p>
                                    <small>Team building activity for the project team</small>
                                </a>
                            </div>
                        </div>
                        <!-- Create Event Tab -->
                        <div class="tab-pane fade" id="create-event-content" role="tabpanel" aria-labelledby="create-event-tab">
                            <form id="create-event-form">
                                <div class="mb-3">
                                    <label for="event-title" class="form-label">Event Title</label>
                                    <input type="text" class="form-control" id="event-title" placeholder="Enter event title">
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="event-date" class="form-label">Date</label>
                                        <input type="date" class="form-control" id="event-date">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="event-start-time" class="form-label">Start Time</label>
                                        <input type="time" class="form-control" id="event-start-time">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="event-end-time" class="form-label">End Time</label>
                                        <input type="time" class="form-control" id="event-end-time">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="event-location" class="form-label">Location</label>
                                    <input type="text" class="form-control" id="event-location" placeholder="Enter location">
                                </div>
                                <div class="mb-3">
                                    <label for="event-description" class="form-label">Description</label>
                                    <textarea class="form-control" id="event-description" rows="3" placeholder="Enter event description"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="event-attendees" class="form-label">Attendees</label>
                                    <input type="text" class="form-control" id="event-attendees" placeholder="Enter email addresses (comma separated)">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="event-reminder">
                                        <label class="form-check-label" for="event-reminder">
                                            Set reminder
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" id="create-event-btn">Create Event</button>
                            </form>
                        </div>
                        <!-- Settings Tab -->
                        <div class="tab-pane fade" id="calendar-settings-content" role="tabpanel" aria-labelledby="calendar-settings-tab">
                            <div class="mb-3">
                                <label for="default-calendar" class="form-label">Default Calendar</label>
                                <select class="form-select" id="default-calendar">
                                    <option selected>Project Calendar</option>
                                    <option>Personal Calendar</option>
                                    <option>Team Calendar</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="default-view" class="form-label">Default View</label>
                                <select class="form-select" id="default-view">
                                    <option selected>Week</option>
                                    <option>Month</option>
                                    <option>Day</option>
                                    <option>Agenda</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="default-reminder" class="form-label">Default Reminder Time</label>
                                <select class="form-select" id="default-reminder">
                                    <option selected>10 minutes before</option>
                                    <option>30 minutes before</option>
                                    <option>1 hour before</option>
                                    <option>1 day before</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email-notifications" checked>
                                    <label class="form-check-label" for="email-notifications">
                                        Email notifications
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="desktop-notifications" checked>
                                    <label class="form-check-label" for="desktop-notifications">
                                        Desktop notifications
                                    </label>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary" id="save-calendar-settings-btn">Save Settings</button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://calendar.google.com" target="_blank" class="btn btn-primary">Open in Google Calendar</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Drive Modal -->
    <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="driveModalLabel"><i class="bi bi-folder"></i> Google Drive</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="driveTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content" type="button" role="tab" aria-controls="files-content" aria-selected="true">Files</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button" role="tab" aria-controls="upload-content" aria-selected="false">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-tab" data-bs-toggle="tab" data-bs-target="#shared-content" type="button" role="tab" aria-controls="shared-content" aria-selected="false">Shared with me</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="driveTabContent">
                        <!-- Files Tab -->
                        <div class="tab-pane fade show active" id="files-content" role="tabpanel" aria-labelledby="files-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="file-search" class="form-control" placeholder="Search files...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-sort-down"></i> Sort by
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="date">Date</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="size">Size</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="type">Type</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Project Documentation</h6>
                                                <small>12 files - Last updated: Yesterday</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'project-documentation')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-share"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Client Presentations</h6>
                                                <small>5 files - Last updated: 3 days ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'client-presentations')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-share"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Project_Proposal_Final.pdf</h6>
                                                <small>2.4 MB - Last updated: Yesterday</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'project-proposal-final')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Project_Timeline_2025.xlsx</h6>
                                                <small>1.8 MB - Last updated: 2 days ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'spreadsheet', 'project-timeline-2025')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Meeting_Minutes.docx</h6>
                                                <small>1.2 MB - Last updated: 1 week ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'meeting-minutes')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Upload Tab -->
                        <div class="tab-pane fade" id="upload-content" role="tabpanel" aria-labelledby="upload-tab">
                            <div class="mb-3">
                                <label for="upload-file" class="form-label">Select file to upload</label>
                                <input class="form-control" type="file" id="upload-file">
                            </div>
                            <div class="mb-3">
                                <label for="upload-folder" class="form-label">Destination folder</label>
                                <select class="form-select" id="upload-folder">
                                    <option selected>My Drive</option>
                                    <option>Project Documentation</option>
                                    <option>Client Presentations</option>
                                    <option>Team Shared Folder</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="convert-to-google-format">
                                    <label class="form-check-label" for="convert-to-google-format">
                                        Convert to Google format
                                    </label>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary" id="upload-file-btn">Upload</button>
                        </div>
                        <!-- Shared with me Tab -->
                        <div class="tab-pane fade" id="shared-content" role="tabpanel" aria-labelledby="shared-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="shared-search" class="form-control" placeholder="Search shared files...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="other">Other</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Client_Requirements.pdf</h6>
                                                <small>Shared by: John Davis - 3 days ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'client-requirements')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Budget_Estimates.xlsx</h6>
                                                <small>Shared by: Sarah Wilson - 1 week ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'spreadsheet', 'budget-estimates')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Project_Scope.docx</h6>
                                                <small>Shared by: David Chen - 2 weeks ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'project-scope')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://drive.google.com" target="_blank" class="btn btn-primary">Open in Google Drive</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Docs Modal -->
    <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="docsModalLabel"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="docsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="my-docs-tab" data-bs-toggle="tab" data-bs-target="#my-docs-content" type="button" role="tab" aria-controls="my-docs-content" aria-selected="true">My Documents</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-doc-tab" data-bs-toggle="tab" data-bs-target="#create-doc-content" type="button" role="tab" aria-controls="create-doc-content" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-docs-tab" data-bs-toggle="tab" data-bs-target="#shared-docs-content" type="button" role="tab" aria-controls="shared-docs-content" aria-selected="false">Shared with me</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="docsTabContent">
                        <!-- My Documents Tab -->
                        <div class="tab-pane fade show active" id="my-docs-content" role="tabpanel" aria-labelledby="my-docs-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" class="form-control" placeholder="Search documents..." id="docs-search">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-sort-down"></i> Sort by
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="date">Last modified</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="owner">Owner</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="docs-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Project Requirements Document</span>
                                                </div>
                                            </td>
                                            <td>Today, 10:30 AM</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'project-requirements-document')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project Requirements Document.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project Requirements Document')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Project Requirements Document')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Meeting Minutes - Client Call</span>
                                                </div>
                                            </td>
                                            <td>Yesterday, 3:45 PM</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'meeting-minutes-client-call')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Meeting Minutes - Client Call.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Meeting Minutes - Client Call')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Meeting Minutes - Client Call')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Project Proposal Draft</span>
                                                </div>
                                            </td>
                                            <td>May 5, 2025</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'project-proposal-draft')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project Proposal Draft.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project Proposal Draft')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Project Proposal Draft')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Team Roles and Responsibilities</span>
                                                </div>
                                            </td>
                                            <td>May 2, 2025</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'team-roles-and-responsibilities')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Team Roles and Responsibilities.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Team Roles and Responsibilities')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Team Roles and Responsibilities')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Project Timeline Documentation</span>
                                                </div>
                                            </td>
                                            <td>April 28, 2025</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'project-timeline-documentation')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project Timeline Documentation.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project Timeline Documentation')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Project Timeline Documentation')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <nav aria-label="Documents pagination" class="mt-3">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>

                        <!-- Create New Document Tab -->
                        <div class="tab-pane fade" id="create-doc-content" role="tabpanel" aria-labelledby="create-doc-tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <h6 class="mb-0">Create from Template</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="list-group">
                                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Blank Document</h6>
                                                        <small class="text-muted">Start with a clean document</small>
                                                    </div>
                                                </a>
                                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Project Brief</h6>
                                                        <small class="text-muted">Template for project overview</small>
                                                    </div>
                                                </a>
                                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Meeting Minutes</h6>
                                                        <small class="text-muted">Template for recording meeting notes</small>
                                                    </div>
                                                </a>
                                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Status Report</h6>
                                                        <small class="text-muted">Template for project status updates</small>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Custom Document</h6>
                                        </div>
                                        <div class="card-body">
                                            <form id="create-doc-form">
                                                <div class="mb-3">
                                                    <label for="doc-title" class="form-label">Document Title</label>
                                                    <input type="text" class="form-control" id="doc-title" placeholder="Enter document title">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="doc-description" class="form-label">Description (optional)</label>
                                                    <textarea class="form-control" id="doc-description" rows="3" placeholder="Add a description for your document"></textarea>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="doc-folder" class="form-label">Save to Folder</label>
                                                    <select class="form-select" id="doc-folder">
                                                        <option value="root">My Drive (root)</option>
                                                        <option value="projects">Projects</option>
                                                        <option value="documentation">Documentation</option>
                                                        <option value="meetings">Meeting Notes</option>
                                                    </select>
                                                </div>
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input" type="checkbox" id="doc-share-team">
                                                    <label class="form-check-label" for="doc-share-team">
                                                        Share with project team
                                                    </label>
                                                </div>
                                                <div class="d-grid">
                                                    <button type="button" class="btn btn-primary" id="create-doc-btn">
                                                        <i class="bi bi-plus-circle me-2"></i>Create Document
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shared Documents Tab -->
                        <div class="tab-pane fade" id="shared-docs-content" role="tabpanel" aria-labelledby="shared-docs-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" class="form-control" placeholder="Search shared documents..." id="shared-docs-search">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Documents</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="recent">Recently Shared</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="owned">Owned by me</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="not-owned">Not owned by me</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Shared By</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="shared-docs-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Client Requirements Specification</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="User Avatar">
                                                    <span>Sarah Wilson</span>
                                                </div>
                                            </td>
                                            <td>Today, 9:15 AM</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'client-requirements-specification')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editFile('Client Requirements Specification')"><i class="bi bi-pencil"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Project Scope Document</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="User Avatar">
                                                    <span>David Chen</span>
                                                </div>
                                            </td>
                                            <td>Yesterday, 2:30 PM</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'project-scope-document')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editFile('Project Scope Document')"><i class="bi bi-pencil"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Development Guidelines</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="User Avatar">
                                                    <span>Emily Brown</span>
                                                </div>
                                            </td>
                                            <td>May 4, 2025</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'development-guidelines')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editFile('Development Guidelines')"><i class="bi bi-pencil"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Risk Assessment Report</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="User Avatar">
                                                    <span>Michael Johnson</span>
                                                </div>
                                            </td>
                                            <td>May 1, 2025</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'risk-assessment-report')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editFile('Risk Assessment Report')"><i class="bi bi-pencil"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize the page
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize sidebar toggle functionality
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    sidebar.classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 768) {
                    const isClickInsideSidebar = sidebar.contains(event.target);
                    const isClickOnToggle = sidebarToggle && sidebarToggle.contains(event.target);

                    if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768 && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });
        });

        // Google Maps Component
        const GoogleMapsHandler = {
            init() {
                // Add event listeners for the modal
                const modalSearchBtn = document.getElementById('modal-search-location');
                if (modalSearchBtn) {
                    modalSearchBtn.addEventListener('click', this.searchLocation.bind(this));
                }

                const modalSearchInput = document.getElementById('modal-location-search');
                if (modalSearchInput) {
                    modalSearchInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.searchLocation();
                        }
                    });
                }

                // Directions tab event listeners
                const getDirectionsBtn = document.getElementById('get-directions');
                if (getDirectionsBtn) {
                    getDirectionsBtn.addEventListener('click', this.getDirections.bind(this));
                }

                // Places tab event listeners
                const searchPlacesBtn = document.getElementById('search-places');
                if (searchPlacesBtn) {
                    searchPlacesBtn.addEventListener('click', this.searchPlaces.bind(this));
                }

                // Place type buttons
                document.querySelectorAll('.place-type-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const placeType = e.currentTarget.dataset.type;
                        const placesSearch = document.getElementById('places-search');
                        if (placesSearch) {
                            placesSearch.value = e.currentTarget.textContent.trim();
                        }
                    });
                });
            },

            async searchLocation() {
                const query = document.getElementById('modal-location-search').value.trim();
                if (!query) return;

                const mapContainer = document.getElementById('modal-map-container');
                if (!mapContainer) return;

                // Show loading state
                mapContainer.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                `;

                try {
                    const response = await fetch(`/api/google-maps/geocode?address=${encodeURIComponent(query)}`);
                    const data = await response.json();

                    if (data.status === 'success') {
                        this.renderMap(data.data);
                    } else {
                        mapContainer.innerHTML = `<div class="error">Error: ${data.message || 'Failed to find location'}</div>`;
                    }
                } catch (error) {
                    console.error('Error searching location:', error);

                    // Mock data for demo
                    mapContainer.innerHTML = `
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            Map would display here for: "${query}"
                        </div>
                        <div class="text-center mt-3">
                            <img src="https://via.placeholder.com/600x350?text=Map+of+${encodeURIComponent(query)}" alt="Map placeholder" class="img-fluid rounded">
                        </div>
                    `;
                }
            },

            renderMap(data) {
                // This would be implemented with the Google Maps JavaScript API
                const mapContainer = document.getElementById('modal-map-container');
                if (!mapContainer) return;

                mapContainer.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Map would display here
                    </div>
                    <div class="text-center mt-3">
                        <img src="https://via.placeholder.com/600x350?text=Google+Maps" alt="Map placeholder" class="img-fluid rounded">
                    </div>
                `;
            },

            async getDirections() {
                const from = document.getElementById('directions-from').value.trim();
                const to = document.getElementById('directions-to').value.trim();

                if (!from || !to) {
                    alert('Please enter both starting point and destination');
                    return;
                }

                const directionsResult = document.getElementById('directions-result');
                if (!directionsResult) return;

                // Show loading state
                directionsResult.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                `;

                // Get selected travel mode
                let travelMode = 'driving';
                if (document.getElementById('travel-transit').checked) {
                    travelMode = 'transit';
                } else if (document.getElementById('travel-walking').checked) {
                    travelMode = 'walking';
                } else if (document.getElementById('travel-cycling').checked) {
                    travelMode = 'bicycling';
                }

                try {
                    const response = await fetch(`/api/google-maps/directions?origin=${encodeURIComponent(from)}&destination=${encodeURIComponent(to)}&mode=${travelMode}`);
                    const data = await response.json();

                    if (data.status === 'success') {
                        this.renderDirections(data.data);
                    } else {
                        directionsResult.innerHTML = `<div class="alert alert-danger">Error: ${data.message || 'Failed to get directions'}</div>`;
                    }
                } catch (error) {
                    console.error('Error getting directions:', error);

                    // Mock data for demo
                    directionsResult.innerHTML = `
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            Directions would display here for route from "${from}" to "${to}" via ${travelMode}
                        </div>
                        <div class="card mt-3">
                            <div class="card-body">
                                <h5 class="card-title">Route Summary</h5>
                                <p class="card-text">Distance: 15.2 km</p>
                                <p class="card-text">Duration: 25 mins</p>
                                <hr>
                                <ol class="list-group list-group-numbered">
                                    <li class="list-group-item">Head north on Main St</li>
                                    <li class="list-group-item">Turn right onto Oak Ave</li>
                                    <li class="list-group-item">Continue onto Highway 101</li>
                                    <li class="list-group-item">Take exit 25 for Central Pkwy</li>
                                    <li class="list-group-item">Turn left onto Central Pkwy</li>
                                    <li class="list-group-item">Destination will be on the right</li>
                                </ol>
                            </div>
                        </div>
                    `;
                }
            },

            renderDirections(data) {
                // This would be implemented with the Google Maps Directions API
                const directionsResult = document.getElementById('directions-result');
                if (!directionsResult) return;

                directionsResult.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Directions would display here
                    </div>
                    <div class="card mt-3">
                        <div class="card-body">
                            <h5 class="card-title">Route Summary</h5>
                            <p class="card-text">Distance: 15.2 km</p>
                            <p class="card-text">Duration: 25 mins</p>
                            <hr>
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item">Head north on Main St</li>
                                <li class="list-group-item">Turn right onto Oak Ave</li>
                                <li class="list-group-item">Continue onto Highway 101</li>
                                <li class="list-group-item">Take exit 25 for Central Pkwy</li>
                                <li class="list-group-item">Turn left onto Central Pkwy</li>
                                <li class="list-group-item">Destination will be on the right</li>
                            </ol>
                        </div>
                    </div>
                `;
            },

            async searchPlaces() {
                const query = document.getElementById('places-search').value.trim();
                const radius = document.getElementById('places-radius').value;

                if (!query) {
                    alert('Please enter a search term');
                    return;
                }

                const placesResults = document.getElementById('places-results');
                if (!placesResults) return;

                // Show loading state
                placesResults.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                `;

                try {
                    const response = await fetch(`/api/google-maps/places?query=${encodeURIComponent(query)}&radius=${radius}`);
                    const data = await response.json();

                    if (data.status === 'success') {
                        this.renderPlaces(data.data);
                    } else {
                        placesResults.innerHTML = `<div class="alert alert-danger">Error: ${data.message || 'Failed to find places'}</div>`;
                    }
                } catch (error) {
                    console.error('Error searching places:', error);

                    // Mock data for demo
                    placesResults.innerHTML = `
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            Places results would display here for: "${query}" within ${radius}km
                        </div>
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">Sample Place 1</h5>
                                    <small>4.5 <i class="bi bi-star-fill text-warning"></i></small>
                                </div>
                                <p class="mb-1">123 Main St, Sample City</p>
                                <small>Open now · 2.3 km away</small>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">Sample Place 2</h5>
                                    <small>4.2 <i class="bi bi-star-fill text-warning"></i></small>
                                </div>
                                <p class="mb-1">456 Oak Ave, Sample City</p>
                                <small>Closed · 3.1 km away</small>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">Sample Place 3</h5>
                                    <small>4.8 <i class="bi bi-star-fill text-warning"></i></small>
                                </div>
                                <p class="mb-1">789 Pine St, Sample City</p>
                                <small>Open until 10 PM · 1.5 km away</small>
                            </a>
                        </div>
                    `;
                }
            },

            renderPlaces(data) {
                // This would be implemented with the Google Maps Places API
                const placesResults = document.getElementById('places-results');
                if (!placesResults) return;

                placesResults.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Places results would display here
                    </div>
                    <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">Sample Place 1</h5>
                                <small>4.5 <i class="bi bi-star-fill text-warning"></i></small>
                            </div>
                            <p class="mb-1">123 Main St, Sample City</p>
                            <small>Open now · 2.3 km away</small>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">Sample Place 2</h5>
                                <small>4.2 <i class="bi bi-star-fill text-warning"></i></small>
                            </div>
                            <p class="mb-1">456 Oak Ave, Sample City</p>
                            <small>Closed · 3.1 km away</small>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">Sample Place 3</h5>
                                <small>4.8 <i class="bi bi-star-fill text-warning"></i></small>
                            </div>
                            <p class="mb-1">789 Pine St, Sample City</p>
                            <small>Open until 10 PM · 1.5 km away</small>
                        </a>
                    </div>
                `;
            }
        };

        // Google Sheets Component
        const GoogleSheetsHandler = {
            init() {
                // Dashboard component event listeners
                const refreshSheetsBtn = document.getElementById('refresh-sheets');
                if (refreshSheetsBtn) {
                    refreshSheetsBtn.addEventListener('click', this.refreshSheets.bind(this));
                }

                const createNewSheetBtn = document.getElementById('create-new-sheet');
                if (createNewSheetBtn) {
                    createNewSheetBtn.addEventListener('click', () => {
                        const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
                        sheetsModal.show();

                        // Switch to the Create New tab
                        const createTab = document.getElementById('create-tab');
                        if (createTab) {
                            createTab.click();
                        }
                    });
                }

                // Modal event listeners
                const createSheetBtn = document.getElementById('create-sheet-btn');
                if (createSheetBtn) {
                    createSheetBtn.addEventListener('click', this.createSheet.bind(this));
                }

                const importSheetBtn = document.getElementById('import-sheet-btn');
                if (importSheetBtn) {
                    importSheetBtn.addEventListener('click', this.importSheet.bind(this));
                }

                // Initialize the modal with event listeners
                const sheetsModal = document.getElementById('sheetsModal');
                if (sheetsModal) {
                    sheetsModal.addEventListener('shown.bs.modal', () => {
                        // Focus on the sheet title input when the create tab is active
                        const createTab = document.getElementById('create-tab');
                        if (createTab.classList.contains('active')) {
                            const sheetTitleInput = document.getElementById('sheet-title');
                            if (sheetTitleInput) {
                                sheetTitleInput.focus();
                            }
                        }
                    });
                }
            },

            refreshSheets() {
                // Show loading state in the dashboard component
                const sheetsComponent = document.querySelector('.google-sheets-component .component-body');
                if (sheetsComponent) {
                    sheetsComponent.innerHTML = '<div class="loading">Refreshing sheets...</div>';
                }

                // Simulate API call
                setTimeout(() => {
                    if (sheetsComponent) {
                        sheetsComponent.innerHTML = `
                            <div class="list-group">
                                <a href="https://docs.google.com/spreadsheets" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <span>Project_Timeline_2025.xlsx</span>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">Just now</span>
                                </a>
                                <a href="https://docs.google.com/spreadsheets" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <span>Budget_Tracking_Q2_2025.xlsx</span>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">1 week ago</span>
                                </a>
                                <a href="https://docs.google.com/spreadsheets" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                        <span>Resource_Allocation_2025.xlsx</span>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">2 weeks ago</span>
                                </a>
                            </div>
                            <div class="d-grid gap-2 mt-3">
                                <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                                    <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                                </button>
                            </div>
                        `;
                    }
                }, 1000);
            },

            createSheet() {
                const sheetTitle = document.getElementById('sheet-title').value.trim();
                const sheetTemplate = document.getElementById('sheet-template').value;

                if (!sheetTitle) {
                    alert('Please enter a title for your sheet');
                    return;
                }

                const createSheetBtn = document.getElementById('create-sheet-btn');
                if (!createSheetBtn) return;

                // Show loading state
                const originalText = createSheetBtn.innerHTML;
                createSheetBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating...';
                createSheetBtn.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    // Reset button state
                    createSheetBtn.innerHTML = originalText;
                    createSheetBtn.disabled = false;

                    // Show success message
                    alert(`Sheet "${sheetTitle}" created successfully!`);

                    // Close the modal
                    const sheetsModal = bootstrap.Modal.getInstance(document.getElementById('sheetsModal'));
                    sheetsModal.hide();

                    // Update the dashboard component to show the new sheet
                    this.refreshSheets();
                }, 2000);
            },

            importSheet() {
                const importTitle = document.getElementById('import-title').value.trim();
                const importFile = document.getElementById('import-file');

                if (!importTitle) {
                    alert('Please enter a title for your imported sheet');
                    return;
                }

                if (!importFile.files || importFile.files.length === 0) {
                    alert('Please select a file to import');
                    return;
                }

                const importSheetBtn = document.getElementById('import-sheet-btn');
                if (!importSheetBtn) return;

                // Show loading state
                const originalText = importSheetBtn.innerHTML;
                importSheetBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Importing...';
                importSheetBtn.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    // Reset button state
                    importSheetBtn.innerHTML = originalText;
                    importSheetBtn.disabled = false;

                    // Show success message
                    alert(`File "${importFile.files[0].name}" imported successfully as "${importTitle}"!`);

                    // Close the modal
                    const sheetsModal = bootstrap.Modal.getInstance(document.getElementById('sheetsModal'));
                    sheetsModal.hide();

                    // Update the dashboard component to show the new sheet
                    this.refreshSheets();
                }, 2000);
            }
        };

        // Gmail Component
        const GoogleGmailHandler = {
            init() {
                // Dashboard component event listeners
                const refreshGmailBtn = document.getElementById('refresh-gmail');
                if (refreshGmailBtn) {
                    refreshGmailBtn.addEventListener('click', this.refreshGmail.bind(this));
                }

                const composeEmailBtn = document.getElementById('compose-email');
                if (composeEmailBtn) {
                    composeEmailBtn.addEventListener('click', () => {
                        // The modal will be shown by the data-bs-toggle attribute
                        // Switch to the Compose tab
                        setTimeout(() => {
                            const composeTab = document.getElementById('compose-tab');
                            if (composeTab) {
                                composeTab.click();
                            }
                        }, 300);
                    });
                }

                // Modal event listeners
                const sendEmailBtn = document.getElementById('send-email');
                if (sendEmailBtn) {
                    sendEmailBtn.addEventListener('click', this.sendEmail.bind(this));
                }

                const saveDraftBtn = document.getElementById('save-draft');
                if (saveDraftBtn) {
                    saveDraftBtn.addEventListener('click', this.saveDraft.bind(this));
                }

                // Email attachments
                const emailAttachments = document.getElementById('email-attachments');
                if (emailAttachments) {
                    emailAttachments.addEventListener('change', this.handleAttachments.bind(this));
                }
            },

            refreshGmail() {
                // Show loading state in the dashboard component
                const gmailComponent = document.querySelector('.google-gmail-component .component-body');
                if (gmailComponent) {
                    gmailComponent.innerHTML = '<div class="loading">Refreshing emails...</div>';
                }

                // Simulate API call
                setTimeout(() => {
                    if (gmailComponent) {
                        gmailComponent.innerHTML = `
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">SC</div>
                                            <div>
                                                <div class="fw-bold">Sarah Chen</div>
                                                <div class="small text-truncate" style="max-width: 200px;">Project update: New timeline for Phase 2</div>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">Just now</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">JD</div>
                                            <div>
                                                <div class="fw-bold">John Davis</div>
                                                <div class="small text-truncate" style="max-width: 200px;">Meeting notes from yesterday's client call</div>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">1h</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">RM</div>
                                            <div>
                                                <div class="fw-bold">Rachel Miller</div>
                                                <div class="small text-truncate" style="max-width: 200px;">Budget approval for new resources</div>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">3h</span>
                                </a>
                            </div>
                            <div class="d-grid gap-2 mt-3">
                                <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#gmailModal">
                                    <i class="bi bi-envelope me-2"></i>Open Gmail
                                </button>
                            </div>
                        `;
                    }
                }, 1000);
            },

            sendEmail() {
                const to = document.getElementById('email-to').value.trim();
                const subject = document.getElementById('email-subject').value.trim();

                if (!to) {
                    alert('Please enter a recipient email address');
                    return;
                }

                if (!subject) {
                    alert('Please enter an email subject');
                    return;
                }

                const sendEmailBtn = document.getElementById('send-email');
                if (!sendEmailBtn) return;

                // Show loading state
                const originalText = sendEmailBtn.innerHTML;
                sendEmailBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';
                sendEmailBtn.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    // Reset button state
                    sendEmailBtn.innerHTML = originalText;
                    sendEmailBtn.disabled = false;

                    // Show success message
                    alert('Email sent successfully!');

                    // Reset form
                    document.getElementById('compose-email-form').reset();
                    document.getElementById('attachment-list').innerHTML = '';

                    // Switch to Sent tab
                    const sentTab = document.getElementById('sent-tab');
                    if (sentTab) {
                        sentTab.click();
                    }
                }, 2000);
            },

            saveDraft() {
                const saveDraftBtn = document.getElementById('save-draft');
                if (!saveDraftBtn) return;

                // Show loading state
                const originalText = saveDraftBtn.innerHTML;
                saveDraftBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
                saveDraftBtn.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    // Reset button state
                    saveDraftBtn.innerHTML = originalText;
                    saveDraftBtn.disabled = false;

                    // Show success message
                    alert('Draft saved successfully!');
                }, 1000);
            },

            handleAttachments(e) {
                const files = e.target.files;
                if (!files || files.length === 0) return;

                const attachmentList = document.getElementById('attachment-list');
                if (!attachmentList) return;

                // Clear previous attachments
                attachmentList.innerHTML = '';

                // Add each file to the list
                Array.from(files).forEach(file => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'attachment-item d-flex justify-content-between align-items-center p-2 border rounded mb-2';

                    // Determine file icon based on type
                    let fileIcon = 'bi-file-earmark';
                    if (file.type.includes('image')) {
                        fileIcon = 'bi-file-earmark-image';
                    } else if (file.type.includes('pdf')) {
                        fileIcon = 'bi-file-earmark-pdf';
                    } else if (file.type.includes('word')) {
                        fileIcon = 'bi-file-earmark-word';
                    } else if (file.type.includes('excel') || file.type.includes('sheet')) {
                        fileIcon = 'bi-file-earmark-excel';
                    }

                    // Format file size
                    const fileSize = file.size < 1024 * 1024
                        ? `${(file.size / 1024).toFixed(1)} KB`
                        : `${(file.size / (1024 * 1024)).toFixed(1)} MB`;

                    fileItem.innerHTML = `
                        <div>
                            <i class="bi ${fileIcon} me-2"></i>
                            <span>${file.name}</span>
                            <small class="text-muted ms-2">${fileSize}</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-attachment">
                            <i class="bi bi-x"></i>
                        </button>
                    `;

                    attachmentList.appendChild(fileItem);

                    // Add event listener to remove button
                    const removeBtn = fileItem.querySelector('.remove-attachment');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', () => {
                            fileItem.remove();
                        });
                    }
                });
            }
        };

        // Google Docs Component
        const GoogleDocsHandler = {
            init() {
                // Modal event listeners
                const createDocBtn = document.getElementById('create-doc-btn');
                if (createDocBtn) {
                    createDocBtn.addEventListener('click', this.createDocument.bind(this));
                }

                // Template links
                document.querySelectorAll('#create-doc-content .list-group-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const templateName = e.currentTarget.querySelector('h6').textContent;
                        this.createDocumentFromTemplate(templateName);
                    });
                });

                // Search functionality
                const docsSearch = document.getElementById('docs-search');
                if (docsSearch) {
                    docsSearch.addEventListener('input', this.searchDocuments.bind(this));
                }

                // Sort functionality
                document.querySelectorAll('#my-docs-content .dropdown-item[data-sort]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const sortBy = e.currentTarget.dataset.sort;
                        this.sortDocuments(sortBy);
                    });
                });

                // Filter functionality for shared docs
                document.querySelectorAll('#shared-docs-content .dropdown-item[data-filter]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const filterBy = e.currentTarget.dataset.filter;
                        this.filterSharedDocuments(filterBy);
                    });
                });
            },

            createDocument() {
                const docTitle = document.getElementById('doc-title').value.trim();
                if (!docTitle) {
                    alert('Please enter a document title');
                    return;
                }

                const createDocBtn = document.getElementById('create-doc-btn');
                if (!createDocBtn) return;

                // Show loading state
                const originalText = createDocBtn.innerHTML;
                createDocBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating...';
                createDocBtn.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    // Reset button state
                    createDocBtn.innerHTML = originalText;
                    createDocBtn.disabled = false;

                    // Show success message
                    alert(`Document "${docTitle}" created successfully!`);

                    // Reset form
                    document.getElementById('create-doc-form').reset();

                    // Switch to My Documents tab
                    const myDocsTab = document.getElementById('my-docs-tab');
                    if (myDocsTab) {
                        myDocsTab.click();
                    }

                    // Add the new document to the list
                    const docsList = document.getElementById('docs-list');
                    if (docsList) {
                        const newRow = document.createElement('tr');
                        newRow.innerHTML = `
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                    <span>${docTitle}</span>
                                </div>
                            </td>
                            <td>Just now</td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-pencil"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                </div>
                            </td>
                        `;
                        docsList.insertBefore(newRow, docsList.firstChild);
                    }
                }, 1500);
            },

            createDocumentFromTemplate(templateName) {
                // Show loading state
                const templateItems = document.querySelectorAll('#create-doc-content .list-group-item');
                templateItems.forEach(item => {
                    item.classList.add('disabled');
                });

                // Simulate API call
                setTimeout(() => {
                    // Reset state
                    templateItems.forEach(item => {
                        item.classList.remove('disabled');
                    });

                    // Show success message
                    alert(`Document created from "${templateName}" template!`);

                    // Switch to My Documents tab
                    const myDocsTab = document.getElementById('my-docs-tab');
                    if (myDocsTab) {
                        myDocsTab.click();
                    }

                    // Add the new document to the list
                    const docsList = document.getElementById('docs-list');
                    if (docsList) {
                        const newRow = document.createElement('tr');
                        newRow.innerHTML = `
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                    <span>New ${templateName}</span>
                                </div>
                            </td>
                            <td>Just now</td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary"><i class="bi bi-pencil"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                </div>
                            </td>
                        `;
                        docsList.insertBefore(newRow, docsList.firstChild);
                    }
                }, 1000);
            },

            searchDocuments() {
                const searchTerm = document.getElementById('docs-search').value.trim().toLowerCase();
                const rows = document.querySelectorAll('#docs-list tr');

                rows.forEach(row => {
                    const docName = row.querySelector('span').textContent.toLowerCase();
                    if (docName.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            },

            sortDocuments(sortBy) {
                const docsList = document.getElementById('docs-list');
                if (!docsList) return;

                const rows = Array.from(docsList.querySelectorAll('tr'));

                // Sort the rows based on the selected criteria
                rows.sort((a, b) => {
                    if (sortBy === 'name') {
                        const nameA = a.querySelector('span').textContent.toLowerCase();
                        const nameB = b.querySelector('span').textContent.toLowerCase();
                        return nameA.localeCompare(nameB);
                    } else if (sortBy === 'date') {
                        const dateA = a.querySelector('td:nth-child(2)').textContent;
                        const dateB = b.querySelector('td:nth-child(2)').textContent;

                        // Handle "Just now" and other special cases
                        if (dateA === 'Just now') return -1;
                        if (dateB === 'Just now') return 1;
                        if (dateA.includes('Today')) return -1;
                        if (dateB.includes('Today')) return 1;
                        if (dateA.includes('Yesterday')) return -1;
                        if (dateB.includes('Yesterday')) return 1;

                        return dateA.localeCompare(dateB);
                    }
                    return 0;
                });

                // Clear the list and append the sorted rows
                while (docsList.firstChild) {
                    docsList.removeChild(docsList.firstChild);
                }

                rows.forEach(row => {
                    docsList.appendChild(row);
                });
            },

            filterSharedDocuments(filterBy) {
                const rows = document.querySelectorAll('#shared-docs-list tr');

                rows.forEach(row => {
                    if (filterBy === 'all') {
                        row.style.display = '';
                        return;
                    }

                    const sharedBy = row.querySelector('td:nth-child(2) span').textContent;
                    const date = row.querySelector('td:nth-child(3)').textContent;

                    if (filterBy === 'recent' && (date.includes('Today') || date.includes('Yesterday'))) {
                        row.style.display = '';
                    } else if (filterBy === 'owned' && sharedBy === 'You') {
                        row.style.display = '';
                    } else if (filterBy === 'not-owned' && sharedBy !== 'You') {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        };

        // Google Drive Component
        const GoogleDriveHandler = {
            init() {
                // Dashboard component event listeners
                const refreshDriveBtn = document.getElementById('refresh-drive');
                if (refreshDriveBtn) {
                    refreshDriveBtn.addEventListener('click', this.refreshDrive.bind(this));
                }

                const uploadFileBtn = document.getElementById('upload-file');
                if (uploadFileBtn) {
                    uploadFileBtn.addEventListener('click', this.uploadFile.bind(this));
                }
            },

            refreshDrive() {
                // Show loading state in the dashboard component
                const driveComponent = document.querySelector('.google-drive-component .component-body');
                if (driveComponent) {
                    driveComponent.innerHTML = '<div class="loading">Refreshing files...</div>';
                }

                // Simulate API call
                setTimeout(() => {
                    if (driveComponent) {
                        driveComponent.innerHTML = `
                            <div class="list-group">
                                <a href="https://drive.google.com" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-folder-fill text-primary me-2"></i>
                                        <span>Project Documentation</span>
                                    </div>
                                    <span class="badge bg-secondary rounded-pill">12 files</span>
                                </a>
                                <a href="https://drive.google.com" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-folder-fill text-primary me-2"></i>
                                        <span>Client Presentations</span>
                                    </div>
                                    <span class="badge bg-secondary rounded-pill">5 files</span>
                                </a>
                                <a href="https://drive.google.com" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                        <span>Project_Proposal_Final.pdf</span>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">Just now</span>
                                </a>
                            </div>
                            <div class="d-grid gap-2 mt-3">
                                <button class="btn btn-outline-primary" type="button">
                                    <i class="bi bi-folder me-2"></i>Open Drive
                                </button>
                            </div>
                        `;
                    }
                }, 1000);
            },

            uploadFile() {
                // Create a file input element
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.multiple = true;
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);

                // Trigger click on the file input
                fileInput.click();

                // Handle file selection
                fileInput.addEventListener('change', (e) => {
                    const files = e.target.files;
                    if (!files || files.length === 0) {
                        document.body.removeChild(fileInput);
                        return;
                    }

                    // Show loading state
                    const driveComponent = document.querySelector('.google-drive-component .component-body');
                    if (driveComponent) {
                        driveComponent.innerHTML = '<div class="loading">Uploading files...</div>';
                    }

                    // Simulate API call
                    setTimeout(() => {
                        // Show success message
                        alert(`${files.length} file(s) uploaded successfully!`);

                        // Refresh the drive component
                        this.refreshDrive();

                        // Remove the file input
                        document.body.removeChild(fileInput);
                    }, 2000);
                });
            }
        };

        // Google Calendar Component
        const GoogleCalendarHandler = {
            init() {
                // Dashboard component event listeners
                const refreshCalendarBtn = document.getElementById('refresh-calendar');
                if (refreshCalendarBtn) {
                    refreshCalendarBtn.addEventListener('click', this.refreshCalendar.bind(this));
                }

                const createNewEventBtn = document.getElementById('create-new-event');
                if (createNewEventBtn) {
                    createNewEventBtn.addEventListener('click', this.createNewEvent.bind(this));
                }
            },

            refreshCalendar() {
                // Show loading state in the dashboard component
                const calendarComponent = document.querySelector('.google-calendar-component .component-body');
                if (calendarComponent) {
                    calendarComponent.innerHTML = '<div class="loading">Refreshing calendar...</div>';
                }

                // Simulate API call
                setTimeout(() => {
                    if (calendarComponent) {
                        calendarComponent.innerHTML = `
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="fw-bold">Project Kickoff Meeting</div>
                                        <div class="small text-muted">
                                            <i class="bi bi-clock me-1"></i>Today, 10:00 AM - 11:30 AM
                                        </div>
                                    </div>
                                    <span class="badge bg-warning rounded-pill">Today</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="fw-bold">Client Demo</div>
                                        <div class="small text-muted">
                                            <i class="bi bi-clock me-1"></i>Tomorrow, 2:00 PM - 3:00 PM
                                        </div>
                                    </div>
                                    <span class="badge bg-info rounded-pill">Tomorrow</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="fw-bold">Sprint Planning</div>
                                        <div class="small text-muted">
                                            <i class="bi bi-clock me-1"></i>May 15, 9:00 AM - 10:30 AM
                                        </div>
                                    </div>
                                    <span class="badge bg-secondary rounded-pill">Next Week</span>
                                </a>
                            </div>
                            <div class="d-grid gap-2 mt-3">
                                <button class="btn btn-outline-primary" type="button">
                                    <i class="bi bi-calendar3 me-2"></i>View Full Calendar
                                </button>
                            </div>
                        `;
                    }
                }, 1000);
            },

            createNewEvent() {
                // In a real implementation, this would open a modal to create a new event
                alert('This would open a modal to create a new calendar event');
            }
        };



        // Attachments Handler
        const AttachmentsHandler = {
            files: [],

            init() {
                // Initialize file upload
                const fileUpload = document.getElementById('file-upload');
                if (fileUpload) {
                    fileUpload.addEventListener('change', this.handleFileSelection.bind(this));
                }

                // Initialize dropzone
                const dropzone = document.getElementById('dropzone');
                if (dropzone) {
                    dropzone.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        dropzone.style.borderColor = 'var(--app-primary-color)';
                        dropzone.style.backgroundColor = '#f0f8ff';
                    });

                    dropzone.addEventListener('dragleave', () => {
                        dropzone.style.borderColor = '#ccc';
                        dropzone.style.backgroundColor = '#f8f9fa';
                    });

                    dropzone.addEventListener('drop', (e) => {
                        e.preventDefault();
                        dropzone.style.borderColor = '#ccc';
                        dropzone.style.backgroundColor = '#f8f9fa';

                        if (e.dataTransfer.files.length > 0) {
                            this.handleFiles(e.dataTransfer.files);
                        }
                    });
                }

                // Initialize clear uploads button
                const clearUploadsBtn = document.getElementById('clear-uploads');
                if (clearUploadsBtn) {
                    clearUploadsBtn.addEventListener('click', this.clearUploads.bind(this));
                }

                // Initialize start upload button
                const startUploadBtn = document.getElementById('start-upload');
                if (startUploadBtn) {
                    startUploadBtn.addEventListener('click', this.startUpload.bind(this));
                }

                // Initialize search functionality
                const fileSearch = document.getElementById('file-search');
                if (fileSearch) {
                    fileSearch.addEventListener('input', this.searchFiles.bind(this));
                }

                const sharedSearch = document.getElementById('shared-search');
                if (sharedSearch) {
                    sharedSearch.addEventListener('input', this.searchSharedFiles.bind(this));
                }

                // Initialize sort functionality
                document.querySelectorAll('[data-sort]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.sortFiles(e.target.dataset.sort);
                    });
                });

                // Initialize filter functionality
                document.querySelectorAll('[data-filter]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.filterFiles(e.target.dataset.filter);
                    });
                });
            },

            handleFileSelection(event) {
                if (event.target.files.length > 0) {
                    this.handleFiles(event.target.files);
                }
            },

            handleFiles(fileList) {
                for (let i = 0; i < fileList.length; i++) {
                    const file = fileList[i];

                    // Check if file is already in the list
                    const isDuplicate = this.files.some(f =>
                        f.name === file.name &&
                        f.size === file.size
                    );

                    if (!isDuplicate) {
                        this.files.push({
                            id: Date.now() + i,
                            file: file,
                            name: file.name,
                            size: file.size,
                            type: file.type,
                            progress: 0,
                            status: 'pending'
                        });
                    }
                }

                this.updateUploadList();
                this.updateUploadButton();
            },

            updateUploadList() {
                const uploadList = document.getElementById('upload-list');
                if (!uploadList) return;

                if (this.files.length === 0) {
                    uploadList.innerHTML = '';
                    return;
                }

                let html = '';

                this.files.forEach(file => {
                    // Format file size
                    let fileSize = '';
                    if (file.size < 1024) {
                        fileSize = file.size + ' B';
                    } else if (file.size < 1024 * 1024) {
                        fileSize = Math.round(file.size / 1024) + ' KB';
                    } else {
                        fileSize = (file.size / (1024 * 1024)).toFixed(1) + ' MB';
                    }

                    // Determine icon based on file type
                    let icon = 'bi-file';
                    if (file.type.includes('image')) {
                        icon = 'bi-file-earmark-image';
                    } else if (file.type.includes('pdf')) {
                        icon = 'bi-file-earmark-pdf';
                    } else if (file.type.includes('word') || file.type.includes('document')) {
                        icon = 'bi-file-earmark-word';
                    } else if (file.type.includes('excel') || file.type.includes('sheet')) {
                        icon = 'bi-file-earmark-excel';
                    } else if (file.type.includes('zip') || file.type.includes('archive')) {
                        icon = 'bi-file-earmark-zip';
                    } else if (file.type.includes('powerpoint') || file.type.includes('presentation')) {
                        icon = 'bi-file-earmark-slides';
                    }

                    // Determine status badge
                    let statusBadge = '';
                    if (file.status === 'pending') {
                        statusBadge = '<span class="badge bg-secondary">Pending</span>';
                    } else if (file.status === 'uploading') {
                        statusBadge = '<span class="badge bg-primary">Uploading</span>';
                    } else if (file.status === 'success') {
                        statusBadge = '<span class="badge bg-success">Uploaded</span>';
                    } else if (file.status === 'error') {
                        statusBadge = '<span class="badge bg-danger">Failed</span>';
                    }

                    html += `
                        <div class="card mb-2">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi ${icon} me-3 fs-4"></i>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <h6 class="mb-0">${file.name}</h6>
                                            ${statusBadge}
                                        </div>
                                        <div class="text-muted small">${fileSize}</div>
                                        ${file.status === 'uploading' ? `
                                            <div class="progress mt-2" style="height: 5px;">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                    role="progressbar" style="width: ${file.progress}%"></div>
                                            </div>
                                        ` : ''}
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger ms-3 remove-file" data-id="${file.id}">
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });

                uploadList.innerHTML = html;

                // Add event listeners to remove buttons
                document.querySelectorAll('.remove-file').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const id = parseInt(e.currentTarget.dataset.id);
                        this.removeFile(id);
                    });
                });
            },

            removeFile(id) {
                this.files = this.files.filter(file => file.id !== id);
                this.updateUploadList();
                this.updateUploadButton();
            },

            clearUploads() {
                this.files = [];
                this.updateUploadList();
                this.updateUploadButton();
            },

            updateUploadButton() {
                const uploadButton = document.getElementById('start-upload');
                if (uploadButton) {
                    uploadButton.disabled = this.files.length === 0;
                }
            },

            startUpload() {
                if (this.files.length === 0) return;

                const progressBar = document.querySelector('.progress');
                if (progressBar) {
                    progressBar.style.display = 'block';
                }

                const progressBarFill = document.querySelector('.progress-bar');
                if (progressBarFill) {
                    progressBarFill.style.width = '0%';
                }

                // Update file statuses
                this.files.forEach(file => {
                    file.status = 'uploading';
                    file.progress = 0;
                });

                this.updateUploadList();

                // Simulate upload progress
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;

                    if (progressBarFill) {
                        progressBarFill.style.width = progress + '%';
                    }

                    this.files.forEach(file => {
                        // Randomize progress a bit for each file
                        const fileProgress = progress + Math.floor(Math.random() * 10) - 5;
                        file.progress = Math.min(Math.max(fileProgress, 0), 100);
                    });

                    this.updateUploadList();

                    if (progress >= 100) {
                        clearInterval(interval);

                        // Mark all files as uploaded
                        this.files.forEach(file => {
                            file.status = 'success';
                            file.progress = 100;
                        });

                        this.updateUploadList();

                        // Hide progress bar after a delay
                        setTimeout(() => {
                            if (progressBar) {
                                progressBar.style.display = 'none';
                            }

                            // Show success message
                            alert('Files uploaded successfully!');

                            // Clear the upload list
                            this.clearUploads();

                            // Switch to the Files tab
                            const filesTab = document.getElementById('files-tab');
                            if (filesTab) {
                                const tab = new bootstrap.Tab(filesTab);
                                tab.show();
                            }
                        }, 1000);
                    }
                }, 200);
            },

            searchFiles(event) {
                const searchTerm = event.target.value.toLowerCase();
                const filesList = document.getElementById('files-list');

                if (!filesList) return;

                const rows = filesList.querySelectorAll('tr');

                rows.forEach(row => {
                    const fileName = row.querySelector('.d-flex span').textContent.toLowerCase();

                    if (fileName.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            },

            searchSharedFiles(event) {
                const searchTerm = event.target.value.toLowerCase();
                const sharedFilesList = document.getElementById('shared-files-list');

                if (!sharedFilesList) return;

                const rows = sharedFilesList.querySelectorAll('tr');

                rows.forEach(row => {
                    const fileName = row.querySelector('.d-flex span').textContent.toLowerCase();
                    const sharedBy = row.querySelectorAll('td')[1].textContent.toLowerCase();

                    if (fileName.includes(searchTerm) || sharedBy.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            },

            sortFiles(sortBy) {
                const filesList = document.getElementById('files-list');

                if (!filesList) return;

                const rows = Array.from(filesList.querySelectorAll('tr'));

                rows.sort((a, b) => {
                    let aValue, bValue;

                    if (sortBy === 'name') {
                        aValue = a.querySelector('.d-flex span').textContent;
                        bValue = b.querySelector('.d-flex span').textContent;
                    } else if (sortBy === 'date') {
                        aValue = a.querySelectorAll('td')[3].textContent;
                        bValue = b.querySelectorAll('td')[3].textContent;
                    } else if (sortBy === 'size') {
                        aValue = this.parseFileSize(a.querySelectorAll('td')[2].textContent);
                        bValue = this.parseFileSize(b.querySelectorAll('td')[2].textContent);
                        return aValue - bValue;
                    } else if (sortBy === 'type') {
                        aValue = a.querySelectorAll('td')[1].textContent;
                        bValue = b.querySelectorAll('td')[1].textContent;
                    }

                    return aValue.localeCompare(bValue);
                });

                // Clear the table
                while (filesList.firstChild) {
                    filesList.removeChild(filesList.firstChild);
                }

                // Add sorted rows
                rows.forEach(row => {
                    filesList.appendChild(row);
                });
            },

            parseFileSize(sizeStr) {
                const size = parseFloat(sizeStr);
                if (sizeStr.includes('KB')) {
                    return size * 1024;
                } else if (sizeStr.includes('MB')) {
                    return size * 1024 * 1024;
                } else if (sizeStr.includes('GB')) {
                    return size * 1024 * 1024 * 1024;
                }
                return size;
            },

            filterFiles(filter) {
                const sharedFilesList = document.getElementById('shared-files-list');

                if (!sharedFilesList) return;

                const rows = sharedFilesList.querySelectorAll('tr');

                rows.forEach(row => {
                    const fileType = row.querySelector('.bi').className;

                    if (filter === 'all') {
                        row.style.display = '';
                    } else if (filter === 'documents' && (fileType.includes('pdf') || fileType.includes('word') || fileType.includes('slides'))) {
                        row.style.display = '';
                    } else if (filter === 'images' && fileType.includes('image')) {
                        row.style.display = '';
                    } else if (filter === 'other' && !(fileType.includes('pdf') || fileType.includes('word') || fileType.includes('slides') || fileType.includes('image'))) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        };

        // Attachments Handler
        const AttachmentsHandler = {
            init() {
                // Search functionality
                const attachmentSearch = document.getElementById('attachment-search');
                if (attachmentSearch) {
                    attachmentSearch.addEventListener('input', this.searchAttachments.bind(this));
                }

                // Sort functionality
                document.querySelectorAll('#all-attachments-content .dropdown-item[data-sort]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const sortBy = e.currentTarget.getAttribute('data-sort');
                        this.sortAttachments(sortBy);
                    });
                });

                // Filter functionality
                document.querySelectorAll('#all-attachments-content .dropdown-item[data-filter]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const filter = e.currentTarget.getAttribute('data-filter');
                        this.filterAttachments(filter);
                    });
                });

                // Upload button
                const uploadAttachmentBtn = document.getElementById('upload-attachment-btn');
                if (uploadAttachmentBtn) {
                    uploadAttachmentBtn.addEventListener('click', this.uploadAttachment.bind(this));
                }
            },

            searchAttachments(event) {
                const searchTerm = event.target.value.toLowerCase();
                const attachmentRows = document.querySelectorAll('#all-attachments-content tbody tr');

                attachmentRows.forEach(row => {
                    const fileName = row.querySelector('.d-flex span').textContent.toLowerCase();
                    if (fileName.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            },

            sortAttachments(sortBy) {
                const attachmentsList = document.querySelector('#all-attachments-content tbody');
                if (!attachmentsList) return;

                const rows = Array.from(attachmentsList.querySelectorAll('tr'));

                rows.sort((a, b) => {
                    let aValue, bValue;

                    if (sortBy === 'name') {
                        aValue = a.querySelector('.d-flex span').textContent;
                        bValue = b.querySelector('.d-flex span').textContent;
                    } else if (sortBy === 'date') {
                        aValue = a.querySelectorAll('td')[3].textContent;
                        bValue = b.querySelectorAll('td')[3].textContent;
                    } else if (sortBy === 'size') {
                        aValue = this.parseFileSize(a.querySelectorAll('td')[2].textContent);
                        bValue = this.parseFileSize(b.querySelectorAll('td')[2].textContent);
                        return aValue - bValue;
                    } else if (sortBy === 'type') {
                        aValue = a.querySelectorAll('td')[1].textContent;
                        bValue = b.querySelectorAll('td')[1].textContent;
                    }

                    return aValue.localeCompare(bValue);
                });

                // Clear the table
                while (attachmentsList.firstChild) {
                    attachmentsList.removeChild(attachmentsList.firstChild);
                }

                // Add sorted rows
                rows.forEach(row => {
                    attachmentsList.appendChild(row);
                });
            },

            parseFileSize(sizeStr) {
                const size = parseFloat(sizeStr);
                if (sizeStr.includes('KB')) {
                    return size * 1024;
                } else if (sizeStr.includes('MB')) {
                    return size * 1024 * 1024;
                } else if (sizeStr.includes('GB')) {
                    return size * 1024 * 1024 * 1024;
                }
                return size;
            },

            filterAttachments(filter) {
                const attachmentRows = document.querySelectorAll('#all-attachments-content tbody tr');

                attachmentRows.forEach(row => {
                    const fileType = row.querySelectorAll('td')[1].textContent;

                    if (filter === 'all') {
                        row.style.display = '';
                    } else if (filter === 'documents' && (fileType === 'Document')) {
                        row.style.display = '';
                    } else if (filter === 'images' && (fileType === 'Image')) {
                        row.style.display = '';
                    } else if (filter === 'spreadsheets' && (fileType === 'Spreadsheet')) {
                        row.style.display = '';
                    } else if (filter === 'pdfs' && (fileType === 'PDF')) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            },

            uploadAttachment() {
                const fileInput = document.getElementById('attachment-file');
                const nameInput = document.getElementById('attachment-name');
                const projectSelect = document.getElementById('attachment-project');
                const descriptionInput = document.getElementById('attachment-description');
                const notifyCheckbox = document.getElementById('attachment-notify');

                if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                    alert('Please select a file to upload');
                    return;
                }

                const file = fileInput.files[0];
                const fileName = nameInput.value || file.name;
                const project = projectSelect.value;
                const description = descriptionInput.value;
                const notify = notifyCheckbox.checked;

                // Show loading state
                const uploadBtn = document.getElementById('upload-attachment-btn');
                const originalText = uploadBtn.innerHTML;
                uploadBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Uploading...';
                uploadBtn.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    // Reset button state
                    uploadBtn.innerHTML = originalText;
                    uploadBtn.disabled = false;

                    // Show success message
                    alert(`File "${fileName}" uploaded successfully!`);

                    // Reset form
                    fileInput.value = '';
                    nameInput.value = '';
                    descriptionInput.value = '';
                    notifyCheckbox.checked = false;

                    // Switch to All Attachments tab
                    const allAttachmentsTab = document.getElementById('all-attachments-tab');
                    if (allAttachmentsTab) {
                        allAttachmentsTab.click();
                    }
                }, 2000);
            }
        };

        // Initialize all components
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Google Integration Components
            GoogleMapsHandler.init();
            GoogleSheetsHandler.init();
            GoogleCalendarHandler.init();
            GoogleGmailHandler.init();
            GoogleDriveHandler.init();
            GoogleDocsHandler.init();

            // Initialize Attachments Handler
            AttachmentsHandler.init();
        });

        // Create a simple health endpoint for status checks
        if (window.fetch) {
            // Create a fake health endpoint for the hub to check
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                if (url.includes('/health')) {
                    return Promise.resolve({
                        ok: true,
                        status: 200,
                        json: () => Promise.resolve({ status: 'ok' })
                    });
                }
                return originalFetch(url, options);
            };
        }
    </script>

    <!-- Attachments Modal -->
    <div class="modal fade" id="attachmentsModal" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="attachmentsModalLabel"><i class="bi bi-paperclip"></i> Attachments</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="attachmentsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="all-attachments-tab" data-bs-toggle="tab" data-bs-target="#all-attachments-content" type="button" role="tab" aria-controls="all-attachments-content" aria-selected="true">All Attachments</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-attachment-tab" data-bs-toggle="tab" data-bs-target="#upload-attachment-content" type="button" role="tab" aria-controls="upload-attachment-content" aria-selected="false">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="recent-attachments-tab" data-bs-toggle="tab" data-bs-target="#recent-attachments-content" type="button" role="tab" aria-controls="recent-attachments-content" aria-selected="false">Recent</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="attachmentsTabContent">
                        <!-- All Attachments Tab -->
                        <div class="tab-pane fade show active" id="all-attachments-content" role="tabpanel" aria-labelledby="all-attachments-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="attachment-search" class="form-control" placeholder="Search attachments...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="spreadsheets">Spreadsheets</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="pdfs">PDFs</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>Size</th>
                                            <th>Uploaded</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2 fs-5"></i>
                                                    <span>Project_Proposal_Final.pdf</span>
                                                </div>
                                            </td>
                                            <td>PDF</td>
                                            <td>2.4 MB</td>
                                            <td>Yesterday</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadAttachment('Project_Proposal_Final.pdf')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('pdf', 'Project_Proposal_Final.pdf')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Proposal_Final.pdf')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Proposal_Final.pdf')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2 fs-5"></i>
                                                    <span>Project_Timeline_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>Spreadsheet</td>
                                            <td>1.8 MB</td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadAttachment('Project_Timeline_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('spreadsheet', 'Project_Timeline_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Timeline_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Timeline_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2 fs-5"></i>
                                                    <span>Meeting_Minutes.docx</span>
                                                </div>
                                            </td>
                                            <td>Document</td>
                                            <td>1.2 MB</td>
                                            <td>1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadAttachment('Meeting_Minutes.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('document', 'Meeting_Minutes.docx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Meeting_Minutes.docx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Meeting_Minutes.docx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-image text-info me-2 fs-5"></i>
                                                    <span>Project_Diagram.png</span>
                                                </div>
                                            </td>
                                            <td>Image</td>
                                            <td>3.5 MB</td>
                                            <td>2 weeks ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadAttachment('Project_Diagram.png')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('image', 'Project_Diagram.png')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Diagram.png')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Diagram.png')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- Upload Attachment Tab -->
                        <div class="tab-pane fade" id="upload-attachment-content" role="tabpanel" aria-labelledby="upload-attachment-tab">
                            <form id="upload-attachment-form">
                                <div class="mb-3">
                                    <label for="attachment-file" class="form-label">Select file to upload</label>
                                    <input class="form-control" type="file" id="attachment-file">
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-name" class="form-label">File Name (optional)</label>
                                    <input type="text" class="form-control" id="attachment-name" placeholder="Enter a name for the file">
                                    <div class="form-text">If left blank, the original file name will be used.</div>
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-project" class="form-label">Associated Project</label>
                                    <select class="form-select" id="attachment-project">
                                        <option selected>Website Redesign</option>
                                        <option>Mobile App Development</option>
                                        <option>Cloud Migration</option>
                                        <option>None</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="attachment-description" class="form-label">Description (optional)</label>
                                    <textarea class="form-control" id="attachment-description" rows="3" placeholder="Enter a description for the file"></textarea>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="attachment-notify">
                                        <label class="form-check-label" for="attachment-notify">
                                            Notify team members
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" id="upload-attachment-btn">Upload</button>
                            </form>
                        </div>
                        <!-- Recent Attachments Tab -->
                        <div class="tab-pane fade" id="recent-attachments-content" role="tabpanel" aria-labelledby="recent-attachments-tab">
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Project_Proposal_Final.pdf</h6>
                                                <small>2.4 MB - Uploaded: Yesterday</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadFile('Project_Proposal_Final.pdf')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('pdf', 'Project_Proposal_Final.pdf')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Proposal_Final.pdf')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Proposal_Final.pdf')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Project_Timeline_2025.xlsx</h6>
                                                <small>1.8 MB - Uploaded: 2 days ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadFile('Project_Timeline_2025.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('spreadsheet', 'Project_Timeline_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Timeline_2025.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Timeline_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Meeting_Minutes.docx</h6>
                                                <small>1.2 MB - Uploaded: 1 week ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadFile('Meeting_Minutes.docx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('document', 'Meeting_Minutes.docx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Meeting_Minutes.docx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Meeting_Minutes.docx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-image text-info me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Project_Diagram.png</h6>
                                                <small>3.5 MB - Uploaded: 2 weeks ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadFile('Project_Diagram.png')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('image', 'Project_Diagram.png')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Diagram.png')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Diagram.png')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Attachment functions
        function downloadAttachment(fileName) {
            downloadFile(fileName);
        }

        function downloadFile(fileName) {
            alert(`Downloading ${fileName}...`);
            // In a real application, this would trigger a download of the file

            // Create a temporary link element to simulate download
            const link = document.createElement('a');
            link.href = '#';
            link.download = fileName;

            // Append to body, click, and remove
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function viewAttachment(type, fileName) {
            // Create a file viewer modal if it doesn't exist
            const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

            // Set the file information
            const fileTitle = document.getElementById('fileViewerTitle');
            const fileContent = document.getElementById('fileViewerContent');

            // Set the title based on the file type and name
            fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;

            // Set the content based on the file type
            fileContent.innerHTML = getAttachmentContent(type, fileName);

            // Show the modal
            const modal = new bootstrap.Modal(fileViewerModal);
            modal.show();

            console.log(`Viewing ${fileName} directly`);
        }

        // Function to create the file viewer modal
        function createFileViewerModal() {
            // Create the modal element
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'fileViewerModal';
            modal.tabIndex = '-1';
            modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
            modal.setAttribute('aria-hidden', 'true');

            // Set the modal content
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                            <h5 class="modal-title" id="fileViewerTitle"></h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="fileViewerContent"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-success" onclick="downloadCurrentFile()"><i class="bi bi-download"></i> Download</button>
                            <button type="button" class="btn btn-outline-info" onclick="shareCurrentFile()"><i class="bi bi-share"></i> Share</button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteCurrentFile()"><i class="bi bi-trash"></i> Delete</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Append the modal to the body
            document.body.appendChild(modal);

            return modal;
        }

        // Helper function to get the appropriate icon for the file type
        function getFileTypeIcon(type) {
            if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
            if (type === 'document') return 'bi-file-earmark-text text-primary';
            if (type === 'spreadsheet') return 'bi-file-earmark-spreadsheet text-success';
            if (type === 'image') return 'bi-file-earmark-image text-info';
            if (type === 'presentation') return 'bi-file-earmark-slides text-warning';
            if (type === 'archive') return 'bi-file-earmark-zip text-warning';
            return 'bi-file-earmark text-secondary';
        }

        // Helper function to generate content for the attachment viewer
        function getAttachmentContent(type, fileName) {
            // Generate simulated content based on file type
            if (type === 'pdf') {
                return `<div class="text-center">
                        <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                        <h4 class="mt-3">${fileName}</h4>
                        <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                            <h5>Document Preview</h5>
                            <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                            <p>The document contains information related to project management.</p>
                        </div>
                        </div>`;
            } else if (type === 'document') {
                return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${fileName}</h4>
                        <hr>
                        <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                        <p>The document contains information related to project management.</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                        </div>`;
            } else if (type === 'spreadsheet') {
                return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${fileName}</h4>
                        <hr>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                <th>Project</th>
                                <th>Status</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                <td>Website Redesign</td>
                                <td>In Progress</td>
                                <td>2025-05-01</td>
                                <td>2025-06-15</td>
                                </tr>
                                <tr>
                                <td>Mobile App Development</td>
                                <td>Planning</td>
                                <td>2025-06-01</td>
                                <td>2025-08-30</td>
                                </tr>
                                <tr>
                                <td>Cloud Migration</td>
                                <td>Completed</td>
                                <td>2025-03-10</td>
                                <td>2025-04-20</td>
                                </tr>
                            </tbody>
                            </table>
                        </div>
                        </div>`;
            } else if (type === 'image') {
                return `<div class="text-center">
                        <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                            <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                            <h5 class="mt-3">${fileName}</h5>
                            <p>This is an image viewer. In a real application, the image would be displayed here.</p>
                        </div>
                        </div>`;
            } else if (type === 'presentation') {
                return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${fileName}</h4>
                        <hr>
                        <div class="text-center">
                            <i class="bi bi-file-earmark-slides text-warning" style="font-size: 48px;"></i>
                            <h5 class="mt-3">Presentation Preview</h5>
                            <div class="border p-3 mt-3 text-start">
                                <h5>Slide 1: Project Overview</h5>
                                <ul>
                                    <li>Project Goals</li>
                                    <li>Timeline</li>
                                    <li>Team Members</li>
                                </ul>
                            </div>
                            <div class="border p-3 mt-3 text-start">
                                <h5>Slide 2: Current Progress</h5>
                                <ul>
                                    <li>Completed Tasks</li>
                                    <li>Ongoing Work</li>
                                    <li>Upcoming Milestones</li>
                                </ul>
                            </div>
                        </div>
                        </div>`;
            } else if (type === 'archive') {
                return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${fileName}</h4>
                        <hr>
                        <div class="text-center">
                            <i class="bi bi-file-earmark-zip text-warning" style="font-size: 48px;"></i>
                            <h5 class="mt-3">Archive Contents</h5>
                        </div>
                        <div class="mt-3">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>File Name</th>
                                        <th>Type</th>
                                        <th>Size</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>project_logo.png</td>
                                        <td>Image</td>
                                        <td>250 KB</td>
                                    </tr>
                                    <tr>
                                        <td>mockups.psd</td>
                                        <td>Photoshop Document</td>
                                        <td>4.2 MB</td>
                                    </tr>
                                    <tr>
                                        <td>icons.svg</td>
                                        <td>SVG Image</td>
                                        <td>180 KB</td>
                                    </tr>
                                    <tr>
                                        <td>design_guidelines.pdf</td>
                                        <td>PDF Document</td>
                                        <td>1.8 MB</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        </div>`;
            }

            return `<div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
                    </div>`;
        }

        function deleteAttachment(fileName) {
            if (confirm(`Are you sure you want to delete ${fileName}?`)) {
                alert(`${fileName} has been deleted.`);
                // In a real application, this would delete the file
            }
        }

        function shareFile(fileName) {
            const email = prompt(`Enter email address to share ${fileName} with:`);
            if (email) {
                alert(`${fileName} has been shared with ${email}.`);
                // In a real application, this would share the file with the specified email
            }
        }

        function downloadSelectedAttachments() {
            alert('Downloading selected attachments...');
            // In a real application, this would download all selected attachments
            // For now, we'll just show an alert
        }

        // Function to download the current file from the file viewer modal
        function downloadCurrentFile() {
            const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();

            // Extract the file name from the title
            let fileName = fileTitle;

            // If the title contains an icon, remove it
            if (fileTitle.includes('<i class="')) {
                fileName = fileTitle.substring(fileTitle.indexOf('</i>') + 4).trim();
            }

            // Add file extension if not present
            if (!fileName.includes('.')) {
                // Determine file extension based on content
                const fileContent = document.getElementById('fileViewerContent');
                if (fileContent.innerHTML.includes('table-bordered')) {
                    fileName += '.xlsx';
                } else if (fileContent.innerHTML.includes('document viewer')) {
                    fileName += '.docx';
                } else if (fileContent.innerHTML.includes('PDF viewer')) {
                    fileName += '.pdf';
                } else if (fileContent.innerHTML.includes('image viewer')) {
                    fileName += '.png';
                } else {
                    fileName += '.txt';
                }
            }

            downloadFile(fileName);
        }

        // Function to delete the current file from the file viewer modal
        function deleteCurrentFile() {
            const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();

            // Extract the file name from the title
            let fileName = fileTitle;

            // If the title contains an icon, remove it
            if (fileTitle.includes('<i class="')) {
                fileName = fileTitle.substring(fileTitle.indexOf('</i>') + 4).trim();
            }

            deleteAttachment(fileName);

            // Close the modal after deletion
            const modal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
            if (modal) {
                modal.hide();
            }
        }

        // Function to share the current file from the file viewer modal
        function shareCurrentFile() {
            const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();

            // Extract the file name from the title
            let fileName = fileTitle;

            // If the title contains an icon, remove it
            if (fileTitle.includes('<i class="')) {
                fileName = fileTitle.substring(fileTitle.indexOf('</i>') + 4).trim();
            }

            shareFile(fileName);
        }

        // Function to edit a file
        function editFile(fileName) {
            alert(`Opening ${fileName} for editing...`);
            // In a real application, this would open the file in edit mode

            // Simulate opening the file in a modal
            const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

            // Set the file information
            const fileTitle = document.getElementById('fileViewerTitle');
            const fileContent = document.getElementById('fileViewerContent');

            // Set the title with an edit indicator
            fileTitle.innerHTML = `<i class="bi bi-pencil-square"></i> ${fileName} (Editing)`;

            // Set editable content
            fileContent.innerHTML = `
                <div class="alert alert-info mb-3">
                    <i class="bi bi-info-circle me-2"></i>
                    You are now editing this file. In a real application, this would be an editable document.
                </div>
                <div class="form-floating mb-3">
                    <textarea class="form-control" id="fileEditor" style="height: 300px;">This is the content of ${fileName}. In a real application, this would be the actual content of the file that you could edit.</textarea>
                    <label for="fileEditor">File Content</label>
                </div>
                <div class="d-grid">
                    <button class="btn btn-primary" onclick="saveFile('${fileName}')">
                        <i class="bi bi-save me-2"></i>Save Changes
                    </button>
                </div>
            `;

            // Show the modal
            const modal = new bootstrap.Modal(fileViewerModal);
            modal.show();
        }

        // Function to save a file after editing
        function saveFile(fileName) {
            alert(`Changes to ${fileName} have been saved.`);
            // In a real application, this would save the changes to the file

            // Close the modal after saving
            const modal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
            if (modal) {
                modal.hide();
            }
        }

        // Function to show email in the read tab
        function showEmail(sender, subject, content, time) {
            // Switch to the read tab
            document.getElementById('read-tab').click();

            // Get sender's full name
            let senderFullName = '';
            let senderEmail = '';

            switch(sender) {
                case 'sarah-chen':
                    senderFullName = 'Sarah Chen';
                    senderEmail = '<EMAIL>';
                    break;
                case 'john-davis':
                    senderFullName = 'John Davis';
                    senderEmail = '<EMAIL>';
                    break;
                case 'rachel-miller':
                    senderFullName = 'Rachel Miller';
                    senderEmail = '<EMAIL>';
                    break;
                case 'michael-johnson':
                    senderFullName = 'Michael Johnson';
                    senderEmail = '<EMAIL>';
                    break;
                case 'emily-rodriguez':
                    senderFullName = 'Emily Rodriguez';
                    senderEmail = '<EMAIL>';
                    break;
                default:
                    senderFullName = 'Unknown Sender';
                    senderEmail = '<EMAIL>';
            }

            // Update the email content in the read tab
            const readTab = document.getElementById('read-content');

            // Update email header
            const emailHeader = readTab.querySelector('.card-header');
            emailHeader.innerHTML = `
                <div>
                    <h5 class="mb-0">${subject}</h5>
                    <div class="text-muted small">From: ${senderFullName} &lt;${senderEmail}&gt;</div>
                    <div class="text-muted small">To: Project Team &lt;<EMAIL>&gt;</div>
                </div>
                <div class="text-muted">${time}</div>
            `;

            // Update email body
            const emailBody = readTab.querySelector('.card-body');

            // Create email content based on the subject
            let emailContent = '';
            let attachments = '';

            switch(subject) {
                case 'Project update: New timeline for Phase 2':
                    emailContent = `
                        <p>Hi team,</p>
                        <p>${content}</p>
                        <p>The key changes include:</p>
                        <ul>
                            <li>Extended the design phase by one week</li>
                            <li>Adjusted the development sprint schedule</li>
                            <li>Added a buffer period before the client demo</li>
                            <li>Updated resource allocations for the testing phase</li>
                        </ul>
                        <p>These changes have been approved by the client and should give us more flexibility to deliver a high-quality product.</p>
                        <p>Let me know if you have any concerns or questions.</p>
                        <p>Best regards,<br>${senderFullName.split(' ')[0]}</p>
                    `;
                    attachments = `
                        <div class="mt-3">
                            <h6>Attachments</h6>
                            <div class="list-group">
                                <a href="#" onclick="openGoogleItem('sheets', 'spreadsheet', 'project-timeline-phase2-updated')" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Project_Timeline_Phase2_Updated.xlsx</h6>
                                        <small>1.2 MB</small>
                                    </div>
                                    <div class="ms-auto">
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'project-timeline-phase2-updated'); event.stopPropagation();"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Timeline_Phase2_Updated.xlsx'); event.stopPropagation();"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Timeline_Phase2_Updated.xlsx'); event.stopPropagation();"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Project_Timeline_Phase2_Updated.xlsx'); event.stopPropagation();"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Client_Approval_Document.pdf</h6>
                                        <small>850 KB</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                    `;
                    break;
                case 'Meeting notes from yesterday\'s client call':
                    emailContent = `
                        <p>Hello everyone,</p>
                        <p>${content}</p>
                        <p>Key points from the meeting:</p>
                        <ul>
                            <li>Client is satisfied with the current progress</li>
                            <li>They requested some minor UI adjustments</li>
                            <li>We need to prioritize the reporting feature</li>
                            <li>Next demo scheduled for next week</li>
                        </ul>
                        <p>Please let me know if you have any questions or if I missed anything important.</p>
                        <p>Thanks,<br>${senderFullName.split(' ')[0]}</p>
                    `;
                    attachments = `
                        <div class="mt-3">
                            <h6>Attachments</h6>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Client_Meeting_Notes_May10.docx</h6>
                                        <small>245 KB</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                    `;
                    break;
                case 'Budget approval for new resources':
                    emailContent = `
                        <p>Team,</p>
                        <p>${content}</p>
                        <p>Details:</p>
                        <ul>
                            <li>Budget for 2 additional developers approved</li>
                            <li>Additional QA resource approved</li>
                            <li>Budget for extended software licenses approved</li>
                        </ul>
                        <p>HR will start the recruitment process next week. Please send me your requirements for the new team members.</p>
                        <p>Regards,<br>${senderFullName.split(' ')[0]}</p>
                    `;
                    attachments = `
                        <div class="mt-3">
                            <h6>Attachments</h6>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Budget_Approval_Document.pdf</h6>
                                        <small>320 KB</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                    `;
                    break;
                case 'Weekly status report':
                    emailContent = `
                        <p>Hi all,</p>
                        <p>${content}</p>
                        <p>Highlights from this week:</p>
                        <ul>
                            <li>Completed the user authentication module</li>
                            <li>Fixed 15 high-priority bugs</li>
                            <li>Started work on the reporting dashboard</li>
                            <li>Updated the project documentation</li>
                        </ul>
                        <p>We're on track to meet our next milestone.</p>
                        <p>Best regards,<br>${senderFullName.split(' ')[0]}</p>
                    `;
                    attachments = `
                        <div class="mt-3">
                            <h6>Attachments</h6>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Weekly_Status_Report_May12.pdf</h6>
                                        <small>1.5 MB</small>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Bug_Tracking_Sheet.xlsx</h6>
                                        <small>780 KB</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                    `;
                    break;
                case 'Client feedback on prototype':
                    emailContent = `
                        <p>Team,</p>
                        <p>${content}</p>
                        <p>Specific feedback points:</p>
                        <ul>
                            <li>They love the new dashboard layout</li>
                            <li>The reporting feature needs more filter options</li>
                            <li>They suggested improving the mobile responsiveness</li>
                            <li>They want to add a new export feature</li>
                        </ul>
                        <p>I've attached their detailed feedback document. Let's discuss these points in our next team meeting.</p>
                        <p>Thanks,<br>${senderFullName.split(' ')[0]}</p>
                    `;
                    attachments = `
                        <div class="mt-3">
                            <h6>Attachments</h6>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Client_Feedback_Document.docx</h6>
                                        <small>420 KB</small>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <i class="bi bi-file-earmark-image text-info me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">UI_Improvement_Suggestions.png</h6>
                                        <small>1.8 MB</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                    `;
                    break;
                default:
                    emailContent = `<p>${content}</p>`;
                    attachments = '';
            }

            // Update the email body content
            emailBody.innerHTML = emailContent + (attachments ? '<hr>' + attachments : '');
        }

        // Function to open specific Google items
        function openGoogleItem(app, type, itemId) {
          // For general app buttons (without specific item), show the modal
          if (app === 'drive' && !type && !itemId) {
            const driveModal = new bootstrap.Modal(document.getElementById('driveModal'));
            driveModal.show();
            return;
          } else if (app === 'docs' && !type && !itemId) {
            const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
            docsModal.show();
            return;
          } else if (app === 'sheets' && !type && !itemId) {
            const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
            sheetsModal.show();
            return;
          } else if (app === 'attachments' && !type && !itemId) {
            const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
            attachmentsModal.show();
            return;
          }

          // For specific items, directly open the file in a viewer
          try {
            // Create a simulated file viewer
            const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

            // Set the file information
            const fileTitle = document.getElementById('fileViewerTitle');
            const fileContent = document.getElementById('fileViewerContent');

            // Format the item ID to make it more readable
            const readableId = itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            // Set the title based on the app and type
            fileTitle.innerHTML = `<i class="bi ${getFileIcon(app, type)}"></i> ${readableId}`;

            // Set the content based on the file type
            fileContent.innerHTML = getFileContent(app, type, itemId);

            // Show the modal
            const modal = new bootstrap.Modal(fileViewerModal);
            modal.show();
          } catch (error) {
            console.error('Error opening file:', error);
            alert('Could not open the file. Please try again later.');
          }
        }

        // Helper function to create a file viewer modal if it doesn't exist
        function createFileViewerModal() {
          const modal = document.createElement('div');
          modal.className = 'modal fade';
          modal.id = 'fileViewerModal';
          modal.tabIndex = '-1';
          modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
          modal.setAttribute('aria-hidden', 'true');

          modal.innerHTML = `
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                  <h5 class="modal-title" id="fileViewerTitle"></h5>
                  <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">Download</button>
                  <button type="button" class="btn btn-success" onclick="shareCurrentFile()">Share</button>
                  <button type="button" class="btn btn-danger" onclick="deleteCurrentFile()">Delete</button>
                </div>
              </div>
            </div>
          `;

          document.body.appendChild(modal);
          return modal;
        }

        // Helper function to get the appropriate icon for the file type
        function getFileIcon(app, type) {
          if (app === 'drive') {
            if (type === 'folder') return 'bi-folder-fill text-primary';
            if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
            if (type === 'image') return 'bi-file-earmark-image text-info';
            if (type === 'document') return 'bi-file-earmark-text text-primary';
            return 'bi-file-earmark text-secondary';
          } else if (app === 'docs') {
            return 'bi-file-earmark-text text-primary';
          } else if (app === 'sheets') {
            return 'bi-file-earmark-spreadsheet text-success';
          } else if (app === 'calendar') {
            return 'bi-calendar-event text-primary';
          }
          return 'bi-file-earmark text-secondary';
        }

        // Helper function to generate content for the file viewer
        function getFileContent(app, type, itemId) {
          // Generate simulated content based on file type
          if (app === 'drive') {
            if (type === 'folder') {
              return `<div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> This is a folder view. In a real application, this would show the contents of the folder.
                      </div>
                      <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-text me-2"></i> Document 1.docx
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-spreadsheet me-2"></i> Spreadsheet 1.xlsx
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                          <i class="bi bi-file-earmark-pdf me-2"></i> Report.pdf
                        </a>
                      </div>`;
            } else if (type === 'pdf') {
              return `<div class="text-center">
                        <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                        <h4 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <div class="alert alert-info mt-3">
                          <i class="bi bi-info-circle"></i> This is a PDF viewer. In a real application, the PDF would be displayed here.
                        </div>
                        <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                          <h5>Document Preview</h5>
                          <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                          <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                        </div>
                      </div>`;
            } else if (type === 'document') {
              return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <hr>
                        <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                        <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                      </div>`;
            } else if (type === 'image') {
              return `<div class="text-center">
                        <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                          <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                          <h5 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h5>
                          <p>This is an image viewer. In a real application, the image would be displayed here.</p>
                        </div>
                      </div>`;
            } else if (type === 'spreadsheet') {
              return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <hr>
                        <div class="table-responsive">
                          <table class="table table-bordered table-hover">
                            <thead>
                              <tr>
                                <th>Project</th>
                                <th>Status</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td>Website Redesign</td>
                                <td>In Progress</td>
                                <td>2025-05-01</td>
                                <td>2025-06-15</td>
                              </tr>
                              <tr>
                                <td>Mobile App Development</td>
                                <td>Planning</td>
                                <td>2025-06-01</td>
                                <td>2025-08-30</td>
                              </tr>
                              <tr>
                                <td>Cloud Migration</td>
                                <td>Completed</td>
                                <td>2025-03-10</td>
                                <td>2025-04-20</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>`;
            }
          } else if (app === 'docs') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                      <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                    </div>`;
          } else if (app === 'sheets') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                          <thead>
                            <tr>
                              <th>Project</th>
                              <th>Status</th>
                              <th>Start Date</th>
                              <th>End Date</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Website Redesign</td>
                              <td>In Progress</td>
                              <td>2025-05-01</td>
                              <td>2025-06-15</td>
                            </tr>
                            <tr>
                              <td>Mobile App Development</td>
                              <td>Planning</td>
                              <td>2025-06-01</td>
                              <td>2025-08-30</td>
                            </tr>
                            <tr>
                              <td>Cloud Migration</td>
                              <td>Completed</td>
                              <td>2025-03-10</td>
                              <td>2025-04-20</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>`;
          } else if (app === 'calendar') {
            return `<div class="border p-3" style="background-color: #f8f9fa;">
                      <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      <hr>
                      <div class="row">
                        <div class="col-md-6">
                          <p><strong>Date:</strong> May 15, 2025</p>
                          <p><strong>Time:</strong> 10:00 AM - 11:30 AM</p>
                          <p><strong>Location:</strong> Conference Room A</p>
                        </div>
                        <div class="col-md-6">
                          <p><strong>Organizer:</strong> John Doe</p>
                          <p><strong>Attendees:</strong> 5</p>
                          <p><strong>Status:</strong> Confirmed</p>
                        </div>
                      </div>
                      <div class="mt-3">
                        <h5>Description</h5>
                        <p>This is a calendar event viewer. In a real application, the event details would be displayed here.</p>
                      </div>
                    </div>`;
          }

          return `<div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
                  </div>`;
        }

        // Function to download the current file
        function downloadCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          alert(`Downloading ${fileTitle}...`);
          // In a real application, this would trigger a download
        }

        // Function to share the current file
        function shareCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          const email = prompt(`Enter email address to share ${fileTitle} with:`);
          if (email) {
            alert(`${fileTitle} has been shared with ${email}.`);
            // In a real application, this would share the file with the specified email
          }
        }

        // Function to delete the current file
        function deleteCurrentFile() {
          const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
          const confirmDelete = confirm(`Are you sure you want to delete ${fileTitle}?`);
          if (confirmDelete) {
            alert(`${fileTitle} has been deleted.`);
            // In a real application, this would delete the file
            // Close the modal after deletion
            const modal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
            if (modal) {
              modal.hide();
            }
          }
        }

        // Function to download a file
        function downloadFile(fileName) {
          alert(`Downloading ${fileName}...`);
          // In a real application, this would trigger a download

          // Create a temporary link element to simulate download
          const link = document.createElement('a');
          link.href = '#';
          link.download = fileName;

          // Append to body, click, and remove
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }

        // Function to delete an attachment
        function deleteAttachment(fileName) {
          if (confirm(`Are you sure you want to delete ${fileName}?`)) {
            alert(`${fileName} has been deleted.`);
            // In a real application, this would delete the file
          }
        }

        // Function to share a file
        function shareFile(fileName) {
          const email = prompt(`Enter email address to share ${fileName} with:`);
          if (email) {
            alert(`${fileName} has been shared with ${email}.`);
            // In a real application, this would share the file with the specified email
          }
        }

        // Initialize sidebar toggle functionality
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                sidebar.classList.toggle('show');
            });
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            if (window.innerWidth < 768) {
                const isClickInsideSidebar = sidebar.contains(event.target);
                const isClickOnToggle = sidebarToggle && sidebarToggle.contains(event.target);

                if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 768 && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
    </script>
</body>
</html>
