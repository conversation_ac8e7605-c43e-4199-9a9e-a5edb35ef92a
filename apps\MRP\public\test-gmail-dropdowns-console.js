/**
 * Comprehensive Gmail Dropdown Test Suite
 * Paste this into the browser console on http://localhost:3002 to test dropdown functionality
 */

(function() {
    'use strict';
    
    console.clear();
    console.log('🔧 Gmail Dropdown Test Suite Starting...');
    console.log('=====================================');
    
    // Test configuration
    const TEST_CONFIG = {
        modalId: 'mrp-gmailModal',
        sortDropdownId: 'sort-dropdown',
        filterDropdownId: 'filter-dropdown',
        delay: 500 // ms between test steps
    };
    
    let testResults = {
        total: 0,
        passed: 0,
        failed: 0,
        warnings: 0
    };
    
    function logResult(message, status = 'info') {
        const icons = {
            pass: '✅',
            fail: '❌',
            warn: '⚠️',
            info: 'ℹ️'
        };
        
        const colors = {
            pass: 'color: green; font-weight: bold;',
            fail: 'color: red; font-weight: bold;',
            warn: 'color: orange; font-weight: bold;',
            info: 'color: blue;'
        };
        
        console.log(`%c${icons[status]} ${message}`, colors[status]);
        
        if (status !== 'info') {
            testResults.total++;
            if (status === 'pass') testResults.passed++;
            else if (status === 'fail') testResults.failed++;
            else if (status === 'warn') testResults.warnings++;
        }
    }
    
    // Test 1: Check if required elements exist
    function testElementsExist() {
        logResult('Test 1: Checking required elements...', 'info');
        
        const modal = document.getElementById(TEST_CONFIG.modalId);
        const sortDropdown = document.getElementById(TEST_CONFIG.sortDropdownId);
        const filterDropdown = document.getElementById(TEST_CONFIG.filterDropdownId);
        
        if (modal) {
            logResult('Gmail modal found', 'pass');
        } else {
            logResult('Gmail modal NOT found - may need to load the page completely', 'fail');
        }
        
        if (sortDropdown) {
            logResult('Sort dropdown button found', 'pass');
        } else {
            logResult('Sort dropdown button NOT found', 'fail');
        }
        
        if (filterDropdown) {
            logResult('Filter dropdown button found', 'pass');
        } else {
            logResult('Filter dropdown button NOT found', 'fail');
        }
        
        return { modal, sortDropdown, filterDropdown };
    }
    
    // Test 2: Check script loading
    function testScriptLoading() {
        logResult('Test 2: Checking script loading...', 'info');
        
        if (typeof window.MRPGmailDropdowns !== 'undefined') {
            logResult('MRP Gmail Dropdowns script loaded', 'pass');
        } else {
            logResult('MRP Gmail Dropdowns script NOT loaded', 'fail');
        }
        
        if (typeof window.MRPGmailUtils !== 'undefined') {
            logResult('MRP Gmail Utils script loaded', 'pass');
        } else {
            logResult('MRP Gmail Utils script NOT loaded', 'fail');
        }
        
        if (typeof bootstrap !== 'undefined') {
            logResult(`Bootstrap ${bootstrap.Tooltip?.VERSION || 'unknown'} available`, 'pass');
        } else {
            logResult('Bootstrap NOT available', 'fail');
        }
    }
    
    // Test 3: Test dropdown attributes
    function testDropdownAttributes(elements) {
        logResult('Test 3: Checking dropdown attributes...', 'info');
        
        if (elements.sortDropdown) {
            const hasToggle = elements.sortDropdown.hasAttribute('data-bs-toggle');
            const hasAriaExpanded = elements.sortDropdown.hasAttribute('aria-expanded');
            
            if (hasToggle) {
                logResult('Sort dropdown has data-bs-toggle attribute', 'pass');
            } else {
                logResult('Sort dropdown missing data-bs-toggle attribute', 'fail');
            }
            
            if (hasAriaExpanded) {
                logResult('Sort dropdown has aria-expanded attribute', 'pass');
            } else {
                logResult('Sort dropdown missing aria-expanded attribute', 'warn');
            }
        }
        
        if (elements.filterDropdown) {
            const hasToggle = elements.filterDropdown.hasAttribute('data-bs-toggle');
            const hasAriaExpanded = elements.filterDropdown.hasAttribute('aria-expanded');
            
            if (hasToggle) {
                logResult('Filter dropdown has data-bs-toggle attribute', 'pass');
            } else {
                logResult('Filter dropdown missing data-bs-toggle attribute', 'fail');
            }
            
            if (hasAriaExpanded) {
                logResult('Filter dropdown has aria-expanded attribute', 'pass');
            } else {
                logResult('Filter dropdown missing aria-expanded attribute', 'warn');
            }
        }
    }
    
    // Test 4: Test dropdown menu structure
    function testDropdownMenus(elements) {
        logResult('Test 4: Checking dropdown menu structure...', 'info');
        
        if (elements.sortDropdown) {
            const sortMenu = elements.sortDropdown.nextElementSibling;
            if (sortMenu && sortMenu.classList.contains('dropdown-menu')) {
                logResult('Sort dropdown menu found', 'pass');
                
                const sortOptions = sortMenu.querySelectorAll('.sort-option');
                if (sortOptions.length > 0) {
                    logResult(`Sort dropdown has ${sortOptions.length} options`, 'pass');
                } else {
                    logResult('Sort dropdown has no options with .sort-option class', 'warn');
                }
            } else {
                logResult('Sort dropdown menu NOT found', 'fail');
            }
        }
        
        if (elements.filterDropdown) {
            const filterMenu = elements.filterDropdown.nextElementSibling;
            if (filterMenu && filterMenu.classList.contains('dropdown-menu')) {
                logResult('Filter dropdown menu found', 'pass');
                
                const filterOptions = filterMenu.querySelectorAll('.filter-option');
                if (filterOptions.length > 0) {
                    logResult(`Filter dropdown has ${filterOptions.length} options`, 'pass');
                } else {
                    logResult('Filter dropdown has no options with .filter-option class', 'warn');
                }
            } else {
                logResult('Filter dropdown menu NOT found', 'fail');
            }
        }
    }
    
    // Test 5: Test Bootstrap dropdown functionality
    function testBootstrapDropdowns(elements) {
        logResult('Test 5: Testing Bootstrap dropdown functionality...', 'info');
        
        if (typeof bootstrap === 'undefined') {
            logResult('Skipping Bootstrap test - Bootstrap not available', 'warn');
            return;
        }
        
        if (elements.sortDropdown) {
            try {
                const dropdownInstance = new bootstrap.Dropdown(elements.sortDropdown);
                logResult('Bootstrap dropdown instance created for sort dropdown', 'pass');
                
                // Test opening
                dropdownInstance.show();
                setTimeout(() => {
                    const menu = elements.sortDropdown.nextElementSibling;
                    const isOpen = menu && menu.classList.contains('show');
                    
                    if (isOpen) {
                        logResult('Bootstrap sort dropdown opens correctly', 'pass');
                        
                        // Test closing
                        dropdownInstance.hide();
                        setTimeout(() => {
                            const isClosed = !menu.classList.contains('show');
                            logResult(isClosed ? 'Bootstrap sort dropdown closes correctly' : 'Bootstrap sort dropdown failed to close', isClosed ? 'pass' : 'fail');
                        }, 200);
                    } else {
                        logResult('Bootstrap sort dropdown failed to open', 'fail');
                    }
                }, 200);
                
            } catch (error) {
                logResult(`Bootstrap dropdown creation failed: ${error.message}`, 'fail');
            }
        }
    }
    
    // Test 6: Test manual dropdown functionality
    function testManualDropdowns(elements) {
        setTimeout(() => {
            logResult('Test 6: Testing manual dropdown functionality...', 'info');
            
            if (elements.sortDropdown) {
                const menu = elements.sortDropdown.nextElementSibling;
                if (menu) {
                    // Test manual show
                    menu.classList.add('show');
                    const isOpen = menu.classList.contains('show');
                    
                    if (isOpen) {
                        logResult('Manual sort dropdown opens correctly', 'pass');
                        
                        // Test manual hide
                        menu.classList.remove('show');
                        const isClosed = !menu.classList.contains('show');
                        logResult(isClosed ? 'Manual sort dropdown closes correctly' : 'Manual sort dropdown failed to close', isClosed ? 'pass' : 'fail');
                    } else {
                        logResult('Manual sort dropdown failed to open', 'fail');
                    }
                }
            }
            
            // Final results
            setTimeout(showFinalResults, 500);
        }, 1000);
    }
    
    // Test 7: Test actual clicking
    function testDropdownClicks(elements) {
        setTimeout(() => {
            logResult('Test 7: Testing dropdown click functionality...', 'info');
            
            if (elements.sortDropdown) {
                logResult('Simulating click on sort dropdown...', 'info');
                elements.sortDropdown.click();
                
                setTimeout(() => {
                    const menu = elements.sortDropdown.nextElementSibling;
                    const isOpen = menu && menu.classList.contains('show');
                    logResult(isOpen ? 'Sort dropdown responds to clicks' : 'Sort dropdown does not respond to clicks', isOpen ? 'pass' : 'fail');
                    
                    if (isOpen) {
                        // Close it
                        elements.sortDropdown.click();
                    }
                }, 300);
            }
            
            if (elements.filterDropdown) {
                setTimeout(() => {
                    logResult('Simulating click on filter dropdown...', 'info');
                    elements.filterDropdown.click();
                    
                    setTimeout(() => {
                        const menu = elements.filterDropdown.nextElementSibling;
                        const isOpen = menu && menu.classList.contains('show');
                        logResult(isOpen ? 'Filter dropdown responds to clicks' : 'Filter dropdown does not respond to clicks', isOpen ? 'pass' : 'fail');
                        
                        if (isOpen) {
                            // Close it
                            elements.filterDropdown.click();
                        }
                    }, 300);
                }, 600);
            }
        }, 1500);
    }
    
    function showFinalResults() {
        setTimeout(() => {
            console.log('');
            console.log('📊 Final Test Results:');
            console.log('======================');
            console.log(`%cTotal Tests: ${testResults.total}`, 'font-weight: bold;');
            console.log(`%c✅ Passed: ${testResults.passed}`, 'color: green; font-weight: bold;');
            console.log(`%c❌ Failed: ${testResults.failed}`, 'color: red; font-weight: bold;');
            console.log(`%c⚠️ Warnings: ${testResults.warnings}`, 'color: orange; font-weight: bold;');
            
            const successRate = Math.round((testResults.passed / testResults.total) * 100);
            console.log(`%cSuccess Rate: ${successRate}%`, successRate >= 80 ? 'color: green; font-weight: bold;' : 'color: red; font-weight: bold;');
            
            if (testResults.failed > 0) {
                console.log('');
                console.log('🔧 Troubleshooting Tips:');
                console.log('- Make sure the Gmail modal is open before testing');
                console.log('- Check that all scripts are loaded properly');
                console.log('- Verify Bootstrap is available');
                console.log('- Try refreshing the page and running the test again');
            }
            
            console.log('');
            console.log('🎯 To manually test:');
            console.log('1. Open Gmail modal by clicking the Gmail button');
            console.log('2. Try clicking the Sort and Filter dropdown buttons');
            console.log('3. Select options from the dropdown menus');
            console.log('');
        }, 2000);
    }
    
    // Helper function to open Gmail modal
    function openGmailModal() {
        const gmailButton = document.querySelector('[data-bs-target="#mrp-gmailModal"]') ||
                           document.querySelector('#mrp-open-gmail-btn') ||
                           document.querySelector('#compose-email');
        
        if (gmailButton) {
            logResult('Opening Gmail modal...', 'info');
            gmailButton.click();
            return true;
        } else {
            logResult('Gmail button not found', 'warn');
            return false;
        }
    }
    
    // Main test execution
    function runAllTests() {
        console.log('🚀 Starting comprehensive dropdown tests...');
        console.log('');
        
        // Check if modal is already open
        const modal = document.getElementById(TEST_CONFIG.modalId);
        const isModalOpen = modal && window.getComputedStyle(modal).display !== 'none';
        
        if (!isModalOpen) {
            logResult('Gmail modal not open, attempting to open...', 'warn');
            if (!openGmailModal()) {
                logResult('Could not open Gmail modal automatically. Please open it manually and run the test again.', 'fail');
                return;
            }
            
            // Wait for modal to open then run tests
            setTimeout(() => {
                runTestSequence();
            }, 1000);
        } else {
            logResult('Gmail modal is already open', 'pass');
            runTestSequence();
        }
    }
    
    function runTestSequence() {
        const elements = testElementsExist();
        testScriptLoading();
        testDropdownAttributes(elements);
        testDropdownMenus(elements);
        testBootstrapDropdowns(elements);
        testManualDropdowns(elements);
        testDropdownClicks(elements);
    }
    
    // Export to global scope for manual execution
    window.testGmailDropdowns = runAllTests;
    window.openGmailModal = openGmailModal;
    
    // Auto-run the tests
    runAllTests();
    
})();

// Instructions
console.log('');
console.log('📋 Manual Testing Instructions:');
console.log('==============================');
console.log('1. The test has run automatically above');
console.log('2. To run again: testGmailDropdowns()');
console.log('3. To open Gmail modal: openGmailModal()');
console.log('4. Manually test by opening Gmail modal and clicking dropdowns');
