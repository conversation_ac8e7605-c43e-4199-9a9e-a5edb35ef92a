@echo off
setlocal

REM Set path to local Node.js installation
set PATH=%CD%\..\..\PortableNodeJS;%PATH%

REM Check if a mode parameter was provided
if "%1"=="" (
    set MODE=production
) else (
    set MODE=%1
)

REM Verify dependencies are installed
if not exist node_modules (
    echo Installing dependencies...
    call %CD%\..\..\PortableNodeJS\pnpm.CMD install || (
        echo Failed to install dependencies
        exit /b 1
    )
)

echo Server running at http://localhost:3001
echo Press Ctrl+C to stop the server
call %CD%\..\..\PortableNodeJS\pnpm.CMD run start:%MODE%
