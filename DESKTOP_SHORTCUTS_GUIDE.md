# ISA APPS Desktop Shortcuts Guide

This guide explains how to use the desktop shortcuts for the Ice Systems Australasia Applications Suite.

## Available Shortcuts

After running the setup script, you will have the following shortcuts on your desktop:

### Main Launcher

- **ISA APPS** - Opens the main launcher menu for all applications

### Web Applications

- **ISA Business Management System (Web)** - Opens the BMS in your web browser
- **ISA Materials Requirements Planning (Web)** - Opens the MRP in your web browser
- **ISA Customer Relationship Management (Web)** - Opens the CRM in your web browser
- **ISA Warehouse Management System (Web)** - Opens the WMS in your web browser
- **ISA Advanced Planning and Scheduling (Web)** - Opens the APS in your web browser
- **ISA Asset Performance Management (Web)** - Opens the APM in your web browser
- **ISA Integration Hub (Web)** - Opens the Integration Hub in your web browser

### Desktop Applications

- **ISA Business Management System (Desktop)** - Opens the BMS as a desktop application
- **ISA Materials Requirements Planning (Desktop)** - Opens the MRP as a desktop application
- **ISA Customer Relationship Management (Desktop)** - Opens the CRM as a desktop application
- **ISA Warehouse Management System (Desktop)** - Opens the WMS as a desktop application
- **ISA Advanced Planning and Scheduling (Desktop)** - Opens the APS as a desktop application
- **ISA Asset Performance Management (Desktop)** - Opens the APM as a desktop application

## Using the Shortcuts

1. **Double-click** on any shortcut to launch the corresponding application
2. Each shortcut will automatically:
   - Check if the Integration Hub is running (and start it if needed)
   - Launch the application
   - Open the application in your browser (for web applications)

## Web vs. Desktop Applications

- **Web Applications** run in your web browser and can be accessed from any device on your network
- **Desktop Applications** run as standalone applications on your computer with their own window

## Troubleshooting

If a shortcut doesn't work:

1. Make sure the setup script has been run successfully
2. Try running the application from the main launcher
3. Check if the Integration Hub is running
4. Restart your computer and try again

For more detailed troubleshooting, refer to the `docs/TROUBLESHOOTING_GUIDE.md` file.
