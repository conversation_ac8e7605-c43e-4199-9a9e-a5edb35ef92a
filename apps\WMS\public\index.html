<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Warehouse Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --app-primary-color: #2ecc71; /* Green for WMS */
            --app-primary-dark: #27ae60;
            --app-primary-light: rgba(46, 204, 113, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            padding-top: 56px; /* Height of navbar */
        }

        /* Sidebar styles */
        .sidebar {
            background-color: var(--app-primary-color);
            color: white;
            height: calc(100vh - 56px);
            position: fixed;
            top: 56px; /* Height of navbar */
            padding-top: 20px;
            width: 250px;
            z-index: 100;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto; /* Add scrollbar when content overflows */
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--app-primary-dark);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar .nav-link {
            color: #f8f9fa;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-heading {
            padding: 0.75rem 1.25rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.1rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Main content area */
        .main-content {
            margin-left: 250px;
            padding: 20px;
            transition: margin-left 0.3s;
            width: calc(100% - 250px); /* Adjust width to account for sidebar */
            box-sizing: border-box;
            float: right; /* Ensure content stays to the right of sidebar */
        }

        /* Dashboard cards */
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        .card-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        /* Navbar */
        .navbar {
            background-color: var(--app-primary-color) !important;
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
        }

        /* Toggle sidebar button */
        #sidebarToggle {
            cursor: pointer;
            background: transparent;
            border: none;
            color: white;
        }

        /* For mobile view */
        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: -250px;
                height: calc(100vh - 56px);
                transition: left 0.3s;
                z-index: 1030;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0;
                padding: 15px;
            }

            .navbar {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-dark fixed-top">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <button id="sidebarToggle" class="d-md-none me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-list fs-4"></i>
                </button>
                <a class="navbar-brand" href="/">WMS System</a>
            </div>
            <div class="d-flex">
                <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3" style="background: transparent; border: 1px solid white; color: white; padding: 5px 10px;">
                    <i class="bi bi-box-arrow-left me-1"></i> Back to Hub
                </a>
                <button class="btn position-relative me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-bell fs-5"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        3
                    </span>
                </button>
                <button class="btn" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-person-circle fs-5"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="pt-2">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="/">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/inventory">
                        <i class="bi bi-box-seam"></i>
                        Inventory
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/receiving">
                        <i class="bi bi-truck"></i>
                        Receiving
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/shipping">
                        <i class="bi bi-send"></i>
                        Shipping
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/locations">
                        <i class="bi bi-geo-alt"></i>
                        Locations
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="bi bi-file-earmark-text"></i>
                        Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/settings">
                        <i class="bi bi-gear-fill"></i>
                        Settings
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                <span>Integrations</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal">
                        <i class="bi bi-calendar3"></i>
                        Google Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal">
                        <i class="bi bi-folder"></i>
                        Google Drive
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal">
                        <i class="bi bi-file-earmark-text"></i>
                        Google Docs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                        <i class="bi bi-file-earmark-spreadsheet"></i>
                        Google Sheets
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" id="gmail-link">
                        <i class="bi bi-envelope"></i>
                        Gmail
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-maps" data-bs-toggle="modal" data-bs-target="#mapsModal">
                        <i class="bi bi-geo-alt"></i>
                        Google Maps
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#warehouse-maps" data-bs-toggle="modal" data-bs-target="#warehouseMapsModal">
                        <i class="bi bi-building"></i>
                        Warehouse Maps
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#attachments" data-bs-toggle="modal" data-bs-target="#attachmentsModal">
                        <i class="bi bi-paperclip"></i>
                        Attachments
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();">
                        <i class="bi bi-box-arrow-left"></i>
                        Back to Hub
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">Warehouse Management Dashboard</h1>
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-primary">Share</button>
                    <button type="button" class="btn btn-sm btn-outline-primary">Export</button>
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle">
                    <i class="bi bi-calendar"></i>
                    This month
                </button>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="card dashboard-card text-white" style="background-color: var(--app-primary-color);">
                    <div class="card-body text-center">
                        <i class="bi bi-box-seam card-icon"></i>
                        <h5 class="card-title">Total Items</h5>
                        <h2 class="card-text">1,750</h2>
                        <p class="card-text">In inventory</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-truck card-icon"></i>
                        <h5 class="card-title">Receiving</h5>
                        <h2 class="card-text">12</h2>
                        <p class="card-text">Scheduled today</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card bg-danger text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-send card-icon"></i>
                        <h5 class="card-title">Shipping</h5>
                        <h2 class="card-text">8</h2>
                        <p class="card-text">Scheduled today</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card bg-warning text-dark">
                    <div class="card-body text-center">
                        <i class="bi bi-exclamation-triangle card-icon"></i>
                        <h5 class="card-title">Alerts</h5>
                        <h2 class="card-text">3</h2>
                        <p class="card-text">Require attention</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Status and Orders -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5>Inventory Status</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Item</th>
                                    <th>Location</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Widget A</td>
                                    <td>A-01-02</td>
                                    <td>500</td>
                                    <td><span class="badge bg-success">In Stock</span></td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Widget B</td>
                                    <td>B-03-01</td>
                                    <td>250</td>
                                    <td><span class="badge bg-warning">Low Stock</span></td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>Widget C</td>
                                    <td>C-02-03</td>
                                    <td>1000</td>
                                    <td><span class="badge bg-success">In Stock</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5>Active Orders</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Customer</th>
                                    <th>Items</th>
                                    <th>Status</th>
                                    <th>Due Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Acme Corp</td>
                                    <td>Widget A x 100</td>
                                    <td><span class="badge bg-info">Processing</span></td>
                                    <td>2024-05-16</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Globex Inc</td>
                                    <td>Widget B x 50</td>
                                    <td><span class="badge bg-warning">Picking</span></td>
                                    <td>2024-05-17</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>Initech</td>
                                    <td>Widget C x 200</td>
                                    <td><span class="badge bg-success">Shipped</span></td>
                                    <td>2024-05-15</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Google Integrations -->
        <div class="row mt-4">
            <div class="col-12">
                <h4 class="mb-3">Google Integrations</h4>
            </div>
            <!-- Google Gmail -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-gmail-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-envelope"></i> Gmail</h5>
                        <div class="component-actions">
                            <button id="refresh-gmail" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="compose-email" class="btn btn-sm btn-outline-success" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-pencil"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">AC</div>
                                    <div>
                                        <div class="fw-bold">Acme Corp</div>
                                        <div class="small text-truncate" style="max-width: 200px;">Shipment confirmation for order #12345</div>
                                        <span class="badge bg-primary rounded-pill">15m</span>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('gmail', 'email', 'Acme_Corp_Shipment_Confirmation')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('gmail', 'email', 'Acme_Corp_Shipment_Confirmation')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('gmail', 'email', 'Acme_Corp_Shipment_Confirmation')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('gmail', 'email', 'Acme_Corp_Shipment_Confirmation')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">JD</div>
                                    <div>
                                        <div class="fw-bold">John Davis</div>
                                        <div class="small text-truncate" style="max-width: 200px;">Warehouse inspection scheduled for tomorrow</div>
                                        <span class="badge bg-primary rounded-pill">2h</span>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('gmail', 'email', 'John_Davis_Warehouse_Inspection')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('gmail', 'email', 'John_Davis_Warehouse_Inspection')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('gmail', 'email', 'John_Davis_Warehouse_Inspection')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('gmail', 'email', 'John_Davis_Warehouse_Inspection')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">GI</div>
                                    <div>
                                        <div class="fw-bold">Globex Inc</div>
                                        <div class="small text-truncate" style="max-width: 200px;">New order placement - Urgent delivery</div>
                                        <span class="badge bg-primary rounded-pill">5h</span>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('gmail', 'email', 'Globex_Inc_New_Order')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('gmail', 'email', 'Globex_Inc_New_Order')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('gmail', 'email', 'Globex_Inc_New_Order')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('gmail', 'email', 'Globex_Inc_New_Order')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" onclick="document.getElementById('gmail-link').click();">
                                <i class="bi bi-envelope me-2"></i>Open Gmail
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Google Drive -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-drive-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-folder"></i> Google Drive</h5>
                        <div class="component-actions">
                            <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="upload-file" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-upload"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div style="cursor: pointer;" onclick="viewGoogleItem('drive', 'folder', 'Warehouse_Documentation')">
                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                    <span>Warehouse Documentation</span>
                                    <span class="badge bg-secondary rounded-pill ms-2">12 files</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Warehouse_Documentation')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Warehouse_Documentation')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Warehouse_Documentation')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Warehouse_Documentation')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div style="cursor: pointer;" onclick="viewGoogleItem('drive', 'folder', 'Shipping_Labels')">
                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                    <span>Shipping Labels</span>
                                    <span class="badge bg-secondary rounded-pill ms-2">25 files</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Shipping_Labels')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Shipping_Labels')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Shipping_Labels')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Shipping_Labels')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div style="cursor: pointer;" onclick="viewGoogleItem('drive', 'pdf', 'Warehouse_Layout_2025.pdf')">
                                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                    <span>Warehouse_Layout_2025.pdf</span>
                                    <span class="badge bg-primary rounded-pill ms-2">Yesterday</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'pdf', 'Warehouse_Layout_2025.pdf')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'pdf', 'Warehouse_Layout_2025.pdf')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'pdf', 'Warehouse_Layout_2025.pdf')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'pdf', 'Warehouse_Layout_2025.pdf')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
                                <i class="bi bi-folder me-2"></i>Open Drive
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Google Docs -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-docs-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                        <div class="component-actions">
                            <button id="refresh-docs" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="create-new-doc" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#docsModal"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div style="cursor: pointer;" onclick="viewGoogleItem('docs', 'document', 'Warehouse_Operations_Manual')">
                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                    <span>Warehouse Operations Manual</span>
                                    <span class="badge bg-primary rounded-pill ms-2">Today</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Warehouse_Operations_Manual')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Warehouse_Operations_Manual')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Warehouse_Operations_Manual')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Warehouse_Operations_Manual')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div style="cursor: pointer;" onclick="viewGoogleItem('docs', 'document', 'Inventory_Audit_Procedures')">
                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                    <span>Inventory Audit Procedures</span>
                                    <span class="badge bg-primary rounded-pill ms-2">Yesterday</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Inventory_Audit_Procedures')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Inventory_Audit_Procedures')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Inventory_Audit_Procedures')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Inventory_Audit_Procedures')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div style="cursor: pointer;" onclick="viewGoogleItem('docs', 'document', 'Shipping_Guidelines')">
                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                    <span>Shipping Guidelines</span>
                                    <span class="badge bg-primary rounded-pill ms-2">3 days ago</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Shipping_Guidelines')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Shipping_Guidelines')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Shipping_Guidelines')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Shipping_Guidelines')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#docsModal">
                                <i class="bi bi-file-earmark-text me-2"></i>View All Documents
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Google Sheets -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-sheets-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                        <div class="component-actions">
                            <button id="refresh-sheets" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="create-new-sheet" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#sheetsModal"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div style="cursor: pointer;" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')">
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                    <span>Inventory_Tracking_2025.xlsx</span>
                                    <span class="badge bg-primary rounded-pill ms-2">2 days ago</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div style="cursor: pointer;" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Warehouse_Capacity_Analysis.xlsx')">
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                    <span>Warehouse_Capacity_Analysis.xlsx</span>
                                    <span class="badge bg-primary rounded-pill ms-2">1 week ago</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Warehouse_Capacity_Analysis.xlsx')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Warehouse_Capacity_Analysis.xlsx')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Warehouse_Capacity_Analysis.xlsx')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Warehouse_Capacity_Analysis.xlsx')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div style="cursor: pointer;" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Shipping_Costs_Q2_2025.xlsx')">
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                    <span>Shipping_Costs_Q2_2025.xlsx</span>
                                    <span class="badge bg-primary rounded-pill ms-2">2 weeks ago</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Shipping_Costs_Q2_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Shipping_Costs_Q2_2025.xlsx')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Shipping_Costs_Q2_2025.xlsx')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Shipping_Costs_Q2_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                                <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Google Calendar -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component google-calendar-component">
                    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                        <div class="component-actions">
                            <button id="refresh-calendar" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                            <button id="create-new-event" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#calendarModal"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center" style="cursor: pointer;" onclick="viewGoogleItem('calendar', 'event', 'Inventory_Audit')">
                                <div>
                                    <div class="fw-bold">Inventory Audit</div>
                                    <div class="small text-muted">
                                        <i class="bi bi-clock me-1"></i>Today, 2:00 PM - 4:00 PM
                                    </div>
                                </div>
                                <span class="badge bg-warning rounded-pill">Today</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center" style="cursor: pointer;" onclick="viewGoogleItem('calendar', 'event', 'Shipping_Coordination_Meeting')">
                                <div>
                                    <div class="fw-bold">Shipping Coordination Meeting</div>
                                    <div class="small text-muted">
                                        <i class="bi bi-clock me-1"></i>Tomorrow, 10:00 AM - 11:00 AM
                                    </div>
                                </div>
                                <span class="badge bg-info rounded-pill">Tomorrow</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center" style="cursor: pointer;" onclick="viewGoogleItem('calendar', 'event', 'Warehouse_Staff_Training')">
                                <div>
                                    <div class="fw-bold">Warehouse Staff Training</div>
                                    <div class="small text-muted">
                                        <i class="bi bi-clock me-1"></i>May 15, 9:00 AM - 12:00 PM
                                    </div>
                                </div>
                                <span class="badge bg-secondary rounded-pill">Next Week</span>
                            </div>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#calendarModal">
                                <i class="bi bi-calendar3 me-2"></i>View Full Calendar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Google Calendar Modal -->
            <div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="calendarModalLabel">Google Calendar</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="d-flex justify-content-between mb-3">
                                <button class="btn btn-primary" id="create-new-event-modal">
                                    <i class="bi bi-calendar-plus me-2"></i>Create Event
                                </button>
                                <button class="btn btn-outline-secondary" id="refresh-calendar-modal">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                                </button>
                            </div>
                            <div class="list-group">
                                <div class="list-group-item" style="cursor: pointer;" onclick="googleIntegrationHandler.viewItem('calendar', 'event', 'Inventory_Audit')">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Inventory Audit</h6>
                                        <small class="text-warning">Today</small>
                                    </div>
                                    <p class="mb-1">2:00 PM - 4:00 PM</p>
                                    <small>Quarterly inventory audit with the warehouse team</small>
                                    <div class="d-flex gap-2 mt-2">
                                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); googleIntegrationHandler.viewItem('calendar', 'event', 'Inventory_Audit')">
                                            <i class="bi bi-eye"></i> View
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="event.stopPropagation(); googleIntegrationHandler.respondToEvent('Inventory_Audit', 'accept')">
                                            <i class="bi bi-check"></i> Accept
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); googleIntegrationHandler.respondToEvent('Inventory_Audit', 'decline')">
                                            <i class="bi bi-x"></i> Decline
                                        </button>
                                    </div>
                                </div>
                                <div class="list-group-item" style="cursor: pointer;" onclick="googleIntegrationHandler.viewItem('calendar', 'event', 'Shipping_Coordination_Meeting')">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Shipping Coordination Meeting</h6>
                                        <small class="text-info">Tomorrow</small>
                                    </div>
                                    <p class="mb-1">10:00 AM - 11:00 AM</p>
                                    <small>Weekly shipping coordination meeting with logistics team</small>
                                    <div class="d-flex gap-2 mt-2">
                                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); googleIntegrationHandler.viewItem('calendar', 'event', 'Shipping_Coordination_Meeting')">
                                            <i class="bi bi-eye"></i> View
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="event.stopPropagation(); googleIntegrationHandler.respondToEvent('Shipping_Coordination_Meeting', 'accept')">
                                            <i class="bi bi-check"></i> Accept
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); googleIntegrationHandler.respondToEvent('Shipping_Coordination_Meeting', 'decline')">
                                            <i class="bi bi-x"></i> Decline
                                        </button>
                                    </div>
                                </div>
                                <div class="list-group-item" style="cursor: pointer;" onclick="googleIntegrationHandler.viewItem('calendar', 'event', 'Warehouse_Staff_Training')">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Warehouse Staff Training</h6>
                                        <small class="text-secondary">Next Week</small>
                                    </div>
                                    <p class="mb-1">May 15, 9:00 AM - 12:00 PM</p>
                                    <small>Training session for new warehouse management software</small>
                                    <div class="d-flex gap-2 mt-2">
                                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); googleIntegrationHandler.viewItem('calendar', 'event', 'Warehouse_Staff_Training')">
                                            <i class="bi bi-eye"></i> View
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="event.stopPropagation(); googleIntegrationHandler.respondToEvent('Warehouse_Staff_Training', 'accept')">
                                            <i class="bi bi-check"></i> Accept
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); googleIntegrationHandler.respondToEvent('Warehouse_Staff_Training', 'decline')">
                                            <i class="bi bi-x"></i> Decline
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <a href="https://calendar.google.com" target="_blank" class="btn btn-primary">Open in Calendar</a>
                        </div>
                    </div>
                </div>
            </div>
                <!-- Google Drive Modal -->
    <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="driveModalLabel"><i class="bi bi-folder"></i> Google Drive</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="driveTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content" type="button" role="tab" aria-controls="files-content" aria-selected="true">Files</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button" role="tab" aria-controls="upload-content" aria-selected="false">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-tab" data-bs-toggle="tab" data-bs-target="#shared-content" type="button" role="tab" aria-controls="shared-content" aria-selected="false">Shared with me</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="driveTabContent">
                        <!-- Files Tab -->
                        <div class="tab-pane fade show active" id="files-content" role="tabpanel" aria-labelledby="files-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="file-search-drive" class="form-control" placeholder="Search files...">
                                    <button type="button" class="btn btn-outline-secondary"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-sort-down"></i> Sort by
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="date">Date</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="size">Size</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="type">Type</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="drive-files-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                                    <span>Project Documentation</span>
                                                </div>
                                            </td>
                                            <td>Yesterday</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'project-documentation')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Documentation')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Documentation')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Project_Documentation')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                                    <span>Reports</span>
                                                </div>
                                            </td>
                                            <td>3 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'folder', 'reports')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Reports')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Reports')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Reports')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                                    <span>Project_Report_Q2_2025.pdf</span>
                                                </div>
                                            </td>
                                            <td>Yesterday</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'project-report-q2-2025')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Report_Q2_2025.pdf')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Report_Q2_2025.pdf')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Project_Report_Q2_2025.pdf')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Project_Timeline_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'spreadsheet', 'project-timeline-2025')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Timeline_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Timeline_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Project_Timeline_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                    <span>Project_Requirements.docx</span>
                                                </div>
                                            </td>
                                            <td>1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'project-requirements')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Requirements.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Requirements.docx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Project_Requirements.docx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Upload Tab -->
                        <div class="tab-pane fade" id="upload-content" role="tabpanel" aria-labelledby="upload-tab">
                            <div class="row">
                                <div class="col-md-7">
                                    <div class="upload-area p-5 mb-3 text-center" id="dropzone" style="border: 2px dashed #ccc; border-radius: 5px; background-color: #f8f9fa;">
                                        <i class="bi bi-cloud-upload fs-1 text-muted mb-3"></i>
                                        <h5>Drag & Drop Files Here</h5>
                                        <p class="text-muted">or</p>
                                        <label for="file-upload" class="btn btn-primary">
                                            Browse Files
                                        </label>
                                        <input id="file-upload" type="file" multiple style="display: none;">
                                        <p class="text-muted mt-3 small">Maximum file size: 50MB</p>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="card">
                                        <div class="card-header">
                                            Upload Options
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="upload-folder" class="form-label">Destination folder</label>
                                                <select class="form-select" id="upload-folder">
                                                    <option selected>My Drive</option>
                                                    <option>Project Documentation</option>
                                                    <option>Reports</option>
                                                    <option>Team Shared Folder</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="convert-to-google-format">
                                                    <label class="form-check-label" for="convert-to-google-format">
                                                        Convert to Google format
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="share-with-team">
                                                    <label class="form-check-label" for="share-with-team">
                                                        Share with team
                                                    </label>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-primary" id="upload-file-btn">
                                                <i class="bi bi-upload me-2"></i>Upload Files
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shared with me Tab -->
                        <div class="tab-pane fade" id="shared-content" role="tabpanel" aria-labelledby="shared-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="shared-search" class="form-control" placeholder="Search shared files...">
                                    <button type="button" class="btn btn-outline-secondary"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-funnel"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-filter="all">All Files</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="documents">Documents</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="spreadsheets">Spreadsheets</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="presentations">Presentations</a></li>
                                        <li><a class="dropdown-item" href="#" data-filter="images">Images</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Shared By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="shared-files-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                                    <span>Project_Proposal.pdf</span>
                                                </div>
                                            </td>
                                            <td>John Davis - 3 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'pdf', 'project-proposal')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Project_Proposal.pdf')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Project_Proposal.pdf')"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Resource_Allocation.xlsx</span>
                                                </div>
                                            </td>
                                            <td>Sarah Wilson - 1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'spreadsheet', 'resource-allocation')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Resource_Allocation.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Resource_Allocation.xlsx')"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                    <span>Meeting_Notes.docx</span>
                                                </div>
                                            </td>
                                            <td>David Chen - 2 weeks ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('drive', 'document', 'meeting-notes')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Meeting_Notes.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Meeting_Notes.docx')"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://drive.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Google Drive</a>
                </div>
            </div>
        </div>
    </div>

<!-- Google Sheets Modal -->
            <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="sheetsModalLabel">Google Sheets</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="d-flex justify-content-between mb-3">
                                <button class="btn btn-primary" id="create-new-spreadsheet">
                                    <i class="bi bi-file-earmark-plus me-2"></i>Create Spreadsheet
                                </button>
                                <button class="btn btn-outline-secondary" id="refresh-sheets-modal">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                                </button>
                            </div>
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Inventory_Tracking_2025.xlsx</h6>
                                                <small>Last edited: 2 days ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Warehouse_Capacity_Analysis.xlsx</h6>
                                                <small>Last edited: 1 week ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Warehouse_Capacity_Analysis.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Warehouse_Capacity_Analysis.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Warehouse_Capacity_Analysis.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Warehouse_Capacity_Analysis.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Shipping_Costs_Q2_2025.xlsx</h6>
                                                <small>Last edited: 2 weeks ago</small>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Shipping_Costs_Q2_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Shipping_Costs_Q2_2025.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Shipping_Costs_Q2_2025.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Shipping_Costs_Q2_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <a href="https://sheets.google.com" target="_blank" class="btn btn-primary">Open in Sheets</a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Google Maps Modal -->
            <div class="modal fade" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="mapsModalLabel">Google Maps</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Search locations" aria-label="Search locations">
                                    <button class="btn btn-primary" type="button">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="ratio ratio-16x9">
                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <a href="https://maps.google.com" target="_blank" class="btn btn-primary">Open in Maps</a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Attachments Modal -->
            <div class="modal fade" id="attachmentsModal" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="attachmentsModalLabel">Attachments</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="d-flex justify-content-between mb-3">
                                <button class="btn btn-primary" id="upload-attachment">
                                    <i class="bi bi-paperclip me-2"></i>Upload Attachment
                                </button>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="attachmentFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        Filter By
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="attachmentFilterDropdown">
                                        <li><a class="dropdown-item" href="#">All Files</a></li>
                                        <li><a class="dropdown-item" href="#">Documents</a></li>
                                        <li><a class="dropdown-item" href="#">Images</a></li>
                                        <li><a class="dropdown-item" href="#">Spreadsheets</a></li>
                                        <li><a class="dropdown-item" href="#">PDFs</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action" data-filename="Warehouse_Layout_2025.pdf" data-filetype="pdf">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Warehouse_Layout_2025.pdf</h6>
                                                <small>2.4 MB - Uploaded: Yesterday</small>
                                            </div>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-link file-action-view"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-link file-action-download"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-link file-action-share"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-link text-danger file-action-delete"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filename="Inventory_Tracking_2025.xlsx" data-filetype="spreadsheet">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Inventory_Tracking_2025.xlsx</h6>
                                                <small>1.8 MB - Uploaded: 2 weeks ago</small>
                                            </div>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-link file-action-view"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-link file-action-download"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-link file-action-share"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-link text-danger file-action-delete"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filename="Warehouse_Photos.jpg" data-filetype="image">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-image text-primary me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Warehouse_Photos.jpg</h6>
                                                <small>3.2 MB - Uploaded: 1 month ago</small>
                                            </div>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-link file-action-view"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-link file-action-download"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-link file-action-share"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-link text-danger file-action-delete"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filename="Shipping_Procedures.docx" data-filetype="document">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                            <div>
                                                <h6 class="mb-1">Shipping_Procedures.docx</h6>
                                                <small>1.5 MB - Uploaded: 2 months ago</small>
                                            </div>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-link file-action-view"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-link file-action-download"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-link file-action-share"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-link text-danger file-action-delete"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" id="download-selected">Download Selected</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Viewer Modal -->
            <div class="modal fade" id="fileViewerModal" tabindex="-1" aria-labelledby="fileViewerModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="fileViewerTitle">File Viewer</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="fileViewerContent" style="min-height: 400px; overflow: auto;">
                                <!-- File content will be loaded here -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="d-flex justify-content-start gap-2 me-auto">
                                <button type="button" class="btn btn-outline-success" onclick="downloadCurrentFile()"><i class="bi bi-download me-1"></i> Download</button>
                                <button type="button" class="btn btn-outline-info" onclick="shareCurrentFile()"><i class="bi bi-share me-1"></i> Share</button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteCurrentFile()"><i class="bi bi-trash me-1"></i> Delete</button>
                            </div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Scripts -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>



        <script src="/js/google-integration-handler.js"></script>
        <script src="/js/attachments-handler.js"></script>
        <script src="/shared/js/isa-modal-manager.js"></script>
        <script src="/js/maps-handler.js"></script>
        <script>
            // Function to open specific Google items
            function openGoogleItem(app, type, itemId) {
              // For general app buttons (without specific item), show the modal
              if (app === 'drive' && !type && !itemId) {
                const driveModal = new bootstrap.Modal(document.getElementById('driveModal'));
                driveModal.show();
                return;
              } else if (app === 'docs' && !type && !itemId) {
                const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
                docsModal.show();
                return;
              } else if (app === 'sheets' && !type && !itemId) {
                const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
                sheetsModal.show();
                return;
              } else if (app === 'attachments' && !type && !itemId) {
                const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
                attachmentsModal.show();
                return;
              } else if (app === 'calendar' && !type && !itemId) {
                const calendarModal = new bootstrap.Modal(document.getElementById('calendarModal'));
                calendarModal.show();
                return;
              } else if (app === 'gmail' && !type && !itemId) {
                const gmailModal = new bootstrap.Modal(document.getElementById('gmailModal'));
                gmailModal.show();
                return;
              } else if (app === 'maps' && !type && !itemId) {
                const mapsModal = new bootstrap.Modal(document.getElementById('mapsModal'));
                mapsModal.show();
                return;
              } else if (app === 'warehouse-maps' && !type && !itemId) {
                const warehouseMapsModal = new bootstrap.Modal(document.getElementById('warehouseMapsModal'));
                warehouseMapsModal.show();
                return;
              }

              // For specific items, directly open the file in a viewer
              if (type && itemId) {
                viewGoogleItem(app, type, itemId);
              }
            }

            // Debug function to check if modals are properly initialized
            function debugModals() {
              const modals = ['fileViewerModal', 'driveModal', 'docsModal', 'sheetsModal', 'calendarModal', 'gmailModal', 'mapsModal', 'warehouseMapsModal'];
              modals.forEach(modalId => {
                const modalEl = document.getElementById(modalId);
                if (modalEl) {
                  console.log(`Modal ${modalId} exists in DOM`);
                  const modalInstance = bootstrap.Modal.getInstance(modalEl);
                  if (modalInstance) {
                    console.log(`Modal ${modalId} has Bootstrap instance`);
                  } else {
                    console.log(`Modal ${modalId} has NO Bootstrap instance, creating one`);
                    try {
                      new bootstrap.Modal(modalEl);
                      console.log(`Modal ${modalId} instance created`);
                    } catch (err) {
                      console.error(`Error creating modal instance for ${modalId}:`, err);
                    }
                  }
                } else {
                  console.error(`Modal ${modalId} does NOT exist in DOM`);
                }
              });
            }

            // Function to view a Google item
            function viewGoogleItem(app, type, itemId) {
              console.log(`Viewing ${app} ${type}: ${itemId}`);

              // Debug modals
              debugModals();

              // Hide any open modals
              const modals = ['driveModal', 'docsModal', 'sheetsModal', 'calendarModal', 'gmailModal', 'mapsModal', 'warehouseMapsModal'];
              modals.forEach(modalId => {
                const modalInstance = bootstrap.Modal.getInstance(document.getElementById(modalId));
                if (modalInstance) {
                  modalInstance.hide();
                }
              });

              // Get the file viewer modal
              const fileViewerModal = document.getElementById('fileViewerModal');

              // Set the file information
              const fileTitle = document.getElementById('fileViewerTitle');
              const fileContent = document.getElementById('fileViewerContent');

              if (fileTitle && fileContent) {
                // Set the title based on the app and type
                let iconClass = 'bi-file-earmark';
                if (app === 'drive') {
                  if (type === 'folder') {
                    iconClass = 'bi-folder-fill text-primary';
                  } else if (type === 'pdf') {
                    iconClass = 'bi-file-earmark-pdf text-danger';
                  } else {
                    iconClass = 'bi-file-earmark text-secondary';
                  }
                } else if (app === 'docs') {
                  iconClass = 'bi-file-earmark-text text-primary';
                } else if (app === 'sheets') {
                  iconClass = 'bi-file-earmark-spreadsheet text-success';
                }

                // Format the item ID to make it more readable
                const readableId = itemId.replace(/_/g, ' ');

                fileTitle.innerHTML = `<i class="bi ${iconClass}"></i> ${readableId}`;

                // Set the content based on the file type
                fileContent.innerHTML = getGoogleItemContent(app, type, itemId);

                // Store current file info for download/share/delete operations
                window.currentGoogleItem = {
                  app: app,
                  type: type,
                  id: itemId
                };

                // Show the modal
                try {
                  let modal = bootstrap.Modal.getInstance(fileViewerModal);
                  if (!modal) {
                    console.log('Creating new modal instance for fileViewerModal');
                    modal = new bootstrap.Modal(fileViewerModal);
                  } else {
                    console.log('Using existing modal instance for fileViewerModal');
                  }

                  // Force hide and then show to ensure it's visible
                  modal.hide();
                  setTimeout(() => {
                    console.log('Showing fileViewerModal');
                    modal.show();
                  }, 100);
                } catch (err) {
                  console.error('Error showing file viewer modal:', err);
                  alert('Error showing file viewer. Please try again.');
                }

                console.log(`Viewing ${app} ${type} ${itemId} directly`);
              } else {
                console.error('File viewer components not found');
                alert('File viewer is not available. Please try again later.');
              }
            }

            // Function to get content for a Google item
            function getGoogleItemContent(app, type, itemId) {
              // Generate preview content based on app and type
              if (app === 'drive') {
                if (type === 'folder') {
                  return `<div class="text-center">
                    <i class="bi bi-folder-fill text-primary" style="font-size: 48px;"></i>
                    <h4 class="mt-3">${itemId.replace(/_/g, ' ')}</h4>
                    <div class="list-group mt-4">
                      <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-pdf text-danger me-3"></i>
                            <div>
                              <h6 class="mb-1">Document 1.pdf</h6>
                              <small>1.2 MB - Last updated: Yesterday</small>
                            </div>
                          </div>
                          <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-eye"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-download"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-share"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-trash"></i></button>
                          </div>
                        </div>
                      </div>
                      <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                          <div class="d-flex align-items-center">
                            <i class="bi bi-file-earmark-text text-primary me-3"></i>
                            <div>
                              <h6 class="mb-1">Document 2.docx</h6>
                              <small>0.8 MB - Last updated: 2 days ago</small>
                            </div>
                          </div>
                          <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Document_2.docx')"><i class="bi bi-eye"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Document_2.docx')"><i class="bi bi-download"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Document_2.docx')"><i class="bi bi-share"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Document_2.docx')"><i class="bi bi-trash"></i></button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>`;
                } else if (type === 'pdf') {
                  return `<div class="text-center">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                    <h4 class="mt-3">${itemId.replace(/_/g, ' ')}</h4>
                    <div class="border p-3 mt-3 text-start bg-light">
                      <h5>PDF Preview</h5>
                      <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                      <p>The document contains warehouse layout information and storage locations.</p>
                    </div>
                  </div>`;
                }
              } else if (app === 'docs') {
                return `<div class="border p-3 bg-light">
                  <h4>${itemId.replace(/_/g, ' ')}</h4>
                  <hr>
                  <p><strong>Warehouse Operations Document</strong></p>
                  <p>Document content would be displayed here in a real application.</p>
                  <p>This document outlines the standard operating procedures for warehouse operations.</p>
                  <ol>
                    <li>Receiving procedures</li>
                    <li>Storage guidelines</li>
                    <li>Picking and packing protocols</li>
                    <li>Shipping procedures</li>
                    <li>Inventory management</li>
                    <li>Quality control</li>
                  </ol>
                </div>`;
              } else if (app === 'sheets') {
                return `<div class="text-center">
                  <i class="bi bi-file-earmark-spreadsheet text-success" style="font-size: 48px;"></i>
                  <h4 class="mt-3">${itemId.replace(/_/g, ' ')}</h4>
                  <div class="table-responsive mt-3">
                    <table class="table table-bordered table-striped">
                      <thead>
                        <tr>
                          <th>Item ID</th>
                          <th>Description</th>
                          <th>Location</th>
                          <th>Quantity</th>
                          <th>Last Updated</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>WH-1001</td>
                          <td>Widget A</td>
                          <td>A-01-02</td>
                          <td>500</td>
                          <td>2025-05-10</td>
                        </tr>
                        <tr>
                          <td>WH-1002</td>
                          <td>Widget B</td>
                          <td>B-03-01</td>
                          <td>250</td>
                          <td>2025-05-09</td>
                        </tr>
                        <tr>
                          <td>WH-1003</td>
                          <td>Widget C</td>
                          <td>C-02-03</td>
                          <td>1000</td>
                          <td>2025-05-08</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>`;
              } else if (app === 'gmail') {
                if (type === 'email') {
                  let emailContent = '';
                  let emailSubject = '';
                  let emailSender = '';
                  let emailDate = '';

                  // Set email content based on the itemId
                  if (itemId === 'Acme_Corp_Shipment_Confirmation') {
                    emailSubject = 'Shipment confirmation for order #12345';
                    emailSender = 'Acme Corp <<EMAIL>>';
                    emailDate = 'May 12, 2025, 10:15 AM';
                    emailContent = `
                      <p>Dear Warehouse Manager,</p>
                      <p>This is to confirm that the shipment for order #12345 has been received by the customer.</p>
                      <p>Order Details:</p>
                      <ul>
                        <li>Order Number: #12345</li>
                        <li>Customer: Acme Corp</li>
                        <li>Delivery Date: May 12, 2025</li>
                        <li>Items:
                          <ul>
                            <li>Widget A (500 units)</li>
                            <li>Widget B (250 units)</li>
                          </ul>
                        </li>
                      </ul>
                      <p>Please update your inventory records accordingly.</p>
                      <p>Thank you,<br>Acme Corp Receiving Department</p>
                    `;
                  } else if (itemId === 'John_Davis_Warehouse_Inspection') {
                    emailSubject = 'Warehouse inspection scheduled for tomorrow';
                    emailSender = 'John Davis <<EMAIL>>';
                    emailDate = 'May 12, 2025, 9:30 AM';
                    emailContent = `
                      <p>Hello Warehouse Team,</p>
                      <p>This is a reminder that the warehouse inspection is scheduled for tomorrow at 10:00 AM.</p>
                      <p>Please ensure that all areas are properly organized and all safety protocols are being followed.</p>
                      <p>The inspection will cover:</p>
                      <ul>
                        <li>Inventory accuracy</li>
                        <li>Storage conditions</li>
                        <li>Safety compliance</li>
                        <li>Equipment maintenance</li>
                      </ul>
                      <p>If you have any questions, please let me know.</p>
                      <p>Regards,<br>John Davis<br>Operations Manager</p>
                    `;
                  } else if (itemId === 'Globex_Inc_New_Order') {
                    emailSubject = 'New order placement - Urgent delivery';
                    emailSender = 'Globex Inc <<EMAIL>>';
                    emailDate = 'May 12, 2025, 8:00 AM';
                    emailContent = `
                      <p>Dear Warehouse Team,</p>
                      <p>We need to place a new order for urgent delivery. Please process this order with highest priority.</p>
                      <p>Order Details:</p>
                      <ul>
                        <li>Order Number: #67890</li>
                        <li>Customer: Globex Inc</li>
                        <li>Required Delivery Date: May 14, 2025</li>
                        <li>Items:
                          <ul>
                            <li>Widget C (1000 units)</li>
                          </ul>
                        </li>
                      </ul>
                      <p>Please confirm receipt of this order and expected delivery date.</p>
                      <p>Thank you,<br>Globex Inc Purchasing Department</p>
                    `;
                  } else {
                    emailSubject = 'Email Subject';
                    emailSender = 'Sender <<EMAIL>>';
                    emailDate = 'May 12, 2025';
                    emailContent = '<p>Email content would be displayed here in a real application.</p>';
                  }

                  return `
                    <div class="card">
                      <div class="card-header bg-light">
                        <h5 class="mb-0">${emailSubject}</h5>
                      </div>
                      <div class="card-body">
                        <div class="d-flex justify-content-between mb-3">
                          <div>
                            <strong>From:</strong> ${emailSender}
                          </div>
                          <div>
                            <strong>Date:</strong> ${emailDate}
                          </div>
                        </div>
                        <hr>
                        <div class="email-content">
                          ${emailContent}
                        </div>
                      </div>
                    </div>
                  `;
                }
              }

              return `<div class="text-center">
                <i class="bi bi-file-earmark text-secondary" style="font-size: 48px;"></i>
                <h4 class="mt-3">${itemId.replace(/_/g, ' ')}</h4>
                <div class="alert alert-info mt-3">
                  <p>Preview not available for this file type.</p>
                </div>
              </div>`;
            }

            // Function to download a Google item
            function downloadGoogleItem(app, type, itemId) {
              console.log(`Downloading ${app} ${type}: ${itemId}`);
              alert(`Download started for ${itemId.replace(/_/g, ' ')}`);
            }

            // Function to share a Google item
            function shareGoogleItem(app, type, itemId) {
              console.log(`Sharing ${app} ${type}: ${itemId}`);
              const shareEmail = prompt('Enter email address to share with:');
              if (shareEmail) {
                alert(`${itemId.replace(/_/g, ' ')} has been shared with ${shareEmail}`);
              }
            }

            // Function to delete a Google item
            function deleteGoogleItem(app, type, itemId) {
              console.log(`Deleting ${app} ${type}: ${itemId}`);
              if (confirm(`Are you sure you want to delete ${itemId.replace(/_/g, ' ')}?`)) {
                alert(`${itemId.replace(/_/g, ' ')} has been deleted.`);
              }
            }

            function deleteCurrentFile() {
              console.log('Deleting current file');
              if (window.currentFile) {
                console.log('Deleting attachment:', window.currentFile.name);
                deleteAttachment(window.currentFile.name);
              } else if (window.currentGoogleItem) {
                console.log('Deleting Google item:', window.currentGoogleItem);
                deleteGoogleItem(window.currentGoogleItem.app, window.currentGoogleItem.type, window.currentGoogleItem.id);
              } else {
                console.warn('No file selected for deletion');
                alert('No file selected for deletion');
              }
            }
            // Initialize the ISA Modal Manager
            document.addEventListener('DOMContentLoaded', function() {
                if (typeof isaModalManager !== 'undefined') {
                    isaModalManager.init();
                }

                // Ensure the warehouse maps modal is included
                const modalContainer = document.getElementById('modal-container');
                if (modalContainer) {
                    fetch('/modals/warehouse-maps-modal.html')
                        .then(response => response.text())
                        .then(html => {
                            modalContainer.innerHTML += html;
                        })
                        .catch(error => {
                            console.error('Error loading warehouse maps modal:', error);
            // Initialize ISA Suite Enhancements
            if (typeof ISASuiteEnhancements === 'function') {
                const enhancements = new ISASuiteEnhancements({
                    appName: 'WMS',
                    appPrefix: 'wms',
                    appColor: '#2ecc71',
                    enableNotifications: true,
                    enableAnalytics: true,
                    enableExport: true,
                    enableSearch: true,
                    debug: true
                });
                enhancements.init();
                console.log('ISA Suite Enhancements initialized for WMS application');

                // Show welcome notification
                setTimeout(() => {
                    enhancements.showNotification('WMS Enhanced!', 'Advanced features are now available. Press Ctrl+? for help.', 'success');
                }, 2000);
            } else {
                console.warn('ISA Suite Enhancements not available');
            }
                        });
                }
            });

            // Google Calendar Component
            const GoogleCalendarHandler = {
                init() {
                    // Dashboard component event listeners
                    const refreshCalendarBtn = document.getElementById('refresh-calendar');
                    if (refreshCalendarBtn) {
                        refreshCalendarBtn.addEventListener('click', this.refreshCalendar.bind(this));
                    }

                    const createEventBtn = document.getElementById('create-new-event');
                    if (createEventBtn) {
                        createEventBtn.addEventListener('click', this.createEvent.bind(this));
                    }
                },

                refreshCalendar() {
                    console.log('Refreshing calendar...');
                    // Simulate API call to refresh calendar data
                    setTimeout(() => {
                        console.log('Calendar refreshed');
                        alert('Calendar refreshed successfully!');
                    }, 1000);
                },

                createEvent() {
                    console.log('Creating new event...');
                    // In a real implementation, this would open a modal or form to create a new event
                    const eventName = prompt('Enter event name:');
                    if (eventName) {
                        alert(`Event "${eventName}" created successfully!`);
                    }
                }
            };

            // Google Sheets Component
            const GoogleSheetsHandler = {
                init() {
                    // Dashboard component event listeners
                    const refreshSheetsBtn = document.getElementById('refresh-sheets');
                    if (refreshSheetsBtn) {
                        refreshSheetsBtn.addEventListener('click', this.refreshSheets.bind(this));
                    }

                    const createSheetBtn = document.getElementById('create-new-sheet');
                    if (createSheetBtn) {
                        createSheetBtn.addEventListener('click', this.createSheet.bind(this));
                    }
                },

                refreshSheets() {
                    console.log('Refreshing sheets...');
                    // Simulate API call to refresh sheets data
                    setTimeout(() => {
                        console.log('Sheets refreshed');
                        alert('Sheets refreshed successfully!');
                    }, 1000);
                },

                createSheet() {
                    console.log('Creating new sheet...');
                    // In a real implementation, this would open a modal or form to create a new sheet
                    const sheetName = prompt('Enter sheet name:');
                    if (sheetName) {
                        alert(`Sheet "${sheetName}" created successfully!`);
                    }
                }
            };

            // Google Gmail Component
            const GoogleGmailHandler = {
                init() {
                    // Dashboard component event listeners
                    const refreshGmailBtn = document.getElementById('refresh-gmail');
                    if (refreshGmailBtn) {
                        refreshGmailBtn.addEventListener('click', this.refreshGmail.bind(this));
                    }

                    const composeEmailBtn = document.getElementById('compose-email');
                    if (composeEmailBtn) {
                        composeEmailBtn.addEventListener('click', this.composeEmail.bind(this));
                    }
                },

                refreshGmail() {
                    console.log('Refreshing Gmail...');
                    // Simulate API call to refresh Gmail data
                    setTimeout(() => {
                        console.log('Gmail refreshed');
                        alert('Gmail refreshed successfully!');
                    }, 1000);
                },

                composeEmail() {
                    console.log('Composing new email...');
                    // In a real implementation, this would open a modal or form to compose a new email
                    const recipient = prompt('Enter recipient email:');
                    if (recipient) {
                        alert(`Email to "${recipient}" composed successfully!`);
                    }
                }
            };

            // Google Drive Component
            const GoogleDriveHandler = {
                init() {
                    // Dashboard component event listeners
                    const refreshDriveBtn = document.getElementById('refresh-drive');
                    if (refreshDriveBtn) {
                        refreshDriveBtn.addEventListener('click', this.refreshDrive.bind(this));
                    }

                    const uploadFileBtn = document.getElementById('upload-file');
                    if (uploadFileBtn) {
                        uploadFileBtn.addEventListener('click', this.uploadFile.bind(this));
                    }
                },

                refreshDrive() {
                    console.log('Refreshing Drive...');
                    // Simulate API call to refresh Drive data
                    setTimeout(() => {
                        console.log('Drive refreshed');
                        alert('Drive refreshed successfully!');
                    }, 1000);
                },

                uploadFile() {
                    console.log('Uploading file...');
                    // In a real implementation, this would open a modal or form to upload a file
                    const fileName = prompt('Enter file name:');
                    if (fileName) {
                        alert(`File "${fileName}" uploaded successfully!`);
                    }
                }
            };

            // Google Docs Component
            const GoogleDocsHandler = {
                init() {
                    // Dashboard component event listeners
                    const refreshDocsBtn = document.getElementById('refresh-docs');
                    if (refreshDocsBtn) {
                        refreshDocsBtn.addEventListener('click', this.refreshDocs.bind(this));
                    }

                    const createDocBtn = document.getElementById('create-new-doc');
                    if (createDocBtn) {
                        createDocBtn.addEventListener('click', this.createDoc.bind(this));
                    }
                },

                refreshDocs() {
                    console.log('Refreshing Docs...');
                    // Simulate API call to refresh Docs data
                    setTimeout(() => {
                        console.log('Docs refreshed');
                        alert('Docs refreshed successfully!');
                    }, 1000);
                },

                createDoc() {
                    console.log('Creating new document...');
                    // In a real implementation, this would open a modal or form to create a new document
                    const docName = prompt('Enter document name:');
                    if (docName) {
                        alert(`Document "${docName}" created successfully!`);
                    }
                }
            };

            // Google Maps Component
            const GoogleMapsHandler = {
                init() {
                    // Dashboard component event listeners
                    const refreshMapsBtn = document.getElementById('refresh-maps');
                    if (refreshMapsBtn) {
                        refreshMapsBtn.addEventListener('click', this.refreshMaps.bind(this));
                    }

                    const searchLocationBtn = document.getElementById('search-location');
                    if (searchLocationBtn) {
                        searchLocationBtn.addEventListener('click', this.searchLocation.bind(this));
                    }
                },

                refreshMaps() {
                    console.log('Refreshing Maps...');
                    // Simulate API call to refresh Maps data
                    setTimeout(() => {
                        console.log('Maps refreshed');
                        alert('Maps refreshed successfully!');
                    }, 1000);
                },

                searchLocation() {
                    console.log('Searching location...');
                    // In a real implementation, this would open a modal or form to search for a location
                    const location = prompt('Enter location:');
                    if (location) {
                        alert(`Location "${location}" found successfully!`);
                    }
                }
            };

            // Attachments Component
            const AttachmentsHandler = {
                init() {
                    // Dashboard component event listeners
                    const uploadAttachmentBtn = document.getElementById('upload-attachment');
                    if (uploadAttachmentBtn) {
                        uploadAttachmentBtn.addEventListener('click', this.uploadAttachment.bind(this));
                    }
                },

                uploadAttachment() {
                    console.log('Uploading attachment...');
                    // In a real implementation, this would open a modal or form to upload an attachment
                    const attachmentName = prompt('Enter attachment name:');
                    if (attachmentName) {
                        alert(`Attachment "${attachmentName}" uploaded successfully!`);
                    }
                }
            };

            // Initialize all components
            document.addEventListener('DOMContentLoaded', () => {
                GoogleCalendarHandler.init();
                GoogleSheetsHandler.init();
                GoogleGmailHandler.init();
                GoogleDriveHandler.init();
                GoogleDocsHandler.init();
                GoogleMapsHandler.init();
                AttachmentsHandler.init();
            });

            // Initialize the page
            document.addEventListener('DOMContentLoaded', () => {
                // Initialize sidebar toggle functionality
                const sidebarToggle = document.getElementById('sidebarToggle');
                const sidebar = document.getElementById('sidebar');
                if (sidebarToggle) {
                    sidebarToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        sidebar.classList.toggle('show');
                    });
                }

                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function(event) {
                    if (window.innerWidth < 768) {
                        const isClickInsideSidebar = sidebar.contains(event.target);
                        const isClickOnToggle = sidebarToggle && sidebarToggle.contains(event.target);
                        if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
                            sidebar.classList.remove('show');
                        }
                    }
                });

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth >= 768 && sidebar.classList.contains('show')) {
                        sidebar.classList.remove('show');
                    }
                });

                // Initialize Google Integration Components
                GoogleCalendarHandler.init();
                GoogleSheetsHandler.init();
                GoogleDocsHandler.init();
                GoogleDriveHandler.init();
                GoogleGmailHandler.init();
                GoogleMapsHandler.init();
                AttachmentsHandler.init();
            });

            // Create a simple health endpoint for status checks
            if (window.fetch) {
                // Create a fake health endpoint for the hub to check
                const originalFetch = window.fetch;
                window.fetch = function(url, options) {
                    if (url.includes('/health')) {
                        return Promise.resolve({
                            ok: true,
                            json: () => Promise.resolve({ status: 'ok' })
                        });
                    }
                    return originalFetch(url, options);
                };
            }


            // Initialize all modals to ensure they're ready
            document.addEventListener('DOMContentLoaded', function() {
                // Make sure Bootstrap is loaded
                if (typeof bootstrap === 'undefined') {
                    console.error('Bootstrap not found. Make sure bootstrap.bundle.min.js is loaded correctly.');
                    return;
                }
                console.log('Setting up modal handlers');
                // Initialize all modals
                document.querySelectorAll('.modal').forEach(modalEl => {
                    try {
                        // Check if modal already has an instance
                        if (!bootstrap.Modal.getInstance(modalEl)) {
                            new bootstrap.Modal(modalEl);
                            console.log(`Modal initialized: ${modalEl.id}`);
                        } else {
                            console.log(`Modal already initialized: ${modalEl.id}`);
                        }
                    } catch (err) {
                        console.error(`Error initializing modal ${modalEl.id}: ${err}`);
                    }
                });
                // Debug all modals
                setTimeout(debugModals, 500);
                // Add click handlers to all view buttons
                document.querySelectorAll('[onclick*="viewGoogleItem"]').forEach(button => {
                    console.log('Found view button:', button);
                    const originalOnClick = button.getAttribute('onclick');
                    button.addEventListener('click', function(e) {
                        console.log('View button clicked:', originalOnClick);
                      });
                });
            });
        </script>




        <!-- Unified Gmail Integration - Single working system -->
    <script src="../../SharedFeatures/ui/gmail-integration-unified.js"></script>

    <!-- ISA Suite Enhancements -->
    <script src="../../SharedFeatures/enhancements/cross-app-enhancements.js"></script>

    <!-- Initialize Unified Gmail Integration -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the unified Gmail integration for WMS
            if (typeof initializeUnifiedGmail === 'function') {
                const gmail = initializeUnifiedGmail({
                    appName: 'WMS',
                    appPrefix: 'wms',
                    appColor: '#2ecc71', // Green theme for WMS
                    modalId: 'wms-gmailModal',
                    triggerId: 'gmail-link', // The sidebar Gmail link
                    debug: true
                });
                console.log('Unified Gmail integration initialized for WMS application');

                // Make globally available for debugging
                window.wmsGmail = gmail;
            } else {
                console.warn('Unified Gmail integration not available');
            }

            // Initialize ISA Suite Enhancements
            if (typeof ISASuiteEnhancements === 'function') {
                const enhancements = new ISASuiteEnhancements({
                    appName: 'WMS',
                    appPrefix: 'wms',
                    appColor: '#2ecc71', // Green theme for WMS
                    enableNotifications: true,
                    enableAnalytics: true,
                    enableExport: true,
                    enableSearch: true,
                    debug: true
                });
                enhancements.init();
                console.log('ISA Suite Enhancements initialized for WMS application');

                // Show welcome notification
                setTimeout(() => {
                    enhancements.showNotification('WMS Enhanced!', 'Advanced features are now available. Press Ctrl+? for help.', 'success');
                }, 2000);
            } else {
                console.warn('ISA Suite Enhancements not available');
            }
        });
    </script>
</body>
</html>