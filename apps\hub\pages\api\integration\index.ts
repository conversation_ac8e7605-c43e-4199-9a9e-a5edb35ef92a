import { NextApiRequest, NextApiResponse } from 'next';
import { createProxyMiddleware } from 'http-proxy-middleware';

const proxy = createProxyMiddleware({
  target: process.env.API_GATEWAY_URL || 'http://localhost:3000',
  changeOrigin: true,
  pathRewrite: {
    '^/api/integration': '/api',
  },
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Proxy error occurred' });
  },
});

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  return proxy(req, res);
}

export const config = {
  api: {
    bodyParser: false,
    externalResolver: true,
  },
};
