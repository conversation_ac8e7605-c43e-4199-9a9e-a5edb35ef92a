@echo off
setlocal enabledelayedexpansion

REM Set PATH to include PortableNodeJS
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Check if the ISASUITE directory exists
if not exist "C:\ISASUITE" (
    echo ERROR: C:\ISASUITE directory not found.
    echo Please make sure the ISA Suite is installed correctly.
    pause
    exit /b 1
)

REM Check if the apps directory exists
if not exist "C:\ISASUITE\apps" (
    echo ERROR: C:\ISASUITE\apps directory not found.
    echo Please make sure the ISA Suite is installed correctly.
    pause
    exit /b 1
)

REM Check if PortableNodeJS exists
if not exist "C:\ISASUITE\PortableNodeJS" (
    echo ERROR: C:\ISASUITE\PortableNodeJS directory not found.
    echo Please make sure the ISA Suite is installed correctly.
    pause
    exit /b 1
)

REM Create a log directory if it doesn't exist
if not exist "C:\ISASUITE\logs" mkdir C:\ISASUITE\logs

REM Check if curl is available
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: curl command not found. Health checks may not work correctly.
    echo You may need to install curl or add it to your PATH.
    pause
)

REM Define colors for console output (using Windows color command instead of ANSI)
set "GREEN=0A"
set "YELLOW=0E"
set "RED=0C"
set "WHITE=0F"
set "CYAN=0B"
set "MAGENTA=0D"

REM Define application information
set "apps[1]=Integration Hub|hub|8000|index.js"
set "apps[2]=Business Management System|BMS|3001|index.js"
set "apps[3]=Materials Requirements Planning|MRP|3002|index.js"
set "apps[4]=Customer Relationship Management|CRM|3003|index.js"
set "apps[5]=Warehouse Management System|WMS|3004|index.js"
set "apps[6]=Advanced Planning and Scheduling|APS|3005|index.js"
set "apps[7]=Asset Performance Management|APM|3006|index.js"
set "apps[8]=Project Management System|PMS|3007|index.js"
set "apps[9]=Supply Chain Management|SCM|3008|server.js"
set "apps[10]=Task Management System|TM|3009|server.js"

REM Set default mode if not already set
if not defined APP_MODE set "APP_MODE=production"

REM Select application mode if not provided as parameter
if "%1"=="" (
    :select_mode
    cls
    color %WHITE%
    echo ===================================
    color %CYAN%
    echo    ISA Suite - Select Mode
    color %WHITE%
    echo ===================================
    echo.
    echo Select application mode:
    echo.
    color %GREEN%
    echo  1. Production Mode
    color %YELLOW%
    echo  2. Sandbox/Training Mode
    color %MAGENTA%
    echo  3. Demo Mode
    color %WHITE%
    echo.
    set /p mode_choice=Enter your choice (1-3):

    if "%mode_choice%"=="1" (
        set "APP_MODE=production"
        set "MODE_COLOR=%GREEN%"
        set "MODE_NAME=Production"
    ) else if "%mode_choice%"=="2" (
        set "APP_MODE=sandbox"
        set "MODE_COLOR=%YELLOW%"
        set "MODE_NAME=Sandbox/Training"
    ) else if "%mode_choice%"=="3" (
        set "APP_MODE=demo"
        set "MODE_COLOR=%MAGENTA%"
        set "MODE_NAME=Demo"
    ) else (
        echo Invalid choice. Please enter a number between 1 and 3.
        timeout /t 2 > nul
        goto select_mode
    )
) else (
    set "APP_MODE=%1"
    if "%APP_MODE%"=="production" (
        set "MODE_COLOR=%GREEN%"
        set "MODE_NAME=Production"
    ) else if "%APP_MODE%"=="sandbox" (
        set "MODE_COLOR=%YELLOW%"
        set "MODE_NAME=Sandbox/Training"
    ) else if "%APP_MODE%"=="demo" (
        set "MODE_COLOR=%MAGENTA%"
        set "MODE_NAME=Demo"
    ) else (
        set "APP_MODE=production"
        set "MODE_COLOR=%GREEN%"
        set "MODE_NAME=Production"
    )
)

REM Display menu
:menu
cls
color %WHITE%
echo ===================================
color %MODE_COLOR%
echo    ISA Suite Service Manager
echo    Mode: %MODE_NAME%
color %WHITE%
echo ===================================
echo.
echo  1. Start all applications
echo  2. Stop all applications
echo  3. Check application status
echo  4. Start individual application
echo  5. Stop individual application
echo  6. Open Integration Hub in browser
echo  7. Change application mode
echo  8. Exit
echo.
set /p choice=Enter your choice (1-8):

if "%choice%"=="1" goto start_all
if "%choice%"=="2" goto stop_all
if "%choice%"=="3" goto check_status
if "%choice%"=="4" goto start_individual
if "%choice%"=="5" goto stop_individual
if "%choice%"=="6" goto open_hub
if "%choice%"=="7" goto change_mode
if "%choice%"=="8" goto exit
goto menu

:change_mode
goto select_mode

:start_all
color %YELLOW%
echo Starting all ISA Suite applications in %MODE_NAME% mode...
color %WHITE%
echo.

REM Start Integration Hub (visible console as the main application)
echo Starting Integration Hub...
start cmd /k "cd C:\ISASUITE\apps\hub && set NODE_ENV=%APP_MODE% && node index.js > C:\ISASUITE\logs\hub.log 2>&1"

REM Wait for Integration Hub to start
timeout /t 5 > nul

REM Start all other applications in background (hidden)
for /L %%i in (2,1,10) do (
    for /F "tokens=1-4 delims=|" %%a in ("!apps[%%i]!") do (
        echo Starting %%a...
        start /b cmd /c "cd C:\ISASUITE\apps\%%b && set NODE_ENV=%APP_MODE% && node %%d > C:\ISASUITE\logs\%%b.log 2>&1"
    )
)

echo.
color %GREEN%
echo All applications started successfully.
color %WHITE%
echo Application logs are saved in C:\ISASUITE\logs directory.
echo.
pause
goto menu

:stop_all
color %YELLOW%
echo Stopping all ISA Suite applications...
color %WHITE%
echo.
taskkill /F /IM node.exe
echo.
color %GREEN%
echo All applications stopped successfully.
color %WHITE%
echo.
pause
goto menu

:check_status
color %YELLOW%
echo Checking application status...
color %WHITE%
echo.
for /L %%i in (1,1,10) do (
    for /F "tokens=1-4 delims=|" %%a in ("!apps[%%i]!") do (
        set "app_name=%%a"
        set "app_port=%%c"
        curl -s http://localhost:%%c/health > nul 2>&1
        if !errorlevel! equ 0 (
            color %GREEN%
            echo [ONLINE]  !app_name! ^(http://localhost:%%c^)
        ) else (
            color %RED%
            echo [OFFLINE] !app_name! ^(http://localhost:%%c^)
        )
        color %WHITE%
    )
)
echo.
pause
goto menu

:start_individual
color %YELLOW%
echo Select an application to start in %MODE_NAME% mode:
color %WHITE%
echo.
for /L %%i in (1,1,10) do (
    for /F "tokens=1-4 delims=|" %%a in ("!apps[%%i]!") do (
        echo %%i. %%a
    )
)
echo.
set /p app_choice=Enter application number (1-10):

if "%app_choice%" geq "1" if "%app_choice%" leq "10" (
    for /F "tokens=1-4 delims=|" %%a in ("!apps[%app_choice%]!") do (
        color %YELLOW%
        echo Starting %%a in %MODE_NAME% mode...
        color %WHITE%
        if "%app_choice%"=="1" (
            start cmd /k "cd C:\ISASUITE\apps\%%b && set NODE_ENV=%APP_MODE% && node %%d > C:\ISASUITE\logs\%%b.log 2>&1"
        ) else (
            start /b cmd /c "cd C:\ISASUITE\apps\%%b && set NODE_ENV=%APP_MODE% && node %%d > C:\ISASUITE\logs\%%b.log 2>&1"
        )
        color %GREEN%
        echo %%a started successfully.
        color %WHITE%
    )
) else (
    color %RED%
    echo Invalid choice. Please enter a number between 1 and 10.
    color %WHITE%
)
echo.
pause
goto menu

:stop_individual
color %YELLOW%
echo Select an application to stop:
color %WHITE%
echo.
for /L %%i in (1,1,10) do (
    for /F "tokens=1-4 delims=|" %%a in ("!apps[%%i]!") do (
        echo %%i. %%a
    )
)
echo.
set /p app_choice=Enter application number (1-10):

if "%app_choice%" geq "1" if "%app_choice%" leq "10" (
    for /F "tokens=1-4 delims=|" %%a in ("!apps[%app_choice%]!") do (
        color %YELLOW%
        echo Stopping %%a...
        color %WHITE%
        for /F "tokens=5" %%p in ('netstat -ano ^| findstr ":%%c"') do (
            taskkill /F /PID %%p > nul 2>&1
        )
        color %GREEN%
        echo %%a stopped successfully.
        color %WHITE%
    )
) else (
    color %RED%
    echo Invalid choice. Please enter a number between 1 and 10.
    color %WHITE%
)
echo.
pause
goto menu

:open_hub
color %YELLOW%
echo Opening Integration Hub in browser...
color %WHITE%
start http://localhost:8000
echo.
pause
goto menu

:exit
color %GREEN%
echo Thank you for using ISA Suite Service Manager.
color %WHITE%
exit /b 0
