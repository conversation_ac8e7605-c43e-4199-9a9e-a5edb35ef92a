/**
 * Google Modals Handler for MRP Application
 * Handles all interactions within the Google modals (She<PERSON>, <PERSON>s, Drive)
 */

document.addEventListener('DOMContentLoaded', function() {
    // Handle upload file in Drive modal
    const uploadFileBtn = document.getElementById('upload-new-file');
    if (uploadFileBtn) {
        uploadFileBtn.addEventListener('click', function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.onchange = function() {
                if (input.files.length > 0) {
                    const fileNames = Array.from(input.files).map(file => file.name).join(', ');
                    alert(`Files selected for upload: ${fileNames}`);
                }
            };
            input.click();
        });
    }
    
    // Handle create new document in Docs modal
    const createDocBtn = document.getElementById('create-new-document');
    if (createDocBtn) {
        createDocBtn.addEventListener('click', function() {
            const docName = prompt('Enter a name for the new document:');
            if (docName) {
                alert(`New document "${docName}" created!`);
                
                // In a real app, this would call an API to create the document
                // For demo purposes, we just show an alert
            }
        });
    }
    
    // Handle create new spreadsheet in Sheets modal
    const createSheetBtn = document.getElementById('create-new-spreadsheet');
    if (createSheetBtn) {
        createSheetBtn.addEventListener('click', function() {
            const sheetName = prompt('Enter a name for the new spreadsheet:');
            if (sheetName) {
                alert(`New spreadsheet "${sheetName}" created!`);
                
                // In a real app, this would call an API to create the spreadsheet
                // For demo purposes, we just show an alert
            }
        });
    }
    
    // Handle refresh buttons
    const refreshDriveBtn = document.getElementById('refresh-drive-modal');
    if (refreshDriveBtn) {
        refreshDriveBtn.addEventListener('click', function() {
            const button = refreshDriveBtn;
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            setTimeout(function() {
                button.disabled = false;
                button.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Refresh';
                alert('Drive files refreshed!');
            }, 1000);
        });
    }
    
    const refreshDocsBtn = document.getElementById('refresh-docs-modal');
    if (refreshDocsBtn) {
        refreshDocsBtn.addEventListener('click', function() {
            const button = refreshDocsBtn;
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            setTimeout(function() {
                button.disabled = false;
                button.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Refresh';
                alert('Google Docs refreshed!');
            }, 1000);
        });
    }
    
    const refreshSheetsBtn = document.getElementById('refresh-sheets-modal');
    if (refreshSheetsBtn) {
        refreshSheetsBtn.addEventListener('click', function() {
            const button = refreshSheetsBtn;
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            setTimeout(function() {
                button.disabled = false;
                button.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Refresh';
                alert('Google Sheets refreshed!');
            }, 1000);
        });
    }
});
