/**
 * Gmail Implementation Script for ISA Suite Applications
 *
 * This script provides a simple way to implement the shared Gmail functionality
 * in any ISA Suite application. It loads the required files and initializes
 * the Gmail integration with the appropriate configuration.
 *
 * Usage:
 * 1. Include this script in your application's HTML file
 * 2. Call initializeGmail() with the appropriate configuration
 *
 * Example:
 * ```
 * <script src="/SharedFeatures/ui/gmail-implementation.js"></script>
 * <script>
 *   document.addEventListener('DOMContentLoaded', function() {
 *     initializeGmail({
 *       appName: 'MRP',
 *       appPrefix: 'mrp',
 *       primaryColor: '#9c27b0', // Purple for MRP
 *       debug: true
 *     });
 *   });
 * </script>
 * ```
 */

/**
 * Initialize Gmail integration for an application
 * @param {Object} config - Configuration options
 */
function initializeGmail(config = {}) {
    console.log('Initializing Gmail integration for', config.appName || 'application');

    // Load the required CSS and JS files
    loadGmailDependencies()
        .then(() => {
            // Initialize the Gmail integration
            if (typeof GmailIntegration !== 'undefined') {
                const gmail = new GmailIntegration(config);
                gmail.init();

                console.log('Gmail integration initialized successfully for', config.appName || 'application');
                return gmail;
            } else {
                console.error('GmailIntegration class not found. Make sure the gmail-integration.js file is loaded correctly.');
                return null;
            }
        })
        .catch(error => {
            console.error('Error initializing Gmail integration:', error);
        });
}

/**
 * Load Gmail integration dependencies (CSS and JS files)
 * @returns {Promise} - Promise that resolves when all dependencies are loaded
 */
function loadGmailDependencies() {
    return new Promise((resolve, reject) => {
        try {
            // Load CSS
            loadCSS('../../SharedFeatures/ui/gmail-integration.css')
                .then(() => {
                    // Load JS
                    return loadScript('../../SharedFeatures/ui/gmail-integration.js');
                })
                .then(() => {
                    resolve();
                })
                .catch(error => {
                    reject(error);
                });
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * Load a CSS file
 * @param {string} url - URL of the CSS file
 * @returns {Promise} - Promise that resolves when the CSS file is loaded
 */
function loadCSS(url) {
    return new Promise((resolve, reject) => {
        // Check if the CSS file is already loaded
        const existingLink = document.querySelector(`link[href="${url}"]`);
        if (existingLink) {
            resolve();
            return;
        }

        // Create a new link element
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;

        // Set up load and error handlers
        link.onload = () => resolve();
        link.onerror = () => reject(new Error(`Failed to load CSS file: ${url}`));

        // Add the link element to the document head
        document.head.appendChild(link);
    });
}

/**
 * Load a JavaScript file
 * @param {string} url - URL of the JavaScript file
 * @returns {Promise} - Promise that resolves when the JavaScript file is loaded
 */
function loadScript(url) {
    return new Promise((resolve, reject) => {
        // Check if the script is already loaded
        const existingScript = document.querySelector(`script[src="${url}"]`);
        if (existingScript) {
            resolve();
            return;
        }

        // Create a new script element
        const script = document.createElement('script');
        script.src = url;

        // Set up load and error handlers
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load script: ${url}`));

        // Add the script element to the document body
        document.body.appendChild(script);
    });
}
