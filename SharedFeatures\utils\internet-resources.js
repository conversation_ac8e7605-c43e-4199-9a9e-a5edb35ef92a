// Internet Resources Module

// Mock implementation for demonstration purposes
// In a real app, this would use various search APIs

const logger = require('../logger').createLogger('InternetResources');

// Mock providers data
const providers = [
  {
    id: 'google',
    name: 'Google',
    description: 'Search the web with Google',
    types: ['web', 'video', 'image', 'news'],
    url: 'https://www.google.com',
  },
  {
    id: 'youtube',
    name: 'YouTube',
    description: 'Search videos on YouTube',
    types: ['video'],
    url: 'https://www.youtube.com',
  },
  {
    id: 'stackoverflow',
    name: 'Stack Overflow',
    description: 'Search for technical questions and answers',
    types: ['technical', 'code'],
    url: 'https://stackoverflow.com',
  },
  {
    id: 'github',
    name: 'GitHub',
    description: 'Search for code repositories',
    types: ['code', 'technical'],
    url: 'https://github.com',
  },
  {
    id: 'medium',
    name: 'Medium',
    description: 'Search for articles and blog posts',
    types: ['article', 'blog'],
    url: 'https://medium.com',
  },
];

/**
 * Get available providers
 */
function getProviders() {
  return providers;
}

/**
 * Search all providers
 */
async function searchAllProviders(query) {
  logger.info('Searching all providers', { query });

  // In a real implementation, this would search multiple providers
  // For this demo, we'll return mock results

  return [
    {
      title: 'Business Management Best Practices',
      url: 'https://www.example.com/business-management-best-practices',
      description:
        'Learn about best practices for business management, including financial management, human resources, and operations.',
      provider: 'google',
      type: 'web',
    },
    {
      title: 'Introduction to Business Management Systems',
      url: 'https://www.youtube.com/watch?v=example2',
      description:
        'This video provides an introduction to business management systems, including key features and benefits.',
      provider: 'youtube',
      type: 'video',
    },
    {
      title: 'How to Implement a Business Management System',
      url: 'https://medium.com/example/how-to-implement-a-business-management-system',
      description:
        'A step-by-step guide to implementing a business management system in your organization.',
      provider: 'medium',
      type: 'article',
    },
  ];
}

/**
 * Search specific providers
 */
async function searchSpecificProviders(query, providerIds) {
  logger.info('Searching specific providers', { query, providerIds });

  // In a real implementation, this would search the specified providers
  // For this demo, we'll return mock results filtered by provider

  const allResults = await searchAllProviders(query);
  return allResults.filter((result) => providerIds.includes(result.provider));
}

/**
 * Search by type
 */
async function searchByType(query, type) {
  logger.info('Searching by type', { query, type });

  // In a real implementation, this would search for the specified type
  // For this demo, we'll return mock results filtered by type

  const allResults = await searchAllProviders(query);
  return allResults.filter((result) => result.type === type);
}

module.exports = {
  getProviders,
  searchAllProviders,
  searchSpecificProviders,
  searchByType,
};
