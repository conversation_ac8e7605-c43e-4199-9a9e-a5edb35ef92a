@echo off
echo Starting Warehouse Management System (WMS)...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Change to the WMS directory
cd /d C:\ISASUITE\apps\WMS\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call C:\ISASUITE\PortableNodeJS\pnpm install
)

REM Close any potentially running instance on the same port
taskkill /F /FI "WINDOWTITLE eq http://localhost:3004*" /T > nul 2>&1

REM Run the application with production mode by default
echo Starting WMS in production mode...
echo To access WMS, go to: http://localhost:3004
start "" http://localhost:3004

REM Start using pnpm (matching the working APM launcher)
pnpm start

echo.
echo WMS application has been closed.
pause