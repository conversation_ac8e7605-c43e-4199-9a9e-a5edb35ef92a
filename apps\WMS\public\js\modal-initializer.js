/**
 * Modal Initializer for WMS
 * Ensures all modals are properly registered and can be opened
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing WMS modals');
    
    // Set up direct click handlers for all sidebar integrations
    document.querySelectorAll('[data-isa-action]').forEach(element => {
        element.addEventListener('click', function(event) {
            event.preventDefault();
            
            const action = element.getAttribute('data-isa-action');
            console.log(`Handling click for ${action}`);
            
            switch(action) {
                case 'open-maps':
                    openMapsModal();
                    break;
                case 'open-warehouse-maps':
                    openWarehouseMapsModal();
                    break;
                case 'open-attachments':
                    openAttachmentsModal();
                    break;
                case 'open-gmail':
                    openGmailModal();
                    break;
                case 'open-calendar':
                    openCalendarModal();
                    break;
                case 'open-drive':
                    openDriveModal();
                    break;
                case 'open-docs':
                    openDocsModal();
                    break;
                case 'open-sheets':
                    openSheetsModal();
                    break;
            }
        });
    });
    
    // Add utility function to clean up modals before opening new ones
    function cleanupModals() {
        // Remove any stray backdrops
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });
        
        // Close any existing modals
        document.querySelectorAll('.modal.show').forEach(openModal => {
            try {
                const instance = bootstrap.Modal.getInstance(openModal);
                if (instance) {
                    instance.hide();
                }
            } catch (err) {
                console.error('Error closing modal:', err);
            }
        });
        
        // Reset body state
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }
    
    // Define global modal opener functions with better error handling and cleanup
    window.openMapsModal = function() {
        console.log('Opening Google Maps modal');
        cleanupModals();
        
        const mapsModal = document.getElementById('mapsModal');
        if (mapsModal) {
            const modal = new bootstrap.Modal(mapsModal);
            modal.show();
            return true;
        } else {
            console.error('Maps modal not found in DOM');
            alert('Google Maps feature is not available. Please try again later.');
            return false;
        }
    };
    
    window.openWarehouseMapsModal = function() {
        console.log('Opening Warehouse Maps modal');
        cleanupModals();
        
        const warehouseMapsModal = document.getElementById('warehouseMapsModal');
        if (warehouseMapsModal) {
            const modal = new bootstrap.Modal(warehouseMapsModal);
            modal.show();
            return true;
        } else {
            // Warehouse maps modal might not exist, create a basic version
            console.warn('Warehouse Maps modal not found, creating placeholder');
            
            const modalHtml = `
                <div class="modal fade" id="warehouseMapsModal" tabindex="-1" aria-labelledby="warehouseMapsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="warehouseMapsModalLabel">Warehouse Maps</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="ratio ratio-16x9">
                                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                                </div>
                                <div class="mt-3">
                                    <h5>Warehouse Locations</h5>
                                    <ul class="list-group">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Main Warehouse
                                            <span class="badge bg-primary rounded-pill">Primary</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            East Distribution Center
                                            <span class="badge bg-secondary rounded-pill">Secondary</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            West Fulfillment Center
                                            <span class="badge bg-info rounded-pill">Fulfillment</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <a href="https://maps.google.com" target="_blank" class="btn btn-primary">Open in Maps</a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = modalHtml;
            document.body.appendChild(tempDiv.firstElementChild);
            
            const modal = new bootstrap.Modal(document.getElementById('warehouseMapsModal'));
            modal.show();
            return true;
        }
    };
    
    window.openAttachmentsModal = function() {
        console.log('Opening Attachments modal');
        cleanupModals();
        
        const attachmentsModal = document.getElementById('attachmentsModal');
        if (attachmentsModal) {
            const modal = new bootstrap.Modal(attachmentsModal);
            modal.show();
            return true;
        } else {
            console.error('Attachments modal not found in DOM');
            alert('Attachments feature is not available. Please try again later.');
            return false;
        }
    };
    
    window.openGmailModal = function() {
        console.log('Opening Gmail modal');
        cleanupModals();
        
        const gmailModal = document.getElementById('gmailModal');
        if (gmailModal) {
            const modal = new bootstrap.Modal(gmailModal);
            modal.show();
            return true;
        } else {
            console.error('Gmail modal not found in DOM');
            alert('Gmail feature is not available. Please try again later.');
            return false;
        }
    };
    
    window.openCalendarModal = function() {
        console.log('Opening Calendar modal');
        cleanupModals();
        
        const calendarModal = document.getElementById('calendarModal');
        if (calendarModal) {
            const modal = new bootstrap.Modal(calendarModal);
            modal.show();
            return true;
        } else {
            console.error('Calendar modal not found in DOM');
            alert('Calendar feature is not available. Please try again later.');
            return false;
        }
    };
    
    window.openDriveModal = function() {
        console.log('Opening Drive modal');
        cleanupModals();
        
        const driveModal = document.getElementById('driveModal');
        if (driveModal) {
            const modal = new bootstrap.Modal(driveModal);
            modal.show();
            return true;
        } else {
            console.error('Drive modal not found in DOM');
            alert('Drive feature is not available. Please try again later.');
            return false;
        }
    };
    
    window.openDocsModal = function() {
        console.log('Opening Docs modal');
        cleanupModals();
        
        const docsModal = document.getElementById('docsModal');
        if (docsModal) {
            const modal = new bootstrap.Modal(docsModal);
            modal.show();
            return true;
        } else {
            console.error('Docs modal not found in DOM');
            alert('Docs feature is not available. Please try again later.');
            return false;
        }
    };
    
    window.openSheetsModal = function() {
        console.log('Opening Sheets modal');
        cleanupModals();
        
        const sheetsModal = document.getElementById('sheetsModal');
        if (sheetsModal) {
            const modal = new bootstrap.Modal(sheetsModal);
            modal.show();
            return true;
        } else {
            console.error('Sheets modal not found in DOM');
            alert('Sheets feature is not available. Please try again later.');
            return false;
        }
    };
    
    // Helper function to handle file downloads
    window.downloadCurrentFile = function() {
        if (window.currentFile) {
            console.log(`Downloading file: ${window.currentFile.name}`);
            alert(`Download started for ${window.currentFile.name}`);
        } else {
            alert('No file selected for download');
        }
    };
    
    // Helper function to handle file sharing
    window.shareCurrentFile = function() {
        if (window.currentFile) {
            console.log(`Sharing file: ${window.currentFile.name}`);
            const email = prompt(`Enter email address to share ${window.currentFile.name} with:`);
            if (email) {
                alert(`${window.currentFile.name} shared with ${email}`);
            }
        } else {
            alert('No file selected for sharing');
        }
    };
});
