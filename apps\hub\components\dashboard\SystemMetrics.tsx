import { useState, useEffect } from 'react';

interface Metrics {
  totalTransactions: number;
  successRate: number;
  averageResponseTime: number;
  activeUsers: number;
}

export default function SystemMetrics() {
  const [metrics, setMetrics] = useState<Metrics>({
    totalTransactions: 0,
    successRate: 0,
    averageResponseTime: 0,
    activeUsers: 0,
  });

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch('/api/metrics');
        const data = await response.json();
        setMetrics(data);
      } catch (error) {
        console.error('Error fetching metrics:', error);
      }
    };

    fetchMetrics();
    const interval = setInterval(fetchMetrics, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-sm font-medium text-gray-500">Total Transactions</h3>
        <p className="mt-2 text-3xl font-semibold text-gray-900">
          {metrics.totalTransactions.toLocaleString()}
        </p>
      </div>
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-sm font-medium text-gray-500">Success Rate</h3>
        <p className="mt-2 text-3xl font-semibold text-green-600">
          {metrics.successRate.toFixed(1)}%
        </p>
      </div>
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-sm font-medium text-gray-500">Avg Response Time</h3>
        <p className="mt-2 text-3xl font-semibold text-blue-600">{metrics.averageResponseTime}ms</p>
      </div>
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-sm font-medium text-gray-500">Active Users</h3>
        <p className="mt-2 text-3xl font-semibold text-purple-600">{metrics.activeUsers}</p>
      </div>
    </div>
  );
}
