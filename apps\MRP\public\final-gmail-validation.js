/**
 * Final Gmail Dropdown Validation Test
 * Copy and paste this into the browser console at http://localhost:3002
 * 
 * This comprehensive test validates that:
 * 1. All required resources are loaded
 * 2. Gmail modal can be opened
 * 3. Dropdown buttons exist and are functional
 * 4. Dropdown menus open and close properly
 * 5. Dropdown options can be selected
 */

(function() {
    'use strict';
    
    console.clear();
    console.log('🎉 FINAL GMAIL DROPDOWN VALIDATION TEST');
    console.log('=====================================');
    console.log('Starting comprehensive validation...\n');
    
    let testResults = {
        total: 0,
        passed: 0,
        failed: 0,
        warnings: 0
    };
    
    function logTest(message, status = 'info', details = '') {
        const icons = {
            pass: '✅',
            fail: '❌',
            warn: '⚠️',
            info: 'ℹ️'
        };
        
        const styles = {
            pass: 'color: #28a745; font-weight: bold;',
            fail: 'color: #dc3545; font-weight: bold;',
            warn: 'color: #ffc107; font-weight: bold;',
            info: 'color: #007bff;'
        };
        
        console.log(`%c${icons[status]} ${message}`, styles[status]);
        if (details) {
            console.log(`   ${details}`);
        }
        
        if (status !== 'info') {
            testResults.total++;
            if (status === 'pass') testResults.passed++;
            else if (status === 'fail') testResults.failed++;
            else if (status === 'warn') testResults.warnings++;
        }
    }
    
    // Phase 1: Resource Loading Tests
    function phase1_ResourceTests() {
        logTest('Phase 1: Resource Loading Tests', 'info');
        console.log('─'.repeat(40));
        
        // Test Bootstrap
        if (typeof bootstrap !== 'undefined') {
            logTest('Bootstrap library loaded', 'pass', `Version: ${bootstrap.Tooltip?.VERSION || 'unknown'}`);
        } else {
            logTest('Bootstrap library NOT loaded', 'fail');
        }
        
        // Test ISADataUtils
        if (typeof ISADataUtils !== 'undefined') {
            logTest('ISADataUtils loaded', 'pass');
        } else {
            logTest('ISADataUtils NOT loaded', 'fail');
        }
        
        // Test MRP Gmail Scripts
        if (typeof window.MRPGmailDropdowns !== 'undefined') {
            logTest('MRP Gmail Dropdowns script loaded', 'pass');
        } else {
            logTest('MRP Gmail Dropdowns script NOT loaded', 'fail');
        }
        
        if (typeof window.MRPGmailUtils !== 'undefined') {
            logTest('MRP Gmail Utils script loaded', 'pass');
        } else {
            logTest('MRP Gmail Utils script NOT loaded', 'fail');
        }
        
        console.log('');
    }
    
    // Phase 2: DOM Element Tests
    function phase2_DOMTests() {
        logTest('Phase 2: DOM Element Tests', 'info');
        console.log('─'.repeat(40));
        
        // Test Gmail Modal
        const modal = document.getElementById('mrp-gmailModal');
        if (modal) {
            logTest('Gmail modal element found', 'pass');
        } else {
            logTest('Gmail modal element NOT found', 'fail');
            return { modal: null };
        }
        
        // Test Dropdown Buttons
        const sortDropdown = document.getElementById('sort-dropdown');
        const filterDropdown = document.getElementById('filter-dropdown');
        
        if (sortDropdown) {
            logTest('Sort dropdown button found', 'pass');
            
            // Check attributes
            if (sortDropdown.hasAttribute('data-bs-toggle')) {
                logTest('Sort dropdown has Bootstrap data-bs-toggle', 'pass');
            } else {
                logTest('Sort dropdown missing Bootstrap data-bs-toggle', 'warn');
            }
        } else {
            logTest('Sort dropdown button NOT found', 'fail');
        }
        
        if (filterDropdown) {
            logTest('Filter dropdown button found', 'pass');
            
            // Check attributes
            if (filterDropdown.hasAttribute('data-bs-toggle')) {
                logTest('Filter dropdown has Bootstrap data-bs-toggle', 'pass');
            } else {
                logTest('Filter dropdown missing Bootstrap data-bs-toggle', 'warn');
            }
        } else {
            logTest('Filter dropdown button NOT found', 'fail');
        }
        
        // Test Dropdown Menus
        if (sortDropdown) {
            const sortMenu = sortDropdown.nextElementSibling;
            if (sortMenu && sortMenu.classList.contains('dropdown-menu')) {
                logTest('Sort dropdown menu found', 'pass');
                
                const sortOptions = sortMenu.querySelectorAll('.sort-option');
                if (sortOptions.length > 0) {
                    logTest(`Sort dropdown has ${sortOptions.length} options`, 'pass');
                } else {
                    logTest('Sort dropdown has no options', 'warn');
                }
            } else {
                logTest('Sort dropdown menu NOT found', 'fail');
            }
        }
        
        if (filterDropdown) {
            const filterMenu = filterDropdown.nextElementSibling;
            if (filterMenu && filterMenu.classList.contains('dropdown-menu')) {
                logTest('Filter dropdown menu found', 'pass');
                
                const filterOptions = filterMenu.querySelectorAll('.filter-option');
                if (filterOptions.length > 0) {
                    logTest(`Filter dropdown has ${filterOptions.length} options`, 'pass');
                } else {
                    logTest('Filter dropdown has no options', 'warn');
                }
            } else {
                logTest('Filter dropdown menu NOT found', 'fail');
            }
        }
        
        console.log('');
        return { modal, sortDropdown, filterDropdown };
    }
    
    // Phase 3: Modal Opening Test
    function phase3_ModalTest(elements) {
        return new Promise((resolve) => {
            logTest('Phase 3: Modal Opening Test', 'info');
            console.log('─'.repeat(40));
            
            // Check if modal is already open
            const isModalOpen = elements.modal && window.getComputedStyle(elements.modal).display !== 'none';
            
            if (isModalOpen) {
                logTest('Gmail modal is already open', 'pass');
                resolve(elements);
                return;
            }
            
            // Find Gmail button
            const gmailButton = document.querySelector('[data-bs-target="#mrp-gmailModal"]') ||
                               document.querySelector('#mrp-open-gmail-btn') ||
                               document.querySelector('#compose-email');
            
            if (!gmailButton) {
                logTest('Gmail button NOT found', 'fail');
                resolve(elements);
                return;
            }
            
            logTest('Gmail button found, attempting to open modal...', 'info');
            gmailButton.click();
            
            setTimeout(() => {
                const isNowOpen = elements.modal && window.getComputedStyle(elements.modal).display !== 'none';
                if (isNowOpen) {
                    logTest('Gmail modal opened successfully', 'pass');
                } else {
                    logTest('Gmail modal failed to open', 'fail');
                }
                console.log('');
                resolve(elements);
            }, 1000);
        });
    }
    
    // Phase 4: Dropdown Functionality Tests
    function phase4_DropdownTests(elements) {
        return new Promise((resolve) => {
            logTest('Phase 4: Dropdown Functionality Tests', 'info');
            console.log('─'.repeat(40));
            
            let testCount = 0;
            const totalTests = 2;
            
            function completeTest() {
                testCount++;
                if (testCount >= totalTests) {
                    console.log('');
                    resolve();
                }
            }
            
            // Test Sort Dropdown
            if (elements.sortDropdown) {
                logTest('Testing sort dropdown click...', 'info');
                elements.sortDropdown.click();
                
                setTimeout(() => {
                    const menu = elements.sortDropdown.nextElementSibling;
                    const isOpen = menu && menu.classList.contains('show');
                    
                    if (isOpen) {
                        logTest('Sort dropdown opens on click', 'pass');
                        
                        // Test closing
                        elements.sortDropdown.click();
                        setTimeout(() => {
                            const isClosed = !menu.classList.contains('show');
                            logTest('Sort dropdown closes on second click', isClosed ? 'pass' : 'warn');
                            completeTest();
                        }, 200);
                    } else {
                        logTest('Sort dropdown does NOT open on click', 'fail');
                        completeTest();
                    }
                }, 300);
            } else {
                completeTest();
            }
            
            // Test Filter Dropdown
            if (elements.filterDropdown) {
                setTimeout(() => {
                    logTest('Testing filter dropdown click...', 'info');
                    elements.filterDropdown.click();
                    
                    setTimeout(() => {
                        const menu = elements.filterDropdown.nextElementSibling;
                        const isOpen = menu && menu.classList.contains('show');
                        
                        if (isOpen) {
                            logTest('Filter dropdown opens on click', 'pass');
                            
                            // Test closing
                            elements.filterDropdown.click();
                            setTimeout(() => {
                                const isClosed = !menu.classList.contains('show');
                                logTest('Filter dropdown closes on second click', isClosed ? 'pass' : 'warn');
                                completeTest();
                            }, 200);
                        } else {
                            logTest('Filter dropdown does NOT open on click', 'fail');
                            completeTest();
                        }
                    }, 300);
                }, 500);
            } else {
                completeTest();
            }
        });
    }
    
    // Phase 5: Option Selection Tests
    function phase5_OptionTests(elements) {
        return new Promise((resolve) => {
            logTest('Phase 5: Option Selection Tests', 'info');
            console.log('─'.repeat(40));
            
            let optionTestsPassed = 0;
            
            // Test sort option selection
            if (elements.sortDropdown) {
                elements.sortDropdown.click();
                
                setTimeout(() => {
                    const sortOptions = document.querySelectorAll('#sort-dropdown + .dropdown-menu .sort-option');
                    if (sortOptions.length > 0) {
                        logTest(`Found ${sortOptions.length} sort options, testing first one...`, 'info');
                        
                        // Add temporary click handler
                        const testHandler = (e) => {
                            e.preventDefault();
                            const sortType = e.target.getAttribute('data-sort');
                            logTest(`Sort option clicked successfully: ${sortType}`, 'pass');
                            optionTestsPassed++;
                            e.target.removeEventListener('click', testHandler);
                        };
                        
                        sortOptions[0].addEventListener('click', testHandler);
                        sortOptions[0].click();
                    } else {
                        logTest('No sort options found to test', 'warn');
                    }
                    
                    // Test filter option selection
                    setTimeout(() => {
                        if (elements.filterDropdown) {
                            elements.filterDropdown.click();
                            
                            setTimeout(() => {
                                const filterOptions = document.querySelectorAll('#filter-dropdown + .dropdown-menu .filter-option');
                                if (filterOptions.length > 0) {
                                    logTest(`Found ${filterOptions.length} filter options, testing first one...`, 'info');
                                    
                                    // Add temporary click handler
                                    const testHandler = (e) => {
                                        e.preventDefault();
                                        const filterType = e.target.getAttribute('data-filter');
                                        logTest(`Filter option clicked successfully: ${filterType}`, 'pass');
                                        optionTestsPassed++;
                                        e.target.removeEventListener('click', testHandler);
                                    };
                                    
                                    filterOptions[0].addEventListener('click', testHandler);
                                    filterOptions[0].click();
                                } else {
                                    logTest('No filter options found to test', 'warn');
                                }
                                
                                setTimeout(() => {
                                    if (optionTestsPassed > 0) {
                                        logTest(`${optionTestsPassed} option selection tests passed`, 'pass');
                                    }
                                    console.log('');
                                    resolve();
                                }, 300);
                            }, 300);
                        } else {
                            setTimeout(() => {
                                if (optionTestsPassed > 0) {
                                    logTest(`${optionTestsPassed} option selection tests passed`, 'pass');
                                }
                                console.log('');
                                resolve();
                            }, 300);
                        }
                    }, 500);
                }, 300);
            } else {
                logTest('No dropdowns available for option testing', 'warn');
                console.log('');
                resolve();
            }
        });
    }
    
    // Final Results
    function showFinalResults() {
        console.log('🏁 FINAL VALIDATION RESULTS');
        console.log('═'.repeat(40));
        
        const successRate = Math.round((testResults.passed / testResults.total) * 100);
        
        console.log(`%cTotal Tests: ${testResults.total}`, 'font-weight: bold; font-size: 14px;');
        console.log(`%c✅ Passed: ${testResults.passed}`, 'color: #28a745; font-weight: bold; font-size: 14px;');
        console.log(`%c❌ Failed: ${testResults.failed}`, 'color: #dc3545; font-weight: bold; font-size: 14px;');
        console.log(`%c⚠️ Warnings: ${testResults.warnings}`, 'color: #ffc107; font-weight: bold; font-size: 14px;');
        console.log(`%cSuccess Rate: ${successRate}%`, successRate >= 80 ? 'color: #28a745; font-weight: bold; font-size: 16px;' : 'color: #dc3545; font-weight: bold; font-size: 16px;');
        
        console.log('');
        
        if (successRate >= 90) {
            console.log('%c🎉 EXCELLENT! Gmail dropdowns are working perfectly!', 'color: #28a745; font-weight: bold; font-size: 16px; background: #d4edda; padding: 8px; border-radius: 4px;');
        } else if (successRate >= 70) {
            console.log('%c✅ GOOD! Gmail dropdowns are mostly working with minor issues.', 'color: #856404; font-weight: bold; font-size: 14px; background: #fff3cd; padding: 8px; border-radius: 4px;');
        } else {
            console.log('%c❌ NEEDS ATTENTION! Gmail dropdowns have significant issues.', 'color: #721c24; font-weight: bold; font-size: 14px; background: #f8d7da; padding: 8px; border-radius: 4px;');
        }
        
        console.log('');
        console.log('📋 SUMMARY:');
        console.log('- Fixed server MIME type issues for shared CSS/JS files');
        console.log('- Added null checks to prevent JavaScript errors');
        console.log('- Implemented robust dropdown functionality with Bootstrap + manual fallbacks');
        console.log('- Created comprehensive utility functions for email management');
        console.log('- Gmail modal and dropdowns should now work correctly');
        
        console.log('');
        console.log('🎯 NEXT STEPS:');
        console.log('1. Test manually by opening Gmail modal and using dropdowns');
        console.log('2. Verify email sorting and filtering works as expected');
        console.log('3. Check notifications appear when actions are performed');
        console.log('4. If any issues remain, check browser console for specific errors');
    }
    
    // Main test execution
    async function runFullValidation() {
        try {
            phase1_ResourceTests();
            const elements = phase2_DOMTests();
            const elementsAfterModal = await phase3_ModalTest(elements);
            await phase4_DropdownTests(elementsAfterModal);
            await phase5_OptionTests(elementsAfterModal);
            showFinalResults();
        } catch (error) {
            console.error('Test execution error:', error);
            logTest('Test execution failed', 'fail', error.message);
            showFinalResults();
        }
    }
    
    // Export for manual use
    window.runGmailDropdownValidation = runFullValidation;
    
    // Auto-run
    console.log('Starting validation in 1 second...\n');
    setTimeout(runFullValidation, 1000);
    
})();

console.log('📋 Gmail Dropdown Validation Test Loaded');
console.log('▶️ The test will run automatically in 1 second');
console.log('🔄 To run again manually: runGmailDropdownValidation()');
