// Live Gmail Modal Testing Script
// Run this in the browser console while the Gmail modal is open

window.testGmailModal = {
    
    // Test dropdown initialization
    testDropdownInit: function() {
        console.log('🔍 Testing dropdown initialization...');
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal) {
            console.error('❌ Gmail modal not found');
            return false;
        }
        
        const sortButton = modal.querySelector('#sort-dropdown');
        const filterButton = modal.querySelector('#filter-dropdown');
        
        console.log('Found elements:', {
            modal: !!modal,
            sortButton: !!sortButton,
            filterButton: !!filterButton,
            modalVisible: modal.classList.contains('show')
        });
        
        return !!(modal && sortButton && filterButton);
    },
    
    // Test Bootstrap dropdown functionality
    testBootstrapDropdowns: function() {
        console.log('🔍 Testing Bootstrap dropdowns...');
        
        if (typeof bootstrap === 'undefined') {
            console.warn('⚠️ Bootstrap not available');
            return false;
        }
        
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal) return false;
        
        const dropdownButtons = modal.querySelectorAll('[data-bs-toggle="dropdown"]');
        console.log(`Found ${dropdownButtons.length} Bootstrap dropdown buttons`);
        
        dropdownButtons.forEach((btn, index) => {
            const instance = bootstrap.Dropdown.getInstance(btn);
            console.log(`Button ${index + 1} (${btn.id}):`, {
                hasInstance: !!instance,
                element: btn
            });
        });
        
        return dropdownButtons.length > 0;
    },
    
    // Test manual dropdown click simulation
    testManualDropdownClick: function(buttonId) {
        console.log(`🔍 Testing manual dropdown click for: ${buttonId}`);
        
        const button = document.getElementById(buttonId);
        if (!button) {
            console.error(`❌ Button ${buttonId} not found`);
            return false;
        }
        
        console.log(`Clicking button: ${buttonId}`);
        button.click();
        
        // Check if dropdown menu opened
        setTimeout(() => {
            const menu = button.nextElementSibling;
            if (menu && menu.classList.contains('dropdown-menu')) {
                const isOpen = menu.classList.contains('show');
                console.log(`Dropdown menu is ${isOpen ? 'OPEN' : 'CLOSED'}`);
                return isOpen;
            }
        }, 100);
        
        return true;
    },
    
    // Test all dropdown functionalities
    testAllDropdowns: function() {
        console.log('🧪 Running comprehensive dropdown tests...');
        
        const results = {
            initialization: this.testDropdownInit(),
            bootstrap: this.testBootstrapDropdowns(),
            sortClick: false,
            filterClick: false
        };
        
        // Test sort dropdown
        setTimeout(() => {
            console.log('Testing sort dropdown click...');
            results.sortClick = this.testManualDropdownClick('sort-dropdown');
        }, 500);
        
        // Test filter dropdown
        setTimeout(() => {
            console.log('Testing filter dropdown click...');
            results.filterClick = this.testManualDropdownClick('filter-dropdown');
        }, 1000);
        
        // Show results summary
        setTimeout(() => {
            console.log('📊 Test Results Summary:', results);
            const passed = Object.values(results).filter(r => r === true).length;
            const total = Object.keys(results).length;
            console.log(`✅ ${passed}/${total} tests passed`);
        }, 1500);
        
        return results;
    },
    
    // Test dropdown option clicks
    testDropdownOptions: function() {
        console.log('🔍 Testing dropdown option clicks...');
        
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal) return false;
        
        // Test sort options
        const sortOptions = modal.querySelectorAll('.sort-option');
        console.log(`Found ${sortOptions.length} sort options`);
        
        if (sortOptions.length > 0) {
            console.log('Clicking first sort option...');
            sortOptions[0].click();
        }
        
        // Test filter options  
        setTimeout(() => {
            const filterOptions = modal.querySelectorAll('.filter-option');
            console.log(`Found ${filterOptions.length} filter options`);
            
            if (filterOptions.length > 0) {
                console.log('Clicking first filter option...');
                filterOptions[0].click();
            }
        }, 1000);
        
        return true;
    },
    
    // Test utility functions
    testUtilityFunctions: function() {
        console.log('🔍 Testing utility functions...');
        
        const functions = [
            'searchEmails',
            'sortEmails', 
            'filterEmails',
            'showAllEmails',
            'showNotification'
        ];
        
        const results = {};
        
        functions.forEach(funcName => {
            results[funcName] = typeof window[funcName] === 'function';
            console.log(`${funcName}: ${results[funcName] ? '✅' : '❌'}`);
        });
        
        // Test notification
        if (results.showNotification) {
            console.log('Testing notification...');
            showNotification('Test notification from console');
        }
        
        return results;
    },
    
    // Test email list interaction
    testEmailListInteraction: function() {
        console.log('🔍 Testing email list interaction...');
        
        const emailList = document.querySelector('#mrp-email-list-section');
        if (!emailList) {
            console.error('❌ Email list section not found');
            return false;
        }
        
        const emailItems = emailList.querySelectorAll('.list-group-item');
        console.log(`Found ${emailItems.length} email items`);
        
        if (emailItems.length > 0) {
            console.log('Testing click on first email...');
            emailItems[0].click();
        }
        
        return emailItems.length > 0;
    },
    
    // Test search functionality
    testSearchFunctionality: function() {
        console.log('🔍 Testing search functionality...');
        
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal) return false;
        
        const searchInput = modal.querySelector('#email-search');
        const searchButton = modal.querySelector('#search-btn');
        
        if (searchInput && searchButton) {
            console.log('Testing search with term "inventory"...');
            searchInput.value = 'inventory';
            searchButton.click();
            
            setTimeout(() => {
                console.log('Clearing search...');
                searchInput.value = '';
                searchButton.click();
            }, 2000);
            
            return true;
        }
        
        console.error('❌ Search elements not found');
        return false;
    },
    
    // Run all tests
    runAllTests: function() {
        console.log('🚀 Starting comprehensive Gmail modal tests...');
        console.log('Make sure the Gmail modal is open before running tests!');
        
        const testSequence = [
            () => this.testDropdownInit(),
            () => this.testBootstrapDropdowns(),
            () => this.testUtilityFunctions(),
            () => this.testEmailListInteraction(),
            () => this.testSearchFunctionality(),
            () => this.testAllDropdowns(),
            () => this.testDropdownOptions()
        ];
        
        let currentTest = 0;
        
        const runNext = () => {
            if (currentTest < testSequence.length) {
                console.log(`\n--- Test ${currentTest + 1}/${testSequence.length} ---`);
                testSequence[currentTest]();
                currentTest++;
                setTimeout(runNext, 2500);
            } else {
                console.log('\n🏁 All tests completed!');
                console.log('Check the console output above for detailed results.');
            }
        };
        
        runNext();
    },
    
    // Quick debug info
    debugInfo: function() {
        console.log('🔧 Gmail Modal Debug Info:');
        
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal) {
            console.error('❌ Gmail modal not found');
            return;
        }
        
        const info = {
            modalVisible: modal.classList.contains('show'),
            bootstrap: typeof bootstrap !== 'undefined',
            dropdownButtons: modal.querySelectorAll('[data-bs-toggle="dropdown"]').length,
            manualButtons: [
                modal.querySelector('#sort-dropdown'),
                modal.querySelector('#filter-dropdown')
            ].filter(Boolean).length,
            emailItems: modal.querySelectorAll('#mrp-email-list-section .list-group-item').length,
            searchElements: {
                input: !!modal.querySelector('#email-search'),
                button: !!modal.querySelector('#search-btn')
            }
        };
        
        console.table(info);
        return info;
    }
};

// Auto-expose for easy console access
console.log('📋 Gmail Modal Test Suite Loaded!');
console.log('Available commands:');
console.log('- testGmailModal.runAllTests() - Run complete test suite');
console.log('- testGmailModal.debugInfo() - Show debug information');
console.log('- testGmailModal.testAllDropdowns() - Test dropdown functionality');
console.log('- testGmailModal.testUtilityFunctions() - Test utility functions');

// Quick start function
window.testDropdowns = () => testGmailModal.testAllDropdowns();
window.debugGmail = () => testGmailModal.debugInfo();
