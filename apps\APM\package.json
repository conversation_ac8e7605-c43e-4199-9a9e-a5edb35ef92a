{"name": "apm", "version": "1.0.0", "description": "Advanced Project Management System", "main": "electron.js", "scripts": {"start": "node index.js production", "dev": "nodemon index.js development", "test": "echo \"Error: no test specified\" && exit 1", "electron-dev": "concurrently \"cross-env BROWSER=none npm run dev\" \"wait-on http://localhost:3006 && electron .\"", "electron-pack": "npm run build && electron-builder build --win --publish never", "electron-dist": "electron-builder --win --publish never", "build": "webpack --mode production", "build:dev": "webpack --mode development"}, "keywords": ["asset", "performance", "management"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "css-loader": "^6.10.0", "electron": "^30.0.0", "electron-builder": "^24.13.3", "html-webpack-plugin": "^5.6.0", "nodemon": "^3.0.1", "style-loader": "^3.3.4", "wait-on": "^7.2.0", "webpack": "^5.99.7", "webpack-cli": "^6.0.1"}, "build": {"appId": "com.isa.apm", "productName": "ISA Advanced Project Management", "files": ["build/**/*", "electron.js", "preload.js", "manifest.json", "package.json"], "directories": {"buildResources": "public"}, "win": {"target": ["nsis"], "icon": "public/icons/icon-512x512.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}