@echo off
echo Creating desktop shortcuts for Ice Systems Australasia applications...
echo.

REM Create VBScript to create shortcuts
echo Set oWS = WScript.CreateObject(\ WScript.Shell\) > \%TEMP%\\create_shortcuts.vbs\
echo sDesktop = oWS.SpecialFolders(\Desktop\) >> \%TEMP%\\create_shortcuts.vbs\

REM Main launcher shortcut
echo Set oLink = oWS.CreateShortcut(sDesktop ^& \\\Ice Systems Suite.lnk\) >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.TargetPath = \D:\\ice-systems-launcher.bat\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.IconLocation = \C:\\Windows\\System32\\SHELL32.dll 22\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.Description = \Launch Ice Systems Australasia Application Suite\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.WorkingDirectory = \D:\\\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.Save >> \%TEMP%\\create_shortcuts.vbs\

REM BMS shortcut
echo Set oLink = oWS.CreateShortcut(sDesktop ^& \\\Ice Systems - BMS.lnk\) >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.TargetPath = \D:\\launch-BMS.bat\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.IconLocation = \C:\\Windows\\System32\\SHELL32.dll 18\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.Description = \Launch Building Management System\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.WorkingDirectory = \D:\\\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.Save >> \%TEMP%\\create_shortcuts.vbs\

REM Hub shortcut
echo Set oLink = oWS.CreateShortcut(sDesktop ^& \\\Ice Systems - Hub.lnk\) >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.TargetPath = \D:\\launch-Hub.bat\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.IconLocation = \C:\\Windows\\System32\\SHELL32.dll 15\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.Description = \Launch Integration Hub\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.WorkingDirectory = \D:\\\ >> \%TEMP%\\create_shortcuts.vbs\
echo oLink.Save >> \%TEMP%\\create_shortcuts.vbs\

REM Run the VBScript to create shortcuts
cscript //nologo \%TEMP%\\create_shortcuts.vbs\

echo Desktop shortcuts created successfully!
echo.
pause
