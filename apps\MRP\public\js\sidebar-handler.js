/**
 * Sidebar Handler for MRP Application
 * Handles sidebar navigation and integration links
 */

document.addEventListener('DOMContentLoaded', function() {
    // Fix for attachments sidebar link
    const attachmentsLink = document.querySelector('a[href="#attachments"]');
    if (attachmentsLink) {
        attachmentsLink.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Attachments link clicked');
            
            // Clean up any existing modals
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });
            
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            // Get the modal element
            const attachmentsModal = document.getElementById('attachmentsModal');
            if (attachmentsModal) {
                console.log('Found attachments modal, showing it');
                // Create a new modal instance and show it
                try {
                    const modal = new bootstrap.Modal(attachmentsModal);
                    modal.show();
                } catch (err) {
                    console.error('Error showing attachments modal:', err);
                }
            } else {
                console.error('Attachments modal not found');
            }
        });
    }

    // Handle all sidebar integration links
    document.querySelectorAll('.nav-link[data-bs-toggle="modal"]').forEach(link => {
        link.addEventListener('click', function(e) {
            const target = this.getAttribute('data-bs-target');
            console.log(`Sidebar link clicked for modal: ${target}`);
            
            if (!target) return;
            
            e.preventDefault();
            
            // Clean up any existing modals
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });
            
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            // Get the modal element
            const modalEl = document.querySelector(target);
            if (modalEl) {
                console.log(`Found modal ${target}, showing it`);
                // Create a new modal instance and show it
                try {
                    const modal = new bootstrap.Modal(modalEl);
                    modal.show();
                } catch (err) {
                    console.error(`Error showing modal ${target}:`, err);
                }
            } else {
                console.error(`Modal ${target} not found`);
            }
        });
    });
});
