// Live Gmail Modal Dropdown Test Script
// This script should be run in the browser console when the Gmail modal is open

class GmailDropdownTester {
    constructor() {
        this.testResults = [];
        this.modalSelector = '#mrp-gmail-modal';
        this.sortButtonSelector = '#sortButton';
        this.sortDropdownSelector = '#sortDropdown';
        this.filterButtonSelector = '#filterButton';
        this.filterDropdownSelector = '#filterDropdown';
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
        console.log(logMessage);
        this.testResults.push({ timestamp, type, message });
    }

    async waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`Element ${selector} not found within ${timeout}ms`));
            }, timeout);
        });
    }

    async openGmailModal() {
        this.log('Attempting to open Gmail modal...');
        
        // Look for Gmail button or link
        const gmailButton = document.querySelector('[onclick*="openGmail"]') || 
                           document.querySelector('[href*="gmail"]') ||
                           document.querySelector('.gmail-btn') ||
                           document.querySelector('#gmail-btn');
        
        if (gmailButton) {
            this.log('Found Gmail button, clicking it...');
            gmailButton.click();
            
            try {
                const modal = await this.waitForElement(this.modalSelector);
                this.log('Gmail modal opened successfully');
                return modal;
            } catch (error) {
                this.log(`Failed to open Gmail modal: ${error.message}`, 'error');
                return null;
            }
        } else {
            this.log('Gmail button not found. Available buttons:', 'warning');
            const allButtons = document.querySelectorAll('button, [onclick], [href]');
            allButtons.forEach((btn, index) => {
                if (btn.textContent.toLowerCase().includes('gmail') || 
                    btn.onclick?.toString().includes('gmail') ||
                    btn.href?.includes('gmail')) {
                    this.log(`  ${index}: ${btn.textContent || btn.outerHTML.substring(0, 100)}...`);
                }
            });
            return null;
        }
    }

    testDropdownElement(buttonSelector, dropdownSelector, name) {
        this.log(`Testing ${name} dropdown...`);
        
        const button = document.querySelector(buttonSelector);
        const dropdown = document.querySelector(dropdownSelector);
        
        if (!button) {
            this.log(`${name} button not found (${buttonSelector})`, 'error');
            return false;
        }
        
        if (!dropdown) {
            this.log(`${name} dropdown not found (${dropdownSelector})`, 'error');
            return false;
        }
        
        this.log(`${name} elements found - Button: ${button.textContent}, Dropdown: ${dropdown.children.length} items`);
        
        // Test Bootstrap dropdown functionality
        const bsDropdown = bootstrap?.Dropdown?.getInstance(button);
        if (bsDropdown) {
            this.log(`${name} has Bootstrap dropdown instance`);
        } else {
            this.log(`${name} does not have Bootstrap dropdown instance`, 'warning');
        }
        
        // Test click functionality
        const originalDisplay = dropdown.style.display;
        this.log(`${name} dropdown original display: ${originalDisplay || 'default'}`);
        
        // Simulate click
        button.click();
        
        setTimeout(() => {
            const newDisplay = dropdown.style.display;
            const isVisible = dropdown.classList.contains('show') || newDisplay === 'block';
            this.log(`${name} dropdown after click - Display: ${newDisplay || 'default'}, Visible: ${isVisible}`);
            
            if (isVisible) {
                this.log(`✅ ${name} dropdown opened successfully`);
                
                // Test dropdown items
                const items = dropdown.querySelectorAll('a, button');
                this.log(`${name} dropdown has ${items.length} items`);
                
                items.forEach((item, index) => {
                    this.log(`  Item ${index}: ${item.textContent}`);
                });
            } else {
                this.log(`❌ ${name} dropdown failed to open`, 'error');
            }
            
            // Close dropdown if it opened
            if (isVisible) {
                button.click();
            }
        }, 100);
        
        return true;
    }

    async runFullTest() {
        this.log('Starting Gmail Dropdown Live Test');
        this.log('================================');
        
        // First check if modal is already open
        let modal = document.querySelector(this.modalSelector);
        
        if (!modal || !modal.classList.contains('show')) {
            this.log('Gmail modal not open, attempting to open it...');
            modal = await this.openGmailModal();
            
            if (!modal) {
                this.log('Could not open Gmail modal. Please open it manually and run: tester.testDropdowns()', 'error');
                return;
            }
            
            // Wait a bit for modal to fully load
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        this.testDropdowns();
    }

    testDropdowns() {
        this.log('Testing dropdowns in open Gmail modal...');
        
        // Test Sort dropdown
        this.testDropdownElement(this.sortButtonSelector, this.sortDropdownSelector, 'Sort');
        
        // Wait a bit between tests
        setTimeout(() => {
            // Test Filter dropdown
            this.testDropdownElement(this.filterButtonSelector, this.filterDropdownSelector, 'Filter');
            
            setTimeout(() => {
                this.showResults();
            }, 500);
        }, 1000);
    }

    testManualDropdownToggle() {
        this.log('Testing manual dropdown toggle functions...');
        
        if (window.toggleSortDropdown) {
            this.log('toggleSortDropdown function exists');
            try {
                window.toggleSortDropdown();
                this.log('✅ toggleSortDropdown executed successfully');
            } catch (error) {
                this.log(`❌ toggleSortDropdown error: ${error.message}`, 'error');
            }
        } else {
            this.log('toggleSortDropdown function not found', 'warning');
        }
        
        if (window.toggleFilterDropdown) {
            this.log('toggleFilterDropdown function exists');
            try {
                window.toggleFilterDropdown();
                this.log('✅ toggleFilterDropdown executed successfully');
            } catch (error) {
                this.log(`❌ toggleFilterDropdown error: ${error.message}`, 'error');
            }
        } else {
            this.log('toggleFilterDropdown function not found', 'warning');
        }
    }

    inspectCurrentState() {
        this.log('Inspecting current page state...');
        
        // Check if scripts are loaded
        const scripts = Array.from(document.querySelectorAll('script')).map(s => s.src);
        this.log('Loaded scripts:');
        scripts.forEach(src => {
            if (src.includes('mrp-gmail')) {
                this.log(`  ✅ ${src}`);
            }
        });
        
        // Check if modal exists
        const modal = document.querySelector(this.modalSelector);
        if (modal) {
            this.log(`Gmail modal exists, visible: ${modal.classList.contains('show')}`);
        } else {
            this.log('Gmail modal not found in DOM', 'warning');
        }
        
        // Check dropdown elements
        const sortBtn = document.querySelector(this.sortButtonSelector);
        const filterBtn = document.querySelector(this.filterButtonSelector);
        const sortDropdown = document.querySelector(this.sortDropdownSelector);
        const filterDropdown = document.querySelector(this.filterDropdownSelector);
        
        this.log(`Sort button: ${sortBtn ? '✅ Found' : '❌ Not found'}`);
        this.log(`Filter button: ${filterBtn ? '✅ Found' : '❌ Not found'}`);
        this.log(`Sort dropdown: ${sortDropdown ? '✅ Found' : '❌ Not found'}`);
        this.log(`Filter dropdown: ${filterDropdown ? '✅ Found' : '❌ Not found'}`);
        
        // Check for Bootstrap
        this.log(`Bootstrap available: ${typeof bootstrap !== 'undefined' ? '✅ Yes' : '❌ No'}`);
        this.log(`jQuery available: ${typeof $ !== 'undefined' ? '✅ Yes' : '❌ No'}`);
    }

    showResults() {
        this.log('================================');
        this.log('Test Results Summary:');
        
        const errors = this.testResults.filter(r => r.type === 'error');
        const warnings = this.testResults.filter(r => r.type === 'warning');
        
        this.log(`Total tests: ${this.testResults.length}`);
        this.log(`Errors: ${errors.length}`);
        this.log(`Warnings: ${warnings.length}`);
        
        if (errors.length > 0) {
            this.log('ERRORS:', 'error');
            errors.forEach(error => this.log(`  ${error.message}`, 'error'));
        }
        
        if (warnings.length > 0) {
            this.log('WARNINGS:', 'warning');
            warnings.forEach(warning => this.log(`  ${warning.message}`, 'warning'));
        }
        
        this.log('================================');
    }
}

// Create global tester instance
window.gmailDropdownTester = new GmailDropdownTester();

// Auto-run test
console.log('Gmail Dropdown Tester loaded. Starting automatic test...');
window.gmailDropdownTester.runFullTest();

// Also provide manual testing methods
console.log('Manual testing methods available:');
console.log('- gmailDropdownTester.inspectCurrentState() - Check current state');
console.log('- gmailDropdownTester.testDropdowns() - Test dropdowns (modal must be open)');
console.log('- gmailDropdownTester.testManualDropdownToggle() - Test manual toggle functions');
console.log('- gmailDropdownTester.runFullTest() - Run complete test');
