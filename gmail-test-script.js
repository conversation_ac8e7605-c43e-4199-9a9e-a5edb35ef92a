// Gmail Modal Fix Verification Script for BMS
// Run this in the browser console on the BMS page to test the fix

console.log('=== BMS Gmail Modal Fix Test ===');

// Test 1: Check if enhanced Gmail script is loaded
console.log('1. Checking if enhanced Gmail script functions exist...');
if (typeof createEnhancedGmailModal === 'function') {
    console.log('✅ createEnhancedGmailModal function found');
} else {
    console.log('❌ createEnhancedGmailModal function NOT found');
}

if (typeof openEnhancedGmailModal === 'function') {
    console.log('✅ openEnhancedGmailModal function found');
} else {
    console.log('❌ openEnhancedGmailModal function NOT found');
}

// Test 2: Check if Gmail modal exists with correct ID
console.log('\n2. Checking Gmail modal...');
const gmailModal = document.getElementById('gmailModal');
if (gmailModal) {
    console.log('✅ Gmail modal found with ID "gmailModal"');
    
    // Check if it's the enhanced version
    const hasLabelsSection = gmailModal.querySelector('.list-group');
    const hasTabsSection = gmailModal.querySelector('#enhancedGmailTab');
    
    if (hasLabelsSection && hasTabsSection) {
        console.log('✅ Enhanced Gmail modal detected (has labels and tabs)');
    } else {
        console.log('❌ Basic Gmail modal detected (missing enhanced features)');
        console.log('   Has labels:', !!hasLabelsSection);
        console.log('   Has tabs:', !!hasTabsSection);
    }
} else {
    console.log('❌ Gmail modal NOT found');
}

// Test 3: Check "View All Emails" link
console.log('\n3. Checking "View All Emails" link...');
const viewAllEmailsLink = document.querySelector('a[data-bs-target="#gmailModal"]');
if (viewAllEmailsLink) {
    console.log('✅ "View All Emails" link found');
    console.log('   Link text:', viewAllEmailsLink.textContent.trim());
    console.log('   Target modal:', viewAllEmailsLink.getAttribute('data-bs-target'));
} else {
    console.log('❌ "View All Emails" link NOT found');
}

// Test 4: Check for conflicts (old enhanced modal)
console.log('\n4. Checking for conflicts...');
const oldEnhancedModal = document.getElementById('enhancedGmailModal');
if (oldEnhancedModal) {
    console.log('⚠️  Old enhanced modal still exists (potential conflict)');
} else {
    console.log('✅ No old enhanced modal found');
}

// Test 5: Check which Gmail scripts are loaded
console.log('\n5. Checking loaded scripts...');
const scripts = Array.from(document.getElementsByTagName('script'))
    .filter(script => script.src && script.src.includes('gmail'))
    .map(script => script.src.split('/').pop());

if (scripts.length > 0) {
    console.log('✅ Gmail scripts found:');
    scripts.forEach(script => console.log('   -', script));
} else {
    console.log('❌ No Gmail scripts found');
}

// Test 6: Manual test helper
console.log('\n6. Manual Test Helper');
console.log('To test manually:');
console.log('1. Run: openEnhancedGmailModal() - to open the enhanced modal directly');
console.log('2. Click the "View All Emails" link on the page');
console.log('3. Check if the modal has a left sidebar with labels');

// Helper function to test modal opening
window.testGmailModal = function() {
    console.log('Testing Gmail modal...');
    if (typeof openEnhancedGmailModal === 'function') {
        openEnhancedGmailModal();
        console.log('✅ Enhanced Gmail modal opened');
    } else {
        console.log('❌ Cannot open enhanced Gmail modal - function not found');
    }
};

console.log('\n=== Test Complete ===');
console.log('Run testGmailModal() to open the enhanced modal manually');
