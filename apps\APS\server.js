const http = require('http');

let schedules = [
  {
    id: 1,
    order: 'PO-1001',
    resource: 'Line 1',
    startTime: '2024-05-15 08:00',
    endTime: '2024-05-15 16:00',
    status: 'Scheduled',
    priority: 'High',
    progress: 0,
  },
  {
    id: 2,
    order: 'PO-1002',
    resource: 'Line 2',
    startTime: '2024-05-15 09:00',
    endTime: '2024-05-15 17:00',
    status: 'In Progress',
    priority: 'Medium',
    progress: 45,
  },
  {
    id: 3,
    order: 'PO-1003',
    resource: 'Line 3',
    startTime: '2024-05-16 07:00',
    endTime: '2024-05-16 15:00',
    status: 'Planned',
    priority: 'Low',
    progress: 0,
  },
];

let resourceUtilization = [
  {
    id: 1,
    resource: 'Line 1',
    currentLoad: 75,
    capacity: 100,
    scheduledHours: 40,
    availableHours: 10,
  },
  {
    id: 2,
    resource: 'Line 2',
    currentLoad: 90,
    capacity: 100,
    scheduledHours: 45,
    availableHours: 5,
  },
  {
    id: 3,
    resource: 'Line 3',
    currentLoad: 60,
    capacity: 100,
    scheduledHours: 30,
    availableHours: 20,
  },
];

const server = http.createServer((req, res) => {
  if (req.url === '/') {
    const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>APS Dashboard</title>
                <style>
                    body { font-family: Arial; margin: 20px; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .dashboard-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }
                    .card {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        padding: 20px;
                        margin-bottom: 20px;
                    }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
                    th { background: #f4f4f4; }
                    .actions { margin: 20px 0; }
                    button { padding: 10px; margin: 5px; cursor: pointer; }
                    .status { 
                        padding: 5px 10px; 
                        border-radius: 3px; 
                        color: white;
                        font-weight: bold;
                    }
                    .scheduled { background: #17a2b8; }
                    .in-progress { background: #ffc107; }
                    .planned { background: #6c757d; }
                    .metrics {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 20px;
                        margin-bottom: 20px;
                    }
                    .metric-card {
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 8px;
                        text-align: center;
                    }
                    .metric-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: #007bff;
                    }
                    .priority {
                        padding: 3px 8px;
                        border-radius: 3px;
                        font-size: 0.9em;
                    }
                    .high { background: #dc3545; color: white; }
                    .medium { background: #ffc107; color: black; }
                    .low { background: #28a745; color: white; }
                    .progress-bar {
                        width: 100%;
                        background: #f0f0f0;
                        border-radius: 5px;
                        margin: 5px 0;
                    }
                    .progress {
                        height: 20px;
                        background: #007bff;
                        border-radius: 5px;
                        text-align: center;
                        line-height: 20px;
                        color: white;
                    }
                    .timeline {
                        font-family: monospace;
                        color: #666;
                    }
                    .utilization {
                        font-weight: bold;
                    }
                    .high-utilization { color: #dc3545; }
                    .medium-utilization { color: #ffc107; }
                    .low-utilization { color: #28a745; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Advanced Planning & Scheduling</h1>
                    <div class="actions">
                        <button onclick="window.location.href='http://localhost:3000'">Back to Hub</button>
                    </div>
                    
                    <div class="metrics">
                        <div class="metric-card">
                            <div class="metric-value">3</div>
                            <div>Active Schedules</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">75%</div>
                            <div>Average Utilization</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">35</div>
                            <div>Available Hours</div>
                        </div>
                    </div>

                    <div class="dashboard-grid">
                        <div class="card">
                            <h2>Production Schedule</h2>
                            <table>
                                <tr>
                                    <th>Order</th>
                                    <th>Resource</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Progress</th>
                                </tr>
                                ${schedules
                                  .map(
                                    (schedule) => `
                                    <tr>
                                        <td>${schedule.order}</td>
                                        <td>${schedule.resource}</td>
                                        <td class="timeline">
                                            ${schedule.startTime} - ${schedule.endTime}
                                        </td>
                                        <td>
                                            <span class="status ${schedule.status.toLowerCase().replace(' ', '-')}">
                                                ${schedule.status}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="priority ${schedule.priority.toLowerCase()}">
                                                ${schedule.priority}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="progress-bar">
                                                <div class="progress" style="width: ${schedule.progress}%">
                                                    ${schedule.progress}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>

                        <div class="card">
                            <h2>Resource Utilization</h2>
                            <table>
                                <tr>
                                    <th>Resource</th>
                                    <th>Current Load</th>
                                    <th>Capacity</th>
                                    <th>Scheduled Hours</th>
                                    <th>Available Hours</th>
                                </tr>
                                ${resourceUtilization
                                  .map(
                                    (resource) => `
                                    <tr>
                                        <td>${resource.resource}</td>
                                        <td>
                                            <span class="utilization ${resource.currentLoad > 80 ? 'high-utilization' : resource.currentLoad > 60 ? 'medium-utilization' : 'low-utilization'}">
                                                ${resource.currentLoad}%
                                            </span>
                                        </td>
                                        <td>${resource.capacity}%</td>
                                        <td>${resource.scheduledHours}h</td>
                                        <td>${resource.availableHours}h</td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `;
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  } else {
    res.writeHead(404);
    res.end('Not found');
  }
});

server.listen(3006, () => {
  console.log('APS running on http://localhost:3006');
});
