# ISA Suite Updated Launcher Guide

This guide explains how to use the updated launcher scripts for the ISA Suite applications.

## Available Scripts

### Main Launcher
- **`single-window-launcher.bat`** - The main launcher script that starts all applications in the background.
  - Automatically installs dependencies for all apps
  - Runs all applications in the background (no separate terminal windows)
  - Logs all output to individual log files in the `logs` directory
  - Opens the Integration Hub in your browser
  - Provides an option to stop all apps at once

### Utility Scripts
- **`check-status.bat`** - Checks the status of all applications and displays whether they are running or not.
- **`open-apps.bat`** - Opens all applications in your default browser.
- **`stop-apps.bat`** - Stops all running applications.

## How to Use

### Starting Applications
1. Run `single-window-launcher.bat`
2. Select the desired mode (Production, Sandbox/Training, or Demo)
3. All applications will start in the background without opening individual terminal windows
4. The Integration Hub will open in your browser automatically

### Checking Application Status
1. Run `check-status.bat`
2. The script will display the status of all applications (online or offline)

### Opening Applications in Browser
1. Run `open-apps.bat`
2. All applications will open in your default browser

### Stopping Applications
1. Press any key in the launcher window to stop all applications
   OR
2. Run `stop-apps.bat` to stop all applications

## Accessing Applications

Once the applications are running, you can access them through the Integration Hub or directly using the following URLs:

- Integration Hub: http://localhost:8000
- Business Management System: http://localhost:3001
- Materials Requirements Planning: http://localhost:3002
- Customer Relationship Management: http://localhost:3003
- Warehouse Management System: http://localhost:3004
- Advanced Planning and Scheduling: http://localhost:3005
- Asset Performance Management: http://localhost:3006
- Project Management System: http://localhost:3007
- Supply Chain Management: http://localhost:3008
- Task Management System: http://localhost:3009

## Troubleshooting

If you encounter issues:

1. Check the log files in the `logs` directory
2. Run `check-apps-status.bat` to see which applications are running
3. Make sure no other applications are using the same ports (8000, 3001-3009)
4. Try stopping all applications using `stop-apps.bat` and then start them again

## Notes

- The Integration Hub links will open applications in separate browser tabs/windows
- All application output is logged to individual log files in the `logs` directory
- Running multiple launchers simultaneously may cause port conflicts
