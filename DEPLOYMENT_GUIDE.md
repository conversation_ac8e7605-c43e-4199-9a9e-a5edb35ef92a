# ISA Suite Deployment Guide

This guide provides instructions for deploying the ISA Suite applications.

## System Requirements

- Windows 10 or later
- Node.js 18+ (included in the portable installation)
- 4GB RAM minimum (8GB recommended)
- 1GB free disk space

## Installation Options

### Option 1: Standard Installation

1. Clone or download the ISA Suite repository to your local machine
2. Navigate to the ISA Suite directory
3. Run the setup script:
   ```
   .\setup-and-run.ps1
   ```
4. Follow the on-screen instructions to complete the installation

### Option 2: Portable Installation

1. Extract the ISA Suite portable package to a directory of your choice (e.g., C:\ISASUITE)
2. Run the single window launcher:
   ```
   .\single-window-launcher.bat
   ```
3. Select the desired mode (Production, Sandbox/Training, or Demo)

### Option 3: USB Installation

1. Insert the ISA Suite USB drive
2. Run the `run-from-usb.bat` file from the USB drive
3. Follow the on-screen instructions

## Starting the Applications

### Single Window Mode (Recommended)

Run the single window launcher to start all applications in a single console window:

```
.\single-window-launcher.bat
```

This will start the Integration Hub and all other applications in the background, and open the Integration Hub in your default browser.

### Individual Application Mode

If you need to run applications individually, use the service manager:

```
.\service-manager.bat
```

This will allow you to start, stop, and check the status of individual applications.

## Accessing the Applications

Once the applications are running, you can access them through the Integration Hub or directly using the following URLs:

- Integration Hub: http://localhost:8000
- Business Management System: http://localhost:3001
- Materials Requirements Planning: http://localhost:3002
- Customer Relationship Management: http://localhost:3003
- Warehouse Management System: http://localhost:3004
- Advanced Planning and Scheduling: http://localhost:3005
- Asset Performance Management: http://localhost:3006
- Project Management System: http://localhost:3007
- Supply Chain Management: http://localhost:3008
- Task Management System: http://localhost:3009

## Troubleshooting

### Application Not Starting

If an application fails to start:

1. Check the logs in the `logs` directory
2. Ensure the port is not already in use by another application
3. Restart the application using the service manager

### Database Connection Issues

If you encounter database connection issues:

1. Ensure MongoDB is running
2. Check the database connection settings in the `.env` file
3. Restart the affected application

### Integration Hub Not Connecting to Applications

If the Integration Hub cannot connect to other applications:

1. Ensure all applications are running
2. Check the application URLs in the Integration Hub's `.env` file
3. Restart the Integration Hub

## Support

For additional support, <NAME_EMAIL> or visit our support portal at https://support.isasuite.com.
