// predeploy-check.js
// Automated pre-deployment checks for ISASUITE

const { execSync } = require('child_process');
const fs = require('fs');

function check(command, description) {
  try {
    console.log(`\n[CHECK] ${description}`);
    execSync(command, { stdio: 'inherit' });
    console.log(`[PASS] ${description}`);
  } catch (e) {
    console.error(`[FAIL] ${description}`);
    process.exit(1);
  }
}

function checkFile(path, description) {
  if (fs.existsSync(path)) {
    console.log(`[PASS] ${description}`);
  } else {
    console.error(`[FAIL] ${description}`);
    process.exit(1);
  }
}

// 1. Lint all JS/TS files
check('npx eslint .', 'Linting (ESLint)');

// 2. Run all tests
check('npx jest', 'Unit/Integration Tests (Jest)');

// 3. Check for required environment variables
const requiredEnv = ['NODE_ENV', 'PORT'];
let missingEnv = requiredEnv.filter((v) => !process.env[v]);
if (missingEnv.length) {
  console.error(`[FAIL] Missing env vars: ${missingEnv.join(', ')}`);
  process.exit(1);
} else {
  console.log(`[PASS] All required environment variables set`);
}

// 4. Check for integration config files
checkFile('./SharedFeatures/integrations/', 'Integrations folder exists');
checkFile('./SharedFeatures/integrations/google.js', 'Google integration config');
checkFile('./SharedFeatures/integrations/payment.js', 'Payment integration config');
checkFile('./SharedFeatures/integrations/outlook.js', 'Outlook integration config');

// 5. Check for feature flag config
checkFile('./SharedFeatures/featureFlags.js', 'Feature flag config');

console.log('\nAll pre-deployment checks passed!');
