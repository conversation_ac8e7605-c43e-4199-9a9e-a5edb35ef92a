/**
 * Attachments Handler for MRP Gmail Integration
 * This file provides the necessary functionality for handling email attachments
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Attachments handler loaded');
    
    // Setup global attachment handling
    setupGlobalAttachmentHandlers();
});

/**
 * Set up attachment handlers for the entire application
 */
function setupGlobalAttachmentHandlers() {
    // We'll use event delegation for dynamically added elements
    document.addEventListener('click', function(e) {
        // Handle view button clicks
        if (e.target.closest('.view-btn')) {
            e.preventDefault();
            const button = e.target.closest('.view-btn');
            
            // Get the file name from the parent container
            const fileElement = button.closest('.d-flex');
            if (!fileElement) return;
            
            const fileName = fileElement.querySelector('strong')?.textContent || 'document';
            console.log(`View button clicked for ${fileName} (global handler)`);
            
            // Show file in viewer
            if (typeof showFileViewer === 'function') {
                showFileViewer(fileName);
            } else {
                console.log(`Viewing ${fileName}...`);
                showAttachmentToast(`Opening ${fileName} in viewer...`, 'info');
            }
        }
        
        // Handle download button clicks
        if (e.target.closest('.download-btn')) {
            e.preventDefault();
            const button = e.target.closest('.download-btn');
            
            // Get the file name from the parent container
            const fileElement = button.closest('.d-flex');
            if (!fileElement) return;
            
            const fileName = fileElement.querySelector('strong')?.textContent || 'document';
            console.log(`Download button clicked for ${fileName} (global handler)`);
            
            // Simulate file download
            if (typeof simulateFileDownload === 'function') {
                simulateFileDownload(fileName);
            } else {
                console.log(`Downloading ${fileName}...`);
                showAttachmentToast(`Downloading ${fileName}...`, 'info');
                
                // Simulate download completion after 2 seconds
                setTimeout(() => {
                    showAttachmentToast(`${fileName} downloaded successfully`, 'success');
                }, 2000);
            }
        }
    });
    
    console.log('Global attachment handlers set up');
}

/**
 * Show a toast notification for attachment actions
 */
function showAttachmentToast(message, type = 'info') {
    // Use the global showToast function if available
    if (typeof showToast === 'function') {
        showToast(message, type);
        return;
    }
    
    // Create our own toast if the global function is not available
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '1050';
        document.body.appendChild(toastContainer);
    }
    
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    // Initialize and show the toast if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 3000
        });
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            toast.remove();
        });
    } else {
        // Fallback if Bootstrap is not available
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}