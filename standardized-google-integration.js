// Standardized Google Integration Functions
// This file contains the standardized implementation of Google integration functions
// to be used across all applications in the ISA Suite

// Add CSS to ensure modals always appear above sidebars
(function() {
  const style = document.createElement('style');
  style.textContent = `
    /* Ensure modals and backdrops always appear above sidebars */
    .modal {
      z-index: 1200 !important;
    }
    .modal-backdrop {
      z-index: 1199 !important;
    }
    #fileViewerModal {
      z-index: 1200 !important;
    }
  `;
  document.head.appendChild(style);
})();

// Function to open specific Google items
function openGoogleItem(app, type, itemId) {
  // For general app buttons (without specific item), show the modal
  if (app === 'drive' && !type && !itemId) {
    const driveModal = new bootstrap.Modal(document.getElementById('driveModal'));
    driveModal.show();
    return;
  } else if (app === 'docs' && !type && !itemId) {
    const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
    docsModal.show();
    return;
  } else if (app === 'sheets' && !type && !itemId) {
    const sheetsModal = new bootstrap.Modal(document.getElementById('sheetsModal'));
    sheetsModal.show();
    return;
  } else if (app === 'attachments' && !type && !itemId) {
    const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
    attachmentsModal.show();
    return;
  }

  // Ensure modal backdrop has proper z-index
  document.addEventListener('shown.bs.modal', function() {
    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
      backdrop.style.zIndex = '1199'; // Just below the modal z-index
    }
  }, { once: true });

  // For specific items, directly open the file in a viewer
  try {
    // Create a simulated file viewer
    const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

    // Set the file information
    const fileTitle = document.getElementById('fileViewerTitle');
    const fileContent = document.getElementById('fileViewerContent');

    // Format the item ID to make it more readable
    const readableId = itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

    // Set the title based on the app and type
    fileTitle.innerHTML = `<i class="bi ${getFileIcon(app, type)}"></i> ${readableId}`;

    // Set the content based on the file type
    fileContent.innerHTML = getFileContent(app, type, itemId);

    // Show the modal
    const modal = new bootstrap.Modal(fileViewerModal);
    modal.show();
  } catch (error) {
    console.error('Error opening file:', error);
    alert('Could not open the file. Please try again later.');
  }
}

// Helper function to create a file viewer modal if it doesn't exist
function createFileViewerModal() {
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = 'fileViewerModal';
  modal.tabIndex = '-1';
  modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
  modal.setAttribute('aria-hidden', 'true');
  // Add a higher z-index to ensure the modal appears above the sidebar
  modal.style.zIndex = '1200';

  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
          <h5 class="modal-title" id="fileViewerTitle"></h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="fileViewerContent" class="p-3" style="min-height: 400px;"></div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">Download</button>
          <button type="button" class="btn btn-success" onclick="shareCurrentFile()">Share</button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);
  return modal;
}

// Helper function to get the appropriate icon for the file type
function getFileIcon(app, type) {
  if (app === 'drive') {
    if (type === 'folder') return 'bi-folder-fill text-primary';
    if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
    if (type === 'image') return 'bi-file-earmark-image text-info';
    if (type === 'document') return 'bi-file-earmark-text text-primary';
    return 'bi-file-earmark text-secondary';
  } else if (app === 'docs') {
    return 'bi-file-earmark-text text-primary';
  } else if (app === 'sheets') {
    return 'bi-file-earmark-spreadsheet text-success';
  } else if (app === 'calendar') {
    return 'bi-calendar-event text-primary';
  }
  return 'bi-file-earmark text-secondary';
}

// Helper function to generate content for the file viewer
function getFileContent(app, type, itemId) {
  // Generate simulated content based on file type
  if (app === 'drive') {
    if (type === 'folder') {
      return `<div class="alert alert-info">
                <i class="bi bi-info-circle"></i> This is a folder view. In a real application, this would show the contents of the folder.
              </div>
              <div class="list-group">
                <a href="#" class="list-group-item list-group-item-action">
                  <i class="bi bi-file-earmark-text me-2"></i> Document 1.docx
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                  <i class="bi bi-file-earmark-spreadsheet me-2"></i> Spreadsheet 1.xlsx
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                  <i class="bi bi-file-earmark-pdf me-2"></i> Report.pdf
                </a>
              </div>`;
    } else if (type === 'pdf') {
      return `<div class="text-center">
                <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                <h4 class="mt-3">${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                <div class="alert alert-info mt-3">
                  <i class="bi bi-info-circle"></i> This is a PDF viewer. In a real application, the PDF would be displayed here.
                </div>
                <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                  <h5>Document Preview</h5>
                  <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                  <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                </div>
              </div>`;
    } else if (type === 'document') {
      return `<div class="border p-3" style="background-color: #f8f9fa;">
                <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                <hr>
                <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
                <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
              </div>`;
    }
  } else if (app === 'docs') {
    return `<div class="border p-3" style="background-color: #f8f9fa;">
              <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
              <hr>
              <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
              <p>The document contains information about ${itemId.replace(/-/g, ' ')}.</p>
              <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
            </div>`;
  } else if (app === 'sheets') {
    return `<div class="border p-3" style="background-color: #f8f9fa;">
              <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
              <hr>
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <th>Item</th>
                      <th>Quantity</th>
                      <th>Price</th>
                      <th>Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Item 1</td>
                      <td>10</td>
                      <td>$10.00</td>
                      <td>$100.00</td>
                    </tr>
                    <tr>
                      <td>Item 2</td>
                      <td>5</td>
                      <td>$20.00</td>
                      <td>$100.00</td>
                    </tr>
                    <tr>
                      <td>Item 3</td>
                      <td>2</td>
                      <td>$30.00</td>
                      <td>$60.00</td>
                    </tr>
                    <tr>
                      <td colspan="3" class="text-end"><strong>Total</strong></td>
                      <td><strong>$260.00</strong></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>`;
  } else if (app === 'calendar') {
    return `<div class="border p-3" style="background-color: #f8f9fa;">
              <h4>${itemId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
              <hr>
              <div class="row">
                <div class="col-md-6">
                  <p><strong>Date:</strong> May 15, 2025</p>
                  <p><strong>Time:</strong> 10:00 AM - 11:30 AM</p>
                  <p><strong>Location:</strong> Conference Room A</p>
                </div>
                <div class="col-md-6">
                  <p><strong>Organizer:</strong> John Doe</p>
                  <p><strong>Attendees:</strong> 5</p>
                  <p><strong>Status:</strong> Confirmed</p>
                </div>
              </div>
              <div class="mt-3">
                <h5>Description</h5>
                <p>This is a calendar event viewer. In a real application, the event details would be displayed here.</p>
              </div>
            </div>`;
  }

  return `<div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
          </div>`;
}

// Function to download the current file
function downloadCurrentFile() {
  const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
  alert(`Downloading ${fileTitle}...`);
  // In a real application, this would trigger a download
}

// Function to share the current file
function shareCurrentFile() {
  const fileTitle = document.getElementById('fileViewerTitle').textContent.trim();
  const email = prompt(`Enter email address to share ${fileTitle} with:`);
  if (email) {
    alert(`${fileTitle} has been shared with ${email}.`);
    // In a real application, this would share the file with the specified email
  }
}

// Function to view attachments
function viewAttachment(type, fileName) {
  // Create a file viewer modal if it doesn't exist
  const fileViewerModal = document.getElementById('fileViewerModal') || createFileViewerModal();

  // Set the file information
  const fileTitle = document.getElementById('fileViewerTitle');
  const fileContent = document.getElementById('fileViewerContent');

  // Set the title based on the file type and name
  fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;

  // Set the content based on the file type
  fileContent.innerHTML = getAttachmentContent(type, fileName);

  // Ensure modal backdrop has proper z-index
  document.addEventListener('shown.bs.modal', function() {
    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
      backdrop.style.zIndex = '1199'; // Just below the modal z-index
    }
  }, { once: true });

  // Show the modal
  const modal = new bootstrap.Modal(fileViewerModal);
  modal.show();

  console.log(`Viewing ${fileName} directly`);
}

// Helper function to get the appropriate icon for the file type
function getFileTypeIcon(type) {
  if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
  if (type === 'document') return 'bi-file-earmark-text text-primary';
  if (type === 'spreadsheet') return 'bi-file-earmark-spreadsheet text-success';
  if (type === 'image') return 'bi-file-earmark-image text-info';
  return 'bi-file-earmark text-secondary';
}

// Helper function to generate content for the attachment viewer
function getAttachmentContent(type, fileName) {
  // Generate simulated content based on file type
  if (type === 'pdf') {
    return `<div class="text-center">
              <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
              <h4 class="mt-3">${fileName}</h4>
              <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                <h5>Document Preview</h5>
                <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                <p>The document contains information related to the application.</p>
              </div>
            </div>`;
  } else if (type === 'document') {
    return `<div class="border p-3" style="background-color: #f8f9fa;">
              <h4>${fileName}</h4>
              <hr>
              <p>This is a document viewer. In a real application, the document content would be displayed here.</p>
              <p>The document contains information related to the application.</p>
              <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
            </div>`;
  } else if (type === 'spreadsheet') {
    return `<div class="border p-3" style="background-color: #f8f9fa;">
              <h4>${fileName}</h4>
              <hr>
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <th>Item</th>
                      <th>Quantity</th>
                      <th>Price</th>
                      <th>Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Item 1</td>
                      <td>10</td>
                      <td>$10.00</td>
                      <td>$100.00</td>
                    </tr>
                    <tr>
                      <td>Item 2</td>
                      <td>5</td>
                      <td>$20.00</td>
                      <td>$100.00</td>
                    </tr>
                    <tr>
                      <td>Item 3</td>
                      <td>2</td>
                      <td>$30.00</td>
                      <td>$60.00</td>
                    </tr>
                    <tr>
                      <td colspan="3" class="text-end"><strong>Total</strong></td>
                      <td><strong>$260.00</strong></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>`;
  } else if (type === 'image') {
    return `<div class="text-center">
              <div class="border p-3 mt-3" style="background-color: #f8f9fa;">
                <i class="bi bi-image text-info" style="font-size: 100px;"></i>
                <h5 class="mt-3">${fileName}</h5>
                <p>This is an image viewer. In a real application, the image would be displayed here.</p>
              </div>
            </div>`;
  }

  return `<div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i> No preview available for this file type.
          </div>`;
}

// Function for downloading attachments
function downloadAttachment(fileName) {
  alert(`Downloading ${fileName}...`);
  // In a real application, this would trigger a download
}

// Function for deleting attachments
function deleteAttachment(fileName) {
  if (confirm(`Are you sure you want to delete ${fileName}?`)) {
    alert(`${fileName} has been deleted.`);
    // In a real application, this would delete the file
  }
}
