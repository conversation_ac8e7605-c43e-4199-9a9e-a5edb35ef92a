<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Performance Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .performance-metrics {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            z-index: 10000;
        }
        .metric {
            margin: 5px 0;
        }
        .metric.good { color: #28a745; }
        .metric.warning { color: #ffc107; }
        .metric.bad { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1>Gmail Performance Test</h1>
                <p class="lead">This page tests Gmail click responsiveness and performance metrics.</p>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Performance Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <button id="test-gmail-btn" class="btn btn-primary mb-3" data-bs-toggle="modal" data-bs-target="#gmailModal">
                            <i class="bi bi-envelope"></i> Test Gmail Modal (Click Response)
                        </button>
                        <br>
                        <button id="reset-metrics-btn" class="btn btn-secondary mb-3">
                            <i class="bi bi-arrow-clockwise"></i> Reset Metrics
                        </button>
                        <br>
                        <button id="run-performance-test" class="btn btn-success">
                            <i class="bi bi-speedometer2"></i> Run Full Performance Test
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <p class="text-muted">Click the test button to measure performance.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics Display -->
    <div id="performance-metrics" class="performance-metrics">
        <div class="metric" id="click-response">Click Response: <span>-</span></div>
        <div class="metric" id="modal-load">Modal Load: <span>-</span></div>
        <div class="metric" id="dom-ready">DOM Ready: <span>-</span></div>
        <div class="metric" id="script-count">Scripts: <span>-</span></div>
        <div class="metric" id="setTimeout-count">SetTimeout Calls: <span>0</span></div>
    </div>

    <!-- Gmail Modal (Simplified for testing) -->
    <div class="modal fade" id="gmailModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-envelope"></i> Gmail Integration
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6>Labels</h6>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action active">
                                    <i class="bi bi-inbox"></i> Inbox
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <i class="bi bi-send"></i> Sent
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <i class="bi bi-file-earmark"></i> Drafts
                                </a>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="p-3">
                                <h6>Performance Test Results</h6>
                                <div id="modal-test-results">
                                    <p>Modal loaded successfully!</p>
                                    <p id="modal-load-time">Load time: Calculating...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Performance Testing Script
        let performanceMetrics = {
            clickStart: 0,
            modalLoadStart: 0,
            modalLoadEnd: 0,
            setTimeoutCalls: 0,
            clickResponses: []
        };

        // Monitor setTimeout calls
        const originalSetTimeout = window.setTimeout;
        window.setTimeout = function(...args) {
            performanceMetrics.setTimeoutCalls++;
            updateMetrics();
            return originalSetTimeout.apply(this, args);
        };

        // Initialize metrics display
        function updateMetrics() {
            document.getElementById('setTimeout-count').innerHTML = 
                `SetTimeout Calls: <span class="${performanceMetrics.setTimeoutCalls > 10 ? 'bad' : performanceMetrics.setTimeoutCalls > 5 ? 'warning' : 'good'}">${performanceMetrics.setTimeoutCalls}</span>`;
            
            const scriptCount = document.querySelectorAll('script').length;
            document.getElementById('script-count').innerHTML = 
                `Scripts: <span class="${scriptCount > 20 ? 'bad' : scriptCount > 10 ? 'warning' : 'good'}">${scriptCount}</span>`;
        }

        // Test click responsiveness
        document.getElementById('test-gmail-btn').addEventListener('click', function() {
            performanceMetrics.clickStart = performance.now();
            performanceMetrics.modalLoadStart = performance.now();
        });

        // Modal event listeners
        const gmailModal = document.getElementById('gmailModal');
        gmailModal.addEventListener('show.bs.modal', function() {
            const clickResponse = performance.now() - performanceMetrics.clickStart;
            performanceMetrics.clickResponses.push(clickResponse);
            
            document.getElementById('click-response').innerHTML = 
                `Click Response: <span class="${clickResponse > 100 ? 'bad' : clickResponse > 50 ? 'warning' : 'good'}">${clickResponse.toFixed(2)}ms</span>`;
        });

        gmailModal.addEventListener('shown.bs.modal', function() {
            performanceMetrics.modalLoadEnd = performance.now();
            const modalLoadTime = performanceMetrics.modalLoadEnd - performanceMetrics.modalLoadStart;
            
            document.getElementById('modal-load').innerHTML = 
                `Modal Load: <span class="${modalLoadTime > 200 ? 'bad' : modalLoadTime > 100 ? 'warning' : 'good'}">${modalLoadTime.toFixed(2)}ms</span>`;
            
            document.getElementById('modal-load-time').textContent = `Load time: ${modalLoadTime.toFixed(2)}ms`;
        });

        // Reset metrics
        document.getElementById('reset-metrics-btn').addEventListener('click', function() {
            performanceMetrics = {
                clickStart: 0,
                modalLoadStart: 0,
                modalLoadEnd: 0,
                setTimeoutCalls: 0,
                clickResponses: []
            };
            updateMetrics();
            document.getElementById('click-response').innerHTML = 'Click Response: <span>-</span>';
            document.getElementById('modal-load').innerHTML = 'Modal Load: <span>-</span>';
            document.getElementById('test-results').innerHTML = '<p class="text-muted">Click the test button to measure performance.</p>';
        });

        // Full performance test
        document.getElementById('run-performance-test').addEventListener('click', function() {
            const testResults = document.getElementById('test-results');
            testResults.innerHTML = '<p>Running performance test...</p>';
            
            // Run multiple tests
            let testCount = 0;
            const maxTests = 5;
            const testInterval = setInterval(() => {
                if (testCount >= maxTests) {
                    clearInterval(testInterval);
                    
                    // Calculate averages
                    const avgResponse = performanceMetrics.clickResponses.reduce((a, b) => a + b, 0) / performanceMetrics.clickResponses.length;
                    
                    let resultClass = 'success';
                    let resultText = 'Excellent';
                    if (avgResponse > 100) {
                        resultClass = 'danger';
                        resultText = 'Poor';
                    } else if (avgResponse > 50) {
                        resultClass = 'warning';
                        resultText = 'Good';
                    }
                    
                    testResults.innerHTML = `
                        <div class="alert alert-${resultClass}">
                            <h6>Performance Test Complete</h6>
                            <p><strong>Average Click Response:</strong> ${avgResponse.toFixed(2)}ms (${resultText})</p>
                            <p><strong>Total setTimeout Calls:</strong> ${performanceMetrics.setTimeoutCalls}</p>
                            <p><strong>Test Cycles:</strong> ${maxTests}</p>
                        </div>
                    `;
                    return;
                }
                
                // Trigger modal open/close
                document.getElementById('test-gmail-btn').click();
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(gmailModal);
                    if (modal) modal.hide();
                    testCount++;
                }, 500);
            }, 1000);
        });

        // Initial setup
        document.addEventListener('DOMContentLoaded', function() {
            const domReadyTime = performance.now();
            document.getElementById('dom-ready').innerHTML = 
                `DOM Ready: <span class="${domReadyTime > 1000 ? 'bad' : domReadyTime > 500 ? 'warning' : 'good'}">${domReadyTime.toFixed(2)}ms</span>`;
            
            updateMetrics();
        });
    </script>
</body>
</html>
