/**
 * Gmail Integration for WMS Application
 * Handles all Gmail-related functionality matching CRM implementation
 */

class GmailIntegration {
    constructor() {
        this.emails = [];
        this.currentFolder = 'inbox';
        this.isLoading = false;
        this.debug = true;
        this.initialized = false;
    }

    /**
     * Initialize the Gmail integration
     */
    init() {
        this.log('Initializing Gmail integration');
        
        if (this.initialized) {
            this.log('Gmail integration already initialized');
            return;
        }
        
        // Load demo emails
        this.loadDemoData();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Mark as initialized
        this.initialized = true;
    }

    /**
     * Set up event listeners for Gmail UI
     */
    setupEventListeners() {
        // Compose button
        const composeBtn = document.getElementById('compose-new-email');
        if (composeBtn) {
            composeBtn.addEventListener('click', this.showComposeModal.bind(this));
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-gmail-modal');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', this.refreshEmails.bind(this));
        }
        
        // Dashboard refresh button
        const dashboardRefreshBtn = document.getElementById('refresh-gmail');
        if (dashboardRefreshBtn) {
            dashboardRefreshBtn.addEventListener('click', this.refreshEmails.bind(this));
        }
        
        // Dashboard compose button
        const dashboardComposeBtn = document.getElementById('compose-email');
        if (dashboardComposeBtn) {
            dashboardComposeBtn.addEventListener('click', this.showComposeModal.bind(this));
        }
        
        // Folder buttons
        document.querySelectorAll('.gmail-folder').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const folder = button.getAttribute('data-folder');
                this.switchFolder(folder);
            });
        });

        // Search form
        const searchForm = document.getElementById('gmail-search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const query = document.getElementById('gmail-search').value;
                this.searchEmails(query);
            });
        }

        // Email actions (Reply, Forward, Delete, etc.)
        document.querySelectorAll('.email-action').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const action = button.getAttribute('data-action');
                const emailId = button.closest('.email-item').getAttribute('data-email-id');
                this.performEmailAction(action, emailId);
            });
        });
    }

    /**
     * Show the compose email modal
     */
    showComposeModal() {
        this.log('Showing compose email modal');
        
        // Get or create the compose modal
        let composeModal = document.getElementById('gmailComposeModal');
        
        if (!composeModal) {
            this.createComposeModal();
            composeModal = document.getElementById('gmailComposeModal');
        }
        
        // Clear any previous content
        const recipientInput = document.getElementById('compose-recipient');
        const subjectInput = document.getElementById('compose-subject');
        const messageInput = document.getElementById('compose-message');
        
        if (recipientInput) recipientInput.value = '';
        if (subjectInput) subjectInput.value = '';
        if (messageInput) messageInput.value = '';

        // Show the modal
        try {
            let modal = bootstrap.Modal.getInstance(composeModal);
            if (!modal) {
                modal = new bootstrap.Modal(composeModal);
            }
            modal.show();
        } catch (err) {
            this.log('Error showing compose modal:', err);
            alert('Could not open email composition window. Please try again.');
        }
    }

    /**
     * Create the compose email modal
     */
    createComposeModal() {
        const modalHtml = `
            <div class="modal fade" id="gmailComposeModal" tabindex="-1" aria-labelledby="gmailComposeModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="gmailComposeModalLabel"><i class="bi bi-envelope-plus me-2"></i>Compose Email</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="compose-form">
                                <div class="mb-3">
                                    <label for="compose-recipient" class="form-label">To:</label>
                                    <input type="email" class="form-control" id="compose-recipient" placeholder="<EMAIL>" required>
                                </div>
                                <div class="mb-3">
                                    <label for="compose-subject" class="form-label">Subject:</label>
                                    <input type="text" class="form-control" id="compose-subject" placeholder="Email subject" required>
                                </div>
                                <div class="mb-3">
                                    <label for="compose-message" class="form-label">Message:</label>
                                    <textarea class="form-control" id="compose-message" rows="10" required></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="compose-attachment" class="form-label">Attachments:</label>
                                    <input type="file" class="form-control" id="compose-attachment" multiple>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-outline-primary" id="save-draft">Save Draft</button>
                            <button type="button" class="btn btn-primary" id="send-email">Send Email</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = modalHtml;
        document.body.appendChild(tempDiv.firstElementChild);
        
        // Add event listeners to the new modal
        const saveDraftBtn = document.getElementById('save-draft');
        if (saveDraftBtn) {
            saveDraftBtn.addEventListener('click', this.saveDraft.bind(this));
        }
        
        const sendEmailBtn = document.getElementById('send-email');
        if (sendEmailBtn) {
            sendEmailBtn.addEventListener('click', this.sendEmail.bind(this));
        }
    }

    /**
     * Switch to a different folder
     * @param {string} folder - The folder to switch to
     */
    switchFolder(folder) {
        this.log(`Switching to folder: ${folder}`);
        
        // Update current folder
        this.currentFolder = folder;
        
        // Update active folder in UI
        document.querySelectorAll('.gmail-folder').forEach(button => {
            if (button.getAttribute('data-folder') === folder) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
        
        // Filter emails based on folder
        const filteredEmails = this.emails.filter(email => email.folder === folder);
        
        // Update the email list
        this.updateEmailList(filteredEmails);
    }

    /**
     * Search emails
     * @param {string} query - Search query
     */
    searchEmails(query) {
        this.log(`Searching emails with query: ${query}`);
        
        if (!query) {
            // If query is empty, show current folder
            this.switchFolder(this.currentFolder);
            return;
        }
        
        // Normalize query for case-insensitive search
        const normalizedQuery = query.toLowerCase();
        
        // Filter emails that match the query in subject or from field
        const matchedEmails = this.emails.filter(email => {
            return email.subject.toLowerCase().includes(normalizedQuery) || 
                   email.from.toLowerCase().includes(normalizedQuery) ||
                   email.message.toLowerCase().includes(normalizedQuery);
        });
        
        // Update the email list with search results
        this.updateEmailList(matchedEmails, `Search results for "${query}"`);
    }

    /**
     * Refresh emails
     */
    refreshEmails() {
        this.log('Refreshing emails');
        
        // Show loading indicator
        this.isLoading = true;
        this.updateLoadingState();
        
        // Simulate API call delay
        setTimeout(() => {
            // Reload demo data and update the view
            this.loadDemoData();
            this.switchFolder(this.currentFolder);
            
            // Hide loading indicator
            this.isLoading = false;
            this.updateLoadingState();
            
            // Show success message
            this.showNotification('Emails refreshed successfully!');
        }, 1000);
    }

    /**
     * Update loading state in the UI
     */
    updateLoadingState() {
        const refreshBtns = document.querySelectorAll('#refresh-gmail-modal, #refresh-gmail');
        
        refreshBtns.forEach(btn => {
            if (this.isLoading) {
                btn.disabled = true;
                btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            } else {
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Refresh';
            }
        });
        
        // Also update the email list container
        const emailList = document.querySelector('.gmail-email-list');
        if (emailList) {
            if (this.isLoading) {
                emailList.classList.add('loading');
                emailList.innerHTML = '<div class="text-center p-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Loading emails...</p></div>';
            } else {
                emailList.classList.remove('loading');
            }
        }
    }

    /**
     * Perform an action on an email
     * @param {string} action - The action to perform
     * @param {string} emailId - The email ID
     */
    performEmailAction(action, emailId) {
        this.log(`Performing action ${action} on email ${emailId}`);
        
        const email = this.emails.find(e => e.id === emailId);
        if (!email) {
            this.showNotification('Email not found', 'error');
            return;
        }
        
        switch (action) {
            case 'reply':
                this.replyToEmail(email);
                break;
            case 'forward':
                this.forwardEmail(email);
                break;
            case 'delete':
                this.deleteEmail(emailId);
                break;
            case 'mark-read':
                this.markEmailAsRead(emailId);
                break;
            case 'mark-unread':
                this.markEmailAsUnread(emailId);
                break;
            case 'star':
                this.starEmail(emailId);
                break;
            default:
                this.log(`Unknown action: ${action}`);
        }
    }

    /**
     * Reply to an email
     * @param {Object} email - The email to reply to
     */
    replyToEmail(email) {
        this.log(`Replying to email: ${email.subject}`);
        
        // Show compose modal with pre-filled content
        this.showComposeModal();
        
        // Wait for the modal to be fully visible before populating
        setTimeout(() => {
            const recipientInput = document.getElementById('compose-recipient');
            const subjectInput = document.getElementById('compose-subject');
            const messageInput = document.getElementById('compose-message');
            
            if (recipientInput) recipientInput.value = email.fromEmail || '<EMAIL>';
            if (subjectInput) subjectInput.value = `Re: ${email.subject}`;
            if (messageInput) {
                const replyPrefix = `\n\n------ Original Message ------\nFrom: ${email.from}\nDate: ${email.date}\nSubject: ${email.subject}\n\n`;
                messageInput.value = replyPrefix + email.message;
            }
        }, 300);
    }

    /**
     * Forward an email
     * @param {Object} email - The email to forward
     */
    forwardEmail(email) {
        this.log(`Forwarding email: ${email.subject}`);
        
        // Show compose modal with pre-filled content
        this.showComposeModal();
        
        // Wait for the modal to be fully visible before populating
        setTimeout(() => {
            const recipientInput = document.getElementById('compose-recipient');
            const subjectInput = document.getElementById('compose-subject');
            const messageInput = document.getElementById('compose-message');
            
            if (recipientInput) recipientInput.value = '';
            if (subjectInput) subjectInput.value = `Fwd: ${email.subject}`;
            if (messageInput) {
                const forwardPrefix = `\n\n------ Forwarded Message ------\nFrom: ${email.from}\nDate: ${email.date}\nSubject: ${email.subject}\n\n`;
                messageInput.value = forwardPrefix + email.message;
            }
        }, 300);
    }

    /**
     * Delete an email
     * @param {string} emailId - The email ID
     */
    deleteEmail(emailId) {
        this.log(`Deleting email: ${emailId}`);
        
        if (confirm('Are you sure you want to delete this email?')) {
            // Find the email
            const index = this.emails.findIndex(e => e.id === emailId);
            if (index !== -1) {
                // Move to trash instead of permanent deletion
                this.emails[index].folder = 'trash';
                
                // Remove from current view
                const emailElement = document.querySelector(`.email-item[data-email-id="${emailId}"]`);
                if (emailElement) {
                    emailElement.remove();
                }
                
                this.showNotification('Email moved to trash');
            } else {
                this.showNotification('Email not found', 'error');
            }
        }
    }

    /**
     * Mark an email as read
     * @param {string} emailId - The email ID
     */
    markEmailAsRead(emailId) {
        this.log(`Marking email as read: ${emailId}`);
        
        const index = this.emails.findIndex(e => e.id === emailId);
        if (index !== -1) {
            this.emails[index].read = true;
            
            const emailElement = document.querySelector(`.email-item[data-email-id="${emailId}"]`);
            if (emailElement) {
                emailElement.classList.remove('unread');
            }
            
            this.showNotification('Email marked as read');
        }
    }

    /**
     * Mark an email as unread
     * @param {string} emailId - The email ID
     */
    markEmailAsUnread(emailId) {
        this.log(`Marking email as unread: ${emailId}`);
        
        const index = this.emails.findIndex(e => e.id === emailId);
        if (index !== -1) {
            this.emails[index].read = false;
            
            const emailElement = document.querySelector(`.email-item[data-email-id="${emailId}"]`);
            if (emailElement) {
                emailElement.classList.add('unread');
            }
            
            this.showNotification('Email marked as unread');
        }
    }

    /**
     * Star or unstar an email
     * @param {string} emailId - The email ID
     */
    starEmail(emailId) {
        this.log(`Toggling star for email: ${emailId}`);
        
        const index = this.emails.findIndex(e => e.id === emailId);
        if (index !== -1) {
            this.emails[index].starred = !this.emails[index].starred;
            
            const starBtn = document.querySelector(`.email-item[data-email-id="${emailId}"] .star-email`);
            if (starBtn) {
                if (this.emails[index].starred) {
                    starBtn.innerHTML = '<i class="bi bi-star-fill text-warning"></i>';
                    this.showNotification('Email starred');
                } else {
                    starBtn.innerHTML = '<i class="bi bi-star"></i>';
                    this.showNotification('Email unstarred');
                }
            }
        }
    }

    /**
     * Save a draft email
     */
    saveDraft() {
        this.log('Saving draft email');
        
        const recipient = document.getElementById('compose-recipient').value;
        const subject = document.getElementById('compose-subject').value;
        const message = document.getElementById('compose-message').value;
        
        if (!recipient && !subject && !message) {
            this.showNotification('Cannot save empty draft', 'error');
            return;
        }
        
        // Create a new draft email
        const newDraft = {
            id: 'draft_' + Date.now(),
            from: '<EMAIL>',
            fromEmail: '<EMAIL>',
            to: recipient || '(No recipient)',
            subject: subject || '(No subject)',
            message: message || '(No message body)',
            date: new Date().toLocaleString(),
            folder: 'drafts',
            read: true,
            starred: false
        };
        
        // Add to emails array
        this.emails.push(newDraft);
        
        this.showNotification('Draft saved successfully');
        
        // Close the compose modal
        const composeModal = document.getElementById('gmailComposeModal');
        if (composeModal) {
            const modal = bootstrap.Modal.getInstance(composeModal);
            if (modal) {
                modal.hide();
            }
        }
        
        // If we're already in the drafts folder, update the view
        if (this.currentFolder === 'drafts') {
            this.switchFolder('drafts');
        }
    }

    /**
     * Send an email
     */
    sendEmail() {
        this.log('Sending email');
        
        const recipient = document.getElementById('compose-recipient').value;
        const subject = document.getElementById('compose-subject').value;
        const message = document.getElementById('compose-message').value;
        
        if (!recipient) {
            this.showNotification('Recipient is required', 'error');
            return;
        }
        
        if (!subject) {
            if (!confirm('Are you sure you want to send an email without a subject?')) {
                return;
            }
        }
        
        if (!message) {
            if (!confirm('Are you sure you want to send an email without a message body?')) {
                return;
            }
        }
        
        // Create a new sent email
        const newSent = {
            id: 'sent_' + Date.now(),
            from: '<EMAIL>',
            fromEmail: '<EMAIL>',
            to: recipient,
            subject: subject || '(No subject)',
            message: message || '',
            date: new Date().toLocaleString(),
            folder: 'sent',
            read: true,
            starred: false
        };
        
        // Add to emails array
        this.emails.push(newSent);
        
        this.showNotification('Email sent successfully');
        
        // Close the compose modal
        const composeModal = document.getElementById('gmailComposeModal');
        if (composeModal) {
            const modal = bootstrap.Modal.getInstance(composeModal);
            if (modal) {
                modal.hide();
            }
        }
        
        // If we're already in the sent folder, update the view
        if (this.currentFolder === 'sent') {
            this.switchFolder('sent');
        }
    }

    /**
     * Update the email list in the UI
     * @param {Array} emails - The emails to display
     * @param {string} title - Optional title for the list
     */
    updateEmailList(emails, title = null) {
        this.log(`Updating email list with ${emails.length} emails`);
        
        // Get the email list container
        const emailList = document.querySelector('.gmail-email-list');
        if (!emailList) {
            this.log('Email list container not found');
            return;
        }
        
        // Clear the list
        emailList.innerHTML = '';
        
        // Update title if provided
        if (title) {
            const titleElement = document.querySelector('.email-list-title');
            if (titleElement) {
                titleElement.textContent = title;
            }
        }
        
        // If no emails, show empty state
        if (emails.length === 0) {
            emailList.innerHTML = `
                <div class="text-center p-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">No emails found</h5>
                    <p class="text-muted">This folder is empty</p>
                </div>
            `;
            return;
        }
        
        // Create email items
        emails.forEach(email => {
            const emailItem = document.createElement('div');
            emailItem.className = `email-item d-flex justify-content-between align-items-center p-2 border-bottom ${!email.read ? 'unread bg-light' : ''}`;
            emailItem.setAttribute('data-email-id', email.id);
            
            // Create avatar based on first letter of sender
            const sender = email.from.split(' ')[0];
            const avatar = sender.charAt(0).toUpperCase();
            
            emailItem.innerHTML = `
                <div class="d-flex align-items-center" style="cursor: pointer;" onclick="gmailIntegration.viewEmail('${email.id}')">
                    <button class="btn btn-sm btn-link star-email me-2" onclick="event.stopPropagation(); gmailIntegration.performEmailAction('star', '${email.id}')">
                        <i class="bi ${email.starred ? 'bi-star-fill text-warning' : 'bi-star'}"></i>
                    </button>
                    <div class="me-3" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">
                        ${avatar}
                    </div>
                    <div class="overflow-hidden">
                        <div class="d-flex justify-content-between">
                            <h6 class="mb-1 ${!email.read ? 'fw-bold' : ''}">${email.from}</h6>
                            <small class="text-muted ms-2">${email.date}</small>
                        </div>
                        <div class="text-truncate ${!email.read ? 'fw-bold' : ''}">${email.subject}</div>
                        <div class="small text-truncate text-muted">${email.message.substring(0, 100)}${email.message.length > 100 ? '...' : ''}</div>
                    </div>
                </div>
                <div class="email-actions">
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); gmailIntegration.performEmailAction('reply', '${email.id}')">
                            <i class="bi bi-reply"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="event.stopPropagation(); gmailIntegration.performEmailAction('forward', '${email.id}')">
                            <i class="bi bi-forward"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); gmailIntegration.performEmailAction('delete', '${email.id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            
            emailList.appendChild(emailItem);
        });
    }

    /**
     * View a specific email
     * @param {string} emailId - The email ID
     */
    viewEmail(emailId) {
        this.log(`Viewing email: ${emailId}`);
        
        // Find the email
        const email = this.emails.find(e => e.id === emailId);
        if (!email) {
            this.showNotification('Email not found', 'error');
            return;
        }
        
        // Mark as read
        if (!email.read) {
            this.markEmailAsRead(emailId);
        }
        
        // Get or create the email viewer modal
        let viewerModal = document.getElementById('gmailViewerModal');
        
        if (!viewerModal) {
            this.createViewerModal();
            viewerModal = document.getElementById('gmailViewerModal');
        }
        
        // Update email content in the modal
        const subject = document.getElementById('email-subject');
        const from = document.getElementById('email-from');
        const to = document.getElementById('email-to');
        const date = document.getElementById('email-date');
        const message = document.getElementById('email-message');
        
        if (subject) subject.textContent = email.subject;
        if (from) from.textContent = email.from;
        if (to) to.textContent = email.to || 'me';
        if (date) date.textContent = email.date;
        if (message) message.innerHTML = email.message.replace(/\n/g, '<br>');
        
        // Update buttons
        const replyBtn = document.getElementById('reply-email-btn');
        const forwardBtn = document.getElementById('forward-email-btn');
        const deleteBtn = document.getElementById('delete-email-btn');
        
        if (replyBtn) {
            replyBtn.onclick = () => {
                const modal = bootstrap.Modal.getInstance(viewerModal);
                if (modal) modal.hide();
                this.replyToEmail(email);
            };
        }
        
        if (forwardBtn) {
            forwardBtn.onclick = () => {
                const modal = bootstrap.Modal.getInstance(viewerModal);
                if (modal) modal.hide();
                this.forwardEmail(email);
            };
        }
        
        if (deleteBtn) {
            deleteBtn.onclick = () => {
                const modal = bootstrap.Modal.getInstance(viewerModal);
                if (modal) modal.hide();
                this.deleteEmail(emailId);
            };
        }
        
        // Show the modal
        try {
            let modal = bootstrap.Modal.getInstance(viewerModal);
            if (!modal) {
                modal = new bootstrap.Modal(viewerModal);
            }
            modal.show();
        } catch (err) {
            this.log('Error showing email viewer modal:', err);
            alert('Could not open email. Please try again.');
        }
    }

    /**
     * Create the email viewer modal
     */
    createViewerModal() {
        const modalHtml = `
            <div class="modal fade" id="gmailViewerModal" tabindex="-1" aria-labelledby="gmailViewerModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="gmailViewerModalLabel"><i class="bi bi-envelope-open me-2"></i>Email Viewer</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="email-header mb-4">
                                <h4 id="email-subject" class="mb-3"></h4>
                                <div class="email-meta">
                                    <div class="row mb-2">
                                        <div class="col-2 text-muted">From:</div>
                                        <div class="col-10" id="email-from"></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-2 text-muted">To:</div>
                                        <div class="col-10" id="email-to"></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-2 text-muted">Date:</div>
                                        <div class="col-10" id="email-date"></div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="email-content mt-4">
                                <div id="email-message" class="p-2"></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-primary" id="reply-email-btn">
                                <i class="bi bi-reply me-1"></i> Reply
                            </button>
                            <button type="button" class="btn btn-outline-info" id="forward-email-btn">
                                <i class="bi bi-forward me-1"></i> Forward
                            </button>
                            <button type="button" class="btn btn-outline-danger" id="delete-email-btn">
                                <i class="bi bi-trash me-1"></i> Delete
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = modalHtml;
        document.body.appendChild(tempDiv.firstElementChild);
    }

    /**
     * Show notification
     * @param {string} message - The notification message
     * @param {string} type - The notification type (success, error)
     */
    showNotification(message, type = 'success') {
        this.log(`Showing notification: ${message} (${type})`);
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : 'success'} gmail-notification`;
        notification.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 2000; min-width: 300px;';
        notification.innerHTML = message;
        
        // Add to document
        document.body.appendChild(notification);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 3000);
    }

    /**
     * Load demo email data
     */
    loadDemoData() {
        this.log('Loading demo email data');
        
        this.emails = [
            {
                id: 'email1',
                from: 'Acme Corp',
                fromEmail: '<EMAIL>',
                to: '<EMAIL>',
                subject: 'Shipment confirmation for order #12345',
                message: `Dear Warehouse Manager,

This is to confirm that the shipment for order #12345 has been received by the customer.

Order Details:
- Order Number: #12345
- Customer: Acme Corp
- Delivery Date: May 12, 2025
- Items:
  - Widget A (500 units)
  - Widget B (250 units)

Please update your inventory records accordingly.

Thank you,
Acme Corp Receiving Department`,
                date: 'May 12, 2025, 10:15 AM',
                folder: 'inbox',
                read: false,
                starred: false
            },
            {
                id: 'email2',
                from: 'John Davis',
                fromEmail: '<EMAIL>',
                to: '<EMAIL>',
                subject: 'Warehouse inspection scheduled for tomorrow',
                message: `Hello Warehouse Team,

This is a reminder that the warehouse inspection is scheduled for tomorrow at 10:00 AM.

Please ensure that all areas are properly organized and all safety protocols are being followed.

The inspection will cover:
- Inventory accuracy
- Storage conditions
- Safety compliance
- Equipment maintenance

If you have any questions, please let me know.

Regards,
John Davis
Operations Manager`,
                date: 'May 12, 2025, 9:30 AM',
                folder: 'inbox',
                read: false,
                starred: false
            },
            {
                id: 'email3',
                from: 'Globex Inc',
                fromEmail: '<EMAIL>',
                to: '<EMAIL>',
                subject: 'New order placement - Urgent delivery',
                message: `Dear Warehouse Team,

We need to place a new order for urgent delivery. Please process this order with highest priority.

Order Details:
- Order Number: #67890
- Customer: Globex Inc
- Required Delivery Date: May 14, 2025
- Items:
  - Widget C (1000 units)

Please confirm receipt of this order and expected delivery date.

Thank you,
Globex Inc Purchasing Department`,
                date: 'May 12, 2025, 8:00 AM',
                folder: 'inbox',
                read: false,
                starred: true
            },
            {
                id: 'email4',
                from: 'Logistics Coordinator',
                fromEmail: '<EMAIL>',
                to: '<EMAIL>',
                subject: 'Weekly logistics meeting - Agenda',
                message: `Hi team,

Here's the agenda for our weekly logistics meeting tomorrow:

1. Review of last week's shipments
2. Upcoming deliveries
3. Warehouse capacity updates
4. Equipment maintenance schedule
5. Any other business

Please come prepared with your updates.

Best regards,
Logistics Coordinator`,
                date: 'May 11, 2025, 4:45 PM',
                folder: 'inbox',
                read: true,
                starred: false
            },
            {
                id: 'email5',
                from: 'System Administrator',
                fromEmail: '<EMAIL>',
                to: '<EMAIL>',
                subject: 'Scheduled system maintenance',
                message: `Important Notice:

The warehouse management system will undergo scheduled maintenance this weekend (May 15-16) from 10:00 PM Friday until 2:00 AM Saturday.

During this time, the system will be unavailable. Please plan your operations accordingly.

Thank you for your understanding.

System Administrator`,
                date: 'May 11, 2025, 2:30 PM',
                folder: 'inbox',
                read: true,
                starred: true
            },
            {
                id: 'email6',
                from: 'HR Department',
                fromEmail: '<EMAIL>',
                to: '<EMAIL>',
                subject: 'Upcoming training sessions',
                message: `Dear Staff,

We're pleased to announce the following training sessions:

1. Warehouse Safety Protocols - May 20, 10:00 AM
2. New Inventory Management System - May 22, 2:00 PM
3. Forklift Certification Renewal - May 25, 9:00 AM

Please register for the sessions you need to attend by replying to this email.

Best regards,
HR Department`,
                date: 'May 10, 2025, 11:20 AM',
                folder: 'inbox',
                read: true,
                starred: false
            },
            {
                id: 'sent1',
                from: '<EMAIL>',
                fromEmail: '<EMAIL>',
                to: '<EMAIL>',
                subject: 'Re: Supply order confirmation',
                message: `Hi,

Thank you for confirming the order. Please ensure delivery by May 18 as discussed.

Best regards,
Warehouse Manager`,
                date: 'May 11, 2025, 10:15 AM',
                folder: 'sent',
                read: true,
                starred: false
            },
            {
                id: 'draft1',
                from: '<EMAIL>',
                fromEmail: '<EMAIL>',
                to: '<EMAIL>',
                subject: 'Monthly inventory report - Draft',
                message: `Team,

Here's the monthly inventory report for April 2025.

[Insert report data here]

Let me know if you have any questions.

Regards,
Warehouse Manager`,
                date: 'May 10, 2025, 4:30 PM',
                folder: 'drafts',
                read: true,
                starred: false
            }
        ];
    }

    /**
     * Log a message to console if debug mode is enabled
     */
    log(...args) {
        if (this.debug) {
            console.log('GmailIntegration:', ...args);
        }
    }
}

// Create and initialize the Gmail integration
const gmailIntegration = new GmailIntegration();
document.addEventListener('DOMContentLoaded', () => {
    gmailIntegration.init();
});
