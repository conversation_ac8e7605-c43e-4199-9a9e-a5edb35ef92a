<div class="google-integration-component google-sheets-component">
    <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
        <h5 class="mb-0"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
        <div class="component-actions">
            <button id="refresh-sheets" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
            <button id="create-new-sheet" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#sheetsModal"><i class="bi bi-plus"></i></button>
        </div>
    </div>
    <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div class="list-group">
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div style="cursor: pointer;" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                    <span>Production_Schedule_2025.xlsx</span>
                    <span class="badge bg-primary rounded-pill ms-2">2 days ago</span>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')"><i class="bi bi-eye"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')"><i class="bi bi-download"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')"><i class="bi bi-share"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')"><i class="bi bi-trash"></i></button>
                </div>
            </div>
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div style="cursor: pointer;" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                    <span>Material_Requirements_Analysis.xlsx</span>
                    <span class="badge bg-primary rounded-pill ms-2">1 week ago</span>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')"><i class="bi bi-eye"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')"><i class="bi bi-download"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')"><i class="bi bi-share"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')"><i class="bi bi-trash"></i></button>
                </div>
            </div>
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div style="cursor: pointer;" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')">
                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                    <span>Capacity_Planning_Q2_2025.xlsx</span>
                    <span class="badge bg-primary rounded-pill ms-2">2 weeks ago</span>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')"><i class="bi bi-eye"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')"><i class="bi bi-download"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')"><i class="bi bi-share"></i></button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')"><i class="bi bi-trash"></i></button>
                </div>
            </div>
        </div>
        <div class="d-grid gap-2 mt-3">
            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
            </button>
        </div>
    </div>
</div>
