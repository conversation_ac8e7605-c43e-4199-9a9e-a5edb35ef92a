@echo off
echo Starting Integration Hub...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%
set PORT=8000
set NODE_ENV=production

REM Change to the Hub directory
cd /d C:\ISASUITE\apps\hub\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call C:\ISASUITE\PortableNodeJS\pnpm install
)

REM Close any potentially running instance on the same port
taskkill /F /FI "WINDOWTITLE eq Integration Hub*" /T > nul 2>&1
taskkill /F /FI "WINDOWTITLE eq http://localhost:8000*" /T > nul 2>&1

REM Build the application if needed
if not exist .next\. (
    echo Building the hub application...
    call C:\ISASUITE\PortableNodeJS\pnpm build
)

REM Start the application
echo Starting Hub...
echo To access the Integration Hub, go to: http://localhost:8000
start "Integration Hub" cmd /c "C:\ISASUITE\PortableNodeJS\node.exe server.js"

REM Wait for server to initialize
timeout /t 2 > nul

REM Open browser
start "" http://localhost:8000

echo.
echo Hub application launched. Close this window to stop the application.