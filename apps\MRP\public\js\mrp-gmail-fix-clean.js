// This file is deprecated. All Gmail modal logic is now in mrp-gmail-search-sort-filter.js

// Execute when the document is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('MRP Gmail Fix (Clean) loaded');

    // Find the Open Gmail button and ensure it works with existing modal
    const openGmailBtn = document.getElementById('mrp-open-gmail-btn');
    if (openGmailBtn) {
        console.log('Found Open Gmail button, ensuring it works with existing modal');        // Make sure it opens the correct modal
        openGmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Opening existing MRP Gmail modal');
            
            const existingModal = document.getElementById('mrp-gmailModal');
            if (existingModal) {
                const modal = new bootstrap.Modal(existingModal);
                modal.show();
                
                // Initialize dropdowns after modal is shown
                setTimeout(() => {
                    console.log('Modal shown, initializing dropdowns...');
                    initializeDropdowns();
                    setupSearchHandler();
                }, 300);
            } else {
                console.error('Could not find existing mrp-gmailModal');
            }
        });
    } else {
        console.error('Could not find Open Gmail button by ID');
    }    // Setup initial event listeners - removed as now handled by event delegation
      // Setup refresh button handler
    document.addEventListener('click', function(e) {
        if (e.target.id === 'refresh-gmail' || e.target.closest('#refresh-gmail')) {
            e.preventDefault();
            showAllEmails();
            showToast('Gmail refreshed');
        }
        
        if (e.target.id === 'debug-dropdowns' || e.target.closest('#debug-dropdowns')) {
            e.preventDefault();
            debugDropdownState();
        }
    });
});

/**
 * Initialize Bootstrap dropdowns properly
 */
function initializeDropdowns() {
    console.log('Initializing dropdowns...');
    
    // Wait for modal to be fully rendered
    setTimeout(() => {
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal) {
            console.error('Modal not found');
            return;
        }        // Remove inline onclick handlers that conflict with Bootstrap
        const sortDropdown = document.getElementById('sort-dropdown');
        const filterDropdown = document.getElementById('filter-dropdown');
        
        console.log('Found dropdowns:', {
            sortDropdown: !!sortDropdown,
            filterDropdown: !!filterDropdown
        });
        
        if (sortDropdown) {
            sortDropdown.removeAttribute('onclick');
            console.log('Removed onclick from sort dropdown');
        }
        if (filterDropdown) {
            filterDropdown.removeAttribute('onclick');
            console.log('Removed onclick from filter dropdown');
        }
        
        // Remove onclick from dropdown items and use proper event delegation
        const sortOptions = modal.querySelectorAll('.sort-option');
        const filterOptions = modal.querySelectorAll('.filter-option');
        
        sortOptions.forEach(option => {
            option.removeAttribute('onclick');
        });
        
        filterOptions.forEach(option => {
            option.removeAttribute('onclick');
        });
        
        // First try Bootstrap's initialization
        if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
            try {
                const dropdownToggles = modal.querySelectorAll('[data-bs-toggle="dropdown"]');
                dropdownToggles.forEach(toggle => {
                    // Dispose existing dropdown if any
                    const existingDropdown = bootstrap.Dropdown.getInstance(toggle);
                    if (existingDropdown) {
                        existingDropdown.dispose();
                    }
                    // Create new dropdown with proper configuration
                    const dropdown = new bootstrap.Dropdown(toggle, {
                        autoClose: true,
                        boundary: modal
                    });
                    console.log('Bootstrap dropdown initialized for:', toggle.id);
                });
                  // Setup event delegation for dropdown options
                setupBootstrapDropdownEvents(modal);
                
                // Debug the current state
                debugDropdownState();
                
            } catch (error) {
                console.error('Error initializing Bootstrap dropdowns:', error);
                initializeManualDropdowns();
            }
        } else {
            console.warn('Bootstrap not available, using manual dropdown implementation');
            initializeManualDropdowns();
        }
    }, 300);
}

/**
 * Setup Bootstrap dropdown event delegation
 */
function setupBootstrapDropdownEvents(modal) {
    // Event delegation for sort options
    modal.addEventListener('click', function(e) {
        if (e.target.classList.contains('sort-option')) {
            e.preventDefault();
            const sortType = e.target.getAttribute('data-sort');
            const buttonText = e.target.textContent.trim();
            
            // Update button text
            const sortButton = document.getElementById('sort-dropdown');
            if (sortButton) {
                sortButton.innerHTML = `<i class="bi bi-sort-down"></i> ${buttonText}`;
            }
            
            // Hide dropdown (Bootstrap will handle this automatically)
            const dropdown = bootstrap.Dropdown.getInstance(document.getElementById('sort-dropdown'));
            if (dropdown) {
                dropdown.hide();
            }
            
            // Show toast notification
            showToast(`Emails sorted by: ${buttonText}`);
            
            // Call the sort function
            sortEmails(sortType);
        }
        
        if (e.target.classList.contains('filter-option')) {
            e.preventDefault();
            const filterType = e.target.getAttribute('data-filter');
            const buttonText = e.target.textContent.trim();
            
            // Update button text
            const filterButton = document.getElementById('filter-dropdown');
            if (filterButton) {
                filterButton.innerHTML = `<i class="bi bi-funnel"></i> ${buttonText}`;
            }
            
            // Hide dropdown (Bootstrap will handle this automatically)
            const dropdown = bootstrap.Dropdown.getInstance(document.getElementById('filter-dropdown'));
            if (dropdown) {
                dropdown.hide();
            }
            
            // Show toast notification
            showToast(`Emails filtered by: ${buttonText}`);
            
            // Call the filter function
            filterEmails(filterType);
        }
    });
}

/**
 * Manual dropdown implementation as fallback
 */
/**
 * Manual dropdown implementation as fallback
 */
function initializeManualDropdowns() {
    console.log('Setting up manual dropdown implementation');
    const modal = document.getElementById('mrp-gmailModal');
    if (!modal) return;
    
    const sortDropdownEl = document.getElementById('sort-dropdown');
    const filterDropdownEl = document.getElementById('filter-dropdown');
    
    // Remove any existing event listeners and Bootstrap attributes
    if (sortDropdownEl) {
        sortDropdownEl.removeAttribute('data-bs-toggle');
        sortDropdownEl.removeAttribute('onclick');
    }
    if (filterDropdownEl) {
        filterDropdownEl.removeAttribute('data-bs-toggle');
        filterDropdownEl.removeAttribute('onclick');
    }
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            modal.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });
      // Manual dropdown event delegation
    modal.addEventListener('click', function(e) {
        console.log('Modal click detected:', e.target.id, e.target.className);
        
        // Handle dropdown toggle buttons
        if (e.target.id === 'sort-dropdown' || e.target.closest('#sort-dropdown')) {
            console.log('Sort dropdown button clicked');
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown('sort-dropdown');
        }
        
        if (e.target.id === 'filter-dropdown' || e.target.closest('#filter-dropdown')) {
            console.log('Filter dropdown button clicked');
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown('filter-dropdown');
        }
        
        // Handle dropdown options
        if (e.target.classList.contains('sort-option')) {
            e.preventDefault();
            const sortType = e.target.getAttribute('data-sort');
            const buttonText = e.target.textContent.trim();
            
            // Update button text
            const sortButton = document.getElementById('sort-dropdown');
            if (sortButton) {
                sortButton.innerHTML = `<i class="bi bi-sort-down"></i> ${buttonText}`;
            }
            
            // Hide dropdown
            hideAllDropdowns();
            
            // Show toast notification
            showToast(`Emails sorted by: ${buttonText}`);
            
            // Call the sort function
            sortEmails(sortType);
        }
        
        if (e.target.classList.contains('filter-option')) {
            e.preventDefault();
            const filterType = e.target.getAttribute('data-filter');
            const buttonText = e.target.textContent.trim();
            
            // Update button text
            const filterButton = document.getElementById('filter-dropdown');
            if (filterButton) {
                filterButton.innerHTML = `<i class="bi bi-funnel"></i> ${buttonText}`;
            }
            
            // Hide dropdown
            hideAllDropdowns();
            
            // Show toast notification
            showToast(`Emails filtered by: ${buttonText}`);
            
            // Call the filter function
            filterEmails(filterType);
        }    });
    
    console.log('Manual dropdown handlers initialized');
    
    // Debug the current state
    debugDropdownState();
}

function toggleDropdown(dropdownId) {
    const button = document.getElementById(dropdownId);
    if (!button) return;
    
    const dropdownMenu = button.nextElementSibling;
    if (!dropdownMenu || !dropdownMenu.classList.contains('dropdown-menu')) return;
    
    const isVisible = dropdownMenu.classList.contains('show');
    
    // Hide all other dropdowns first
    hideAllDropdowns();
    
    // Toggle this dropdown
    if (!isVisible) {
        dropdownMenu.classList.add('show');
        button.setAttribute('aria-expanded', 'true');
        console.log(`${dropdownId} opened manually`);
    }
}

function hideAllDropdowns() {
    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
        menu.classList.remove('show');
    });
    document.querySelectorAll('[data-bs-toggle="dropdown"], [aria-expanded="true"]').forEach(button => {
        button.setAttribute('aria-expanded', 'false');
    });
}

// Remove the old individual handler functions as they're now integrated above

/**
 * Setup search handler
 */
function setupSearchHandler() {
    const searchInput = document.getElementById('email-search');
    const searchBtn = document.getElementById('search-btn');
    
    if (searchInput) {
        // Handle Enter key in search input
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchEmails(this.value);
            }
        });
        
        // Clear search when input is empty
        searchInput.addEventListener('input', function(e) {
            if (this.value === '') {
                showAllEmails();
            }
        });
    }
    
    if (searchBtn) {
        searchBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const searchValue = searchInput ? searchInput.value : '';
            searchEmails(searchValue);
        });
    }
    
    console.log('Search handlers initialized');
}

// Enhanced search functionality
function searchEmails(searchTerm) {
    console.log('Searching emails for:', searchTerm);
    
    if (!searchTerm) {
        showAllEmails();
        showToast('Showing all emails');
        return;    }
    
    const emailItems = document.querySelectorAll('#mrp-email-list-section .list-group-item');
    let visibleCount = 0;
    
    emailItems.forEach(item => {
        const senderElement = item.querySelector('h6.mb-1.fw-bold');
        const subjectElement = item.querySelector('p.mb-1.fw-bold');
        const previewElement = item.querySelector('p.mb-0.text-muted');
        
        const senderText = senderElement ? senderElement.textContent.toLowerCase() : '';
        const subjectText = subjectElement ? subjectElement.textContent.toLowerCase() : '';
        const previewText = previewElement ? previewElement.textContent.toLowerCase() : '';
        
        const searchLower = searchTerm.toLowerCase();
        
        if (senderText.includes(searchLower) || 
            subjectText.includes(searchLower) || 
            previewText.includes(searchLower)) {
            item.style.display = '';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    showToast(`Found ${visibleCount} email(s) matching "${searchTerm}"`);
}

/**
 * Debug function to check modal and dropdown states
 */
function debugDropdownState() {
    console.log('=== DROPDOWN DEBUG INFO ===');
    const modal = document.getElementById('mrp-gmailModal');
    const sortDropdown = document.getElementById('sort-dropdown');
    const filterDropdown = document.getElementById('filter-dropdown');
    
    console.log('Modal found:', !!modal);
    console.log('Sort dropdown found:', !!sortDropdown);
    console.log('Filter dropdown found:', !!filterDropdown);
    
    if (sortDropdown) {
        console.log('Sort dropdown attributes:', {
            'data-bs-toggle': sortDropdown.getAttribute('data-bs-toggle'),
            'onclick': sortDropdown.getAttribute('onclick'),
            'aria-expanded': sortDropdown.getAttribute('aria-expanded')
        });
        
        const sortMenu = sortDropdown.nextElementSibling;
        console.log('Sort menu found:', !!sortMenu);
        if (sortMenu) {
            console.log('Sort menu classes:', sortMenu.className);
        }
    }
    
    if (filterDropdown) {
        console.log('Filter dropdown attributes:', {
            'data-bs-toggle': filterDropdown.getAttribute('data-bs-toggle'),
            'onclick': filterDropdown.getAttribute('onclick'),
            'aria-expanded': filterDropdown.getAttribute('aria-expanded')
        });
        
        const filterMenu = filterDropdown.nextElementSibling;
        console.log('Filter menu found:', !!filterMenu);
        if (filterMenu) {
            console.log('Filter menu classes:', filterMenu.className);
        }
    }
    
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap Dropdown available:', typeof bootstrap.Dropdown !== 'undefined');
    }
    console.log('=== END DEBUG INFO ===');
}

// Make debug function globally available
window.debugDropdownState = debugDropdownState;

// Enhanced sort functionality
function sortEmails(sortType) {
    console.log('Sorting emails by:', sortType);
    
    const emailList = document.getElementById('mrp-email-list');
    if (!emailList) return;
    
    const emailItems = Array.from(emailList.querySelectorAll('.list-group-item'));
    
    emailItems.sort((a, b) => {
        let aValue, bValue;
        
        switch(sortType) {
            case 'date-desc':
            case 'date-asc':
                // For demo purposes, use the order they appear in the HTML
                const aIndex = emailItems.indexOf(a);
                const bIndex = emailItems.indexOf(b);
                return sortType === 'date-desc' ? aIndex - bIndex : bIndex - aIndex;
                
            case 'sender-asc':
            case 'sender-desc':
                aValue = a.querySelector('h6.mb-1.fw-bold')?.textContent || '';
                bValue = b.querySelector('h6.mb-1.fw-bold')?.textContent || '';
                const senderCompare = aValue.localeCompare(bValue);
                return sortType === 'sender-asc' ? senderCompare : -senderCompare;
                
            case 'subject-asc':
            case 'subject-desc':
                aValue = a.querySelector('p.mb-1.fw-bold')?.textContent || '';
                bValue = b.querySelector('p.mb-1.fw-bold')?.textContent || '';
                const subjectCompare = aValue.localeCompare(bValue);
                return sortType === 'subject-asc' ? subjectCompare : -subjectCompare;
                
            default:
                return 0;
        }
    });
    
    // Reorder the DOM elements
    emailItems.forEach(item => emailList.appendChild(item));
}

// Enhanced filter functionality
function filterEmails(filterType) {
    console.log('Filtering emails by:', filterType);
    
    const emailItems = document.querySelectorAll('#mrp-email-list .list-group-item');
    let visibleCount = 0;
    
    emailItems.forEach(item => {
        let shouldShow = false;
        
        switch(filterType) {
            case 'all':
                shouldShow = true;
                break;
                
            case 'unread':
                // Check if the item has unread styling (bold text or other indicators)
                const hasUnreadClass = item.classList.contains('unread') || 
                                     item.querySelector('.fw-bold');
                shouldShow = hasUnreadClass;
                break;
                
            case 'read':
                // Opposite of unread
                const hasReadClass = !item.classList.contains('unread');
                shouldShow = hasReadClass;
                break;
                
            case 'label-inventory':
                shouldShow = item.textContent.toLowerCase().includes('inventory');
                break;
                
            case 'label-production':
                shouldShow = item.textContent.toLowerCase().includes('production');
                break;
                
            case 'label-urgent':
                shouldShow = item.textContent.toLowerCase().includes('urgent');
                break;
                
            default:
                shouldShow = true;
        }
        
        if (shouldShow) {
            item.style.display = '';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    const filterText = filterType === 'all' ? 'all emails' : filterType.replace('label-', '').replace('-', ' ');
    showToast(`Showing ${visibleCount} email(s) - ${filterText}`);
}

// Show all emails (reset filters)
function showAllEmails() {
    console.log('Showing all emails');
    
    const emailItems = document.querySelectorAll('#mrp-email-list .list-group-item');
    emailItems.forEach(item => {
        item.style.display = '';
    });
    
    // Reset button texts
    const sortButton = document.getElementById('sort-dropdown');
    const filterButton = document.getElementById('filter-dropdown');
    
    if (sortButton) {
        sortButton.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
    }
    if (filterButton) {
        filterButton.innerHTML = '<i class="bi bi-funnel"></i> Filter';
    }
}

// Toast notification function
function showToast(message) {
    // Remove any existing toast
    const existingToast = document.querySelector('.custom-toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'custom-toast';
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #28a745;
        color: white;
        padding: 12px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        z-index: 9999;
        font-weight: 500;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    toast.textContent = message;
    
    // Add to page
    document.body.appendChild(toast);
    
    // Fade in
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 10);
    
    // Remove after 3 seconds
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Enhanced search input with Enter key support
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('email-search');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchEmails(this.value);
            }
        });
    }
});
