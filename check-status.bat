@echo off
echo ===================================
echo    ISA Suite - Application Status
echo ===================================
echo.
echo Checking status of all ISA Suite applications...
echo.

REM Check Integration Hub
netstat -ano | findstr ":8000" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Integration Hub (http://localhost:8000)
) else (
    echo [OFFLINE] Integration Hub (http://localhost:8000)
)

REM Check Business Management System
netstat -ano | findstr ":3001" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Business Management System (http://localhost:3001)
) else (
    echo [OFFLINE] Business Management System (http://localhost:3001)
)

REM Check Materials Requirements Planning
netstat -ano | findstr ":3002" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Materials Requirements Planning (http://localhost:3002)
) else (
    echo [OFFLINE] Materials Requirements Planning (http://localhost:3002)
)

REM Check Customer Relationship Management
netstat -ano | findstr ":3003" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Customer Relationship Management (http://localhost:3003)
) else (
    echo [OFFLINE] Customer Relationship Management (http://localhost:3003)
)

REM Check Warehouse Management System
netstat -ano | findstr ":3004" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Warehouse Management System (http://localhost:3004)
) else (
    echo [OFFLINE] Warehouse Management System (http://localhost:3004)
)

REM Check Advanced Planning and Scheduling
netstat -ano | findstr ":3005" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Advanced Planning and Scheduling (http://localhost:3005)
) else (
    echo [OFFLINE] Advanced Planning and Scheduling (http://localhost:3005)
)

REM Check Asset Performance Management
netstat -ano | findstr ":3006" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Asset Performance Management (http://localhost:3006)
) else (
    echo [OFFLINE] Asset Performance Management (http://localhost:3006)
)

REM Check Project Management System
netstat -ano | findstr ":3007" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Project Management System (http://localhost:3007)
) else (
    echo [OFFLINE] Project Management System (http://localhost:3007)
)

REM Check Supply Chain Management
netstat -ano | findstr ":3008" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Supply Chain Management (http://localhost:3008)
) else (
    echo [OFFLINE] Supply Chain Management (http://localhost:3008)
)

REM Check Task Management System
netstat -ano | findstr ":3009" > nul
if %errorlevel% equ 0 (
    echo [ONLINE]  Task Management System (http://localhost:3009)
) else (
    echo [OFFLINE] Task Management System (http://localhost:3009)
)

echo.
echo ===================================
echo.
echo To start all applications, run single-window-launcher.bat
echo To stop all applications, run stop-apps.bat
echo.
pause
