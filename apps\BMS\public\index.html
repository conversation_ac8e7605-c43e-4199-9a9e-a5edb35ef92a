<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Management System</title>
    <link rel="icon" type="image/png" href="icons/icon-96x96.png">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Remove or comment out the problematic CSS link -->
    <!-- <link rel="stylesheet" href="../../shared/css/isa-modals.css"> -->
    <!-- <link rel="stylesheet" href="css/gmail-modal-fix.css"> -->
    <style>
        :root {
            --app-primary-color: #00acc1; /* Teal/Turquoise for BMS */
            --app-primary-dark: #007c91;
            --app-primary-light: rgba(0, 172, 193, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            padding-top: 56px; /* Height of navbar */
        }

        /* Sidebar styles */
        .sidebar {
            background-color: var(--app-primary-color);
            color: white;
            height: calc(100vh - 56px);
            position: fixed;
            top: 56px; /* Height of navbar */
            padding-top: 20px;
            width: 250px;
            z-index: 100;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto; /* Add scrollbar when content overflows */
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--app-primary-dark);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar .nav-link {
            color: #f8f9fa;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-heading {
            padding: 0.75rem 1.25rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.1rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Main content area */
        .main-content {
            margin-left: 250px;
            padding: 20px;
            transition: margin-left 0.3s;
        }

        /* Dashboard cards */
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        .card-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        /* Navbar */
        .navbar {
            background-color: var(--app-primary-color) !important;
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
        }

        /* Toggle sidebar button */
        #sidebarToggle {
            cursor: pointer;
            background: transparent;
            border: none;
            color: white;
        }

        /* For mobile view */
        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: -250px;
                height: calc(100vh - 56px);
                transition: left 0.3s;
                z-index: 1030;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0;
                padding: 15px;
            }

            .navbar {
                padding: 0.5rem;
            }
        }

        /* Google Integration Components Styles */
        .google-integration-component {
            border: 1px solid #ddd;
            border-radius: 10px;
            margin-bottom: 20px;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .google-integration-component:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .component-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--app-primary-color);
            color: white;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .component-header h5 {
            margin: 0;
            font-size: 18px;
            color: white;
            font-weight: 600;
        }

        .component-body {
            padding: 20px;
            min-height: 100px;
            background-color: #fff;
        }

        .component-footer {
            padding: 12px 20px;
            border-top: 1px solid #eee;
            text-align: right;
            background-color: #f8f9fa;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        /* App Modal Header Styles */
        .app-modal-header {
            background-color: var(--app-primary-color);
            color: white;
        }

        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .email-item {
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .email-item:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .email-item[data-read="false"] h6 {
            font-weight: bold;
        }

        .email-item[data-read="false"] p {
            font-weight: 600;
        }

        /* Hide file input */
        #attachment-input {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-dark fixed-top">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <button id="sidebarToggle" class="d-md-none me-2 sidebar-toggle-btn" title="Toggle sidebar">
                    <i class="bi bi-list fs-4"></i>
                </button>
                <a class="navbar-brand" href="/">BMS System</a>
            </div>
            <div class="d-flex">
                <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3 btn-back-to-hub">
                    <i class="bi bi-box-arrow-left me-1"></i> Back to Hub
                </a>
                <button class="btn position-relative me-2 btn-bell">
                    <i class="bi bi-bell fs-5"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        3
                    </span>
                </button>
                <button class="btn btn-navbar-icon" title="User Profile">
                    <i class="bi bi-person-circle fs-5"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="pt-2">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#financial">
                        <i class="bi bi-cash-coin"></i>
                        Financial Management
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#hr">
                        <i class="bi bi-people"></i>
                        Human Resources
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#operations">
                        <i class="bi bi-gear"></i>
                        Operations
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#reports">
                        <i class="bi bi-file-earmark-text"></i>
                        Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#settings">
                        <i class="bi bi-gear-fill"></i>
                        Settings
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#attachments" data-bs-toggle="modal" data-bs-target="#attachmentsModal">
                        <i class="bi bi-paperclip"></i>
                        Attachments
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                <span>Integrations</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal">
                        <i class="bi bi-calendar3"></i>
                        Google Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal">
                        <i class="bi bi-folder"></i>
                        Google Drive
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal">
                        <i class="bi bi-file-earmark-text"></i>
                        Google Docs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                        <i class="bi bi-file-earmark-spreadsheet"></i>
                        Google Sheets
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-maps" data-bs-toggle="modal" data-bs-target="#mapsModal">
                        <i class="bi bi-geo-alt"></i>
                        Google Maps
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" id="gmail-link">
                        <i class="bi bi-envelope"></i>
                        Gmail
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-translate" data-bs-toggle="modal" data-bs-target="#translateModal">
                        <i class="bi bi-translate"></i>
                        Google Translate
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();">
                        <i class="bi bi-box-arrow-left"></i>
                        Back to Hub
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">Business Management Dashboard</h1>
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-primary">Share</button>
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle">
                    <i class="bi bi-calendar"></i>
                    This month
                </button>
            </div>
        </div>

        <!-- AI Insights Card -->
        <div class="card mb-4 card-border-primary">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-2">AI Business Insights</h5>
                    <span class="badge bg-primary"><i class="bi bi-robot"></i> AI Powered</span>
                </div>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="refresh-insights-btn" title="Refresh AI Insights">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                    <div class="dropdown d-inline-block ms-2">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="insightsDropdown" data-bs-toggle="dropdown" aria-expanded="false" title="Insights Settings">
                            <i class="bi bi-gear"></i>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="insightsDropdown">
                            <li><a class="dropdown-item" href="#" id="expand-all-insights">Expand All</a></li>
                            <li><a class="dropdown-item" href="#" id="collapse-all-insights">Collapse All</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="export-insights">Export Insights</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="text-muted">Based on your business data, here are some insights and recommendations:</p>
                <div class="row" id="ai-insights-container">
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6><i class="bi bi-graph-up text-success"></i> Loading insights...</h6>
                                <p class="small">Connecting to AI analytics engine...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="card dashboard-card text-white dashboard-card-primary">
                    <div class="card-body text-center">
                        <i class="bi bi-cash-coin card-icon"></i>
                        <h5 class="card-title">Revenue</h5>
                        <h2 class="card-text">$125,000</h2>
                        <p class="card-text">Current quarter</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card bg-danger text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-credit-card card-icon"></i>
                        <h5 class="card-title">Expenses</h5>
                        <h2 class="card-text">$78,000</h2>
                        <p class="card-text">Current quarter</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-graph-up card-icon"></i>
                        <h5 class="card-title">Profit</h5>
                        <h2 class="card-text">$47,000</h2>
                        <p class="card-text">Current quarter</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-people card-icon"></i>
                        <h5 class="card-title">Employees</h5>
                        <h2 class="card-text">42</h2>
                        <p class="card-text">Total staff</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Overview -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5>Financial Overview</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Budget</th>
                                    <th>Expenses</th>
                                    <th>Remaining</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Marketing</td>
                                    <td>$45,000</td>
                                    <td>$32,500</td>
                                    <td>$12,500</td>
                                    <td><span class="badge bg-success">On Track</span></td>
                                </tr>
                                <tr>
                                    <td>Operations</td>
                                    <td>$120,000</td>
                                    <td>$95,000</td>
                                    <td>$25,000</td>
                                    <td><span class="badge bg-warning">Caution</span></td>
                                </tr>
                                <tr>
                                    <td>Research & Development</td>
                                    <td>$75,000</td>
                                    <td>$42,000</td>
                                    <td>$33,000</td>
                                    <td><span class="badge bg-success">On Track</span></td>
                                </tr>
                                <tr>
                                    <td>Human Resources</td>
                                    <td>$35,000</td>
                                    <td>$33,500</td>
                                    <td>$1,500</td>
                                    <td><span class="badge bg-danger">At Risk</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Google Integrations -->
        <div class="row mt-4">
            <div class="col-12">
                <h4 class="mb-3">Google Integrations</h4>
            </div>

            <!-- Google Gmail -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component">
                    <div class="component-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-envelope me-2"></i> Gmail</h5>
                        <div class="component-actions">
                            <button class="btn btn-sm btn-outline-light" id="refresh-gmail" title="Refresh Gmail"><i class="bi bi-arrow-clockwise"></i></button>
                            <button class="btn btn-sm btn-outline-light" id="compose-email" onclick="document.getElementById('gmail-link').click();" title="Compose Email"><i class="bi bi-pencil"></i></button>
                        </div>
                    </div>
                    <div class="component-body" id="gmail-dashboard-body">
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center email-item" onclick="document.getElementById('gmail-link').click();">
                                        <div class="me-2 avatar-initials">JD</div>
                                        <div>
                                            <div class="fw-bold">John Davis</div>
                                            <div class="small text-truncate email-subject">Quarterly Financial Report - Please review the attached Q2 financial report</div>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary rounded-pill me-2">2h</span>
                                        <div class="btn-group btn-group-sm email-actions">
                                            <button type="button" class="btn btn-outline-primary" title="View" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-outline-success" title="Reply" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-reply"></i></button>
                                            <button type="button" class="btn btn-outline-info" title="Forward" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-outline-danger" title="Delete" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center email-item" onclick="document.getElementById('gmail-link').click();">
                                        <div class="me-2 avatar-initials">SW</div>
                                        <div>
                                            <div class="fw-bold">Sarah Wilson</div>
                                            <div class="small text-truncate email-subject">HR Policy Update - New employee handbook available</div>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary rounded-pill me-2">1d</span>
                                        <div class="btn-group btn-group-sm email-actions">
                                            <button type="button" class="btn btn-outline-primary" title="View" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-outline-success" title="Reply" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-reply"></i></button>
                                            <button type="button" class="btn btn-outline-info" title="Forward" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-outline-danger" title="Delete" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center email-item" onclick="document.getElementById('gmail-link').click();">
                                        <div class="me-2 avatar-initials">DC</div>
                                        <div>
                                            <div class="fw-bold">David Chen</div>
                                            <div class="small text-truncate email-subject">Marketing Campaign Results - Q2 campaign exceeded targets</div>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary rounded-pill me-2">2d</span>
                                        <div class="btn-group btn-group-sm email-actions">
                                            <button type="button" class="btn btn-outline-primary" title="View" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-outline-success" title="Reply" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-reply"></i></button>
                                            <button type="button" class="btn btn-outline-info" title="Forward" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-outline-danger" title="Delete" onclick="document.getElementById('gmail-link').click();"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="component-footer">
                        <button class="btn btn-outline-primary" type="button" onclick="document.getElementById('gmail-link').click();">
                            <i class="bi bi-envelope me-2"></i>Open Gmail
                        </button>
                    </div>
                </div>
            </div>

            <!-- Google Drive -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component">
                    <div class="component-header">
                        <h5><i class="bi bi-folder me-2"></i> Google Drive</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-light" id="refresh-drive" title="Refresh Drive"><i class="bi bi-arrow-clockwise"></i></button>
                            <button class="btn btn-sm btn-outline-light" id="upload-file" data-bs-toggle="modal" data-bs-target="#driveModal" title="Upload File"><i class="bi bi-upload"></i></button>
                        </div>
                    </div>
                    <div class="component-body">
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                        <div>
                                            <div class="fw-bold">Q2_Financial_Report.pdf</div>
                                            <div class="small text-muted">Modified: Yesterday</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewGoogleItem('drive', 'pdf', 'Q2_Financial_Report.pdf')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Download" onclick="downloadGoogleItem('drive', 'pdf', 'Q2_Financial_Report.pdf')"><i class="bi bi-download"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareGoogleItem('drive', 'pdf', 'Q2_Financial_Report.pdf')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteGoogleItem('drive', 'pdf', 'Q2_Financial_Report.pdf')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                        <div>
                                            <div class="fw-bold">Budget_Planning_2025.xlsx</div>
                                            <div class="small text-muted">Modified: 2 days ago</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewGoogleItem('drive', 'spreadsheet', 'Budget_Planning_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Download" onclick="downloadGoogleItem('drive', 'spreadsheet', 'Budget_Planning_2025.xlsx')"><i class="bi bi-download"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareGoogleItem('drive', 'spreadsheet', 'Budget_Planning_2025.xlsx')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteGoogleItem('drive', 'spreadsheet', 'Budget_Planning_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                        <div>
                                            <div class="fw-bold">Business_Strategy_2025.docx</div>
                                            <div class="small text-muted">Modified: 1 week ago</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewGoogleItem('drive', 'document', 'Business_Strategy_2025.docx')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Download" onclick="downloadGoogleItem('drive', 'document', 'Business_Strategy_2025.docx')"><i class="bi bi-download"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareGoogleItem('drive', 'document', 'Business_Strategy_2025.docx')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteGoogleItem('drive', 'document', 'Business_Strategy_2025.docx')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="component-footer">
                        <a href="#" class="btn btn-link p-0 border-0" data-bs-toggle="modal" data-bs-target="#driveModal">
                            View All Files <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Google Sheets -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component">
                    <div class="component-header">
                        <h5><i class="bi bi-file-earmark-spreadsheet me-2"></i> Google Sheets</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-light" id="refresh-sheets" title="Refresh Sheets"><i class="bi bi-arrow-clockwise"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-light" id="create-new-sheet" data-bs-toggle="modal" data-bs-target="#sheetsModal" title="Create New Sheet"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body">
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                        <div>
                                            <div class="fw-bold">Financial_Report_Q2_2025.xlsx</div>
                                            <div class="small text-muted">Modified: 2 days ago</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Financial_Report_Q2_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Download" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Financial_Report_Q2_2025.xlsx')"><i class="bi bi-download"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Financial_Report_Q2_2025.xlsx')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Financial_Report_Q2_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                        <div>
                                            <div class="fw-bold">Budget_Forecast_2025.xlsx</div>
                                            <div class="small text-muted">Modified: 1 week ago</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Budget_Forecast_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Download" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Budget_Forecast_2025.xlsx')"><i class="bi bi-download"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Budget_Forecast_2025.xlsx')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Budget_Forecast_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                        <div>
                                            <div class="fw-bold">Expense_Tracking_2025.xlsx</div>
                                            <div class="small text-muted">Modified: 2 weeks ago</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Expense_Tracking_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Download" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Expense_Tracking_2025.xlsx')"><i class="bi bi-download"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Expense_Tracking_2025.xlsx')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Expense_Tracking_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="component-footer">
                        <a href="#" data-bs-toggle="modal" data-bs-target="#sheetsModal">View All Sheets <i class="bi bi-arrow-right"></i></a>
                    </div>
                </div>
            </div>
            <!-- Google Docs -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component">
                    <div class="component-header">
                        <h5><i class="bi bi-file-earmark-text me-2"></i> Google Docs</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-light" id="refresh-docs" title="Refresh Documents"><i class="bi bi-arrow-clockwise"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-light" id="create-new-doc" data-bs-toggle="modal" data-bs-target="#docsModal" title="Create New Document"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body">
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                        <div>
                                            <div class="fw-bold">Business_Plan_2025.docx</div>
                                            <div class="small text-muted">Modified: Yesterday</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewGoogleItem('docs', 'document', 'Business_Plan_2025.docx')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Download" onclick="downloadGoogleItem('docs', 'document', 'Business_Plan_2025.docx')"><i class="bi bi-download"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareGoogleItem('docs', 'document', 'Business_Plan_2025.docx')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteGoogleItem('docs', 'document', 'Business_Plan_2025.docx')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                        <div>
                                            <div class="fw-bold">Marketing_Strategy_Q3.docx</div>
                                            <div class="small text-muted">Modified: 3 days ago</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewGoogleItem('docs', 'document', 'Marketing_Strategy_Q3.docx')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Download" onclick="downloadGoogleItem('docs', 'document', 'Marketing_Strategy_Q3.docx')"><i class="bi bi-download"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareGoogleItem('docs', 'document', 'Marketing_Strategy_Q3.docx')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteGoogleItem('docs', 'document', 'Marketing_Strategy_Q3.docx')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                        <div>
                                            <div class="fw-bold">Annual_Report_2024.docx</div>
                                            <div class="small text-muted">Modified: 1 week ago</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewGoogleItem('docs', 'document', 'Annual_Report_2024.docx')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Download" onclick="downloadGoogleItem('docs', 'document', 'Annual_Report_2024.docx')"><i class="bi bi-download"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareGoogleItem('docs', 'document', 'Annual_Report_2024.docx')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteGoogleItem('docs', 'document', 'Annual_Report_2024.docx')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="component-footer">
                        <a href="#" data-bs-toggle="modal" data-bs-target="#docsModal">View All Documents <i class="bi bi-arrow-right"></i></a>
                    </div>
                </div>
            </div>
            <!-- Google Calendar -->
            <div class="col-md-6 mb-4">
                <div class="google-integration-component">
                    <div class="component-header">
                        <h5><i class="bi bi-calendar3 me-2"></i> Google Calendar</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-light" id="refresh-calendar" title="Refresh Calendar"><i class="bi bi-arrow-clockwise"></i></button>
                            <button type="button" class="btn btn-sm btn-outline-light" id="create-new-event" data-bs-toggle="modal" data-bs-target="#calendarModal" title="Create New Event"><i class="bi bi-plus"></i></button>
                        </div>
                    </div>
                    <div class="component-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <button class="btn btn-sm btn-outline-secondary" id="prev-week" title="Previous Week"><i class="bi bi-chevron-left"></i></button>
                            <span id="current-week" class="fw-bold">June 10 - June 16, 2025</span>
                            <button class="btn btn-sm btn-outline-secondary" id="next-week" title="Next Week"><i class="bi bi-chevron-right"></i></button>
                        </div>
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="calendar-event-indicator bg-primary me-3"></div>
                                        <div>
                                            <div class="fw-bold">Quarterly Budget Review</div>
                                            <div class="small text-muted">June 12, 2025 • 10:00 AM - 11:30 AM</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewCalendarEvent('Quarterly Budget Review', '2025-06-12T10:00:00', '2025-06-12T11:30:00', 'Conference Room A', 'Review Q2 budget performance and projections for Q3')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Edit" onclick="editCalendarEvent('Quarterly Budget Review', '2025-06-12T10:00:00', '2025-06-12T11:30:00', 'Conference Room A', 'Review Q2 budget performance and projections for Q3')"><i class="bi bi-pencil"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareCalendarEvent('Quarterly Budget Review', '2025-06-12T10:00:00')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteCalendarEvent('Quarterly Budget Review', '2025-06-12T10:00:00')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="calendar-event-indicator bg-success me-3"></div>
                                        <div>
                                            <div class="fw-bold">Marketing Strategy Meeting</div>
                                            <div class="small text-muted">June 14, 2025 • 2:00 PM - 3:30 PM</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewCalendarEvent('Marketing Strategy Meeting', '2025-06-14T14:00:00', '2025-06-14T15:30:00', 'Meeting Room B', 'Discuss Q3 marketing campaigns and strategy')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Edit" onclick="editCalendarEvent('Marketing Strategy Meeting', '2025-06-14T14:00:00', '2025-06-14T15:30:00', 'Meeting Room B', 'Discuss Q3 marketing campaigns and strategy')"><i class="bi bi-pencil"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareCalendarEvent('Marketing Strategy Meeting', '2025-06-14T14:00:00')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteCalendarEvent('Marketing Strategy Meeting', '2025-06-14T14:00:00')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="calendar-event-indicator bg-warning me-3"></div>
                                        <div>
                                            <div class="fw-bold">Investor Call</div>
                                            <div class="small text-muted">June 15, 2025 • 11:00 AM - 12:00 PM</div>
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="View" onclick="viewCalendarEvent('Investor Call', '2025-06-15T11:00:00', '2025-06-15T12:00:00', 'Conference Call', 'Quarterly update with key investors')"><i class="bi bi-eye"></i></button>
                                        <button type="button" class="btn btn-outline-success" title="Edit" onclick="editCalendarEvent('Investor Call', '2025-06-15T11:00:00', '2025-06-15T12:00:00', 'Conference Call', 'Quarterly update with key investors')"><i class="bi bi-pencil"></i></button>
                                        <button type="button" class="btn btn-outline-info" title="Share" onclick="shareCalendarEvent('Investor Call', '2025-06-15T11:00:00')"><i class="bi bi-share"></i></button>
                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteCalendarEvent('Investor Call', '2025-06-15T11:00:00')"><i class="bi bi-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="component-footer">
                        <a href="#" data-bs-toggle="modal" data-bs-target="#calendarModal">View Full Calendar <i class="bi bi-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Google Calendar Modal -->
    <div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header app-modal-header">
                    <h5 class="modal-title" id="calendarModalLabel"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="calendarTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="calendar-view-tab" data-bs-toggle="tab" data-bs-target="#calendar-view-content" type="button" role="tab" aria-controls="calendar-view-content" aria-selected="true">Calendar View</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-event-tab" data-bs-toggle="tab" data-bs-target="#create-event-content" type="button" role="tab" aria-controls="create-event-content" aria-selected="false">Create Event</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="calendar-settings-tab" data-bs-toggle="tab" data-bs-target="#calendar-settings-content" type="button" role="tab" aria-controls="calendar-settings-content" aria-selected="false">Settings</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="calendarTabContent">
                        <!-- Calendar View Tab -->
                        <div class="tab-pane fade show active" id="calendar-view-content" role="tabpanel" aria-labelledby="calendar-view-tab">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <button type="button" class="btn btn-outline-secondary" id="prev-month" title="Previous Month"><i class="bi bi-chevron-left"></i></button>
                                    <button type="button" class="btn btn-outline-secondary" id="next-month" title="Next Month"><i class="bi bi-chevron-right"></i></button>
                                    <button type="button" class="btn btn-outline-primary ms-2" id="today-btn">Today</button>
                                </div>
                                <h4 id="calendar-month-year" class="mb-0">June 2025</h4>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-secondary active" id="month-view-btn">Month</button>
                                    <button type="button" class="btn btn-outline-secondary" id="week-view-btn">Week</button>
                                    <button type="button" class="btn btn-outline-secondary" id="day-view-btn">Day</button>
                                </div>
                            </div>

                            <!-- Calendar Grid -->
                            <div class="calendar-grid mb-3">
                                <div class="calendar-day-header">Sunday</div>
                                <div class="calendar-day-header">Monday</div>
                                <div class="calendar-day-header">Tuesday</div>
                                <div class="calendar-day-header">Wednesday</div>
                                <div class="calendar-day-header">Thursday</div>
                                <div class="calendar-day-header">Friday</div>
                                <div class="calendar-day-header">Saturday</div>

                                <!-- Week 1 -->
                                <div class="calendar-day">
                                    <div class="calendar-day-number">1</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">2</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">3</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">4</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">5</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">6</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">7</div>
                                </div>

                                <!-- Week 2 -->
                                <div class="calendar-day">
                                    <div class="calendar-day-number">8</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">9</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">10</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">11</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">12</div>
                                    <div class="calendar-event bg-primary" onclick="viewCalendarEvent('Quarterly Budget Review', '2025-06-12T10:00:00', '2025-06-12T11:30:00', 'Conference Room A', 'Review Q2 budget performance and projections for Q3')">
                                        10:00 AM - Quarterly Budget Review
                                    </div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">13</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">14</div>
                                    <div class="calendar-event bg-success" onclick="viewCalendarEvent('Marketing Strategy Meeting', '2025-06-14T14:00:00', '2025-06-14T15:30:00', 'Meeting Room B', 'Discuss Q3 marketing campaigns and strategy')">
                                        2:00 PM - Marketing Strategy Meeting
                                    </div>
                                </div>

                                <!-- Week 3 -->
                                <div class="calendar-day">
                                    <div class="calendar-day-number">15</div>
                                    <div class="calendar-event bg-warning" onclick="viewCalendarEvent('Investor Call', '2025-06-15T11:00:00', '2025-06-15T12:00:00', 'Conference Call', 'Quarterly update with key investors')">
                                        11:00 AM - Investor Call
                                    </div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">16</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">17</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">18</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">19</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">20</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">21</div>
                                </div>

                                <!-- Week 4 -->
                                <div class="calendar-day">
                                    <div class="calendar-day-number">22</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">23</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">24</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">25</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">26</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">27</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">28</div>
                                </div>

                                <!-- Week 5 -->
                                <div class="calendar-day">
                                    <div class="calendar-day-number">29</div>
                                </div>
                                <div class="calendar-day">
                                    <div class="calendar-day-number">30</div>
                                </div>
                                <div class="calendar-day calendar-day-outside">
                                    <div class="calendar-day-number text-muted">1</div>
                                </div>
                                <div class="calendar-day calendar-day-outside">
                                    <div class="calendar-day-number text-muted">2</div>
                                </div>
                                <div class="calendar-day calendar-day-outside">
                                    <div class="calendar-day-number text-muted">3</div>
                                </div>
                                <div class="calendar-day calendar-day-outside">
                                    <div class="calendar-day-number text-muted">4</div>
                                </div>
                                <div class="calendar-day calendar-day-outside">
                                    <div class="calendar-day-number text-muted">5</div>
                                </div>
                            </div>
                        </div>

                        <!-- Create Event Tab -->
                        <div class="tab-pane fade" id="create-event-content" role="tabpanel" aria-labelledby="create-event-tab">
                            <form id="create-event-form">
                                <div class="mb-3">
                                    <label for="event-title" class="form-label">Event Title</label>
                                    <input type="text" class="form-control" id="event-title" placeholder="Enter event title">
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="event-start-date" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="event-start-date">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="event-start-time" class="form-label">Start Time</label>
                                        <input type="time" class="form-control" id="event-start-time">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="event-end-date" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="event-end-date">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="event-end-time" class="form-label">End Time</label>
                                        <input type="time" class="form-control" id="event-end-time">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="event-location" class="form-label">Location</label>
                                    <input type="text" class="form-control" id="event-location" placeholder="Enter event location">
                                </div>
                                <div class="mb-3">
                                    <label for="event-description" class="form-label">Description</label>
                                    <textarea class="form-control" id="event-description" rows="3" placeholder="Enter event description"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="event-color" class="form-label">Color</label>
                                    <select class="form-select" id="event-color">
                                        <option value="primary" class="option-blue">Blue</option>
                                        <option value="success" class="option-green">Green</option>
                                        <option value="danger" class="option-red">Red</option>
                                        <option value="warning" class="option-yellow">Yellow</option>
                                        <option value="info" class="option-teal">Teal</option>
                                        <option value="secondary" class="option-gray">Gray</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="event-all-day">
                                        <label class="form-check-label" for="event-all-day">
                                            All day event
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="event-reminder" class="form-label">Reminder</label>
                                    <select class="form-select" id="event-reminder">
                                        <option value="0">None</option>
                                        <option value="5">5 minutes before</option>
                                        <option value="10">10 minutes before</option>
                                        <option value="15">15 minutes before</option>
                                        <option value="30">30 minutes before</option>
                                        <option value="60">1 hour before</option>
                                        <option value="120">2 hours before</option>
                                        <option value="1440">1 day before</option>
                                    </select>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="save-event-btn">
                                        <i class="bi bi-plus-circle me-2"></i>Create Event
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Settings Tab -->
                        <div class="tab-pane fade" id="calendar-settings-content" role="tabpanel" aria-labelledby="calendar-settings-tab">
                            <div class="mb-3">
                                <label for="default-calendar" class="form-label">Default Calendar</label>
                                <select class="form-select" id="default-calendar">
                                    <option value="primary">Primary Calendar</option>
                                    <option value="work">Work Calendar</option>
                                    <option value="personal">Personal Calendar</option>
                                    <option value="holidays">Holidays</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="default-view" class="form-label">Default View</label>
                                <select class="form-select" id="default-view">
                                    <option value="month">Month</option>
                                    <option value="week">Week</option>
                                    <option value="day">Day</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="start-day" class="form-label">Week Starts On</label>
                                <select class="form-select" id="start-day">
                                    <option value="0">Sunday</option>
                                    <option value="1">Monday</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="time-format" class="form-label">Time Format</label>
                                <select class="form-select" id="time-format">
                                    <option value="12">12-hour (AM/PM)</option>
                                    <option value="24">24-hour</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show-weekends" checked>
                                    <label class="form-check-label" for="show-weekends">
                                        Show weekends
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show-declined" checked>
                                    <label class="form-check-label" for="show-declined">
                                        Show declined events
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto-add-reminders">
                                    <label class="form-check-label" for="auto-add-reminders">
                                        Automatically add reminders to new events
                                    </label>
                                </div>
                            </div>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" id="save-settings-btn">
                                    <i class="bi bi-save me-2"></i>Save Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://calendar.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Google Calendar</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS and Popper.js (loaded at bottom of page) -->

     -->

    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle sidebar on mobile
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            // Google Translate functionality
            const translateTextBtn = document.getElementById('translate-text-btn');
            const translateDocumentBtn = document.getElementById('translate-document-btn');
            const translateWebsiteBtn = document.getElementById('translate-website-btn');
            const clearSourceBtn = document.getElementById('clear-source');
            const copySourceBtn = document.getElementById('copy-source');
            const copyTranslationBtn = document.getElementById('copy-translation');
            const listenTranslationBtn = document.getElementById('listen-translation');
            const sourceText = document.getElementById('source-text');
            const translatedText = document.getElementById('translated-text');

            if (translateTextBtn) {
                translateTextBtn.addEventListener('click', function() {
                    const text = sourceText.value.trim();
                    if (!text) {
                        alert('Please enter text to translate');
                        return;
                    }

                    const sourceLang = document.getElementById('source-language').value;
                    const targetLang = document.getElementById('target-language').value;

                    // Simulate translation (in a real app, this would call an API)
                    translatedText.value = simulateTranslation(text, sourceLang, targetLang);
                });
            }

            if (translateDocumentBtn) {
                translateDocumentBtn.addEventListener('click', function() {
                    const fileInput = document.getElementById('document-file');
                    if (!fileInput.files || fileInput.files.length === 0) {
                        alert('Please select a document to translate');
                        return;
                    }

                    const file = fileInput.files[0];
                    const sourceLang = document.getElementById('doc-source-language').value;
                    const targetLang = document.getElementById('doc-target-language').value;

                    // Show loading state
                    translateDocumentBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Translating...';
                    translateDocumentBtn.disabled = true;

                    // Simulate API call
                    setTimeout(() => {
                        // Reset button state
                        translateDocumentBtn.innerHTML = '<i class="bi bi-translate"></i> Translate Document';
                        translateDocumentBtn.disabled = false;

                        // Show result
                        document.getElementById('document-result').style.display = 'block';
                    }, 2000);
                });
            }

            if (translateWebsiteBtn) {
                translateWebsiteBtn.addEventListener('click', function() {
                    const url = document.getElementById('website-url').value.trim();
                    if (!url) {
                        alert('Please enter a website URL');
                        return;
                    }

                    const sourceLang = document.getElementById('website-source-language').value;
                    const targetLang = document.getElementById('website-target-language').value;

                    // In a real app, this would redirect to Google Translate website
                    alert(`Translating ${url} from ${sourceLang} to ${targetLang}. In a real application, this would open the translated website in a new tab.`);
                });
            }

            if (clearSourceBtn) {
                clearSourceBtn.addEventListener('click', function() {
                    sourceText.value = '';
                    sourceText.focus();
                });
            }

            if (copySourceBtn) {
                copySourceBtn.addEventListener('click', function() {
                    sourceText.select();
                    document.execCommand('copy');
                    alert('Source text copied to clipboard');
                });
            }

            if (copyTranslationBtn) {
                copyTranslationBtn.addEventListener('click', function() {
                    translatedText.select();
                    document.execCommand('copy');
                    alert('Translation copied to clipboard');
                });
            }

            if (listenTranslationBtn) {
                listenTranslationBtn.addEventListener('click', function() {
                    const text = translatedText.value.trim();
                    if (!text) {
                        alert('No translation to listen to');
                        return;
                    }

                    // In a real app, this would use the Web Speech API
                    alert('In a real application, this would use text-to-speech to read the translation aloud.');
                });
            }

            // Add event listeners to the recently translated website buttons
            document.querySelectorAll('#website-content .list-group-item button').forEach(button => {
                button.addEventListener('click', function() {
                    const website = this.closest('.list-group-item').querySelector('span').textContent;
                    alert(`Opening translated version of ${website}. In a real application, this would open the translated website in a new tab.`);
                });
            });

            // Add event listener to the download translated document button
            const downloadTranslatedDocBtn = document.getElementById('download-translated-doc');
            if (downloadTranslatedDocBtn) {
                downloadTranslatedDocBtn.addEventListener('click', function() {
                    alert('In a real application, this would download the translated document.');
                });
            }

            // Google Sheets functionality
            const refreshSheetsBtn = document.getElementById('refresh-sheets');
            if (refreshSheetsBtn) {
                refreshSheetsBtn.addEventListener('click', function() {
                    alert('Refreshing sheets... In a real application, this would refresh the list of Google Sheets.');
                });
            }

            const createSheetBtn = document.getElementById('create-sheet-btn');
            if (createSheetBtn) {
                createSheetBtn.addEventListener('click', function() {
                    const title = document.getElementById('sheet-title').value.trim();
                    const template = document.getElementById('sheet-template').value;

                    if (!title) {
                        alert('Please enter a title for your sheet');
                        return;
                    }

                    alert(`Creating new sheet "${title}" using the ${template} template. In a real application, this would create a new Google Sheet.`);

                    // Reset form
                    document.getElementById('sheet-title').value = '';
                });
            }

            const importSheetBtn = document.getElementById('import-sheet-btn');
            if (importSheetBtn) {
                importSheetBtn.addEventListener('click', function() {
                    const title = document.getElementById('import-title').value.trim();
                    const fileInput = document.getElementById('import-file');
                    const hasHeaders = document.getElementById('import-first-row').checked;

                    if (!title) {
                        alert('Please enter a title for your imported sheet');
                        return;
                    }

                    if (!fileInput.files || fileInput.files.length === 0) {
                        alert('Please select a file to import');
                        return;
                    }

                    const file = fileInput.files[0];
                    alert(`Importing "${file.name}" as "${title}". Headers: ${hasHeaders ? 'Yes' : 'No'}. In a real application, this would import the file to Google Sheets.`);

                    // Reset form
                    document.getElementById('import-title').value = '';
                    document.getElementById('import-file').value = '';
                    document.getElementById('import-first-row').checked = false;
                });
            }

            // Google Docs functionality
            const refreshDocsBtn = document.getElementById('refresh-docs');
            if (refreshDocsBtn) {
                refreshDocsBtn.addEventListener('click', function() {
                    alert('Refreshing documents... In a real application, this would refresh the list of Google Docs.');
                });
            }

            const createDocBtn = document.getElementById('create-doc-btn');
            if (createDocBtn) {
                createDocBtn.addEventListener('click', function() {
                    const title = document.getElementById('doc-title').value.trim();
                    const template = document.getElementById('doc-template').value;

                    if (!title) {
                        alert('Please enter a title for your document');
                        return;
                    }

                    alert(`Creating new document "${title}" using the ${template} template. In a real application, this would create a new Google Doc.`);

                    // Reset form
                    document.getElementById('doc-title').value = '';
                });
            }

            const importDocBtn = document.getElementById('import-doc-btn');
            if (importDocBtn) {
                importDocBtn.addEventListener('click', function() {
                    const title = document.getElementById('import-doc-title').value.trim();
                    const fileInput = document.getElementById('import-doc-file');
                    const convertFormat = document.getElementById('docs-convert-to-google-format').checked;

                    if (!title) {
                        alert('Please enter a title for your imported document');
                        return;
                    }

                    if (!fileInput.files || fileInput.files.length === 0) {
                        alert('Please select a file to import');
                        return;
                    }

                    const file = fileInput.files[0];
                    alert(`Importing "${file.name}" as "${title}". Convert to Google format: ${convertFormat ? 'Yes' : 'No'}. In a real application, this would import the file to Google Docs.`);

                    // Reset form
                    document.getElementById('import-doc-title').value = '';
                    document.getElementById('import-doc-file').value = '';
                    document.getElementById('docs-convert-to-google-format').checked = false;
                });
            }

            // Google Calendar functionality
            const refreshCalendarBtn = document.getElementById('refresh-calendar');
            if (refreshCalendarBtn) {
                refreshCalendarBtn.addEventListener('click', function() {
                    alert('Refreshing calendar... In a real application, this would refresh the list of calendar events.');
                });
            }

            const prevWeekBtn = document.getElementById('prev-week');
            if (prevWeekBtn) {
                prevWeekBtn.addEventListener('click', function() {
                    alert('Navigating to previous week... In a real application, this would show the previous week\'s events.');
                });
            }

            const nextWeekBtn = document.getElementById('next-week');
            if (nextWeekBtn) {
                nextWeekBtn.addEventListener('click', function() {
                    alert('Navigating to next week... In a real application, this would show the next week\'s events.');
                });
            }

            // Calendar Modal functionality
            const prevMonthBtn = document.getElementById('prev-month');
            if (prevMonthBtn) {
                prevMonthBtn.addEventListener('click', function() {
                    alert('Navigating to previous month... In a real application, this would show the previous month\'s calendar.');
                });
            }

            const nextMonthBtn = document.getElementById('next-month');
            if (nextMonthBtn) {
                nextMonthBtn.addEventListener('click', function() {
                    alert('Navigating to next month... In a real application, this would show the next month\'s calendar.');
                });
            }

            const todayBtn = document.getElementById('today-btn');
            if (todayBtn) {
                todayBtn.addEventListener('click', function() {
                    alert('Navigating to today... In a real application, this would show today\'s calendar view.');
                });
            }

            const monthViewBtn = document.getElementById('month-view-btn');
            if (monthViewBtn) {
                monthViewBtn.addEventListener('click', function() {
                    alert('Switching to month view... In a real application, this would show the month view of the calendar.');
                });
            }

            const weekViewBtn = document.getElementById('week-view-btn');
            if (weekViewBtn) {
                weekViewBtn.addEventListener('click', function() {
                    alert('Switching to week view... In a real application, this would show the week view of the calendar.');
                });
            }

            const dayViewBtn = document.getElementById('day-view-btn');
            if (dayViewBtn) {
                dayViewBtn.addEventListener('click', function() {
                    alert('Switching to day view... In a real application, this would show the day view of the calendar.');
                });
            }

            const saveEventBtn = document.getElementById('save-event-btn');
            if (saveEventBtn) {
                saveEventBtn.addEventListener('click', function() {
                    const title = document.getElementById('event-title').value.trim();
                    const startDate = document.getElementById('event-start-date').value;
                    const startTime = document.getElementById('event-start-time').value;
                    const endDate = document.getElementById('event-end-date').value;
                    const endTime = document.getElementById('event-end-time').value;
                    const location = document.getElementById('event-location').value.trim();
                    const description = document.getElementById('event-description').value.trim();
                    const color = document.getElementById('event-color').value;
                    const allDay = document.getElementById('event-all-day').checked;
                    const reminder = document.getElementById('event-reminder').value;

                    if (!title) {
                        alert('Please enter a title for your event');
                        return;
                    }

                    if (!startDate) {
                        alert('Please select a start date for your event');
                        return;
                    }

                    if (!allDay && !startTime) {
                        alert('Please select a start time for your event or mark it as an all-day event');
                        return;
                    }

                    if (!endDate) {
                        alert('Please select an end date for your event');
                        return;
                    }

                    if (!allDay && !endTime) {
                        alert('Please select an end time for your event or mark it as an all-day event');
                        return;
                    }

                    alert(`Creating event "${title}" from ${startDate} ${allDay ? '(all day)' : startTime} to ${endDate} ${allDay ? '(all day)' : endTime}. In a real application, this would create a new Google Calendar event.`);

                    // Reset form
                    document.getElementById('event-title').value = '';
                    document.getElementById('event-start-date').value = '';
                    document.getElementById('event-start-time').value = '';
                    document.getElementById('event-end-date').value = '';
                    document.getElementById('event-end-time').value = '';
                    document.getElementById('event-location').value = '';
                    document.getElementById('event-description').value = '';
                    document.getElementById('event-all-day').checked = false;
                });
            }

            const saveSettingsBtn = document.getElementById('save-settings-btn');
            if (saveSettingsBtn) {
                saveSettingsBtn.addEventListener('click', function() {
                    const defaultCalendar = document.getElementById('default-calendar').value;
                    const defaultView = document.getElementById('default-view').value;
                    const startDay = document.getElementById('start-day').value;
                    const timeFormat = document.getElementById('time-format').value;
                    const showWeekends = document.getElementById('show-weekends').checked;
                    const showDeclined = document.getElementById('show-declined').checked;
                    const autoAddReminders = document.getElementById('auto-add-reminders').checked;

                    alert(`Saving calendar settings. In a real application, these settings would be saved to your Google Calendar preferences.`);
                });
            }
        });

        // Google Sheets functions
        function viewGoogleItem(app, type, id) {
            console.log(`Viewing ${app} ${type} with ID: ${id}`);

            // Create a file viewer modal if it doesn't exist
            let fileViewerModal = document.getElementById('fileViewerModal');
            if (!fileViewerModal) {
                fileViewerModal = document.createElement('div');
                fileViewerModal.id = 'fileViewerModal';
                fileViewerModal.className = 'modal fade';
                fileViewerModal.setAttribute('tabindex', '-1');
                fileViewerModal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
                fileViewerModal.setAttribute('aria-hidden', 'true');

                fileViewerModal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header app-modal-header">
                                <h5 class="modal-title" id="fileViewerModalLabel"></h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="fileViewerContent" class="p-2"></div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary" onclick="openInNewTab()"><i class="bi bi-eye"></i> View</button>
                                    <button type="button" class="btn btn-outline-success" onclick="downloadCurrentFile()"><i class="bi bi-download"></i> Download</button>
                                    <button type="button" class="btn btn-outline-info" onclick="shareCurrentFile()"><i class="bi bi-share"></i> Share</button>
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteCurrentFile()"><i class="bi bi-trash"></i> Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(fileViewerModal);
            }

            // Set the current file information
            window.currentFile = {
                app: app,
                type: type,
                id: id
            };

            // Set the title and content
            const title = document.getElementById('fileViewerModalLabel');
            const content = document.getElementById('fileViewerContent');

            if (title && content) {
                // Set the title with appropriate icon
                let iconClass = 'bi-file-earmark';
                if (type === 'spreadsheet') {
                    iconClass = 'bi-file-earmark-spreadsheet text-success';
                } else if (type === 'document') {
                    iconClass = 'bi-file-earmark-text text-primary';
                } else if (type === 'presentation') {
                    iconClass = 'bi-file-earmark-slides text-warning';
                }

                title.innerHTML = `<i class="bi ${iconClass}"></i> ${id}`;

                // Set the content based on the file type
                if (type === 'spreadsheet') {
                    content.innerHTML = `
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            This is a preview of the spreadsheet. In a real application, this would display the actual spreadsheet content.
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Column A</th>
                                        <th scope="col">Column B</th>
                                        <th scope="col">Column C</th>
                                        <th scope="col">Column D</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <th scope="row">1</th>
                                        <td>Data 1A</td>
                                        <td>Data 1B</td>
                                        <td>Data 1C</td>
                                        <td>Data 1D</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">2</th>
                                        <td>Data 2A</td>
                                        <td>Data 2B</td>
                                        <td>Data 2C</td>
                                        <td>Data 2D</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">3</th>
                                        <td>Data 3A</td>
                                        <td>Data 3B</td>
                                        <td>Data 3C</td>
                                        <td>Data 3D</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    `;
                } else {
                    content.innerHTML = `
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            This is a preview of the file. In a real application, this would display the actual file content.
                        </div>
                        <div class="p-3 bg-light">
                            <p>Sample content for ${id}</p>
                            <p>This would show the actual content of the file in a real application.</p>
                        </div>
                    `;
                }

                // Show the modal
                const modal = new bootstrap.Modal(fileViewerModal);
                modal.show();
            }
        }

        function downloadGoogleItem(app, type, id) {
            console.log(`Downloading ${app} ${type} with ID: ${id}`);

            // Create a temporary link element
            const link = document.createElement('a');
            link.href = '#'; // In a real app, this would be the download URL
            link.download = id;

            // Simulate download by showing alert
            alert(`Download started for ${id}`);

            // In a real app, this would trigger the download
            // document.body.appendChild(link);
            // link.click();
            // document.body.removeChild(link);
        }

        function shareGoogleItem(app, type, id) {
            console.log(`Sharing ${app} ${type} with ID: ${id}`);

            // Create a sharing modal if it doesn't exist
            let sharingModal = document.getElementById('sharingModal');
            if (!sharingModal) {
                sharingModal = document.createElement('div');
                sharingModal.id = 'sharingModal';
                sharingModal.className = 'modal fade';
                sharingModal.setAttribute('tabindex', '-1');
                sharingModal.setAttribute('aria-labelledby', 'sharingModalLabel');
                sharingModal.setAttribute('aria-hidden', 'true');

                sharingModal.innerHTML = `
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header app-modal-header">
                                <h5 class="modal-title" id="sharingModalLabel"><i class="bi bi-share"></i> Share File</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="share-email" class="form-label">Email address</label>
                                    <input type="email" class="form-control" id="share-email" placeholder="Enter email address">
                                </div>
                                <div class="mb-3">
                                    <label for="share-permission" class="form-label">Permission</label>
                                    <select class="form-select" id="share-permission">
                                        <option value="view">Can view</option>
                                        <option value="comment">Can comment</option>
                                        <option value="edit">Can edit</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="share-message" class="form-label">Message (optional)</label>
                                    <textarea class="form-control" id="share-message" rows="3" placeholder="Add a message"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" onclick="confirmShare()">Share</button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(sharingModal);
            }

            // Set the current file information for sharing
            window.fileToShare = {
                app: app,
                type: type,
                id: id
            };

            // Update the modal title
            const title = document.getElementById('sharingModalLabel');
            if (title) {
                title.innerHTML = `<i class="bi bi-share"></i> Share ${id}`;
            }

            // Show the modal
            const modal = new bootstrap.Modal(sharingModal);
            modal.show();
        }

        function confirmShare() {
            const email = document.getElementById('share-email').value.trim();
            const permission = document.getElementById('share-permission').value;

            if (!email) {
                alert('Please enter an email address');
                return;
            }

            if (window.fileToShare) {
                alert(`${window.fileToShare.id} has been shared with ${email} with ${permission} permission.`);

                // Close the modal
                const sharingModal = document.getElementById('sharingModal');
                if (sharingModal) {
                    const modal = bootstrap.Modal.getInstance(sharingModal);
                    if (modal) {
                        modal.hide();
                    }
                }

                // Reset the form
                document.getElementById('share-email').value = '';
                document.getElementById('share-message').value = '';
            }
        }

        function deleteGoogleItem(app, type, id) {
            console.log(`Deleting ${app} ${type} with ID: ${id}`);
            if (confirm(`Are you sure you want to delete ${id}?`)) {
                alert(`${id} has been deleted.`);

                // In a real app, this would remove the item from the list
                // For demonstration, we'll just log it
                console.log(`${id} deleted successfully`);
            }
        }

        // Functions for the file viewer modal
        function openInNewTab() {
            if (window.currentFile) {
                // Open in a new tab
                window.open(`https://docs.google.com/${window.currentFile.app}/d/example/${window.currentFile.id}`, '_blank');
            }
        }

        function viewCurrentFile() {
            if (window.currentFile) {
                // Open in a new tab
                openInNewTab();
            }
        }

        function downloadCurrentFile() {
            if (window.currentFile) {
                downloadGoogleItem(window.currentFile.app, window.currentFile.type, window.currentFile.id);
            }
        }

        function shareCurrentFile() {
            if (window.currentFile) {
                // Close the viewer modal first
                const fileViewerModal = document.getElementById('fileViewerModal');
                if (fileViewerModal) {
                    const modal = bootstrap.Modal.getInstance(fileViewerModal);
                    if (modal) {
                        modal.hide();
                    }
                }

                // Then open the sharing modal
                setTimeout(() => {
                    shareGoogleItem(window.currentFile.app, window.currentFile.type, window.currentFile.id);
                }, 500);
            }
        }

        function deleteCurrentFile() {
            if (window.currentFile) {
                // Close the viewer modal first
                const fileViewerModal = document.getElementById('fileViewerModal');
                if (fileViewerModal) {
                    const modal = bootstrap.Modal.getInstance(fileViewerModal);
                    if (modal) {
                        modal.hide();
                    }
                }

                // Then delete the file
                setTimeout(() => {
                    deleteGoogleItem(window.currentFile.app, window.currentFile.type, window.currentFile.id);
                }, 500);
            }
        }

        // Calendar event functions
        function viewCalendarEvent(title, startTime, endTime, location, description) {
            console.log(`Viewing calendar event: ${title}`);

            // Create an event viewer modal if it doesn't exist
            let eventViewerModal = document.getElementById('eventViewerModal');
            if (!eventViewerModal) {
                eventViewerModal = document.createElement('div');
                eventViewerModal.id = 'eventViewerModal';
                eventViewerModal.className = 'modal fade';
                eventViewerModal.setAttribute('tabindex', '-1');
                eventViewerModal.setAttribute('aria-labelledby', 'eventViewerModalLabel');
                eventViewerModal.setAttribute('aria-hidden', 'true');

                eventViewerModal.innerHTML = `
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header app-modal-header">
                                <h5 class="modal-title" id="eventViewerModalLabel"></h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="eventViewerContent" class="p-2"></div>
                            </div>
                            <div class="modal-footer">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-success" onclick="editCurrentEvent()"><i class="bi bi-pencil"></i> Edit</button>
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteCurrentEvent()"><i class="bi bi-trash"></i> Delete</button>
                                </div>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(eventViewerModal);
            }

            // Set the current event information
            window.currentEvent = {
                title: title,
                startTime: startTime,
                endTime: endTime,
                location: location,
                description: description
            };

            // Format dates for display
            const startDate = new Date(startTime);
            const endDate = new Date(endTime);
            const formattedStartDate = startDate.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
            const formattedStartTime = startDate.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
            const formattedEndDate = endDate.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
            const formattedEndTime = endDate.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            // Set the title and content
            const modalTitle = document.getElementById('eventViewerModalLabel');
            const content = document.getElementById('eventViewerContent');

            if (modalTitle && content) {
                modalTitle.innerHTML = `<i class="bi bi-calendar-event"></i> ${title}`;

                content.innerHTML = `
                    <div class="mb-3">
                        <div class="fw-bold mb-1"><i class="bi bi-clock me-2"></i>When</div>
                        <div>${formattedStartDate} at ${formattedStartTime} to</div>
                        <div>${formattedEndDate} at ${formattedEndTime}</div>
                    </div>
                    ${location ? `
                    <div class="mb-3">
                        <div class="fw-bold mb-1"><i class="bi bi-geo-alt me-2"></i>Where</div>
                        <div>${location}</div>
                    </div>
                    ` : ''}
                    ${description ? `
                    <div class="mb-3">
                        <div class="fw-bold mb-1"><i class="bi bi-card-text me-2"></i>Description</div>
                        <div>${description}</div>
                    </div>
                    ` : ''}
                `;

                // Show the modal
                const modal = new bootstrap.Modal(eventViewerModal);
                modal.show();
            }
        }

        function editCalendarEvent(title, startTime, endTime, location, description) {
            console.log(`Editing calendar event: ${title}`);

            // Create an event editor modal if it doesn't exist
            let eventEditorModal = document.getElementById('eventEditorModal');
            if (!eventEditorModal) {
                eventEditorModal = document.createElement('div');
                eventEditorModal.id = 'eventEditorModal';
                eventEditorModal.className = 'modal fade';
                eventEditorModal.setAttribute('tabindex', '-1');
                eventEditorModal.setAttribute('aria-labelledby', 'eventEditorModalLabel');
                eventEditorModal.setAttribute('aria-hidden', 'true');

                eventEditorModal.innerHTML = `
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header app-modal-header">
                                <h5 class="modal-title" id="eventEditorModalLabel"><i class="bi bi-pencil-square"></i> Edit Event</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="edit-event-form">
                                    <div class="mb-3">
                                        <label for="edit-event-title" class="form-label">Event Title</label>
                                        <input type="text" class="form-control" id="edit-event-title">
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="edit-event-start-date" class="form-label">Start Date</label>
                                            <input type="date" class="form-control" id="edit-event-start-date">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="edit-event-start-time" class="form-label">Start Time</label>
                                            <input type="time" class="form-control" id="edit-event-start-time">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="edit-event-end-date" class="form-label">End Date</label>
                                            <input type="date" class="form-control" id="edit-event-end-date">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="edit-event-end-time" class="form-label">End Time</label>
                                            <input type="time" class="form-control" id="edit-event-end-time">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit-event-location" class="form-label">Location</label>
                                        <input type="text" class="form-control" id="edit-event-location">
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit-event-description" class="form-label">Description</label>
                                        <textarea class="form-control" id="edit-event-description" rows="3"></textarea>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="update-event-btn">Update Event</button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(eventEditorModal);

                // Add event listener for the update button
                const updateEventBtn = document.getElementById('update-event-btn');
                if (updateEventBtn) {
                    updateEventBtn.addEventListener('click', function() {
                        const updatedTitle = document.getElementById('edit-event-title').value.trim();
                        const updatedStartDate = document.getElementById('edit-event-start-date').value;
                        const updatedStartTime = document.getElementById('edit-event-start-time').value;
                        const updatedEndDate = document.getElementById('edit-event-end-date').value;
                        const updatedEndTime = document.getElementById('edit-event-end-time').value;
                        const updatedLocation = document.getElementById('edit-event-location').value.trim();
                        const updatedDescription = document.getElementById('edit-event-description').value.trim();

                        if (!updatedTitle) {
                            alert('Please enter a title for your event');
                            return;
                        }

                        if (!updatedStartDate || !updatedStartTime) {
                            alert('Please select a start date and time for your event');
                            return;
                        }

                        if (!updatedEndDate || !updatedEndTime) {
                            alert('Please select an end date and time for your event');
                            return;
                        }

                        alert(`Event "${updatedTitle}" has been updated. In a real application, this would update the event in Google Calendar.`);

                        // Close the modal
                        const modal = bootstrap.Modal.getInstance(eventEditorModal);
                        if (modal) {
                            modal.hide();
                        }
                    });
                }
            }

            // Set the current event information
            window.currentEvent = {
                title: title,
                startTime: startTime,
                endTime: endTime,
                location: location,
                description: description
            };

            // Format dates for form fields
            const startDate = new Date(startTime);
            const endDate = new Date(endTime);
            const formattedStartDate = startDate.toISOString().split('T')[0];
            const formattedStartTime = startDate.toTimeString().substring(0, 5);
            const formattedEndDate = endDate.toISOString().split('T')[0];
            const formattedEndTime = endDate.toTimeString().substring(0, 5);

            // Set the form values
            document.getElementById('edit-event-title').value = title;
            document.getElementById('edit-event-start-date').value = formattedStartDate;
            document.getElementById('edit-event-start-time').value = formattedStartTime;
            document.getElementById('edit-event-end-date').value = formattedEndDate;
            document.getElementById('edit-event-end-time').value = formattedEndTime;
            document.getElementById('edit-event-location').value = location || '';
            document.getElementById('edit-event-description').value = description || '';

            // Show the modal
            const modal = new bootstrap.Modal(eventEditorModal);
            modal.show();
        }

        function deleteCalendarEvent(title, startTime) {
            console.log(`Deleting calendar event: ${title}`);
            if (confirm(`Are you sure you want to delete the event "${title}"?`)) {
                alert(`Event "${title}" has been deleted. In a real application, this would delete the event from Google Calendar.`);
            }
        }

        function shareCalendarEvent(title, startTime) {
            console.log(`Sharing calendar event: ${title}`);
            alert(`Sharing event "${title}". In a real application, this would open a sharing dialog for Google Calendar.`);
        }

        function editCurrentEvent() {
            if (window.currentEvent) {
                // Close the viewer modal first
                const eventViewerModal = document.getElementById('eventViewerModal');
                if (eventViewerModal) {
                    const modal = bootstrap.Modal.getInstance(eventViewerModal);
                    if (modal) {
                        modal.hide();
                    }
                }

                // Then open the editor modal
                setTimeout(() => {
                    editCalendarEvent(
                        window.currentEvent.title,
                        window.currentEvent.startTime,
                        window.currentEvent.endTime,
                        window.currentEvent.location,
                        window.currentEvent.description
                    );
                }, 500);
            }
        }

        function deleteCurrentEvent() {
            if (window.currentEvent) {
                // Close the viewer modal first
                const eventViewerModal = document.getElementById('eventViewerModal');
                if (eventViewerModal) {
                    const modal = bootstrap.Modal.getInstance(eventViewerModal);
                    if (modal) {
                        modal.hide();
                    }
                }

                // Then delete the event
                setTimeout(() => {
                    deleteCalendarEvent(window.currentEvent.title, window.currentEvent.startTime);
                }, 500);
            }
        }

        // Helper function to simulate translation
        function simulateTranslation(text, sourceLang, targetLang) {
            // In a real app, this would call a translation API
            // This is just a simple simulation
            if (sourceLang === targetLang) {
                return text;
            }

            // Simple simulation - just add a prefix to show it's "translated"
            return `[${sourceLang} to ${targetLang}] ${text}`;
        }
    </script>

    <!-- Google Drive Modal -->
    <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header app-modal-header">
                    <h5 class="modal-title" id="driveModalLabel"><i class="bi bi-folder"></i> Google Drive</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="driveTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files-content" type="button" role="tab" aria-controls="files-content" aria-selected="true">Files</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button" role="tab" aria-controls="upload-content" aria-selected="false">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="shared-tab" data-bs-toggle="tab" data-bs-target="#shared-content" type="button" role="tab" aria-controls="shared-content" aria-selected="false">Shared with me</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="driveTabContent">
                        <!-- Files Tab -->
                        <div class="tab-pane fade show active" id="files-content" role="tabpanel" aria-labelledby="files-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="file-search-drive" placeholder="Search files...">
                                        <button class="btn btn-outline-secondary" type="button" id="search-files-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="file-sort" class="form-label visually-hidden">Sort files</label>
                                    <select class="form-select" id="file-sort" title="Sort files" aria-label="Sort files">
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="date-desc">Newest First</option>
                                        <option value="date-asc">Oldest First</option>
                                        <option value="size-desc">Largest First</option>
                                        <option value="size-asc">Smallest First</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="file-filter" class="form-label visually-hidden">Filter files</label>
                                    <select class="form-select" id="file-filter" title="Filter files" aria-label="Filter files">
                                        <option value="all">All Files</option>
                                        <option value="folders">Folders</option>
                                        <option value="documents">Documents</option>
                                        <option value="spreadsheets">Spreadsheets</option>
                                        <option value="presentations">Presentations</option>
                                        <option value="images">Images</option>
                                        <option value="pdfs">PDFs</option>
                                    </select>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="drive-files-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                                    <span>Financial Reports</span>
                                                </div>
                                            </td>
                                            <td>Yesterday</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-folder-fill text-primary me-2"></i>
                                                    <span>Business Plans</span>
                                                </div>
                                            </td>
                                            <td>3 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                                    <span>Financial_Report_Q2_2025.pdf</span>
                                                </div>
                                            </td>
                                            <td>Yesterday</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Budget_Planning_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                    <span>Business_Strategy_2025.docx</span>
                                                </div>
                                            </td>
                                            <td>1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Upload Tab -->
                        <div class="tab-pane fade" id="upload-content" role="tabpanel" aria-labelledby="upload-tab">
                            <div class="row">
                                <div class="col-md-7">
                                    <div class="upload-area p-5 mb-3 text-center dropzone-style" id="dropzone">
                                        <i class="bi bi-cloud-upload fs-1 text-muted mb-3"></i>
                                        <h5>Drag & Drop Files Here</h5>
                                        <p class="text-muted">or</p>
                                        <label for="file-upload" class="btn btn-primary">
                                            Browse Files
                                        </label>
                                        <input id="file-upload" type="file" multiple class="hidden-file-input">
                                        <p class="text-muted mt-3 small">Maximum file size: 50MB</p>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="card">
                                        <div class="card-header">
                                            Upload Options
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="upload-folder" class="form-label">Destination folder</label>
                                                <select class="form-select" id="upload-folder">
                                                    <option selected>My Drive</option>
                                                    <option>Financial Reports</option>
                                                    <option>Business Plans</option>
                                                    <option>Team Shared Folder</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="drive-convert-to-google-format">
                                                    <label class="form-check-label" for="drive-convert-to-google-format">
                                                        Convert to Google format
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="share-with-team">
                                                    <label class="form-check-label" for="share-with-team">
                                                        Share with team
                                                    </label>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-primary" id="upload-file-btn">
                                                <i class="bi bi-upload me-2"></i>Upload Files
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shared with me Tab -->
                        <div class="tab-pane fade" id="shared-content" role="tabpanel" aria-labelledby="shared-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="shared-search" placeholder="Search shared files...">
                                        <button class="btn btn-outline-secondary" type="button" id="search-shared-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="shared-sort" class="form-label visually-hidden">Sort shared files</label>
                                    <select class="form-select" id="shared-sort" title="Sort shared files" aria-label="Sort shared files">
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="date-desc">Newest First</option>
                                        <option value="date-asc">Oldest First</option>
                                        <option value="shared-desc">Recently Shared</option>
                                        <option value="shared-asc">Oldest Shared</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="shared-filter" class="form-label visually-hidden">Filter shared files</label>
                                    <select class="form-select" id="shared-filter" title="Filter shared files" aria-label="Filter shared files">
                                        <option value="all">All Files</option>
                                        <option value="documents">Documents</option>
                                        <option value="spreadsheets">Spreadsheets</option>
                                        <option value="presentations">Presentations</option>
                                        <option value="images">Images</option>
                                        <option value="pdfs">PDFs</option>
                                    </select>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Shared By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="shared-files-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                                    <span>Company_Policy_Update.pdf</span>
                                                </div>
                                            </td>
                                            <td>John Davis - 3 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Department_Budget_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>Sarah Wilson - 1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                    <span>Strategic_Plan_2025-2030.docx</span>
                                                </div>
                                            </td>
                                            <td>David Chen - 2 weeks ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://drive.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Google Drive</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Translate Modal -->
    <div class="modal fade" id="translateModal" tabindex="-1" aria-labelledby="translateModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header app-modal-header">
                    <h5 class="modal-title" id="translateModalLabel"><i class="bi bi-translate"></i> Google Translate</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="translateTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="text-tab" data-bs-toggle="tab" data-bs-target="#text-content" type="button" role="tab" aria-controls="text-content" aria-selected="true">Text</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="document-tab" data-bs-toggle="tab" data-bs-target="#document-content" type="button" role="tab" aria-controls="document-content" aria-selected="false">Document</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="website-tab" data-bs-toggle="tab" data-bs-target="#website-content" type="button" role="tab" aria-controls="website-content" aria-selected="false">Website</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="translateTabContent">
                        <!-- Text Tab -->
                        <div class="tab-pane fade show active" id="text-content" role="tabpanel" aria-labelledby="text-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="source-language" class="form-label">From</label>
                                    <select class="form-select" id="source-language">
                                        <option value="auto" selected>Detect language</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="target-language" class="form-label">To</label>
                                    <select class="form-select" id="target-language">
                                        <option value="en" selected>English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="source-text" class="form-label">Source Text</label>
                                        <textarea class="form-control" id="source-text" rows="8" placeholder="Enter text to translate"></textarea>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-source">
                                            <i class="bi bi-x-lg"></i> Clear
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="copy-source">
                                            <i class="bi bi-clipboard"></i> Copy
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="translated-text" class="form-label">Translated Text</label>
                                        <textarea class="form-control" id="translated-text" rows="8" readonly></textarea>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="listen-translation">
                                            <i class="bi bi-volume-up"></i> Listen
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="copy-translation">
                                            <i class="bi bi-clipboard"></i> Copy
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="d-grid gap-2 mt-3">
                                <button type="button" class="btn btn-primary" id="translate-text-btn">
                                    <i class="bi bi-translate"></i> Translate
                                </button>
                            </div>
                        </div>

                        <!-- Document Tab -->
                        <div class="tab-pane fade" id="document-content" role="tabpanel" aria-labelledby="document-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="doc-source-language" class="form-label">From</label>
                                    <select class="form-select" id="doc-source-language">
                                        <option value="auto" selected>Detect language</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="doc-target-language" class="form-label">To</label>
                                    <select class="form-select" id="doc-target-language">
                                        <option value="en" selected>English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="document-file" class="form-label">Upload Document</label>
                                <input class="form-control" type="file" id="document-file">
                                <div class="form-text">Supported formats: .docx, .pptx, .xlsx, .pdf, .txt</div>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" id="translate-document-btn">
                                    <i class="bi bi-translate"></i> Translate Document
                                </button>
                            </div>
                            <div class="mt-4" id="document-result">
                                <h5>Translation Result</h5>
                                <div class="alert alert-success">
                                    <i class="bi bi-check-circle-fill me-2"></i> Document translated successfully!
                                </div>
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-success" id="download-translated-doc">
                                        <i class="bi bi-download"></i> Download Translated Document
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Website Tab -->
                        <div class="tab-pane fade" id="website-content" role="tabpanel" aria-labelledby="website-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="website-source-language" class="form-label">From</label>
                                    <select class="form-select" id="website-source-language">
                                        <option value="auto" selected>Detect language</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="website-target-language" class="form-label">To</label>
                                    <select class="form-select" id="website-target-language">
                                        <option value="en" selected>English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="website-url" class="form-label">Website URL</label>
                                <div class="input-group">
                                    <input type="url" class="form-control" id="website-url" placeholder="https://example.com">
                                    <button class="btn btn-primary" type="button" id="translate-website-btn">
                                        <i class="bi bi-translate"></i> Translate
                                    </button>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle-fill me-2"></i> The translated website will open in a new tab.
                                </div>
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Recently Translated Websites</h6>
                                    </div>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <span>example.com</span>
                                                <small class="text-muted d-block">Spanish to English</small>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-outline-primary">Open</button>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <span>businessnews.com</span>
                                                <small class="text-muted d-block">French to English</small>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-outline-primary">Open</button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://translate.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Google Translate</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Attachments Modal -->
    <div class="modal fade" id="attachmentsModal" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header app-modal-header">
                    <h5 class="modal-title" id="attachmentsModalLabel"><i class="bi bi-paperclip"></i> Attachments</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="attachmentsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="attachments-files-tab" data-bs-toggle="tab" data-bs-target="#attachments-files-content" type="button" role="tab" aria-controls="attachments-files-content" aria-selected="true">Files</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="attachments-upload-tab" data-bs-toggle="tab" data-bs-target="#attachments-upload-content" type="button" role="tab" aria-controls="attachments-upload-content" aria-selected="false">Upload</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="attachmentsTabContent">
                        <!-- Files Tab -->
                        <div class="tab-pane fade show active" id="attachments-files-content" role="tabpanel" aria-labelledby="attachments-files-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group max-width-300">
                                    <input type="text" id="attachment-search" class="form-control" placeholder="Search files...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>Size</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Financial_Report_Q2_2025.pdf</td>
                                            <td>PDF</td>
                                            <td>2.4 MB</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" onclick="viewAttachment('pdf', 'Financial_Report_Q2_2025.pdf')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-outline-success" onclick="downloadAttachment('Financial_Report_Q2_2025.pdf')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-outline-info" onclick="shareFile('Financial_Report_Q2_2025.pdf')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteAttachment('Financial_Report_Q2_2025.pdf')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Budget_Planning_2025.xlsx</td>
                                            <td>Spreadsheet</td>
                                            <td>1.8 MB</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" onclick="viewAttachment('spreadsheet', 'Budget_Planning_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-outline-success" onclick="downloadAttachment('Budget_Planning_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-outline-info" onclick="shareFile('Budget_Planning_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteAttachment('Budget_Planning_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Business_Strategy_2025.docx</td>
                                            <td>Document</td>
                                            <td>1.2 MB</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" onclick="viewAttachment('document', 'Business_Strategy_2025.docx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-outline-success" onclick="downloadAttachment('Business_Strategy_2025.docx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-outline-info" onclick="shareFile('Business_Strategy_2025.docx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteAttachment('Business_Strategy_2025.docx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Upload Tab -->
                        <div class="tab-pane fade" id="attachments-upload-content" role="tabpanel" aria-labelledby="attachments-upload-tab">
                            <div class="upload-area p-5 mb-3 text-center border rounded" id="upload-dropzone">
                                <i class="bi bi-cloud-upload fs-1 text-muted mb-3"></i>
                                <h5>Drag & Drop Files Here</h5>
                                <p class="text-muted">or</p>
                                <button type="button" class="btn btn-primary" id="upload-attachment-btn">
                                    Browse Files
                                </button>
                                <p class="text-muted mt-3 small">Maximum file size: 50MB</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Sheets Modal -->
    <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header app-modal-header">
                    <h5 class="modal-title" id="sheetsModalLabel"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="sheetsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="sheets-tab" data-bs-toggle="tab" data-bs-target="#sheets-content" type="button" role="tab" aria-controls="sheets-content" aria-selected="true">My Sheets</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create-content" type="button" role="tab" aria-controls="create-content" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import-content" type="button" role="tab" aria-controls="import-content" aria-selected="false">Import</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="sheetsTabContent">
                        <!-- My Sheets Tab -->
                        <div class="tab-pane fade show active" id="sheets-content" role="tabpanel" aria-labelledby="sheets-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="sheets-search" placeholder="Search sheets...">
                                        <button class="btn btn-outline-secondary" type="button" id="search-sheets-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="sheets-sort" class="form-label visually-hidden">Sort sheets</label>
                                    <select class="form-select" id="sheets-sort" title="Sort sheets" aria-label="Sort sheets">
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="date-desc">Newest First</option>
                                        <option value="date-asc">Oldest First</option>
                                        <option value="size-desc">Largest First</option>
                                        <option value="size-asc">Smallest First</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="sheets-filter" class="form-label visually-hidden">Filter sheets</label>
                                    <select class="form-select" id="sheets-filter" title="Filter sheets" aria-label="Filter sheets">
                                        <option value="all">All Files</option>
                                        <option value="spreadsheets">Spreadsheets</option>
                                        <option value="templates">Templates</option>
                                        <option value="shared">Shared</option>
                                    </select>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sheets-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Financial_Report_Q2_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Budget_Forecast_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Expense_Tracking_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>2 weeks ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Create New Tab -->
                        <div class="tab-pane fade" id="create-content" role="tabpanel" aria-labelledby="create-tab">
                            <form id="create-sheet-form">
                                <div class="mb-3">
                                    <label for="sheet-title" class="form-label">Sheet Title</label>
                                    <input type="text" class="form-control" id="sheet-title" placeholder="Enter a title for your sheet">
                                </div>
                                <div class="mb-3">
                                    <label for="sheet-template" class="form-label">Template</label>
                                    <select class="form-select" id="sheet-template">
                                        <option value="blank">Blank</option>
                                        <option value="financial">Financial Report</option>
                                        <option value="budget">Budget Tracker</option>
                                        <option value="expense">Expense Tracker</option>
                                        <option value="invoice">Invoice Template</option>
                                    </select>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="create-sheet-btn">
                                        <i class="bi bi-plus-circle me-2"></i>Create Sheet
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Import Tab -->
                        <div class="tab-pane fade" id="import-content" role="tabpanel" aria-labelledby="import-tab">
                            <form id="import-sheet-form">
                                <div class="mb-3">
                                    <label for="import-title" class="form-label">Sheet Title</label>
                                    <input type="text" class="form-control" id="import-title" placeholder="Enter a title for your imported sheet">
                                </div>
                                <div class="mb-3">
                                    <label for="import-file" class="form-label">File to Import</label>
                                    <input class="form-control" type="file" id="import-file" accept=".xlsx,.xls,.csv,.ods">
                                    <div class="form-text">Supported formats: Excel (.xlsx, .xls), CSV, OpenDocument Spreadsheet (.ods)</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="import-first-row">
                                        <label class="form-check-label" for="import-first-row">
                                            First row contains column headers
                                        </label>
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="import-sheet-btn">
                                        <i class="bi bi-upload me-2"></i>Import Sheet
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://sheets.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Google Sheets</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Docs Modal -->
    <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header app-modal-header">
                    <h5 class="modal-title" id="docsModalLabel"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="docsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents-content" type="button" role="tab" aria-controls="documents-content" aria-selected="true">My Documents</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-doc-tab" data-bs-toggle="tab" data-bs-target="#create-doc-content" type="button" role="tab" aria-controls="create-doc-content" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-doc-tab" data-bs-toggle="tab" data-bs-target="#import-doc-content" type="button" role="tab" aria-controls="import-doc-content" aria-selected="false">Import</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="docsTabContent">
                        <!-- My Documents Tab -->
                        <div class="tab-pane fade show active" id="documents-content" role="tabpanel" aria-labelledby="documents-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="docs-search" placeholder="Search documents...">
                                        <button class="btn btn-outline-secondary" type="button" id="search-docs-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="docs-sort" class="form-label visually-hidden">Sort documents</label>
                                    <select class="form-select" id="docs-sort" title="Sort documents" aria-label="Sort documents">
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="date-desc">Newest First</option>
                                        <option value="date-asc">Oldest First</option>
                                        <option value="size-desc">Largest First</option>
                                        <option value="size-asc">Smallest First</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="docs-filter" class="form-label visually-hidden">Filter documents</label>
                                    <select class="form-select" id="docs-filter" title="Filter documents" aria-label="Filter documents">
                                        <option value="all">All Files</option>
                                        <option value="documents">Documents</option>
                                        <option value="templates">Templates</option>
                                        <option value="shared">Shared</option>
                                        <option value="recent">Recent</option>
                                    </select>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="docs-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                    <span>Business_Plan_2025.docx</span>
                                                </div>
                                            </td>
                                            <td>Yesterday</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                    <span>Marketing_Strategy_Q3.docx</span>
                                                </div>
                                            </td>
                                            <td>3 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                    <span>Annual_Report_2024.docx</span>
                                                </div>
                                            </td>
                                            <td>1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Create New Tab -->
                        <div class="tab-pane fade" id="create-doc-content" role="tabpanel" aria-labelledby="create-doc-tab">
                            <form id="create-doc-form">
                                <div class="mb-3">
                                    <label for="doc-title" class="form-label">Document Title</label>
                                    <input type="text" class="form-control" id="doc-title" placeholder="Enter a title for your document">
                                </div>
                                <div class="mb-3">
                                    <label for="doc-template" class="form-label">Template</label>
                                    <select class="form-select" id="doc-template">
                                        <option value="blank">Blank</option>
                                        <option value="business-plan">Business Plan</option>
                                        <option value="report">Report</option>
                                        <option value="letter">Business Letter</option>
                                        <option value="proposal">Project Proposal</option>
                                    </select>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="create-doc-btn">
                                        <i class="bi bi-plus-circle me-2"></i>Create Document
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Import Tab -->
                        <div class="tab-pane fade" id="import-doc-content" role="tabpanel" aria-labelledby="import-doc-tab">
                            <form id="import-doc-form">
                                <div class="mb-3">
                                    <label for="import-doc-title" class="form-label">Document Title</label>
                                    <input type="text" class="form-control" id="import-doc-title" placeholder="Enter a title for your imported document">
                                </div>
                                <div class="mb-3">
                                    <label for="import-doc-file" class="form-label">File to Import</label>
                                    <input class="form-control" type="file" id="import-doc-file" accept=".docx,.doc,.txt,.rtf,.odt">
                                    <div class="form-text">Supported formats: Word (.docx, .doc), Text (.txt), Rich Text (.rtf), OpenDocument (.odt)</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="docs-convert-to-google-format">
                                        <label class="form-check-label" for="docs-convert-to-google-format">
                                            Convert to Google Docs format
                                        </label>
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="import-doc-btn">
                                        <i class="bi bi-upload me-2"></i>Import Document
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://docs.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Google Docs</a>
                </div>
            </div>
        </div>
    </div>



    <script>
        function openEmailContent(sender, subject, content) {
            const readTab = document.getElementById('read-tab');
            const emailReader = document.getElementById('email-reader');

            if (readTab && emailReader) {
                // Switch to read tab
                readTab.click();

                // Set email content
                emailReader.innerHTML = `
                    <div class="email-header mb-3">
                        <h5>${subject}</h5>
                        <p class="text-muted">From: ${sender}</p>
                    </div>
                    <div class="email-content">
                        <p>${content}</p>
                    </div>
                `;
            }
        }

        function replyToEmail(sender, subject) {
            openComposeTab();
            setTimeout(() => {
                const toField = document.getElementById('compose-to');
                const subjectField = document.getElementById('compose-subject');
                if (toField) toField.value = sender;
                if (subjectField) subjectField.value = 'Re: ' + subject;
            }, 500);
        }

        function forwardEmail(sender, subject) {
            openComposeTab();
            setTimeout(() => {
                const subjectField = document.getElementById('compose-subject');
                if (subjectField) subjectField.value = 'Fwd: ' + subject;
            }, 500);
        }

        function deleteEmail(sender, subject) {
            if (confirm(`Delete email from ${sender}?`)) {
                alert('Email deleted successfully');
            }
        }

        function testAlert() {
            alert('Test button works! If you can see this alert but not the Gmail modal, there might be a CSS issue.');
        }

        // Gmail functionality setup

        // Gmail search, sort, and filter functionality is now handled by the modern system
        // in google-drive-actions.js - initializeGmailSearch() function

        // Removed problematic attachment modal functions that were causing stuck modals

        // Email expansion and reply functionality
        function toggleEmailContent(emailId) {
            const content = document.getElementById(`email-content-${emailId}`);
            const icon = document.getElementById(`expand-icon-${emailId}`);

            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.classList.remove('bi-chevron-down');
                icon.classList.add('bi-chevron-up');
            } else {
                content.style.display = 'none';
                icon.classList.remove('bi-chevron-up');
                icon.classList.add('bi-chevron-down');

                // Hide reply form when collapsing
                hideReplyForm(emailId);
            }
        }

        function showReplyForm(emailId) {
            const replyForm = document.getElementById(`reply-form-${emailId}`);
            if (replyForm) {
                replyForm.style.display = 'block';

                // Focus on the message textarea
                const textarea = replyForm.querySelector('textarea');
                if (textarea) {
                    setTimeout(() => textarea.focus(), 100);
                }
            }
        }

        function showReplyAllForm(emailId) {
            showReplyForm(emailId);
            // In a real implementation, this would modify the To field to include all recipients
            alert('Reply All functionality would include all recipients in the To field.');
        }

        function showForwardForm(emailId) {
            // Switch to compose tab with forward content
            const composeTab = document.getElementById('compose-tab');
            if (composeTab) {
                composeTab.click();

                setTimeout(() => {
                    const subjectField = document.getElementById('compose-subject');
                    const bodyField = document.getElementById('compose-body');

                    if (subjectField) {
                        subjectField.value = 'Fwd: Quarterly Financial Report';
                    }

                    if (bodyField) {
                        bodyField.value = '\n\n---------- Forwarded message ----------\nFrom: John Davis <<EMAIL>>\nSubject: Quarterly Financial Report\n\nDear Team,\n\nPlease review the attached Q2 financial report and provide feedback by Friday...';
                    }
                }, 300);
            }
        }

        function hideReplyForm(emailId) {
            const replyForm = document.getElementById(`reply-form-${emailId}`);
            if (replyForm) {
                replyForm.style.display = 'none';

                // Clear the textarea
                const textarea = replyForm.querySelector('textarea');
                if (textarea) {
                    textarea.value = '';
                }
            }
        }

        function sendReply(emailId) {
            const replyForm = document.getElementById(`reply-form-${emailId}`);
            const textarea = replyForm.querySelector('textarea');

            if (textarea && textarea.value.trim()) {
                alert(`Reply sent successfully!\n\nMessage: ${textarea.value.trim()}`);
                hideReplyForm(emailId);
            } else {
                alert('Please enter a message before sending.');
                textarea.focus();
            }
        }

        // Note: openDriveFilesTab function removed - now using proper Bootstrap modal attributes
    </script>

    <!-- Google Item Functions -->
    <script>
        // Function to create a file viewer modal
        function createFileViewerModal() {
            // Create the modal element
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'fileViewerModal';
            modal.tabIndex = '-1';
            modal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
            modal.setAttribute('aria-hidden', 'true');

            // Set the modal content
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header app-modal-header">
                            <h5 class="modal-title" id="fileViewerTitle"></h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="fileViewerContent"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" onclick="viewCurrentFile()"><i class="bi bi-eye"></i> View</button>
                            <button type="button" class="btn btn-outline-primary" onclick="downloadCurrentFile()"><i class="bi bi-download"></i> Download</button>
                            <button type="button" class="btn btn-outline-info" onclick="shareCurrentFile()"><i class="bi bi-share"></i> Share</button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteCurrentFile()"><i class="bi bi-trash"></i> Delete</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Add the modal to the document
            document.body.appendChild(modal);

            return modal;
        }

        // Function to get file type icon
        function getFileTypeIcon(type) {
            switch (type.toLowerCase()) {
                case 'pdf':
                    return 'bi-file-earmark-pdf-fill text-danger';
                case 'document':
                case 'doc':
                case 'docx':
                    return 'bi-file-earmark-word-fill text-primary';
                case 'spreadsheet':
                case 'xls':
                case 'xlsx':
                    return 'bi-file-earmark-excel-fill text-success';
                case 'presentation':
                case 'ppt':
                case 'pptx':
                    return 'bi-file-earmark-ppt-fill text-warning';
                case 'image':
                case 'img':
                case 'jpg':
                case 'jpeg':
                case 'png':
                    return 'bi-file-earmark-image-fill text-info';
                default:
                    return 'bi-file-earmark-fill text-secondary';
            }
        }

        // Function to get attachment content
        function getAttachmentContent(type, fileName) {
            if (type === 'pdf') {
                if (fileName.includes('Product_Demo_Feedback')) {
                    return `<div class="text-center">
                            <div class="border p-3 mt-3 app-bg-light">
                                <i class="bi bi-file-earmark-pdf-fill text-danger display-1 mb-3"></i>
                                <h4 class="mt-3">${fileName}</h4>
                                <div class="text-start mt-4">
                                    <h5>Product Demo Feedback Report</h5>
                                    <p class="fw-bold">Executive Summary</p>
                                    <p>Thank you for the product demonstration yesterday. Our team was impressed with the features and capabilities of your CRM system.</p>
                                    <p class="fw-bold">Key Observations</p>
                                    <ul>
                                        <li>User interface is intuitive and well-designed</li>
                                        <li>Integration capabilities exceed our requirements</li>
                                        <li>AI-powered analytics are impressive</li>
                                        <li>Mobile responsiveness is excellent</li>
                                    </ul>
                                    <p class="fw-bold">Next Steps</p>
                                    <p>We have a few questions about customization options and pricing tiers that we'd like to discuss further. Would you be available for a follow-up call next week?</p>
                                </div>
                            </div>
                            </div>`;
                } else {
                    return `<div class="text-center">
                            <div class="border p-3 mt-3 app-bg-light">
                                <i class="bi bi-file-earmark-pdf-fill text-danger display-1 mb-3"></i>
                                <h4 class="mt-3">${fileName}</h4>
                                <p>This is a PDF viewer. In a real application, the PDF would be displayed here.</p>
                            </div>
                            </div>`;
                }
            } else if (type === 'document' || type === 'doc' || type === 'docx') {
                if (fileName.includes('Requirements_List')) {
                    return `<div class="text-center">
                            <div class="border p-3 mt-3 app-bg-light">
                                <i class="bi bi-file-earmark-word-fill text-primary display-1 mb-3"></i>
                                <h4 class="mt-3">${fileName}</h4>
                                <div class="text-start mt-4">
                                    <h5>System Requirements List</h5>
                                    <p class="fw-bold">Functional Requirements</p>
                                    <ol>
                                        <li>User authentication and authorization system</li>
                                        <li>Customer relationship management module</li>
                                        <li>Sales pipeline tracking and reporting</li>
                                        <li>Email integration and automation</li>
                                        <li>Document management system</li>
                                        <li>Real-time analytics dashboard</li>
                                    </ol>
                                    <p class="fw-bold">Technical Requirements</p>
                                    <ul>
                                        <li>Cloud-based deployment</li>
                                        <li>Mobile-responsive design</li>
                                        <li>API integration capabilities</li>
                                        <li>Data backup and recovery</li>
                                        <li>SSL encryption for security</li>
                                    </ul>
                                    <p class="fw-bold">Performance Requirements</p>
                                    <ul>
                                        <li>Support for 500+ concurrent users</li>
                                        <li>Page load times under 3 seconds</li>
                                        <li>99.9% uptime availability</li>
                                    </ul>
                                </div>
                            </div>
                            </div>`;
                } else {
                    return `<div class="text-center">
                            <div class="border p-3 mt-3 app-bg-light">
                                <i class="bi bi-file-earmark-word-fill text-primary display-1 mb-3"></i>
                                <h4 class="mt-3">${fileName}</h4>
                                <p>This is a document viewer. In a real application, the document would be displayed here.</p>
                            </div>
                            </div>`;
                }
            } else if (type === 'spreadsheet' || type === 'xls' || type === 'xlsx') {
                return `<div class="text-center">
                        <div class="border p-3 mt-3 app-bg-light">
                            <i class="bi bi-file-earmark-excel-fill text-success display-1 mb-3"></i>
                            <h4 class="mt-3">${fileName}</h4>
                            <p>This is a spreadsheet viewer. In a real application, the spreadsheet would be displayed here.</p>
                            <div class="table-responsive mt-3">
                                <table class="table table-bordered table-sm">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>A</th>
                                            <th>B</th>
                                            <th>C</th>
                                            <th>D</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <th>1</th>
                                            <td>Data</td>
                                            <td>Data</td>
                                            <td>Data</td>
                                            <td>Data</td>
                                        </tr>
                                        <tr>
                                            <th>2</th>
                                            <td>Data</td>
                                            <td>Data</td>
                                            <td>Data</td>
                                            <td>Data</td>
                                        </tr>
                                        <tr>
                                            <th>3</th>
                                            <td>Data</td>
                                            <td>Data</td>
                                            <td>Data</td>
                                            <td>Data</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        </div>`;
            } else if (type === 'image' || type === 'img' || type === 'jpg' || type === 'jpeg' || type === 'png') {
                return `<div class="text-center">
                        <div class="border p-3 mt-3 app-bg-light">
                            <i class="bi bi-file-earmark-image-fill text-info display-1 mb-3"></i>
                            <h4 class="mt-3">${fileName}</h4>
                            <p>This is an image viewer. In a real application, the image would be displayed here.</p>
                        </div>
                        </div>`;
            } else {
                return `<div class="text-center">
                        <i class="bi bi-file-earmark-fill text-secondary display-1 mb-3"></i>
                        <h4 class="mt-3">${fileName}</h4>
                        <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                            <p>Preview not available for this file type.</p>
                        </div>
                        </div>`;
            }
        }

        // Function to view an attachment
        function viewAttachment(type, fileName) {
            console.log(`viewAttachment called with type: ${type}, fileName: ${fileName}`);

            try {
                // Create a file viewer modal if it doesn't exist
                let fileViewerModal = document.getElementById('fileViewerModal');
                if (!fileViewerModal) {
                    console.log('Creating new file viewer modal');
                    fileViewerModal = createFileViewerModal();
                }

                // Set the file information
                const fileTitle = document.getElementById('fileViewerTitle');
                const fileContent = document.getElementById('fileViewerContent');

                if (!fileTitle || !fileContent) {
                    console.error('Modal elements not found');
                    return;
                }

                // Set the title based on the file type and name
                fileTitle.innerHTML = `<i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}`;
                fileTitle.setAttribute('data-file-type', type);

                // Set the content based on the file type
                fileContent.innerHTML = getAttachmentContent(type, fileName);

                // Store current file info for download/share/delete operations
                window.currentFile = {
                    name: fileName,
                    type: type
                };

                // Show the modal
                const modal = new bootstrap.Modal(fileViewerModal);
                modal.show();

                console.log(`Successfully opened viewer for ${fileName}`);
            } catch (error) {
                console.error('Error in viewAttachment:', error);
                alert(`Error opening file viewer: ${error.message}`);
            }
        }

        // Make functions globally accessible
        window.viewAttachment = viewAttachment;
        window.createFileViewerModal = createFileViewerModal;
        window.getFileTypeIcon = getFileTypeIcon;
        window.getAttachmentContent = getAttachmentContent;

        // Function to download an attachment
        function downloadAttachment(fileName) {
            console.log(`Downloading ${fileName}...`);
            alert(`Downloading ${fileName}...`);
            // In a real application, this would trigger a download of the file
        }

        // Function to share a file
        function shareFile(fileName) {
            console.log(`Sharing ${fileName}...`);
            alert(`Sharing ${fileName}...`);
            // In a real application, this would open a sharing dialog
        }

        // Function to delete an attachment
        function deleteAttachment(fileName) {
            if (confirm(`Are you sure you want to delete ${fileName}?`)) {
                alert(`${fileName} has been deleted.`);
                // In a real application, this would delete the file
            }
        }

        // Make these functions globally accessible too
        window.downloadAttachment = downloadAttachment;
        window.shareFile = shareFile;
        window.deleteAttachment = deleteAttachment;
        window.shareCurrentFile = shareCurrentFile;
        window.downloadCurrentFile = downloadCurrentFile;
        window.viewCurrentFile = viewCurrentFile;
        window.deleteCurrentFile = deleteCurrentFile;

        // Function to share the current file
        function shareCurrentFile() {
            if (window.currentFile) {
                shareFile(window.currentFile.name);
            }
        }

        // Function to download the current file
        function downloadCurrentFile() {
            if (window.currentFile) {
                downloadAttachment(window.currentFile.name);
            }
        }

        // Function to view the current file (refresh the view)
        function viewCurrentFile() {
            if (window.currentFile) {
                console.log(`Refreshing view for ${window.currentFile.name}`);
                // Refresh the content in the current modal
                const fileContent = document.getElementById('fileViewerContent');
                if (fileContent) {
                    fileContent.innerHTML = getAttachmentContent(window.currentFile.type, window.currentFile.name);
                }
            }
        }

        // Function to delete the current file
        function deleteCurrentFile() {
            if (window.currentFile) {
                // Close the viewer modal first
                const fileViewerModal = document.getElementById('fileViewerModal');
                if (fileViewerModal) {
                    const modal = bootstrap.Modal.getInstance(fileViewerModal);
                    if (modal) {
                        modal.hide();
                    }
                }

                // Then delete the file
                setTimeout(() => {
                    deleteAttachment(window.currentFile.name);
                }, 500);
            }
        }

        // View a Google item (Drive, Sheets, Docs)
        function viewGoogleItem(app, type, filename) {
            console.log(`Viewing ${app} item: ${filename} (${type})`);

            // Create a file viewer modal
            let modalTitle = 'View File';
            let fileIcon = 'bi-file-earmark';
            let fileClass = '';

            // Set appropriate icon and title based on file type
            if (type === 'pdf') {
                fileIcon = 'bi-file-earmark-pdf';
                fileClass = 'text-danger';
                modalTitle = 'PDF Viewer';
            } else if (type === 'spreadsheet') {
                fileIcon = 'bi-file-earmark-spreadsheet';
                fileClass = 'text-success';
                modalTitle = 'Spreadsheet Viewer';
            } else if (type === 'document') {
                fileIcon = 'bi-file-earmark-text';
                fileClass = 'text-primary';
                modalTitle = 'Document Viewer';
            }

            // Create file viewer content
            const modalContent = `
                <div class="file-viewer">
                    <div class="text-center mb-4">
                        <i class="bi ${fileIcon} ${fileClass} display-1 mb-3"></i>
                        <h4>${filename}</h4>
                    </div>
                    <div class="file-content p-3 border rounded bg-light">
                        ${getFileContent(app, type, filename)}
                    </div>
                    <div class="d-flex justify-content-between mt-3">
                        <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-dismiss="modal">
                            Close
                        </button>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary" onclick="viewGoogleItem('${app}', '${type}', '${filename}')">
                                <i class="bi bi-eye"></i> View
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="downloadGoogleItem('${app}', '${type}', '${filename}')">
                                <i class="bi bi-download"></i> Download
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="shareGoogleItem('${app}', '${type}', '${filename}')">
                                <i class="bi bi-share"></i> Share
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteGoogleItem('${app}', '${type}', '${filename}')">
                                <i class="bi bi-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Show the file viewer modal
            showFileViewerModal(modalTitle, modalContent);
        }

        /**
         * Show a modal for file viewing
         * @param {string} title - The modal title
         * @param {string} content - The modal content HTML
         */
        function showFileViewerModal(title, content) {
            // Create a unique ID for the modal
            const modalId = 'file-viewer-modal-' + Date.now();

            // Create the modal HTML
            const modalHtml = `
                <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}-label" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                                <h5 class="modal-title" id="${modalId}-label">${title}</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add the modal to the document
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Initialize and show the modal
            const modalElement = document.getElementById(modalId);
            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();

                // Remove the modal from the DOM after it's hidden
                modalElement.addEventListener('hidden.bs.modal', function() {
                    modalElement.remove();
                });
            }
        }

        /**
         * Get file content based on file type and name
         * @param {string} app - The app (drive, sheets, docs)
         * @param {string} type - The file type (pdf, spreadsheet, document)
         * @param {string} filename - The filename
         * @returns {string} - The file content HTML
         */
        function getFileContent(app, type, filename) {
            // This would normally fetch the actual file content from an API
            // For now, we'll simulate different content based on file type

            if (type === 'pdf') {
                if (filename.includes('Financial_Report')) {
                    return `
                        <h5>Q2 Financial Report</h5>
                        <p class="fw-bold">Executive Summary</p>
                        <p>The second quarter of 2025 showed strong financial performance across all business units. Revenue increased by 15% compared to Q1, while operating expenses were reduced by 8%, resulting in a profit margin improvement to 22%.</p>
                        <p class="fw-bold">Key Financial Metrics</p>
                        <ul>
                            <li>Total Revenue: $24.5M (↑15% from Q1)</li>
                            <li>Gross Profit: $14.7M (↑18% from Q1)</li>
                            <li>Operating Expenses: $9.2M (↓8% from Q1)</li>
                            <li>Net Income: $5.5M (↑32% from Q1)</li>
                            <li>Profit Margin: 22% (↑3% from Q1)</li>
                        </ul>
                    `;
                } else {
                    return `<p>PDF content for ${filename}</p>`;
                }
            } else if (type === 'spreadsheet') {
                if (filename.includes('Budget_Planning')) {
                    return `
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Q1 Budget</th>
                                    <th>Q2 Budget</th>
                                    <th>Q3 Budget</th>
                                    <th>Q4 Budget</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Marketing</td>
                                    <td>$1.2M</td>
                                    <td>$1.5M</td>
                                    <td>$1.8M</td>
                                    <td>$2.0M</td>
                                    <td>$6.5M</td>
                                </tr>
                                <tr>
                                    <td>Sales</td>
                                    <td>$2.0M</td>
                                    <td>$2.2M</td>
                                    <td>$2.5M</td>
                                    <td>$2.8M</td>
                                    <td>$9.5M</td>
                                </tr>
                                <tr>
                                    <td>R&D</td>
                                    <td>$3.0M</td>
                                    <td>$3.2M</td>
                                    <td>$3.5M</td>
                                    <td>$3.8M</td>
                                    <td>$13.5M</td>
                                </tr>
                                <tr>
                                    <td>Operations</td>
                                    <td>$1.8M</td>
                                    <td>$1.9M</td>
                                    <td>$2.0M</td>
                                    <td>$2.1M</td>
                                    <td>$7.8M</td>
                                </tr>
                                <tr>
                                    <td><strong>Total</strong></td>
                                    <td><strong>$8.0M</strong></td>
                                    <td><strong>$8.8M</strong></td>
                                    <td><strong>$9.8M</strong></td>
                                    <td><strong>$10.7M</strong></td>
                                    <td><strong>$37.3M</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    `;
                } else {
                    return `<p>Spreadsheet content for ${filename}</p>`;
                }
            } else if (type === 'document') {
                if (filename.includes('Business_Strategy')) {
                    return `
                        <h4>Business Strategy 2025</h4>
                        <p class="fw-bold">Vision</p>
                        <p>To become the industry leader in business management solutions by providing innovative, integrated, and user-friendly software that empowers organizations to achieve operational excellence.</p>
                        <p class="fw-bold">Strategic Objectives</p>
                        <ol>
                            <li><strong>Market Expansion:</strong> Increase market share by 15% through targeted expansion into emerging markets and strategic partnerships.</li>
                            <li><strong>Product Innovation:</strong> Launch 3 new product features that address evolving customer needs and maintain competitive advantage.</li>
                            <li><strong>Operational Excellence:</strong> Improve operational efficiency by 20% through process optimization and automation.</li>
                            <li><strong>Customer Success:</strong> Achieve 95% customer satisfaction rating and reduce churn rate to below 5%.</li>
                        </ol>
                    `;
                } else {
                    return `<p>Document content for ${filename}</p>`;
                }
            } else {
                return `<p>Content for ${filename}</p>`;
            }
        }

        // Download a Google item
        function downloadGoogleItem(app, type, filename) {
            console.log(`Downloading ${app} item: ${filename} (${type})`);

            // Show a toast notification
            showToast(`Downloading ${filename}...`);

            // Simulate download completion after a delay
            setTimeout(() => {
                showToast(`${filename} downloaded successfully`);
            }, 2000);
        }

        // Share a Google item
        function shareGoogleItem(app, type, filename) {
            console.log(`Sharing ${app} item: ${filename} (${type})`);

            // Create a sharing modal
            const modalTitle = 'Share Item';
            const modalContent = `
                <div class="p-3">
                    <h5>${filename}</h5>
                    <div class="mb-3">
                        <label for="share-email" class="form-label">Share with email addresses:</label>
                        <input type="text" class="form-control" id="share-email" placeholder="Enter email addresses separated by commas">
                    </div>
                    <div class="mb-3">
                        <label for="share-message" class="form-label">Message (optional):</label>
                        <textarea class="form-control" id="share-message" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Permission:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="permission" id="permission-view" checked>
                            <label class="form-check-label" for="permission-view">
                                Can view
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="permission" id="permission-edit">
                            <label class="form-check-label" for="permission-edit">
                                Can edit
                            </label>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="button" class="btn btn-primary" onclick="confirmShare('${filename}')">Share</button>
                    </div>
                </div>
            `;

            // Show the sharing modal
            showAttachmentModal(modalTitle, modalContent);
        }

        // Delete a Google item
        function deleteGoogleItem(app, type, filename) {
            console.log(`Deleting ${app} item: ${filename} (${type})`);

            // Show confirmation dialog
            if (confirm(`Are you sure you want to delete ${filename}?`)) {
                // Show a toast notification
                showToast(`${filename} deleted successfully`);

                // Remove the item from the list if it exists
                const items = document.querySelectorAll(`.dashboard-${app} .list-group-item`);
                items.forEach(item => {
                    const itemName = item.querySelector('.fw-bold')?.textContent;
                    if (itemName === filename) {
                        // Remove with animation
                        item.style.transition = 'opacity 0.5s ease';
                        item.style.opacity = '0';

                        // Remove after animation
                        setTimeout(() => {
                            item.remove();
                        }, 500);
                    }
                });
            }
        }

        // Toggle attachments in compose form
        function toggleAttachments() {
            const attachSection = document.querySelector('.compose-attachments');
            if (attachSection) {
                if (attachSection.style.display === 'none') {
                    attachSection.style.display = 'block';
                } else {
                    attachSection.style.display = 'none';
                }
            }
        }

        // Add attachment to compose form
        function addAttachment() {
            // Create a file input element
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.multiple = true;
            fileInput.style.display = 'none';
            document.body.appendChild(fileInput);

            // Trigger click on the file input
            fileInput.click();

            // Handle file selection
            fileInput.addEventListener('change', function() {
                if (this.files && this.files.length > 0) {
                    // Get the attachments container
                    const attachmentsList = document.querySelector('.compose-attachments .list-group');
                    if (!attachmentsList) return;

                    // Add each selected file to the attachments list
                    for (let i = 0; i < this.files.length; i++) {
                        const file = this.files[i];

                        // Determine the icon based on file type
                        let iconClass = 'bi-file-earmark';
                        if (file.type.includes('pdf')) {
                            iconClass = 'bi-file-earmark-pdf text-danger';
                        } else if (file.type.includes('spreadsheet') || file.type.includes('excel') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                            iconClass = 'bi-file-earmark-spreadsheet text-success';
                        } else if (file.type.includes('document') || file.type.includes('word') || file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
                            iconClass = 'bi-file-earmark-text text-primary';
                        } else if (file.type.includes('image')) {
                            iconClass = 'bi-file-earmark-image text-info';
                        }

                        // Create attachment item
                        const attachmentItem = document.createElement('div');
                        attachmentItem.className = 'list-group-item';
                        attachmentItem.innerHTML = `
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi ${iconClass} me-2 fs-5"></i>
                                    <span>${file.name}</span>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.closest('.list-group-item').remove()">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        `;

                        // Add to the list
                        attachmentsList.appendChild(attachmentItem);
                    }

                    // Show the attachments section
                    const attachSection = document.querySelector('.compose-attachments');
                    if (attachSection) {
                        attachSection.style.display = 'block';
                    }
                }

                // Remove the temporary file input
                document.body.removeChild(fileInput);
            });
        }

        // Show an email from the inbox list by ID
        function showEmail(emailId) {
            console.log(`Showing email with ID: ${emailId}`);

            // Map email IDs to sender and subject
            let sender = '';
            let subject = '';
            let content = '';
            let date = '';

            switch (emailId) {
                case 1:
                    sender = '<EMAIL>';
                    subject = 'Quarterly Financial Report';
                    date = '2 hours ago';
                    content = `
                        <p>Hello,</p>
                        <p>I'm pleased to share the Q2 financial report with you. Our team has worked diligently to compile all the necessary data and analysis.</p>
                        <p>Key highlights:</p>
                        <ul>
                            <li>Revenue increased by 15% compared to Q1</li>
                            <li>Operating expenses reduced by 8%</li>
                            <li>Profit margin improved to 22%</li>
                        </ul>
                        <p>Please review the attached report and let me know if you have any questions or concerns.</p>
                        <p>Best regards,<br>John Davis<br>Financial Director</p>
                    `;
                    break;
                case 2:
                    sender = '<EMAIL>';
                    subject = 'HR Policy Update';
                    date = '1 day ago';
                    content = `
                        <p>Dear Team,</p>
                        <p>I'm writing to inform you that we have updated our employee handbook with several important policy changes that will take effect next month.</p>
                        <p>The key updates include:</p>
                        <ul>
                            <li>Revised remote work policy</li>
                            <li>Updated benefits package</li>
                            <li>New professional development opportunities</li>
                            <li>Expanded parental leave</li>
                        </ul>
                        <p>Please review the attached handbook and complete the acknowledgment form by the end of the week.</p>
                        <p>Best regards,<br>Sarah Wilson<br>HR Director</p>
                    `;
                    break;
                case 3:
                    sender = '<EMAIL>';
                    subject = 'Marketing Campaign Results';
                    date = '2 days ago';
                    content = `
                        <p>Hi there,</p>
                        <p>I'm excited to share the results of our Q2 marketing campaign. We've exceeded all targets and achieved record-breaking engagement metrics.</p>
                        <p>Campaign results:</p>
                        <ul>
                            <li>Lead generation: 142% of target</li>
                            <li>Conversion rate: 28% (up from 18% last quarter)</li>
                            <li>ROI: 315%</li>
                            <li>Social media engagement: +87% YoY</li>
                        </ul>
                        <p>I've attached the detailed report with all metrics and analytics. Let's discuss how we can build on this success in our next strategy meeting.</p>
                        <p>Regards,<br>David Chen<br>Marketing Director</p>
                    `;
                    break;
                default:
                    console.error('Unknown email ID');
                    return;
            }

            // Open the email with the retrieved information
            openEmail(sender, subject);
        }

        // Open an email
        function openEmail(sender, subject) {
            console.log(`Opening email from ${sender} with subject: ${subject}`);

            // Show the Gmail modal if it's not already open
            const gmailModal = document.getElementById('gmailModal');
            if (gmailModal && !gmailModal.classList.contains('show')) {
                const modal = new bootstrap.Modal(gmailModal);
                modal.show();
            }

            // Switch to the read tab
            setTimeout(() => {
                const readTab = document.getElementById('read-tab');
                if (readTab) {
                    const tabTrigger = new bootstrap.Tab(readTab);
                    tabTrigger.show();
                }

                // Get the read content container
                const readContent = document.getElementById('read-content');
                if (readContent) {
                    // Extract sender name and email
                    const senderName = sender.split('@')[0].replace('.', ' ');
                    const senderEmail = sender;

                    // Create email content
                    let emailContent = `
                        <div class="email-header mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">${subject}</h5>
                                <div class="email-actions">
                                    <button class="btn btn-sm btn-outline-primary" onclick="replyToEmail('${sender}', '${subject}')">
                                        <i class="bi bi-reply"></i> Reply
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="forwardEmail('${sender}', '${subject}')">
                                        <i class="bi bi-share"></i> Forward
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteEmail('${sender}', '${subject}')">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="me-3 avatar-initials">${senderName.split(' ').map(n => n[0]).join('')}</div>
                                <div>
                                    <div class="fw-bold">${senderName}</div>
                                    <div class="small text-muted">To: <EMAIL></div>
                                </div>
                                <div class="ms-auto small text-muted">
                                    ${new Date().toLocaleString()}
                                </div>
                            </div>
                        </div>
                        <div class="email-body">
                            <p>Dear Business Manager,</p>
                            <p>This is a sample email content for the subject "${subject}".</p>
                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
                            <p>Best regards,<br>${senderName}</p>
                        </div>
                        <div class="email-attachments mt-4">
                            <h6><i class="bi bi-paperclip"></i> Attachments (2)</h6>
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                            <div>
                                                <span>Product_Demo_Feedback.pdf</span>
                                                <div class="small text-muted">245 KB</div>
                                            </div>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" onclick="viewAttachment('pdf', 'Product_Demo_Feedback.pdf');" title="View">
                                                <i class="bi bi-eye"></i> View
                                            </button>
                                            <button type="button" class="btn btn-outline-success" onclick="downloadAttachment('Product_Demo_Feedback.pdf')" title="Download">
                                                <i class="bi bi-download"></i> Download
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <div>
                                                <span>Requirements_List.docx</span>
                                                <div class="small text-muted">89 KB</div>
                                            </div>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" onclick="viewAttachment('document', 'Requirements_List.docx');" title="View">
                                                <i class="bi bi-eye"></i> View
                                            </button>
                                            <button type="button" class="btn btn-outline-success" onclick="downloadAttachment('Requirements_List.docx')" title="Download">
                                                <i class="bi bi-download"></i> Download
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Set the content
                    readContent.innerHTML = emailContent;
                }
            }, 300);
        }

        // Reply to an email
        function replyToEmail(sender, subject) {
            console.log(`Replying to email from ${sender} with subject: ${subject}`);

            // Show the Gmail modal if it's not already open
            const gmailModal = document.getElementById('gmailModal');
            if (gmailModal && !gmailModal.classList.contains('show')) {
                const modal = new bootstrap.Modal(gmailModal);
                modal.show();
            }

            // Switch to the compose tab
            setTimeout(() => {
                const composeTab = document.getElementById('compose-tab');
                if (composeTab) {
                    const tabTrigger = new bootstrap.Tab(composeTab);
                    tabTrigger.show();
                }

                // Get form fields
                const toField = document.getElementById('email-to');
                const subjectField = document.getElementById('email-subject');
                const bodyField = document.getElementById('email-body');

                if (toField && subjectField && bodyField) {
                    // Set recipient to sender
                    toField.value = sender;

                    // Add Re: prefix to subject if not already there
                    if (!subject.startsWith('Re:')) {
                        subjectField.value = 'Re: ' + subject;
                    } else {
                        subjectField.value = subject;
                    }

                    // Create reply template
                    const now = new Date();
                    const dateString = now.toLocaleDateString() + ' ' + now.toLocaleTimeString();

                    bodyField.value = '\n\n' +
                        'On ' + dateString + ', ' + sender + ' wrote:\n' +
                        '> This is the content of the original email.';

                    // Focus on the body field
                    bodyField.focus();

                    // Place cursor at the beginning of the body
                    bodyField.setSelectionRange(0, 0);
                }
            }, 300);
        }

        // Reply All to an email
        function replyAllToEmail(sender, subject) {
            console.log(`Reply All to email from ${sender} with subject: ${subject}`);

            // Show the Gmail modal if it's not already open
            const gmailModal = document.getElementById('gmailModal');
            if (gmailModal && !gmailModal.classList.contains('show')) {
                const modal = new bootstrap.Modal(gmailModal);
                modal.show();
            }

            // Switch to the compose tab
            setTimeout(() => {
                const composeTab = document.getElementById('compose-tab');
                if (composeTab) {
                    const tabTrigger = new bootstrap.Tab(composeTab);
                    tabTrigger.show();
                }

                // Get form fields
                const toField = document.getElementById('email-to');
                const ccField = document.getElementById('email-cc');
                const subjectField = document.getElementById('email-subject');
                const bodyField = document.getElementById('email-body');

                if (toField && ccField && subjectField && bodyField) {
                    // Set recipient to sender
                    toField.value = sender;

                    // Set CC to all other recipients (simulated)
                    ccField.value = '<EMAIL>';

                    // Add Re: prefix to subject if not already there
                    if (!subject.startsWith('Re:')) {
                        subjectField.value = 'Re: ' + subject;
                    } else {
                        subjectField.value = subject;
                    }

                    // Create reply template
                    const now = new Date();
                    const dateString = now.toLocaleDateString() + ' ' + now.toLocaleTimeString();

                    bodyField.value = '\n\n' +
                        'On ' + dateString + ', ' + sender + ' wrote:\n' +
                        '> This is the content of the original email.';

                    // Focus on the body field
                    bodyField.focus();

                    // Place cursor at the beginning of the body
                    bodyField.setSelectionRange(0, 0);
                }
            }, 300);
        }

        // Forward an email
        function forwardEmail(sender, subject) {
            console.log(`Forwarding email from ${sender} with subject: ${subject}`);

            // Show the Gmail modal if it's not already open
            const gmailModal = document.getElementById('gmailModal');
            if (gmailModal && !gmailModal.classList.contains('show')) {
                const modal = new bootstrap.Modal(gmailModal);
                modal.show();
            }

            // Switch to the compose tab
            setTimeout(() => {
                const composeTab = document.getElementById('compose-tab');
                if (composeTab) {
                    const tabTrigger = new bootstrap.Tab(composeTab);
                    tabTrigger.show();
                }

                // Get form fields
                const toField = document.getElementById('email-to');
                const subjectField = document.getElementById('email-subject');
                const bodyField = document.getElementById('email-body');

                if (toField && subjectField && bodyField) {
                    // Clear recipient
                    toField.value = '';

                    // Add Fwd: prefix to subject if not already there
                    if (!subject.startsWith('Fwd:')) {
                        subjectField.value = 'Fwd: ' + subject;
                    } else {
                        subjectField.value = subject;
                    }

                    // Create forwarded message template
                    const now = new Date();
                    const dateString = now.toLocaleDateString() + ' ' + now.toLocaleTimeString();

                    // Extract sender name and email
                    const senderName = sender.split('@')[0].replace('.', ' ');
                    const senderEmail = sender;

                    bodyField.value = '\n\n' +
                        '---------- Forwarded Message ----------\n' +
                        'From: ' + senderName + ' <' + senderEmail + '>\n' +
                        'Date: ' + dateString + '\n' +
                        'Subject: ' + subject + '\n\n' +
                        'This is the content of the original email.';

                    // Focus on the recipient field
                    toField.focus();
                }
            }, 300);
        }

        // Open the compose tab in the Gmail modal
        function openComposeTab() {
            console.log('Opening compose tab');

            // Show the Gmail modal if it's not already open
            const gmailModal = document.getElementById('gmailModal');
            if (gmailModal && !gmailModal.classList.contains('show')) {
                const modal = new bootstrap.Modal(gmailModal);
                modal.show();
            }

            // Switch to the compose tab
            setTimeout(() => {
                const composeTab = document.getElementById('compose-tab');
                if (composeTab) {
                    const tabTrigger = new bootstrap.Tab(composeTab);
                    tabTrigger.show();
                }

                // Focus on the recipient field
                const toField = document.getElementById('email-to');
                if (toField) {
                    toField.focus();
                }
            }, 300);
        }

        // Show a toast notification
        function showToast(message, type = 'success') {
            // Create toast container if it doesn't exist
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            // Create a unique ID for this toast
            const toastId = 'toast-' + Date.now();

            // Determine the background color based on type
            let bgClass = 'bg-success';
            if (type === 'error') bgClass = 'bg-danger';
            if (type === 'warning') bgClass = 'bg-warning';
            if (type === 'info') bgClass = 'bg-info';

            // Create the toast element
            const toastHTML = `
                <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header ${bgClass} text-white">
                        <strong class="me-auto">BMS</strong>
                        <small>Just now</small>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            // Add the toast to the container
            toastContainer.insertAdjacentHTML('beforeend', toastHTML);

            // Initialize and show the toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
            toast.show();

            // Remove the toast after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                this.remove();
            });
        }

        // Delete an email
        function deleteEmail(sender, subject) {
            console.log(`Deleting email from ${sender} with subject: ${subject}`);

            // Ask for confirmation
            if (confirm(`Are you sure you want to delete the email from ${sender} with subject "${subject}"?`)) {
                // Find the email item in the list
                const emailItems = document.querySelectorAll('.list-group-item');
                let found = false;

                emailItems.forEach(item => {
                    // Check if this is the email we want to delete
                    const emailSender = item.querySelector('.fw-bold')?.textContent;
                    const emailSubject = item.querySelector('.small')?.textContent;

                    if (emailSender && emailSubject) {
                        const senderName = sender.split('@')[0].replace('.', ' ');

                        if (emailSender.includes(senderName) && emailSubject.includes(subject)) {
                            // Add fade-out animation
                            item.style.transition = 'opacity 0.5s ease';
                            item.style.opacity = '0';

                            // Remove the item after animation completes
                            setTimeout(() => {
                                item.remove();
                            }, 500);

                            found = true;
                        }
                    }
                });

                // Show success message
                if (found) {
                    showToast('Email deleted successfully!');

                    // Close the modal if it's open
                    const gmailModal = document.getElementById('gmailModal');
                    if (gmailModal && gmailModal.classList.contains('show')) {
                        const modal = bootstrap.Modal.getInstance(gmailModal);
                        if (modal) {
                            modal.hide();
                        }
                    }
                } else {
                    showToast('Email not found in the list.');
                }
            }
        }

        // Send an email
        function sendEmail() {
            // Get form values
            const toField = document.getElementById('email-to');
            const subjectField = document.getElementById('email-subject');
            const bodyField = document.getElementById('email-body');

            if (!toField || !subjectField || !bodyField) {
                console.error('Compose form fields not found');
                return;
            }

            // Validate form
            if (!toField.value) {
                alert('Please enter a recipient email address');
                toField.focus();
                return;
            }

            if (!subjectField.value) {
                const proceed = confirm('Send this message without a subject?');
                if (!proceed) {
                    subjectField.focus();
                    return;
                }
            }

            // Show sending message
            showToast('Sending email...');

            // Simulate sending delay
            setTimeout(() => {
                // Show success message
                showToast('Email sent successfully!');

                // Close the modal
                const gmailModal = document.getElementById('gmailModal');
                if (gmailModal) {
                    const modal = bootstrap.Modal.getInstance(gmailModal);
                    if (modal) {
                        modal.hide();
                    }
                }

                // Clear the form
                toField.value = '';
                if (document.getElementById('email-cc')) document.getElementById('email-cc').value = '';
                subjectField.value = '';
                bodyField.value = '';

                // Clear attachments
                const attachmentsList = document.querySelector('.compose-attachments .list-group');
                if (attachmentsList) {
                    attachmentsList.innerHTML = '';
                }

                // Hide attachments section
                const attachSection = document.querySelector('.compose-attachments');
                if (attachSection) {
                    attachSection.style.display = 'none';
                }
            }, 2000);
        }
    </script>

<style>
.input-group-max-300 {
    max-width: 300px;
}

.email-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.email-item:hover {
    background-color: rgba(var(--app-primary-rgb), 0.05);
}

.email-subject {
    max-width: 200px;
}

.email-actions {
    min-width: 150px;
    display: flex;
    justify-content: flex-end;
}

.card-border-primary {
    border-left: 4px solid var(--app-primary-color);
}

.dashboard-card-primary {
    background-color: var(--app-primary-color);
}
</style>

<script>
    // Function to fetch AI-powered business intelligence insights
    function fetchAIInsights() {
        const insightsContainer = document.getElementById('ai-insights-container');

        // Show loading spinner
        insightsContainer.innerHTML = `
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        `;

        fetch('http://localhost:8000/api/ai-analytics/business-intelligence')
            .then(response => response.json())
            .then(data => {
                console.log('AI insights:', data);

                // Store insights data globally for export
                window.aiInsightsData = data;

                // Clear loading placeholder
                insightsContainer.innerHTML = '';

                // Generate sample insights if the API doesn't return the expected data structure
                if (!data || !data.insights) {
                    generateSampleInsights(insightsContainer);
                    return;
                }

                // Create insights sections based on the data
                createInsightsSections(data, insightsContainer);
            })
            .catch(error => {
                console.error('Error fetching AI insights:', error);
                insightsContainer.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            Unable to connect to AI analytics engine. Please make sure the Integration Hub is running.
                        </div>
                    </div>
                `;

                // Generate sample insights after a short delay
                setTimeout(() => {
                    generateSampleInsights(insightsContainer);
                }, 2000);
            });
    }

    // Generate sample insights for demonstration
    function generateSampleInsights(container) {
        container.innerHTML = `
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="bi bi-graph-up text-success me-2"></i> Revenue Trends</h6>
                        <p class="card-text">Revenue is up 12% compared to last quarter, with the strongest growth in the Technology sector.</p>
                        <div class="text-end">
                            <button type="button" class="btn btn-sm btn-outline-primary">View Details</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="bi bi-exclamation-triangle text-warning me-2"></i> Budget Alert</h6>
                        <p class="card-text">HR department is at 95% of quarterly budget allocation with 2 weeks remaining.</p>
                        <div class="text-end">
                            <button type="button" class="btn btn-sm btn-outline-primary">View Details</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="bi bi-lightbulb text-primary me-2"></i> Opportunity</h6>
                        <p class="card-text">Marketing campaign ROI analysis suggests increasing digital ad spend by 15% could yield 22% more conversions.</p>
                        <div class="text-end">
                            <button type="button" class="btn btn-sm btn-outline-primary">View Details</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Create insights sections based on the data from the API
    function createInsightsSections(data, container) {
        // Check if we have the insights data
        if (!data.insights || !Array.isArray(data.insights)) {
            generateSampleInsights(container);
            return;
        }

        // Process each insight and add it to the container
        data.insights.forEach(insight => {
            // Determine the icon based on insight type
            let iconClass = 'bi-info-circle text-primary';
            if (insight.type === 'positive' || insight.type === 'growth') {
                iconClass = 'bi-graph-up text-success';
            } else if (insight.type === 'warning' || insight.type === 'alert') {
                iconClass = 'bi-exclamation-triangle text-warning';
            } else if (insight.type === 'negative' || insight.type === 'decline') {
                iconClass = 'bi-graph-down text-danger';
            } else if (insight.type === 'opportunity') {
                iconClass = 'bi-lightbulb text-primary';
            }

            // Create the insight card
            const insightElement = document.createElement('div');
            insightElement.className = 'col-md-4 mb-3';
            insightElement.innerHTML = `
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="bi ${iconClass} me-2"></i> ${insight.title}</h6>
                        <p class="card-text">${insight.description}</p>
                        ${insight.data ? `<div class="small text-muted mb-2">Based on data from ${insight.data.source || 'system analysis'}</div>` : ''}
                        <div class="text-end">
                            <button type="button" class="btn btn-sm btn-outline-primary insight-details-btn"
                                data-insight-id="${insight.id || ''}">View Details</button>
                        </div>
                    </div>
                </div>
            `;

            // Add to container
            container.appendChild(insightElement);
        });

        // Add event listeners to the detail buttons
        const detailButtons = container.querySelectorAll('.insight-details-btn');
        detailButtons.forEach(button => {
            button.addEventListener('click', function() {
                const insightId = this.getAttribute('data-insight-id');
                showInsightDetails(insightId, data.insights);
            });
        });
    }

    // Show detailed information for a specific insight
    function showInsightDetails(insightId, insights) {
        // Find the insight with the matching ID
        const insight = insights.find(item => item.id === insightId);

        if (!insight) {
            console.error('Insight not found:', insightId);
            return;
        }

        // Create modal content
        const modalHTML = `
            <div class="modal fade" id="insightDetailModal" tabindex="-1" aria-labelledby="insightDetailModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                            <h5 class="modal-title" id="insightDetailModalLabel">
                                <i class="bi bi-graph-up me-2"></i> ${insight.title}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>${insight.description}</p>
                            ${insight.details ? `<div class="mt-3">${insight.details}</div>` : ''}
                            ${insight.recommendations ? `
                                <div class="mt-4">
                                    <h6>Recommendations:</h6>
                                    <ul>
                                        ${insight.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary">Take Action</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove any existing modal
        const existingModal = document.getElementById('insightDetailModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to the document
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('insightDetailModal'));
        modal.show();
    }

    // Initialize the dashboard
    document.addEventListener('DOMContentLoaded', function() {
        // Fetch AI insights when the page loads
        fetchAIInsights();

        // Add event listener for the refresh button
        const refreshButton = document.getElementById('refresh-insights-btn');
        if (refreshButton) {
            refreshButton.addEventListener('click', fetchAIInsights);
        }
    });
</script>

    <!-- Gmail Modal -->
    <div class="modal fade" id="gmailModal" tabindex="-1" aria-labelledby="gmailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header app-modal-header">
                    <h5 class="modal-title" id="gmailModalLabel"><i class="bi bi-envelope"></i> Gmail</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="gmailTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox-content" type="button" role="tab" aria-controls="inbox-content" aria-selected="true">
                                <i class="bi bi-inbox"></i> Inbox
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose-content" type="button" role="tab" aria-controls="compose-content" aria-selected="false">
                                <i class="bi bi-pencil"></i> Compose
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="read-tab" data-bs-toggle="tab" data-bs-target="#read-content" type="button" role="tab" aria-controls="read-content" aria-selected="false">
                                <i class="bi bi-envelope-open"></i> Read
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent-content" type="button" role="tab" aria-controls="sent-content" aria-selected="false">
                                <i class="bi bi-send"></i> Sent
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="gmailTabContent">
                        <!-- Inbox Tab -->
                        <div class="tab-pane fade show active" id="inbox-content" role="tabpanel" aria-labelledby="inbox-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="email-search" placeholder="Search emails...">
                                        <button class="btn btn-outline-secondary" type="button" id="search-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="email-sort" class="form-label visually-hidden">Sort emails</label>
                                    <select class="form-select" id="email-sort" title="Sort emails" aria-label="Sort emails">
                                        <option value="date-desc">Newest First</option>
                                        <option value="date-asc">Oldest First</option>
                                        <option value="sender-asc">Sender A-Z</option>
                                        <option value="sender-desc">Sender Z-A</option>
                                        <option value="subject-asc">Subject A-Z</option>
                                        <option value="subject-desc">Subject Z-A</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="email-filter" class="form-label visually-hidden">Filter emails</label>
                                    <select class="form-select" id="email-filter" title="Filter emails" aria-label="Filter emails">
                                        <option value="all">All Emails</option>
                                        <option value="unread">Unread</option>
                                        <option value="read">Read</option>
                                        <option value="important">Important</option>
                                        <option value="starred">Starred</option>
                                    </select>
                                </div>
                            </div>
                            <div class="email-list" id="email-list">
                                <div class="list-group">
                                    <div class="list-group-item email-item" data-sender="John Davis" data-subject="Quarterly Financial Report" data-date="2025-06-10T14:30:00" data-read="false">
                                        <div class="email-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center flex-grow-1" onclick="toggleEmailContent('email1')">
                                                    <div class="me-3">
                                                        <div class="avatar-circle bg-primary">JD</div>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div class="d-flex justify-content-between">
                                                            <h6 class="mb-1 fw-bold">John Davis</h6>
                                                            <small class="text-muted">2h ago</small>
                                                        </div>
                                                        <p class="mb-1">Quarterly Financial Report</p>
                                                        <small class="text-muted">Please review the attached Q2 financial report...</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group btn-group-sm ms-2">
                                                    <button type="button" class="btn btn-outline-primary" title="Expand" onclick="toggleEmailContent('email1')">
                                                        <i class="bi bi-chevron-down" id="expand-icon-email1"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteEmail('John Davis', 'Quarterly Financial Report')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="email-content-expanded" id="email-content-email1" style="display: none;">
                                            <hr class="my-3">
                                            <div class="email-body mb-3">
                                                <p><strong>From:</strong> John Davis &lt;<EMAIL>&gt;</p>
                                                <p><strong>To:</strong> <EMAIL></p>
                                                <p><strong>Subject:</strong> Quarterly Financial Report</p>
                                                <p><strong>Date:</strong> June 10, 2025 at 2:30 PM</p>
                                                <hr class="my-2">
                                                <div class="email-message">
                                                    <p>Dear Team,</p>
                                                    <p>Please review the attached Q2 financial report and provide feedback by Friday. The report shows strong performance across all departments with a 15% increase in revenue compared to Q1.</p>
                                                    <p>Key highlights:</p>
                                                    <ul>
                                                        <li>Revenue increased by 15%</li>
                                                        <li>Operating costs reduced by 8%</li>
                                                        <li>Customer satisfaction improved to 94%</li>
                                                    </ul>
                                                    <p>Please let me know if you have any questions.</p>
                                                    <p>Best regards,<br>John Davis</p>
                                                </div>
                                            </div>

                                            <!-- Attachments Section -->
                                            <div class="attachments-section mb-3">
                                                <h6><i class="bi bi-paperclip me-2"></i>Attachments (3)</h6>
                                                <div class="attachment-list">
                                                    <div class="attachment-item d-flex align-items-center justify-content-between p-2 border rounded mb-2">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-file-earmark-pdf me-2 fs-5 text-danger"></i>
                                                            <div>
                                                                <div class="fw-bold">Q4_Financial_Report.pdf</div>
                                                                <small class="text-muted">2.3 MB</small>
                                                            </div>
                                                        </div>
                                                        <div class="attachment-actions btn-group">
                                                            <button class="btn btn-sm btn-outline-primary me-1">
                                                                <i class="bi bi-eye"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-success me-1">
                                                                <i class="bi bi-download"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-info">
                                                                <i class="bi bi-share"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="attachment-item d-flex align-items-center justify-content-between p-2 border rounded mb-2">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-file-earmark-excel me-2 fs-5 text-success"></i>
                                                            <div>
                                                                <div class="fw-bold">Budget_Analysis.xlsx</div>
                                                                <small class="text-muted">1.8 MB</small>
                                                            </div>
                                                        </div>
                                                        <div class="attachment-actions btn-group">
                                                            <button class="btn btn-sm btn-outline-primary me-1">
                                                                <i class="bi bi-eye"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-success me-1">
                                                                <i class="bi bi-download"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-info">
                                                                <i class="bi bi-share"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="attachment-item d-flex align-items-center justify-content-between p-2 border rounded mb-2">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-file-earmark-image me-2 fs-5 text-warning"></i>
                                                            <div>
                                                                <div class="fw-bold">Revenue_Chart.png</div>
                                                                <small class="text-muted">456 KB</small>
                                                            </div>
                                                        </div>
                                                        <div class="attachment-actions btn-group">
                                                            <button class="btn btn-sm btn-outline-primary me-1">
                                                                <i class="bi bi-eye"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-success me-1">
                                                                <i class="bi bi-download"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-info">
                                                                <i class="bi bi-share"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Reply Section -->
                                            <div class="reply-section">
                                                <div class="d-flex gap-2 mb-3">
                                                    <button class="btn btn-success" onclick="showReplyForm('email1')">
                                                        <i class="bi bi-reply me-1"></i>Reply
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="showReplyAllForm('email1')">
                                                        <i class="bi bi-reply-all me-1"></i>Reply All
                                                    </button>
                                                    <button class="btn btn-outline-info" onclick="showForwardForm('email1')">
                                                        <i class="bi bi-share me-1"></i>Forward
                                                    </button>
                                                </div>

                                                <!-- Reply Form (Hidden by default) -->
                                                <div class="reply-form" id="reply-form-email1" style="display: none;">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <h6 class="mb-0">Reply to John Davis</h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <form>
                                                                <div class="mb-2">
                                                                    <label class="form-label">To:</label>
                                                                    <input type="email" class="form-control" value="<EMAIL>" readonly>
                                                                </div>
                                                                <div class="mb-2">
                                                                    <label class="form-label">Subject:</label>
                                                                    <input type="text" class="form-control" value="Re: Quarterly Financial Report" readonly>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label class="form-label">Message:</label>
                                                                    <textarea class="form-control" rows="4" placeholder="Type your reply here..."></textarea>
                                                                </div>
                                                                <div class="d-flex gap-2">
                                                                    <button type="button" class="btn btn-primary" onclick="sendReply('email1')">
                                                                        <i class="bi bi-send me-1"></i>Send Reply
                                                                    </button>
                                                                    <button type="button" class="btn btn-outline-secondary" onclick="hideReplyForm('email1')">
                                                                        Cancel
                                                                    </button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item email-item" data-sender="Sarah Wilson" data-subject="HR Policy Update" data-date="2025-06-09T10:15:00" data-read="true">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center flex-grow-1" onclick="openEmailContent('Sarah Wilson', 'HR Policy Update', 'New employee handbook is now available on the company portal.')">
                                                <div class="me-3">
                                                    <div class="avatar-circle bg-success">SW</div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="d-flex justify-content-between">
                                                        <h6 class="mb-1">Sarah Wilson</h6>
                                                        <small class="text-muted">1d ago</small>
                                                    </div>
                                                    <p class="mb-1">HR Policy Update</p>
                                                    <small class="text-muted">New employee handbook is now available...</small>
                                                </div>
                                            </div>
                                            <div class="btn-group btn-group-sm ms-2">
                                                <button type="button" class="btn btn-outline-warning attachment-btn" title="Attachments" onclick="alert('Attachment feature temporarily disabled for maintenance')">
                                                    <i class="bi bi-paperclip"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-success" title="Reply" onclick="replyToEmail('Sarah Wilson', 'HR Policy Update')">
                                                    <i class="bi bi-reply"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-info" title="Forward" onclick="forwardEmail('Sarah Wilson', 'HR Policy Update')">
                                                    <i class="bi bi-share"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteEmail('Sarah Wilson', 'HR Policy Update')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item email-item" data-sender="David Chen" data-subject="Marketing Campaign Results" data-date="2025-06-08T16:45:00" data-read="true">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center flex-grow-1" onclick="openEmailContent('David Chen', 'Marketing Campaign Results', 'Q2 marketing campaign exceeded our targets by 15%. Great work team!')">
                                                <div class="me-3">
                                                    <div class="avatar-circle bg-warning">DC</div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="d-flex justify-content-between">
                                                        <h6 class="mb-1">David Chen</h6>
                                                        <small class="text-muted">2d ago</small>
                                                    </div>
                                                    <p class="mb-1">Marketing Campaign Results</p>
                                                    <small class="text-muted">Q2 marketing campaign exceeded our targets...</small>
                                                </div>
                                            </div>
                                            <div class="btn-group btn-group-sm ms-2">
                                                <button type="button" class="btn btn-outline-warning attachment-btn" title="Attachments" onclick="alert('Attachment feature temporarily disabled for maintenance')">
                                                    <i class="bi bi-paperclip"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-success" title="Reply" onclick="replyToEmail('David Chen', 'Marketing Campaign Results')">
                                                    <i class="bi bi-reply"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-info" title="Forward" onclick="forwardEmail('David Chen', 'Marketing Campaign Results')">
                                                    <i class="bi bi-share"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteEmail('David Chen', 'Marketing Campaign Results')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Compose Tab -->
                        <div class="tab-pane fade" id="compose-content" role="tabpanel" aria-labelledby="compose-tab">
                            <form id="compose-email-form">
                                <div class="mb-3">
                                    <label for="compose-to" class="form-label">To</label>
                                    <input type="email" class="form-control" id="compose-to" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label for="compose-subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="compose-subject" placeholder="Email subject">
                                </div>
                                <div class="mb-3">
                                    <label for="compose-body" class="form-label">Message</label>
                                    <textarea class="form-control" id="compose-body" rows="10" placeholder="Type your message here..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Attachments</label>
                                    <div class="d-flex align-items-center mb-2">
                                        <input type="file" class="form-control" id="attachment-input" multiple>
                                        <button type="button" class="btn btn-outline-secondary" onclick="document.getElementById('attachment-input').click()">
                                            <i class="bi bi-paperclip"></i> Add Attachment
                                        </button>
                                        <small class="text-muted ms-2">Max 25MB per file</small>
                                    </div>
                                    <div id="attachment-list" class="list-group">
                                        <!-- Attachments will be added here dynamically -->
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-primary" onclick="sendEmail()">
                                            <i class="bi bi-send"></i> Send
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                                            <i class="bi bi-save"></i> Save Draft
                                        </button>
                                    </div>
                                    <button type="button" class="btn btn-outline-danger" onclick="clearCompose()">
                                        <i class="bi bi-trash"></i> Clear
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Read Tab -->
                        <div class="tab-pane fade" id="read-content" role="tabpanel" aria-labelledby="read-tab">
                            <div class="email-reader" id="email-reader">
                                <div class="alert alert-info text-center">
                                    <i class="bi bi-envelope me-2"></i>
                                    Select an email from the inbox to view its content
                                </div>
                            </div>
                        </div>

                        <!-- Sent Tab -->
                        <div class="tab-pane fade" id="sent-content" role="tabpanel" aria-labelledby="sent-tab">
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">To: <EMAIL></h6>
                                            <p class="mb-1">Weekly Team Update</p>
                                            <small class="text-muted">Sent 3 days ago</small>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" title="View">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-info" title="Forward">
                                                <i class="bi bi-share"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">To: <EMAIL></h6>
                                            <p class="mb-1">Project Proposal</p>
                                            <small class="text-muted">Sent 1 week ago</small>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" title="View">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-info" title="Forward">
                                                <i class="bi bi-share"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://mail.google.com" target="_blank" rel="noopener" class="btn btn-primary">Open in Gmail</a>
                </div>
            </div>
        </div>
    </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Unified Gmail Integration - Single working system -->
    <script src="../../SharedFeatures/ui/gmail-integration-unified.js"></script>

    <!-- ISA Suite Enhancements -->
    <script src="../../SharedFeatures/enhancements/cross-app-enhancements.js"></script>

    <!-- Google Drive Action Buttons Handler -->
    <script src="js/google-drive-actions.js"></script>


<style>
    .max-width-300 {
        max-width: 300px;
    }

    /* Gmail Modal Fixes */
    #gmailModal {
        z-index: 1055 !important;
    }

    #gmailModal .modal-dialog {
        z-index: 1056 !important;
        margin: 1.75rem auto !important;
    }

    #gmailModal .modal-content {
        z-index: 1057 !important;
        background-color: white !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 0.375rem !important;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }



    .modal-backdrop {
        z-index: 1050 !important;
    }

    /* Avatar circles for emails */
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 14px;
    }

    /* Email item hover effects */
    .email-item {
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .email-item:hover {
        background-color: rgba(var(--app-primary-rgb), 0.05);
    }

    /* Refresh button spin animation */
    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Email expansion styles */
    .email-content-expanded {
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .email-body {
        background-color: white;
        border-radius: 0.375rem;
        padding: 1rem;
        border: 1px solid #dee2e6;
    }

    .attachments-section {
        background-color: white;
        border-radius: 0.375rem;
        padding: 1rem;
        border: 1px solid #dee2e6;
    }

    .attachment-item {
        background-color: #f8f9fa;
        transition: background-color 0.2s ease;
    }

    .attachment-item:hover {
        background-color: #e9ecef;
    }

    .reply-section {
        background-color: white;
        border-radius: 0.375rem;
        padding: 1rem;
        border: 1px solid #dee2e6;
    }

    .reply-form {
        margin-top: 1rem;
    }

    .email-header {
        cursor: pointer;
    }

    .email-header:hover {
        background-color: rgba(var(--app-primary-rgb), 0.05);
    }
</style>



    <!-- Initialize Unified Gmail Integration -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Clean up any stuck modals or backdrops first
            cleanupStuckModals();

            // Initialize the unified Gmail integration for BMS
            if (typeof initializeUnifiedGmail === 'function') {
                const gmail = initializeUnifiedGmail({
                    appName: 'BMS',
                    appPrefix: 'bms',
                    appColor: '#00acc1', // Teal theme for BMS
                    modalId: 'gmailModal',
                    triggerId: 'gmail-link', // The sidebar Gmail link
                    debug: true
                });
                console.log('Unified Gmail integration initialized for BMS application');

                // Make globally available for debugging
                window.bmsGmail = gmail;
            } else {
                console.warn('Unified Gmail integration not available');
            }

            // Initialize ISA Suite Enhancements
            if (typeof ISASuiteEnhancements === 'function') {
                const enhancements = new ISASuiteEnhancements({
                    appName: 'BMS',
                    appPrefix: 'bms',
                    appColor: '#00acc1', // Teal theme for BMS
                    enableNotifications: true,
                    enableAnalytics: true,
                    enableExport: true,
                    enableSearch: true,
                    debug: true
                });
                enhancements.init();
                console.log('ISA Suite Enhancements initialized for BMS application');

                // Show welcome notification
                setTimeout(() => {
                    enhancements.showNotification('BMS Enhanced!', 'Advanced features are now available. Press Ctrl+? for help.', 'success');
                }, 2000);
            } else {
                console.warn('ISA Suite Enhancements not available');
            }
        });

        // Function to clean up any stuck modals or backdrops
        function cleanupStuckModals() {
            console.log('Cleaning up any stuck modals...');

            // Remove any stuck attachment modals
            const stuckModals = document.querySelectorAll('#attachmentModal, #emailAttachmentModal');
            stuckModals.forEach(modal => {
                console.log('Removing stuck modal:', modal.id);
                modal.remove();
            });

            // Remove any stuck backdrops
            const stuckBackdrops = document.querySelectorAll('#attachmentBackdrop, .modal-backdrop');
            stuckBackdrops.forEach(backdrop => {
                console.log('Removing stuck backdrop');
                backdrop.remove();
            });

            // Reset body overflow in case it was set by a modal
            document.body.style.overflow = '';
            document.body.classList.remove('modal-open');

            console.log('Modal cleanup completed');
        }
    </script>
</body>
</html>

