<!DOCTYPE html>
<!--
    This HTML file serves as the main entry point for the Materials Requirements Planning (MRP) System.
    It includes a responsive layout with a sidebar, top navbar, and various sections for displaying data.
    The design is based on Bootstrap and includes custom styles for a cohesive look.
-->
<!--
    The sidebar contains links to different sections of the MRP System, including Inventory, Production, Planning, Orders, Suppliers, Reports, and Settings.
    The top navbar includes the system title and user controls for notifications and profile settings.
    The main content area displays dashboard cards, charts, and tables related to inventory management.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Materials Requirements Planning System</title>
    <link rel="manifest" href="manifest.json">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../../shared/css/isa-modals.css">
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --app-primary-color: #9c27b0; /* Purple for MRP */
            --app-primary-dark: #7b1fa2;
            --app-primary-light: rgba(156, 39, 176, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            padding-top: 56px; /* Height of navbar */
        }

        /* Sidebar styles */
        .sidebar {
            position: fixed;
            top: 56px; /* Height of navbar */
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: var(--app-primary-color);
            width: 250px;
            transition: all 0.3s;
            overflow-y: auto; /* Add scrollbar when content overflows */
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--app-primary-dark);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar .nav-link {
            color: #f8f9fa;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-heading {
            padding: 0.75rem 1.25rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.1rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Main content area */
        .main-content {
            margin-left: 0;
            transition: margin-left 0.3s;
            padding: 20px;
        }

        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px; /* Width of sidebar */
            }
        }

        /* Navbar */
        .navbar {
            background-color: var(--app-primary-color) !important;
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
        }

        /* Toggle sidebar button */
        #sidebarToggle {
            cursor: pointer;
            background: transparent;
            border: none;
            color: white;
        }

        /* For mobile view */
        @media (max-width: 767.98px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .navbar {
                padding: 0.5rem;
            }
        }
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .card-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .inventory-table {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .inventory-table th {
            background-color: #343a40;
            color: white;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-good {
            background-color: #28a745;
        }
        .status-warning {
            background-color: #ffc107;
        }
        .status-danger {
            background-color: #dc3545;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-dark fixed-top" style="background-color: var(--app-primary-color);">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <button id="sidebarToggle" class="d-md-none me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-list fs-4"></i>
                </button>
                <a class="navbar-brand" href="/">MRP System</a>
            </div> <!-- Close the align-items-center div -->
            <div class="d-flex">
                <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3" style="background: transparent; border: 1px solid white; color: white; padding: 5px 10px;">
                    <i class="bi bi-box-arrow-left me-1"></i> Back to Hub
                </a>
                <button class="btn position-relative me-2" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-bell fs-5"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        5
                    </span>
                </button>
                <button class="btn" style="background: transparent; border: none; color: white;">
                    <i class="bi bi-person-circle fs-5"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="pt-2">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#inventory">
                        <i class="bi bi-box-seam"></i>
                        Inventory
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#production">
                        <i class="bi bi-gear"></i>
                        Production
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#planning">
                        <i class="bi bi-calendar3"></i>
                        Planning
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#orders">
                        <i class="bi bi-cart"></i>
                        Orders
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#suppliers">
                        <i class="bi bi-truck"></i>
                        Suppliers
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#reports">
                        <i class="bi bi-file-earmark-text"></i>
                        Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#settings">
                        <i class="bi bi-gear-fill"></i>
                        Settings
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                <span>Integrations</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="#shopify">
                        <i class="bi bi-shop"></i>
                        Shopify
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal">
                        <i class="bi bi-calendar3"></i>
                        Google Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal">
                        <i class="bi bi-folder"></i>
                        Google Drive
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal">
                        <i class="bi bi-file-earmark-text"></i>
                        Google Docs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#mrp-sheetsModal">
                        <i class="bi bi-file-earmark-spreadsheet"></i>
                        Google Sheets
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-maps" data-bs-toggle="modal" data-bs-target="#mapsModal">
                        <i class="bi bi-geo-alt"></i>
                        Google Maps
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#gmail" data-bs-toggle="modal" data-bs-target="#mrp-gmailModal">
                        <i class="bi bi-envelope"></i>
                        Gmail
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#slack" data-bs-toggle="modal" data-bs-target="#slackModal">
                        <i class="bi bi-slack"></i>
                        Slack
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#attachments" data-bs-toggle="modal" data-bs-target="#mrp-attachmentsModal" id="mrp-attachments-link">
                        <i class="bi bi-paperclip"></i>
                        Attachments
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();">
                        <i class="bi bi-box-arrow-left"></i>
                        Back to Hub
                    </a>
                </li>
            </ul>





            <div class="px-3 py-3">
                <div class="d-grid">
                    <a class="btn btn-outline-light" href="javascript:void(0);" onclick="window.close(); window.opener.focus();">
                        <i class="bi bi-box-arrow-left me-2"></i>Back to Hub
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">Materials Requirements Planning Dashboard</h1>
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                </div>
                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle">
                    <i class="bi bi-calendar"></i>
                    This month
                </button>
            </div>
        </div>

                <!-- AI Insights Card -->
                <div class="card mb-4" style="border-left: 4px solid var(--app-primary-color);">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-robot"></i> AI-Powered Materials Planning
                            <span class="badge bg-primary ms-2">AI Insights</span>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" id="refresh-insights-btn">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                            <div class="dropdown d-inline-block ms-2">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="insightsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-gear"></i>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="insightsDropdown">
                                    <li><a class="dropdown-item" href="#" id="expand-all-insights">Expand All</a></li>
                                    <li><a class="dropdown-item" href="#" id="collapse-all-insights">Collapse All</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" id="export-insights">Export Insights</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="ai-insights-container">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Cards -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="card dashboard-card" style="background-color: var(--app-primary-color); color: white;">
                            <div class="card-body text-center">
                                <i class="bi bi-box-seam card-icon"></i>
                                <h5 class="card-title">Total Inventory</h5>
                                <h2 class="card-text">1,250</h2>
                                <p class="card-text">Items in stock</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-check-circle card-icon"></i>
                                <h5 class="card-title">Completed Orders</h5>
                                <h2 class="card-text">85</h2>
                                <p class="card-text">Last 30 days</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-warning text-dark">
                            <div class="card-body text-center">
                                <i class="bi bi-exclamation-triangle card-icon"></i>
                                <h5 class="card-title">Low Stock</h5>
                                <h2 class="card-text">12</h2>
                                <p class="card-text">Items below threshold</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-x-circle card-icon"></i>
                                <h5 class="card-title">Out of Stock</h5>
                                <h2 class="card-text">3</h2>
                                <p class="card-text">Items to reorder</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Overview Chart -->
                <div class="row mt-4">
                    <div class="col-md-8">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Inventory Levels</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="inventoryChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Inventory Status</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="statusChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Table -->
                <h2 class="mt-4">Inventory Items</h2>
                <div class="table-responsive inventory-table">
                    <table class="table table-striped table-sm" id="inventoryTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Item Name</th>
                                <th>Category</th>
                                <th>Quantity</th>
                                <th>Reorder Level</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- Google Integrations -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4 class="mb-3">Google Integrations</h4>
                    </div>

                    <!-- Google Gmail -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-gmail-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-envelope"></i> Gmail</h5>
                                <div class="component-actions">
                                    <button id="refresh-gmail" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="compose-email" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#mrp-gmailModal"><i class="bi bi-pencil"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" onclick="openEmail('sarah-chen', 'Inventory update: New shipment arrived')">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">SC</div>
                                                <div>
                                                    <div class="fw-bold">Sarah Chen</div>
                                                    <div class="small text-truncate" style="max-width: 200px;">Inventory update: New shipment arrived</div>
                                                </div>
                                            </div>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">10m</span>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" onclick="openEmail('john-davis', 'Production schedule for next week')">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">JD</div>
                                                <div>
                                                    <div class="fw-bold">John Davis</div>
                                                    <div class="small text-truncate" style="max-width: 200px;">Production schedule for next week</div>
                                                </div>
                                            </div>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">1h</span>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" onclick="openEmail('rachel-miller', 'Supplier price update for Raw Material A')">
                                        <div>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2" style="width: 40px; height: 40px; border-radius: 50%; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #666;">RM</div>
                                                <div>
                                                    <div class="fw-bold">Rachel Miller</div>
                                                    <div class="small text-truncate" style="max-width: 200px;">Supplier price update for Raw Material A</div>
                                                </div>
                                            </div>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">3h</span>
                                    </a>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" id="mrp-open-gmail-btn" data-bs-toggle="modal" data-bs-target="#mrp-gmailModal">
                                        <i class="bi bi-envelope me-2"></i>Open Gmail
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Drive -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-drive-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-folder"></i> Google Drive</h5>
                                <div class="component-actions">
                                    <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="upload-file" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#driveModal"><i class="bi bi-upload"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div style="cursor: pointer;" onclick="viewGoogleItem('drive', 'folder', 'Production_Documentation')">
                                            <i class="bi bi-folder-fill text-primary me-2"></i>
                                            <span>Production Documentation</span>
                                            <span class="badge bg-secondary rounded-pill ms-2">12 files</span>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Production_Documentation')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Production_Documentation')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Production_Documentation')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Production_Documentation')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div style="cursor: pointer;" onclick="viewGoogleItem('drive', 'folder', 'Manufacturing_Plans')">
                                            <i class="bi bi-folder-fill text-primary me-2"></i>
                                            <span>Manufacturing Plans</span>
                                            <span class="badge bg-secondary rounded-pill ms-2">25 files</span>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Manufacturing_Plans')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Manufacturing_Plans')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Manufacturing_Plans')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Manufacturing_Plans')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div style="cursor: pointer;" onclick="viewGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')">
                                            <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                                            <span>MRP_System_Manual.pdf</span>
                                            <span class="badge bg-primary rounded-pill ms-2">Yesterday</span>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'pdf', 'MRP_System_Manual.pdf')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#driveModal">
                                        <i class="bi bi-folder me-2"></i>Open Drive
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Docs -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-docs-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-file-earmark-text"></i> Google Docs</h5>
                                <div class="component-actions">
                                    <button id="refresh-docs" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="create-new-doc" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#docsModal"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div style="cursor: pointer;" onclick="viewGoogleItem('docs', 'document', 'Production_Planning_Guide')">
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <span>Production Planning Guide</span>
                                            <span class="badge bg-primary rounded-pill ms-2">Today</span>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Production_Planning_Guide')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Production_Planning_Guide')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Production_Planning_Guide')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Production_Planning_Guide')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div style="cursor: pointer;" onclick="viewGoogleItem('docs', 'document', 'Inventory_Control_Procedures')">
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <span>Inventory Control Procedures</span>
                                            <span class="badge bg-primary rounded-pill ms-2">Yesterday</span>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Inventory_Control_Procedures')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Inventory_Control_Procedures')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Inventory_Control_Procedures')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Inventory_Control_Procedures')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div style="cursor: pointer;" onclick="viewGoogleItem('docs', 'document', 'Quality_Control_Guidelines')">
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            <span>Quality Control Guidelines</span>
                                            <span class="badge bg-primary rounded-pill ms-2">3 days ago</span>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Quality_Control_Guidelines')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Quality_Control_Guidelines')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Quality_Control_Guidelines')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Quality_Control_Guidelines')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#docsModal">
                                        <i class="bi bi-file-earmark-text me-2"></i>View All Documents
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Sheets -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-sheets-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                                <div class="component-actions">
                                    <button id="refresh-sheets" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="create-new-sheet" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#mrp-sheetsModal"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div style="cursor: pointer;" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <span>Production_Schedule_2025.xlsx</span>
                                            <span class="badge bg-primary rounded-pill ms-2">2 days ago</span>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Production_Schedule_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div style="cursor: pointer;" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <span>Material_Requirements_Analysis.xlsx</span>
                                            <span class="badge bg-primary rounded-pill ms-2">1 week ago</span>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Analysis.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div style="cursor: pointer;" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')">
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <span>Capacity_Planning_Q2_2025.xlsx</span>
                                            <span class="badge bg-primary rounded-pill ms-2">2 weeks ago</span>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Capacity_Planning_Q2_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" id="view-all-sheets-btn" data-bs-toggle="modal" data-bs-target="#mrp-sheetsModal">
                                        <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Drive Modal -->
                    <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="driveModalLabel">Google Drive</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-3">
                                            <button class="btn btn-primary" id="upload-new-file">
                                                <i class="bi bi-upload me-2"></i>Upload File
                                            </button>
                                            <button class="btn btn-outline-secondary" id="refresh-drive-modal">
                                                <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                                            </button>
                                        </div>
                                        <div class="list-group">
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Inventory Documentation</h6>
                                                        <small>15 files - Last updated: Yesterday</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Inventory_Documentation')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Inventory_Documentation')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Inventory_Documentation')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Inventory_Documentation')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Supplier Contracts</h6>
                                                        <small>8 files - Last updated: 3 days ago</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Supplier_Contracts')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Supplier_Contracts')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Supplier_Contracts')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Supplier_Contracts')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Inventory_Report_Q2_2025.pdf</h6>
                                                        <small>2.4 MB - Last updated: Yesterday</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'pdf', 'Inventory_Report_Q2_2025.pdf')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'pdf', 'Inventory_Report_Q2_2025.pdf')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'pdf', 'Inventory_Report_Q2_2025.pdf')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'pdf', 'Inventory_Report_Q2_2025.pdf')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <a href="https://drive.google.com" target="_blank" class="btn btn-primary">Open in Drive</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Docs Modal -->
                    <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="docsModalLabel">Google Docs</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-3">
                                            <button class="btn btn-primary" id="create-new-document">
                                                <i class="bi bi-file-earmark-plus me-2"></i>Create Document
                                            </button>
                                            <button class="btn btn-outline-secondary" id="refresh-docs-modal">
                                                <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                                            </button>
                                        </div>
                                        <div class="list-group">
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Inventory Management Procedures</h6>
                                                        <small>Last edited: Today</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Inventory_Management_Procedures')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Inventory_Management_Procedures')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Inventory_Management_Procedures')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Inventory_Management_Procedures')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Supplier Evaluation Criteria</h6>
                                                        <small>Last edited: Yesterday</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Supplier_Evaluation_Criteria')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Supplier_Evaluation_Criteria')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Supplier_Evaluation_Criteria')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Supplier_Evaluation_Criteria')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Production Planning Guidelines</h6>
                                                        <small>Last edited: 3 days ago</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Production_Planning_Guidelines')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Production_Planning_Guidelines')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Production_Planning_Guidelines')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Production_Planning_Guidelines')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <a href="https://docs.google.com" target="_blank" class="btn btn-primary">Open in Docs</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Sheets Modal -->
                    <div class="modal fade" id="mrp-sheetsModal-2" tabindex="-1" aria-labelledby="mrp-sheetsModalLabel-2" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="sheetsModalLabel">Google Sheets</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-3">
                                            <button class="btn btn-primary" id="create-new-spreadsheet">
                                                <i class="bi bi-file-earmark-plus me-2"></i>Create Spreadsheet
                                            </button>
                                            <button class="btn btn-outline-secondary" id="refresh-sheets-modal">
                                                <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                                            </button>
                                        </div>
                                        <div class="list-group">
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Inventory_Tracking_2025.xlsx</h6>
                                                        <small>Last edited: 2 days ago</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Material_Requirements_Q2_2025.xlsx</h6>
                                                        <small>Last edited: 1 week ago</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Q2_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Q2_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Q2_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Q2_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                                    <div>
                                                        <h6 class="mb-1">Supplier_Pricing_Comparison.xlsx</h6>
                                                        <small>Last edited: 2 weeks ago</small>
                                                    </div>
                                                </div>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Supplier_Pricing_Comparison.xlsx')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Supplier_Pricing_Comparison.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Supplier_Pricing_Comparison.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Supplier_Pricing_Comparison.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <a href="https://sheets.google.com" target="_blank" class="btn btn-primary">Open in Sheets</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Calendar -->
                    <div class="col-md-6 mb-4">
                        <div class="google-integration-component google-calendar-component">
                            <div class="component-header d-flex justify-content-between align-items-center p-3" style="background-color: var(--app-primary-light); border-radius: 10px 10px 0 0;">
                                <h5 class="mb-0"><i class="bi bi-calendar3"></i> Google Calendar</h5>
                                <div class="component-actions">
                                    <button id="refresh-calendar" class="btn btn-sm btn-outline-primary"><i class="bi bi-arrow-clockwise"></i></button>
                                    <button id="create-new-event" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#calendarModal"><i class="bi bi-plus"></i></button>
                                </div>
                            </div>
                            <div class="component-body p-3" style="background-color: white; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                <div class="list-group">
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Inventory Audit</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-clock me-1"></i>Today, 2:00 PM - 4:00 PM
                                            </div>
                                        </div>
                                        <span class="badge bg-warning rounded-pill">Today</span>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Supplier Meeting</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-clock me-1"></i>Tomorrow, 10:00 AM - 11:00 AM
                                            </div>
                                        </div>
                                        <span class="badge bg-info rounded-pill">Tomorrow</span>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Production Planning</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-clock me-1"></i>May 15, 9:00 AM - 10:30 AM
                                            </div>
                                        </div>
                                        <span class="badge bg-secondary rounded-pill">Next Week</span>
                                    </a>
                                </div>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#calendarModal">
                                        <i class="bi bi-calendar3 me-2"></i>View Full Calendar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Integration Modals -->
    <!-- Gmail Modal -->        <div class="modal fade" id="mrp-gmailModal" tabindex="-1" aria-labelledby="mrp-gmailModalLabel" aria-hidden="true" data-bs-backdrop="true" data-bs-keyboard="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="mrp-gmailModalLabel"><i class="bi bi-envelope"></i> Gmail</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" onclick="if(typeof cleanupModalBackdrops==='function'){cleanupModalBackdrops();}"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-3 border-end">
                            <div class="d-grid gap-2 mb-3">
                                <button class="btn btn-primary" id="mrp-compose-new-email" onclick="document.getElementById('mrp-compose-tab').click()">
                                    <i class="bi bi-pencil-square me-2"></i>Compose
                                </button>
                            </div>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action active d-flex justify-content-between align-items-center" id="mrp-inbox-link" onclick="document.getElementById('mrp-inbox-tab').click()">
                                    <div><i class="bi bi-inbox me-2"></i>Inbox</div>
                                    <span class="badge bg-primary rounded-pill">3</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="mrp-starred-link">
                                    <div><i class="bi bi-star me-2"></i>Starred</div>
                                    <span class="badge bg-secondary rounded-pill">2</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="mrp-sent-link" onclick="document.getElementById('mrp-sent-tab').click()">
                                    <div><i class="bi bi-send me-2"></i>Sent</div>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="mrp-drafts-link">
                                    <div><i class="bi bi-file-earmark me-2"></i>Drafts</div>
                                    <span class="badge bg-secondary rounded-pill">1</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="mrp-important-link">
                                    <div><i class="bi bi-bookmark me-2"></i>Important</div>
                                </a>
                            </div>

                            <div class="mt-4">
                                <h6 class="text-muted mb-2">LABELS</h6>
                                <div class="list-group">
                                    <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="color-dot bg-success me-2"></span>Inventory
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="color-dot bg-danger me-2"></span>Urgent
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="color-dot bg-warning me-2"></span>Follow-up
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="color-dot bg-info me-2"></span>Production
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" class="form-control" placeholder="Search emails..." id="email-search">
                                    <button class="btn btn-outline-secondary" type="button" id="search-btn" onclick="searchEmails(document.getElementById('email-search').value)"><i class="bi bi-search"></i></button>
                                </div>
                                <div>
                                    <div class="dropdown d-inline-block me-2">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sort-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-sort-down"></i> Sort
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="sort-dropdown">
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="date-desc" onclick="sortEmails('date-desc'); return false;">Newest first</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="date-asc" onclick="sortEmails('date-asc'); return false;">Oldest first</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="sender-asc" onclick="sortEmails('sender-asc'); return false;">Sender A-Z</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="sender-desc" onclick="sortEmails('sender-desc'); return false;">Sender Z-A</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="subject-asc" onclick="sortEmails('subject-asc'); return false;">Subject A-Z</a></li>
                                            <li><a class="dropdown-item sort-option" href="#" data-sort="subject-desc" onclick="sortEmails('subject-desc'); return false;">Subject Z-A</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown d-inline-block me-2">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="filter-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-funnel"></i> Filter
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="filter-dropdown">
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="all" onclick="filterEmails('all'); return false;">All emails</a></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="unread" onclick="filterEmails('unread'); return false;">Unread</a></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="read" onclick="filterEmails('read'); return false;">Read</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><h6 class="dropdown-header">By Label</h6></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="label-inventory" onclick="filterEmails('label-inventory'); return false;">
                                                <span class="badge bg-success">Inventory</span>
                                            </a></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="label-production" onclick="filterEmails('label-production'); return false;">
                                                <span class="badge bg-info">Production</span>
                                            </a></li>
                                            <li><a class="dropdown-item filter-option" href="#" data-filter="label-urgent" onclick="filterEmails('label-urgent'); return false;">
                                                <span class="badge bg-danger">Urgent</span>
                                            </a></li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-outline-secondary" id="refresh-gmail" onclick="showAllEmails(); return false;">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Gmail Tabs -->
                            <ul class="nav nav-tabs mb-3" id="mrp-gmailTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="mrp-inbox-tab" data-bs-toggle="tab" data-bs-target="#mrp-inbox-content" type="button" role="tab" aria-controls="mrp-inbox-content" aria-selected="true">Inbox</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="mrp-sent-tab" data-bs-toggle="tab" data-bs-target="#mrp-sent-content" type="button" role="tab" aria-controls="mrp-sent-content" aria-selected="false">Sent</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="mrp-read-tab" data-bs-toggle="tab" data-bs-target="#mrp-read-content" type="button" role="tab" aria-controls="mrp-read-content" aria-selected="false">Read</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="mrp-compose-tab" data-bs-toggle="tab" data-bs-target="#mrp-compose-content" type="button" role="tab" aria-controls="mrp-compose-content" aria-selected="false">Compose</button>
                                </li>
                            </ul>

                            <div class="tab-content" id="mrp-gmailTabContent">
                                <!-- Inbox Tab Content -->
                                <div class="tab-pane fade show active" id="mrp-inbox-content" role="tabpanel" aria-labelledby="mrp-inbox-tab">
                                    <!-- Email List Section -->
                                    <div id="mrp-email-list-section">
                                        <div class="list-group">
                                            <a href="#" class="list-group-item list-group-item-action unread" data-sender="sarah-chen" data-subject="Inventory update: New shipment arrived" onclick="openEmail('sarah-chen', 'Inventory update: New shipment arrived')">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">SC</div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1 fw-bold">Sarah Chen</h6>
                                                            <small class="text-muted">10m</small>
                                                        </div>
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <p class="mb-1 fw-bold">Inventory update: New shipment arrived</p>
                                                            <span class="badge bg-success rounded-pill">Inventory</span>
                                                        </div>
                                                        <small class="text-muted">The new shipment of Raw Material A has arrived and been logged in the system.</small>
                                                    </div>
                                                </div>
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action unread" data-sender="john-davis" data-subject="Production schedule for next week" onclick="openEmail('john-davis', 'Production schedule for next week')">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #ea4335; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">JD</div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1 fw-bold">John Davis</h6>
                                                            <small class="text-muted">1h</small>
                                                        </div>
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <p class="mb-1 fw-bold">Production schedule for next week</p>
                                                            <span class="badge bg-info rounded-pill">Production</span>
                                                        </div>
                                                        <small class="text-muted">Please review the attached production schedule for next week.</small>
                                                    </div>
                                                </div>
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action unread" data-sender="rachel-miller" data-subject="Supplier price update for Raw Material A" onclick="openEmail('rachel-miller', 'Supplier price update for Raw Material A')">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #34a853; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">RM</div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <h6 class="mb-1 fw-bold">Rachel Miller</h6>
                                                            <small class="text-muted">3h</small>
                                                        </div>
                                                        <div class="d-flex w-100 justify-content-between">
                                                            <p class="mb-1 fw-bold">Supplier price update for Raw Material A</p>
                                                            <span class="badge bg-danger rounded-pill">Urgent</span>
                                                        </div>
                                                        <small class="text-muted">Our supplier has informed us of a 5% price increase for Raw Material A starting next month.</small>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Sent Tab Content -->
                                <div class="tab-pane fade" id="mrp-sent-content" role="tabpanel" aria-labelledby="mrp-sent-tab">
                                    <div class="list-group">
                                        <a href="#" class="list-group-item list-group-item-action">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0 me-3">
                                                    <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">ME</div>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <div class="d-flex w-100 justify-content-between">
                                                        <h6 class="mb-1">To: Supplier Team</h6>
                                                        <small class="text-muted">11:45 AM</small>
                                                    </div>
                                                    <p class="mb-1">Re: Material order confirmation</p>
                                                    <small class="text-muted">Thank you for confirming our order. We look forward to receiving the shipment next week...</small>
                                                </div>
                                            </div>
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0 me-3">
                                                    <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">ME</div>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <div class="d-flex w-100 justify-content-between">
                                                        <h6 class="mb-1">To: Production Team</h6>
                                                        <small class="text-muted">Yesterday</small>
                                                    </div>
                                                    <p class="mb-1">Q2 Production Schedule</p>
                                                    <small class="text-muted">Please find attached the Q2 production schedule for review...</small>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>

                                <!-- Read Tab Content -->
                                <div class="tab-pane fade" id="mrp-read-content" role="tabpanel" aria-labelledby="mrp-read-tab">
                                    <!-- This content will be populated dynamically by the openEmail function -->
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i> Select an email from the inbox to view its content.
                                    </div>
                                </div>

                                <!-- Compose Tab Content -->
                                <div class="tab-pane fade" id="mrp-compose-content" role="tabpanel" aria-labelledby="mrp-compose-tab">
                                    <div class="card">
                                        <div class="card-body">
                                            <form>
                                                <div class="mb-3">
                                                    <label for="email-to" class="form-label">To</label>
                                                    <input type="email" class="form-control" id="email-to" placeholder="<EMAIL>" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="email-cc" class="form-label">Cc/Bcc</label>
                                                    <input type="text" class="form-control" id="email-cc" placeholder="<EMAIL>">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="email-subject" class="form-label">Subject</label>
                                                    <input type="text" class="form-control" id="email-subject" placeholder="Email subject">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="email-body" class="form-label">Message</label>
                                                    <textarea class="form-control" id="email-body" rows="10" placeholder="Compose your message here..."></textarea>
                                                </div>
                                                <div class="mb-3">
                                                    <div id="mrp-attachments-container" class="d-none mb-3">
                                                        <h6>Attachments</h6>
                                                        <div id="mrp-attachments-list" class="border p-2 rounded mb-2" style="max-height: 150px; overflow-y: auto;">
                                                            <!-- Attachments will be added here dynamically -->
                                                        </div>
                                                    </div>
                                                    <button type="button" class="btn btn-outline-secondary" id="mrp-attach-btn">
                                                        <i class="bi bi-paperclip"></i> Attach
                                                    </button>
                                                    <input type="file" class="d-none" id="mrp-file-input" multiple>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <button type="button" class="btn btn-outline-secondary">Discard</button>
                                                    <div>
                                                        <button type="button" class="btn btn-outline-primary me-2">Save Draft</button>
                                                        <button type="button" class="btn btn-primary">Send</button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="if(typeof cleanupModalBackdrops==='function'){cleanupModalBackdrops();}">Close</button>
                    <a href="https://mail.google.com" target="_blank" class="btn btn-primary">Open in Gmail</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Drive Modal -->
    <div class="modal fade" id="driveModal" tabindex="-1" aria-labelledby="driveModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="driveModalLabel">Google Drive</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-3">
                            <button class="btn btn-primary" id="upload-new-file">
                                <i class="bi bi-upload me-2"></i>Upload File
                            </button>
                            <button class="btn btn-outline-secondary" id="refresh-drive-modal">
                                <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                            </button>
                        </div>
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Inventory Documentation</h6>
                                        <small>15 files - Last updated: Yesterday</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Inventory_Documentation')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Inventory_Documentation')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Inventory_Documentation')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Inventory_Documentation')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-folder-fill text-primary me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Supplier Contracts</h6>
                                        <small>8 files - Last updated: 3 days ago</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'folder', 'Supplier_Contracts')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'folder', 'Supplier_Contracts')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'folder', 'Supplier_Contracts')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'folder', 'Supplier_Contracts')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Inventory_Report_Q2_2025.pdf</h6>
                                        <small>2.4 MB - Last updated: Yesterday</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'pdf', 'Inventory_Report_Q2_2025.pdf')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'pdf', 'Inventory_Report_Q2_2025.pdf')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'pdf', 'Inventory_Report_Q2_2025.pdf')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'pdf', 'Inventory_Report_Q2_2025.pdf')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://drive.google.com" target="_blank" class="btn btn-primary">Open in Drive</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Docs Modal -->
    <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="docsModalLabel">Google Docs</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-3">
                            <button class="btn btn-primary" id="create-new-document">
                                <i class="bi bi-file-earmark-plus me-2"></i>Create Document
                            </button>
                            <button class="btn btn-outline-secondary" id="refresh-docs-modal">
                                <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                            </button>
                        </div>
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Inventory Management Procedures</h6>
                                        <small>Last edited: Today</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Inventory_Management_Procedures')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Inventory_Management_Procedures')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Inventory_Management_Procedures')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Inventory_Management_Procedures')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Supplier Evaluation Criteria</h6>
                                        <small>Last edited: Yesterday</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Supplier_Evaluation_Criteria')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Supplier_Evaluation_Criteria')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Supplier_Evaluation_Criteria')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Supplier_Evaluation_Criteria')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Production Planning Guidelines</</h6>
                                        <small>Last edited: 3 days ago</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Production_Planning_Guidelines')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Production_Planning_Guidelines')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Production_Planning_Guidelines')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Production_Planning_Guidelines')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://docs.google.com" target="_blank" class="btn btn-primary">Open in Docs</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Sheets Modal -->
    <div class="modal fade" id="mrp-sheetsModal" tabindex="-1" aria-labelledby="mrp-sheetsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mrp-sheetsModalLabel">Google Sheets</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-3">
                            <button class="btn btn-primary" id="create-new-spreadsheet">
                                <i class="bi bi-file-earmark-plus me-2"></i>Create Spreadsheet
                            </button>
                            <button class="btn btn-outline-secondary" id="refresh-sheets-modal">
                                <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                            </button>
                        </div>
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Inventory_Tracking_2025.xlsx</h6>
                                        <small>Last edited: 2 days ago</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Inventory_Tracking_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Material_Requirements_Q2_2025.xlsx</h6>
                                        <small>Last edited: 1 week ago</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Q2_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Q2_2025.xlsx')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Q2_2025.xlsx')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Material_Requirements_Q2_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Supplier_Pricing_Comparison.xlsx</h6>
                                        <small>Last edited: 2 weeks ago</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Supplier_Pricing_Comparison.xlsx')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Supplier_Pricing_Comparison.xlsx')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Supplier_Pricing_Comparison.xlsx')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Supplier_Pricing_Comparison.xlsx')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://sheets.google.com" target="_blank" class="btn btn-primary">Open in Sheets</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Maps Modal -->
    <div class="modal fade" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mapsModalLabel">Google Maps</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-3">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search locations" aria-label="Search locations">
                                <button class="btn btn-primary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="ratio ratio-16x9">
                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://maps.google.com" target="_blank" class="btn btn-primary">Open in Maps</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Slack Modal -->
    <div class="modal fade" id="slackModal" tabindex="-1" aria-labelledby="slackModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="slackModalLabel">Slack</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-3">
                            <button class="btn btn-primary" id="new-slack-message">
                                <i class="bi bi-chat-text me-2"></i>New Message
                            </button>
                            <button class="btn btn-outline-secondary" id="refresh-slack">
                                <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                            </button>
                        </div>
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">#inventory-management</h6>
                                    <small>5 minutes ago</small>
                                </div>
                                <p class="mb-1">Sarah Chen: The new shipment of Raw Material A has been logged in the system.</p>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">#production-planning</h6>
                                    <small>30 minutes ago</small>
                                </div>
                                <p class="mb-1">John Davis: Updated production schedule for next week has been shared in Google Drive.</p>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">#supplier-management</h6>
                                    <small>2 hours ago</small>
                                </div>
                                <p class="mb-1">Rachel Miller: Supplier price update for Raw Material A - 5% increase starting next month.</p>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://slack.com" target="_blank" class="btn btn-primary">Open in Slack</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Attachments Modal -->
    <div class="modal fade" id="mrp-attachmentsModal" tabindex="-1" aria-labelledby="mrp-attachmentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mrp-attachmentsModalLabel">Attachments</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-between mb-3">
                        <button class="btn btn-primary" id="upload-attachment">
                            <i class="bi bi-paperclip me-2"></i>Upload Attachment
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="attachmentFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Filter By
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="attachmentFilterDropdown">
                                <li><a class="dropdown-item" href="#">All Files</a></li>
                                <li><a class="dropdown-item" href="#">Documents</a></li>
                                <li><a class="dropdown-item" href="#">Images</a></li>
                                <li><a class="dropdown-item" href="#">Spreadsheets</a></li>
                                <li><a class="dropdown-item" href="#">PDFs</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action" data-filename="BOM_Specifications.pdf" data-filetype="pdf">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">BOM_Specifications.pdf</h6>
                                        <small>2.4 MB - Uploaded: Yesterday</small>
                                    </div>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-sm btn-link file-action-view"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-link file-action-download"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-link file-action-share"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-link text-danger file-action-delete"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-filename="Production_Schedule_2025.xlsx" data-filetype="spreadsheet">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Production_Schedule_2025.xlsx</h6>
                                        <small>1.8 MB - Uploaded: 2 weeks ago</small>
                                    </div>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-sm btn-link file-action-view"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-link file-action-download"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-link file-action-share"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-link text-danger file-action-delete"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-filename="Product_Diagram.jpg" data-filetype="image">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-image text-primary me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Product_Diagram.jpg</h6>
                                        <small>3.2 MB - Uploaded: 1 month ago</small>
                                    </div>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-sm btn-link file-action-view"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-link file-action-download"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-link file-action-share"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-link text-danger file-action-delete"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" data-filename="Supplier_Requirements.docx" data-filetype="document">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1">Supplier_Requirements.docx</h6>
                                        <small>1.5 MB - Uploaded: 2 months ago</small>
                                    </div>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-sm btn-link file-action-view"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-link file-action-download"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-link file-action-share"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-link text-danger file-action-delete"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="download-selected">Download Selected</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Sample inventory data
        const inventoryData = [
            { id: 1, name: 'Raw Material A', category: 'Raw Materials', quantity: 500, reorderLevel: 100, status: 'good' },
            { id: 2, name: 'Component B', category: 'Components', quantity: 350, reorderLevel: 75, status: 'good' },
            { id: 3, name: 'Finished Product C', category: 'Finished Products', quantity: 200, reorderLevel: 50, status: 'good' },
            { id: 4, name: 'Raw Material D', category: 'Raw Materials', quantity: 80, reorderLevel: 100, status: 'warning' },
            { id: 5, name: 'Component E', category: 'Components', quantity: 30, reorderLevel: 50, status: 'warning' },
            { id: 6, name: 'Finished Product F', category: 'Finished Products', quantity: 5, reorderLevel: 20, status: 'danger' },
            { id: 7, name: 'Raw Material G', category: 'Raw Materials', quantity: 0, reorderLevel: 50, status: 'danger' },
            { id: 8, name: 'Component H', category: 'Components', quantity: 120, reorderLevel: 50, status: 'good' },
            { id: 9, name: 'Finished Product I', category: 'Finished Products', quantity: 45, reorderLevel: 30, status: 'good' },
            { id: 10, name: 'Raw Material J', category: 'Raw Materials', quantity: 250, reorderLevel: 100, status: 'good' },
        ];

        // Populate inventory table
        function populateInventoryTable() {
            const tableBody = document.getElementById('inventoryTableBody');
            tableBody.innerHTML = '';

            inventoryData.forEach(item => {
                const row = document.createElement('tr');

                // Determine status class
                let statusClass = 'status-good';
                if (item.status === 'warning') {
                    statusClass = 'status-warning';
                } else if (item.status === 'danger') {
                    statusClass = 'status-danger';
                }

                row.innerHTML = `
                    <td>${item.id}</td>
                    <td>${item.name}</td>
                    <td>${item.category}</td>
                    <td>${item.quantity}</td>
                    <td>${item.reorderLevel}</td>
                    <td><span class="status-indicator ${statusClass}"></span> ${item.status === 'good' ? 'Good' : item.status === 'warning' ? 'Low' : 'Critical'}</td>
                    <td>
                        <button class="btn btn-sm btn-primary">View</button>
                        <button class="btn btn-sm btn-secondary">Edit</button>
                    </td>
                `;

                tableBody.appendChild(row);
            });
        }

        // Create inventory chart
        function createInventoryChart() {
            const ctx = document.getElementById('inventoryChart').getContext('2d');

            // Group data by category
            const categories = [...new Set(inventoryData.map(item => item.category))];
            const categoryData = categories.map(category => {
                return {
                    category,
                    items: inventoryData.filter(item => item.category === category)
                };
            });

            // Prepare data for chart
            const labels = categoryData.map(cat => cat.category);
            const data = categoryData.map(cat => {
                return cat.items.reduce((sum, item) => sum + item.quantity, 0);
            });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Inventory Quantity by Category',
                        data: data,
                        backgroundColor: [
                            'rgba(156, 39, 176, 0.7)',
                            'rgba(123, 31, 162, 0.7)',
                            'rgba(186, 104, 200, 0.7)'
                        ],
                        borderColor: [
                            'rgba(156, 39, 176, 1)',
                            'rgba(123, 31, 162, 1)',
                            'rgba(186, 104, 200, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Create status chart
        function createStatusChart() {
            const ctx = document.getElementById('statusChart').getContext('2d');

            // Count items by status
            const statusCounts = {
                good: inventoryData.filter(item => item.status === 'good').length,
                warning: inventoryData.filter(item => item.status === 'warning').length,
                danger: inventoryData.filter(item => item.status === 'danger').length
            };

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Good', 'Low Stock', 'Critical'],
                    datasets: [{
                        data: [statusCounts.good, statusCounts.warning, statusCounts.danger],
                        backgroundColor: [
                            'rgba(156, 39, 176, 0.7)',
                            'rgba(255, 193, 7, 0.7)',
                            'rgba(220, 53, 69, 0.7)'
                        ],
                        borderColor: [
                            'rgba(156, 39, 176, 1)',
                            'rgba(255, 193, 7, 1)',
                            'rgba(220, 53, 69, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Function to generate sample insights when the API is not available
        function generateSampleInsights() {
            const sampleData = {
                inventoryOptimization: {
                    items: [
                        {
                            id: 1,
                            name: "Raw Material A",
                            currentStock: 500,
                            optimalStock: 350,
                            excessCost: 1500,
                            recommendation: "Reduce stock level by 150 units to save $1,500 in carrying costs"
                        },
                        {
                            id: 4,
                            name: "Raw Material D",
                            currentStock: 80,
                            optimalStock: 150,
                            stockoutRisk: "High",
                            recommendation: "Increase stock level by 70 units to prevent production delays"
                        },
                        {
                            id: 6,
                            name: "Finished Product F",
                            currentStock: 5,
                            optimalStock: 25,
                            stockoutRisk: "Critical",
                            recommendation: "Expedite production to increase stock by 20 units"
                        }
                    ]
                },
                demandForecasting: {
                    products: [
                        {
                            id: 3,
                            name: "Finished Product C",
                            currentDemand: 45,
                            forecastedDemand: 65,
                            trend: "Increasing",
                            confidence: 85,
                            seasonalFactors: ["Holiday Season", "Promotional Event"]
                        },
                        {
                            id: 6,
                            name: "Finished Product F",
                            currentDemand: 30,
                            forecastedDemand: 15,
                            trend: "Decreasing",
                            confidence: 78,
                            seasonalFactors: ["End of Season"]
                        },
                        {
                            id: 9,
                            name: "Finished Product I",
                            currentDemand: 25,
                            forecastedDemand: 40,
                            trend: "Increasing",
                            confidence: 92,
                            seasonalFactors: ["New Market Entry"]
                        }
                    ]
                },
                productionPlanning: {
                    schedules: [
                        {
                            id: 1,
                            product: "Finished Product C",
                            currentCapacity: 50,
                            requiredCapacity: 65,
                            bottleneck: "Assembly Line 2",
                            recommendation: "Add additional shift to increase capacity by 20 units"
                        },
                        {
                            id: 2,
                            product: "Finished Product F",
                            currentCapacity: 40,
                            requiredCapacity: 25,
                            excessCapacity: 15,
                            recommendation: "Reallocate resources to other product lines"
                        },
                        {
                            id: 3,
                            product: "Finished Product I",
                            currentCapacity: 35,
                            requiredCapacity: 40,
                            bottleneck: "Quality Control",
                            recommendation: "Optimize QC process to increase throughput"
                        }
                    ]
                },
                recommendations: [
                    "Implement just-in-time inventory management for Raw Material A to reduce carrying costs",
                    "Increase safety stock for Raw Material D to prevent production delays",
                    "Adjust production schedule to accommodate increased demand for Finished Product C",
                    "Develop contingency plan for potential stockout of Finished Product F",
                    "Optimize quality control process to increase production capacity for Finished Product I"
                ],
                timestamp: new Date().toISOString()
            };

            const insightsContainer = document.getElementById('ai-insights-container');
            insightsContainer.innerHTML = '';

            // Store insights data globally for export
            window.aiInsightsData = sampleData;

            // Create insights sections
            createInventoryOptimizationSection(sampleData, insightsContainer);
            createDemandForecastingSection(sampleData, insightsContainer);
            createProductionPlanningSection(sampleData, insightsContainer);
            createRecommendationsSection(sampleData, insightsContainer);

            // Add event listeners for interactive elements
            addInteractiveEventListeners(sampleData);
        }

        // Function to fetch and display AI-powered materials planning insights
        function fetchAIInsights() {
            const insightsContainer = document.getElementById('ai-insights-container');

            // Show loading spinner
            insightsContainer.innerHTML = `
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;

            // Fetch insights from the Integration Hub
            fetch('http://localhost:8000/api/ai-analytics/materials-planning')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('AI insights:', data);

                    // Store insights data globally for export
                    window.aiInsightsData = data;

                    // Clear the container
                    insightsContainer.innerHTML = '';

                    // Generate sample insights if the API doesn't return the expected data structure
                    if (!data.inventoryOptimization || !data.demandForecasting || !data.productionPlanning) {
                        generateSampleInsights();
                        return;
                    }

                    // Create insights sections
                    createInventoryOptimizationSection(data, insightsContainer);
                    createDemandForecastingSection(data, insightsContainer);
                    createProductionPlanningSection(data, insightsContainer);
                    createRecommendationsSection(data, insightsContainer);

                    // Add event listeners for interactive elements
                    addInteractiveEventListeners(data);
                })
                .catch(error => {
                    console.error('Error fetching AI insights:', error);
                    insightsContainer.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            Unable to connect to AI analytics engine. Please make sure the Integration Hub is running.
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-primary" id="generate-sample-insights">
                                    Generate Sample Insights
                                </button>
                            </div>
                        </div>
                    `;

                    // Add event listener for the sample insights button
                    document.getElementById('generate-sample-insights').addEventListener('click', generateSampleInsights);
                });
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize sidebar toggle functionality
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebarMenu');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    sidebar.classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth < 768) {
                    const isClickInsideSidebar = sidebar.contains(event.target);
                    const isClickOnToggle = sidebarToggle && sidebarToggle.contains(event.target);

                    if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768 && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });

            populateInventoryTable();
            createInventoryChart();
            createStatusChart();

            // Fetch AI insights
            fetchAIInsights();

            // Add event listener for refresh insights button
            document.getElementById('refresh-insights-btn').addEventListener('click', fetchAIInsights);

            // Add event listeners for expand/collapse all insights
            document.getElementById('expand-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.show();
                });
            });

            document.getElementById('collapse-all-insights').addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('[id$="Insights"], #recommendationsSection').forEach(section => {
                    const bsCollapse = new bootstrap.Collapse(section, { toggle: false });
                    bsCollapse.hide();
                });
            });

            // Add event listener for export insights
            document.getElementById('export-insights').addEventListener('click', function(e) {
                e.preventDefault();
                if (window.aiInsightsData) {
                    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(window.aiInsightsData, null, 2));
                    const downloadAnchorNode = document.createElement('a');
                    downloadAnchorNode.setAttribute("href", dataStr);
                    downloadAnchorNode.setAttribute("download", "materials_planning_insights.json");
                    document.body.appendChild(downloadAnchorNode);
                    downloadAnchorNode.click();
                    downloadAnchorNode.remove();
                }
            });

            // Add event listeners for data-isa-action attributes
            document.addEventListener('click', function(event) {
                // Handle attachments links
                if (event.target.matches('[data-isa-action="open-attachments"]') ||
                    (event.target.parentElement && event.target.parentElement.matches('[data-isa-action="open-attachments"]'))) {
                    event.preventDefault();
                    const attachmentsModal = document.getElementById('attachmentsModal');
                    if (attachmentsModal) {
                        // Remove any existing modal first
                        const existingModal = bootstrap.Modal.getInstance(attachmentsModal);
                        if (existingModal) {
                            existingModal.dispose();
                        }

                        // Create and show a new modal
                        const modal = new bootstrap.Modal(attachmentsModal);
                        modal.show();

                        console.log('Attachments modal opened');
                    } else {
                        console.error('Attachments modal not found');
                    }
                }
            });

            // Add direct click handler for the attachments link in the sidebar
            document.getElementById('attachments-link').addEventListener('click', function(e) {
                e.preventDefault();
                const attachmentsModal = document.getElementById('attachmentsModal');
                if (attachmentsModal) {
                    // Remove any existing modal first
                    const existingModal = bootstrap.Modal.getInstance(attachmentsModal);
                    if (existingModal) {
                        existingModal.dispose();
                    }

                    // Create and show a new modal
                    const modal = new bootstrap.Modal(attachmentsModal);
                    modal.show();

                    console.log('Attachments modal opened via direct click');

                    // Add click handlers to attachment items after the modal is shown
                    setTimeout(() => {
                        setupAttachmentItemClickHandlers();
                    }, 300);
                }
            });

            // Function to set up click handlers for attachment items
            function setupAttachmentItemClickHandlers() {
                console.log('Setting up attachment item click handlers');
                const attachmentItems = document.querySelectorAll('.attachment-item');

                attachmentItems.forEach(item => {
                    console.log('Adding click handler to:', item.dataset.filename);

                    // Remove any existing click handlers
                    item.removeEventListener('click', attachmentClickHandler);

                    // Add new click handler
                    item.addEventListener('click', attachmentClickHandler);

                    // Make sure the item is clickable
                    item.style.cursor = 'pointer';
                });
            }

            // Function to handle attachment item clicks
            function attachmentClickHandler(e) {
                // Get the filename and filetype from the data attributes
                const filename = this.dataset.filename;
                const filetype = this.dataset.filetype || 'document';

                console.log(`Clicked on attachment: ${filename} (${filetype})`);

                // Close the attachments modal
                const attachmentsModal = bootstrap.Modal.getInstance(document.getElementById('attachmentsModal'));
                if (attachmentsModal) {
                    attachmentsModal.hide();
                }

                // Open the file viewer after a short delay
                setTimeout(() => {
                    viewAttachment(filetype, filename);
                }, 300);
            }

            // Add click handlers for attachment items in the modal
            document.addEventListener('DOMContentLoaded', function() {
                // Setup handlers when the modal is shown
                const attachmentsModal = document.getElementById('attachmentsModal');
                if (attachmentsModal) {
                    attachmentsModal.addEventListener('shown.bs.modal', function() {
                        console.log('Attachments modal shown, setting up click handlers');
                        setupAttachmentItemClickHandlers();
                    });
                    console.log('Added shown.bs.modal event listener to attachmentsModal');
                }

                // Initial setup of attachment item click handlers
                setupAttachmentItemClickHandlers();
            });

        // Function to create the Inventory Optimization section
        function createInventoryOptimizationSection(data, container) {
            if (!data.inventoryOptimization || !data.inventoryOptimization.items) {
                return;
            }

            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'inventoryOptimization';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
                <h6 class="mb-0">
                    <i class="bi bi-box-seam me-2"></i>
                    Inventory Optimization
                </h6>
                <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#inventoryOptimizationInsights">
                    <i class="bi bi-chevron-down"></i>
                </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'inventoryOptimizationInsights';

            const itemsRow = document.createElement('div');
            itemsRow.className = 'row';

            data.inventoryOptimization.items.forEach(item => {
                const itemCol = document.createElement('div');
                itemCol.className = 'col-md-4 mb-3';

                const itemCard = document.createElement('div');
                itemCard.className = 'card h-100';

                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';

                // Determine status badge class
                let statusClass = '';
                let statusText = '';

                if (item.excessCost) {
                    statusClass = 'bg-warning';
                    statusText = 'Excess Stock';
                } else if (item.stockoutRisk === 'High') {
                    statusClass = 'bg-warning';
                    statusText = 'Low Stock';
                } else if (item.stockoutRisk === 'Critical') {
                    statusClass = 'bg-danger';
                    statusText = 'Critical Stock';
                } else {
                    statusClass = 'bg-success';
                    statusText = 'Optimal';
                }

                cardBody.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${item.name}</h6>
                        <span class="badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Current Stock:</span>
                        <span class="fw-bold">${item.currentStock} units</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Optimal Stock:</span>
                        <span class="fw-bold">${item.optimalStock} units</span>
                    </div>
                    ${item.excessCost ? `
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Excess Cost:</span>
                            <span class="fw-bold text-danger">$${item.excessCost}</span>
                        </div>
                    ` : ''}
                    ${item.stockoutRisk ? `
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Stockout Risk:</span>
                            <span class="fw-bold text-${item.stockoutRisk === 'Critical' ? 'danger' : 'warning'}">${item.stockoutRisk}</span>
                        </div>
                    ` : ''}
                    <p class="small text-muted mb-2">${item.recommendation}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2 optimize-inventory-btn"
                            data-item-id="${item.id}"
                            data-item-name="${item.name}">
                        Apply Optimization
                    </button>
                `;

                itemCard.appendChild(cardBody);
                itemCol.appendChild(itemCard);
                itemsRow.appendChild(itemCol);
            });

            sectionBody.appendChild(itemsRow);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            container.appendChild(sectionCol);
        }

        // Function to create the Demand Forecasting section
        function createDemandForecastingSection(data, container) {
            if (!data.demandForecasting || !data.demandForecasting.products) {
                return;
            }

            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'demandForecasting';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
                <h6 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    Demand Forecasting
                </h6>
                <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#demandForecastingInsights">
                    <i class="bi bi-chevron-down"></i>
                </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'demandForecastingInsights';

            const productsRow = document.createElement('div');
            productsRow.className = 'row';

            data.demandForecasting.products.forEach(product => {
                const productCol = document.createElement('div');
                productCol.className = 'col-md-4 mb-3';

                const productCard = document.createElement('div');
                productCard.className = 'card h-100';

                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';

                // Determine trend badge class
                let trendClass = '';
                let trendIcon = '';

                if (product.trend === 'Increasing') {
                    trendClass = 'bg-success';
                    trendIcon = 'bi-graph-up';
                } else if (product.trend === 'Decreasing') {
                    trendClass = 'bg-danger';
                    trendIcon = 'bi-graph-down';
                } else {
                    trendClass = 'bg-info';
                    trendIcon = 'bi-dash';
                }

                // Calculate percentage change
                const percentChange = ((product.forecastedDemand - product.currentDemand) / product.currentDemand * 100).toFixed(1);

                cardBody.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${product.name}</h6>
                        <span class="badge ${trendClass}"><i class="bi ${trendIcon} me-1"></i>${product.trend}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Current Demand:</span>
                        <span class="fw-bold">${product.currentDemand} units/month</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Forecasted Demand:</span>
                        <span class="fw-bold">${product.forecastedDemand} units/month</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Change:</span>
                        <span class="fw-bold text-${percentChange > 0 ? 'success' : 'danger'}">${percentChange}%</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Confidence:</span>
                        <span class="fw-bold">${product.confidence}%</span>
                    </div>
                    <div class="progress mb-2" style="height: 5px;">
                        <div class="progress-bar bg-${product.confidence > 80 ? 'success' : (product.confidence > 60 ? 'warning' : 'danger')}"
                             style="width: ${product.confidence}%"></div>
                    </div>
                    <p class="small text-muted mb-2">Seasonal Factors: ${product.seasonalFactors.join(', ')}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2 view-forecast-btn"
                            data-product-id="${product.id}"
                            data-product-name="${product.name}">
                        View Detailed Forecast
                    </button>
                `;

                productCard.appendChild(cardBody);
                productCol.appendChild(productCard);
                productsRow.appendChild(productCol);
            });

            sectionBody.appendChild(productsRow);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            container.appendChild(sectionCol);
        }

        // Function to create the Production Planning section
        function createProductionPlanningSection(data, container) {
            if (!data.productionPlanning || !data.productionPlanning.schedules) {
                return;
            }

            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'productionPlanning';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
                <h6 class="mb-0">
                    <i class="bi bi-gear me-2"></i>
                    Production Planning
                </h6>
                <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#productionPlanningInsights">
                    <i class="bi bi-chevron-down"></i>
                </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'productionPlanningInsights';

            const schedulesRow = document.createElement('div');
            schedulesRow.className = 'row';

            data.productionPlanning.schedules.forEach(schedule => {
                const scheduleCol = document.createElement('div');
                scheduleCol.className = 'col-md-4 mb-3';

                const scheduleCard = document.createElement('div');
                scheduleCard.className = 'card h-100';

                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';

                // Determine capacity status
                let statusClass = '';
                let statusText = '';

                if (schedule.bottleneck) {
                    statusClass = 'bg-danger';
                    statusText = 'Capacity Shortage';
                } else if (schedule.excessCapacity) {
                    statusClass = 'bg-warning';
                    statusText = 'Excess Capacity';
                } else {
                    statusClass = 'bg-success';
                    statusText = 'Optimal Capacity';
                }

                // Calculate capacity utilization
                const capacityUtilization = ((schedule.requiredCapacity / schedule.currentCapacity) * 100).toFixed(0);

                cardBody.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${schedule.product}</h6>
                        <span class="badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Current Capacity:</span>
                        <span class="fw-bold">${schedule.currentCapacity} units/week</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Required Capacity:</span>
                        <span class="fw-bold">${schedule.requiredCapacity} units/week</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">Utilization:</span>
                        <span class="fw-bold">${capacityUtilization}%</span>
                    </div>
                    <div class="progress mb-2" style="height: 5px;">
                        <div class="progress-bar bg-${capacityUtilization > 95 ? 'danger' : (capacityUtilization < 70 ? 'warning' : 'success')}"
                             style="width: ${Math.min(capacityUtilization, 100)}%"></div>
                    </div>
                    ${schedule.bottleneck ? `
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Bottleneck:</span>
                            <span class="fw-bold text-danger">${schedule.bottleneck}</span>
                        </div>
                    ` : ''}
                    ${schedule.excessCapacity ? `
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Excess Capacity:</span>
                            <span class="fw-bold text-warning">${schedule.excessCapacity} units/week</span>
                        </div>
                    ` : ''}
                    <p class="small text-muted mb-2">${schedule.recommendation}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2 optimize-production-btn"
                            data-schedule-id="${schedule.id}"
                            data-product-name="${schedule.product}">
                        Optimize Production
                    </button>
                `;

                scheduleCard.appendChild(cardBody);
                scheduleCol.appendChild(scheduleCard);
                schedulesRow.appendChild(scheduleCol);
            });

            sectionBody.appendChild(schedulesRow);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            container.appendChild(sectionCol);
        }

        // Function to create the Recommendations section
        // Function to add interactive event listeners to the AI insights
        function addInteractiveEventListeners(data) {
            // Add event listeners for inventory optimization buttons
            document.querySelectorAll('.optimize-inventory-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const itemId = this.dataset.itemId;
                    const itemName = this.dataset.itemName;

                    // Find the item in the data
                    let item;
                    if (data.inventoryOptimization && data.inventoryOptimization.items) {
                        item = data.inventoryOptimization.items.find(i => i.id == itemId);
                    }

                    if (!item) return;

                    // Create modal for inventory optimization
                    const modalHTML = `
                        <div class="modal fade" id="inventoryOptimizationModal" tabindex="-1" aria-labelledby="inventoryOptimizationModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="inventoryOptimizationModalLabel">Inventory Optimization: ${itemName}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="alert alert-${item.excessCost ? 'warning' : (item.stockoutRisk ? 'danger' : 'info')}">
                                            <i class="bi bi-info-circle"></i>
                                            ${item.recommendation}
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Current Status</div>
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Current Stock:</span>
                                                            <span class="fw-bold">${item.currentStock} units</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Optimal Stock:</span>
                                                            <span class="fw-bold">${item.optimalStock} units</span>
                                                        </div>
                                                        ${item.excessCost ? `
                                                            <div class="d-flex justify-content-between mb-2">
                                                                <span>Excess Cost:</span>
                                                                <span class="fw-bold text-danger">$${item.excessCost}</span>
                                                            </div>
                                                        ` : ''}
                                                        ${item.stockoutRisk ? `
                                                            <div class="d-flex justify-content-between mb-2">
                                                                <span>Stockout Risk:</span>
                                                                <span class="fw-bold text-${item.stockoutRisk === 'Critical' ? 'danger' : 'warning'}">${item.stockoutRisk}</span>
                                                            </div>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Action Plan</div>
                                                    <div class="card-body">
                                                        ${item.excessCost ? `
                                                            <ol>
                                                                <li class="mb-2">Reduce order quantity for next procurement cycle</li>
                                                                <li class="mb-2">Consider promotional activities to increase demand</li>
                                                                <li class="mb-2">Review safety stock levels</li>
                                                                <li class="mb-2">Adjust reorder points in the system</li>
                                                            </ol>
                                                        ` : ''}
                                                        ${item.stockoutRisk ? `
                                                            <ol>
                                                                <li class="mb-2">Place emergency order for ${item.optimalStock - item.currentStock} units</li>
                                                                <li class="mb-2">Expedite existing purchase orders</li>
                                                                <li class="mb-2">Review lead times with suppliers</li>
                                                                <li class="mb-2">Adjust safety stock levels</li>
                                                            </ol>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-3">
                                            <div class="card-header">Implementation</div>
                                            <div class="card-body">
                                                <form>
                                                    <div class="mb-3">
                                                        <label for="adjustmentQuantity" class="form-label">Adjustment Quantity</label>
                                                        <input type="number" class="form-control" id="adjustmentQuantity" value="${item.optimalStock - item.currentStock}">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="adjustmentDate" class="form-label">Implementation Date</label>
                                                        <input type="date" class="form-control" id="adjustmentDate">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="assignee" class="form-label">Assign To</label>
                                                        <select class="form-select" id="assignee">
                                                            <option>John Doe</option>
                                                            <option>Jane Smith</option>
                                                            <option>Mike Johnson</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="notes" class="form-label">Notes</label>
                                                        <textarea class="form-control" id="notes" rows="3"></textarea>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary">Implement Optimization</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Remove any existing modal
                    const existingModal = document.getElementById('inventoryOptimizationModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // Add modal to the document
                    document.body.insertAdjacentHTML('beforeend', modalHTML);

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('inventoryOptimizationModal'));
                    modal.show();
                });
            });

            // Add event listeners for demand forecast buttons
            document.querySelectorAll('.view-forecast-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const productId = this.dataset.productId;
                    const productName = this.dataset.productName;

                    // Find the product in the data
                    let product;
                    if (data.demandForecasting && data.demandForecasting.products) {
                        product = data.demandForecasting.products.find(p => p.id == productId);
                    }

                    if (!product) return;

                    // Create modal for detailed forecast
                    const modalHTML = `
                        <div class="modal fade" id="forecastModal" tabindex="-1" aria-labelledby="forecastModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="forecastModalLabel">Demand Forecast: ${productName}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Forecast Summary</div>
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Current Demand:</span>
                                                            <span class="fw-bold">${product.currentDemand} units/month</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Forecasted Demand:</span>
                                                            <span class="fw-bold">${product.forecastedDemand} units/month</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Trend:</span>
                                                            <span class="fw-bold text-${product.trend === 'Increasing' ? 'success' : (product.trend === 'Decreasing' ? 'danger' : 'info')}">${product.trend}</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Confidence:</span>
                                                            <span class="fw-bold">${product.confidence}%</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Seasonal Factors:</span>
                                                            <span class="fw-bold">${product.seasonalFactors.join(', ')}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Monthly Forecast</div>
                                                    <div class="card-body">
                                                        <table class="table table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>Month</th>
                                                                    <th>Forecast</th>
                                                                    <th>Confidence</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>
                                                                    <td>Month 1</td>
                                                                    <td>${product.forecastedDemand} units</td>
                                                                    <td>${product.confidence}%</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Month 2</td>
                                                                    <td>${Math.round(product.forecastedDemand * (product.trend === 'Increasing' ? 1.05 : (product.trend === 'Decreasing' ? 0.95 : 1)))} units</td>
                                                                    <td>${Math.max(product.confidence - 5, 60)}%</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Month 3</td>
                                                                    <td>${Math.round(product.forecastedDemand * (product.trend === 'Increasing' ? 1.1 : (product.trend === 'Decreasing' ? 0.9 : 1)))} units</td>
                                                                    <td>${Math.max(product.confidence - 10, 50)}%</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-3">
                                            <div class="card-header">Recommendations</div>
                                            <div class="card-body">
                                                <ul class="list-group">
                                                    ${product.trend === 'Increasing' ? `
                                                        <li class="list-group-item">Increase production capacity to meet rising demand</li>
                                                        <li class="list-group-item">Secure additional raw materials</li>
                                                        <li class="list-group-item">Consider expanding warehouse space</li>
                                                    ` : (product.trend === 'Decreasing' ? `
                                                        <li class="list-group-item">Reduce production to avoid excess inventory</li>
                                                        <li class="list-group-item">Consider promotional activities to stimulate demand</li>
                                                        <li class="list-group-item">Review pricing strategy</li>
                                                    ` : `
                                                        <li class="list-group-item">Maintain current production levels</li>
                                                        <li class="list-group-item">Monitor market conditions for changes</li>
                                                    `)}
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="adjustProductionPlan">
                                            <label class="form-check-label" for="adjustProductionPlan">Adjust production plan based on forecast</label>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        <button type="button" class="btn btn-primary">Apply Forecast</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Remove any existing modal
                    const existingModal = document.getElementById('forecastModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // Add modal to the document
                    document.body.insertAdjacentHTML('beforeend', modalHTML);

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('forecastModal'));
                    modal.show();
                });
            });

            // Add event listeners for production optimization buttons
            document.querySelectorAll('.optimize-production-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const scheduleId = this.dataset.scheduleId;
                    const productName = this.dataset.productName;

                    // Find the schedule in the data
                    let schedule;
                    if (data.productionPlanning && data.productionPlanning.schedules) {
                        schedule = data.productionPlanning.schedules.find(s => s.id == scheduleId);
                    }

                    if (!schedule) return;

                    // Create modal for production optimization
                    const modalHTML = `
                        <div class="modal fade" id="productionOptimizationModal" tabindex="-1" aria-labelledby="productionOptimizationModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="productionOptimizationModalLabel">Production Optimization: ${productName}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="alert alert-${schedule.bottleneck ? 'danger' : (schedule.excessCapacity ? 'warning' : 'info')}">
                                            <i class="bi bi-info-circle"></i>
                                            ${schedule.recommendation}
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Current Status</div>
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Current Capacity:</span>
                                                            <span class="fw-bold">${schedule.currentCapacity} units/week</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>Required Capacity:</span>
                                                            <span class="fw-bold">${schedule.requiredCapacity} units/week</span>
                                                        </div>
                                                        ${schedule.bottleneck ? `
                                                            <div class="d-flex justify-content-between mb-2">
                                                                <span>Bottleneck:</span>
                                                                <span class="fw-bold text-danger">${schedule.bottleneck}</span>
                                                            </div>
                                                        ` : ''}
                                                        ${schedule.excessCapacity ? `
                                                            <div class="d-flex justify-content-between mb-2">
                                                                <span>Excess Capacity:</span>
                                                                <span class="fw-bold text-warning">${schedule.excessCapacity} units/week</span>
                                                            </div>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">Action Plan</div>
                                                    <div class="card-body">
                                                        ${schedule.bottleneck ? `
                                                            <ol>
                                                                <li class="mb-2">Add additional shift at ${schedule.bottleneck}</li>
                                                                <li class="mb-2">Optimize process flow to reduce bottleneck</li>
                                                                <li class="mb-2">Consider outsourcing options for peak demand</li>
                                                                <li class="mb-2">Implement overtime for critical personnel</li>
                                                            </ol>
                                                        ` : ''}
                                                        ${schedule.excessCapacity ? `
                                                            <ol>
                                                                <li class="mb-2">Reduce production shifts</li>
                                                                <li class="mb-2">Reallocate resources to other product lines</li>
                                                                <li class="mb-2">Consider temporary workforce reduction</li>
                                                                <li class="mb-2">Use excess capacity for maintenance activities</li>
                                                            </ol>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-3">
                                            <div class="card-header">Implementation</div>
                                            <div class="card-body">
                                                <form>
                                                    <div class="mb-3">
                                                        <label for="capacityAdjustment" class="form-label">Capacity Adjustment</label>
                                                        <input type="number" class="form-control" id="capacityAdjustment" value="${schedule.requiredCapacity - schedule.currentCapacity}">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="implementationDate" class="form-label">Implementation Date</label>
                                                        <input type="date" class="form-control" id="implementationDate">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="productionManager" class="form-label">Production Manager</label>
                                                        <select class="form-select" id="productionManager">
                                                            <option>John Doe</option>
                                                            <option>Jane Smith</option>
                                                            <option>Mike Johnson</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="implementationNotes" class="form-label">Notes</label>
                                                        <textarea class="form-control" id="implementationNotes" rows="3"></textarea>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary">Implement Optimization</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Remove any existing modal
                    const existingModal = document.getElementById('productionOptimizationModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // Add modal to the document
                    document.body.insertAdjacentHTML('beforeend', modalHTML);

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('productionOptimizationModal'));
                    modal.show();
                });
            });

            // Add event listeners for action buttons
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.dataset.recommendationIndex);
                    const recommendation = data.recommendations[index];

                    // Create modal for action plan
                    const modalHTML = `
                        <div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="actionModalLabel">Action Plan</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <p><strong>Recommendation:</strong> ${recommendation}</p>
                                        <form>
                                            <div class="mb-3">
                                                <label for="actionType" class="form-label">Action Type</label>
                                                <select class="form-select" id="actionType">
                                                    <option>Create Task</option>
                                                    <option>Schedule Meeting</option>
                                                    <option>Create Project</option>
                                                    <option>Assign to Team</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="assignee" class="form-label">Assign to</label>
                                                <select class="form-select" id="assignee">
                                                    <option>John Doe</option>
                                                    <option>Jane Smith</option>
                                                    <option>Mike Johnson</option>
                                                    <option>Sarah Williams</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="dueDate" class="form-label">Due Date</label>
                                                <input type="date" class="form-control" id="dueDate">
                                            </div>
                                            <div class="mb-3">
                                                <label for="priority" class="form-label">Priority</label>
                                                <select class="form-select" id="priority">
                                                    <option>High</option>
                                                    <option selected>Medium</option>
                                                    <option>Low</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="notes" class="form-label">Notes</label>
                                                <textarea class="form-control" id="notes" rows="3"></textarea>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary">Save Action</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Remove any existing modal
                    const existingModal = document.getElementById('actionModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // Add modal to the document
                    document.body.insertAdjacentHTML('beforeend', modalHTML);

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('actionModal'));
                    modal.show();
                });
            });
        }

        // Function to create the Recommendations section
        function createRecommendationsSection(data, container) {
            if (!data.recommendations || !data.recommendations.length) {
                return;
            }

            const sectionCol = document.createElement('div');
            sectionCol.className = 'col-12 mb-3';

            const sectionCard = document.createElement('div');
            sectionCard.className = 'card';
            sectionCard.dataset.insightType = 'recommendations';

            const sectionHeader = document.createElement('div');
            sectionHeader.className = 'card-header d-flex justify-content-between align-items-center';
            sectionHeader.innerHTML = `
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    AI Recommendations
                </h6>
                <button class="btn btn-sm btn-link toggle-section" data-bs-toggle="collapse" data-bs-target="#recommendationsSection">
                    <i class="bi bi-chevron-down"></i>
                </button>
            `;

            const sectionBody = document.createElement('div');
            sectionBody.className = 'card-body collapse show';
            sectionBody.id = 'recommendationsSection';

            const recommendationsList = document.createElement('ul');
            recommendationsList.className = 'list-group';

            data.recommendations.forEach((recommendation, index) => {
                const item = document.createElement('li');
                item.className = 'list-group-item d-flex align-items-center';

                item.innerHTML = `
                    <div class="me-3">
                        <span class="badge bg-primary rounded-circle">
                            ${index + 1}
                        </span>
                    </div>
                    <div>
                        ${recommendation}
                    </div>
                    <div class="ms-auto">
                        <button class="btn btn-sm btn-outline-success action-btn" data-recommendation-index="${index}">
                            Take Action
                        </button>
                    </div>
                `;

                recommendationsList.appendChild(item);
            });

            sectionBody.appendChild(recommendationsList);
            sectionCard.appendChild(sectionHeader);
            sectionCard.appendChild(sectionBody);
            sectionCol.appendChild(sectionCard);
            container.appendChild(sectionCol);
        }

            // Fetch real data from API
            fetch('/api/inventory')
                .then(response => response.json())
                .then(data => {
                    console.log('API data:', data);
                    // Update UI with real data if needed
                })
                .catch(error => {
                    console.error('Error fetching inventory data:', error);
                });

            // Maps and Attachments Functions

            // Function to refresh the map
            function refreshMap() {
                console.log('Refreshing map...');
                // In a real implementation, this would refresh the map data
                alert('Map refreshed!');
            }

            // Function to view a specific location on the map
            function viewLocation(locationId) {
                console.log(`Viewing location: ${locationId}`);

                // Create a modal to show the location details
                const modalHTML = `
                    <div class="modal fade" id="locationModal" tabindex="-1" aria-labelledby="locationModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                                    <h5 class="modal-title" id="locationModalLabel">
                                        <i class="bi bi-geo-alt"></i>
                                        ${locationId === 'acme-supplies' ? 'Acme Supplies' : 'Global Materials'}
                                    </h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="ratio ratio-16x9 mb-3">
                                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <h5>${locationId === 'acme-supplies' ? 'Acme Supplies' : 'Global Materials'}</h5>
                                            <p>
                                                <i class="bi bi-geo-alt text-danger me-2"></i>
                                                ${locationId === 'acme-supplies' ? '123 Manufacturing Blvd, New York, NY 10001' : '456 Industrial Way, New York, NY 10002'}
                                            </p>
                                            <p>
                                                <i class="bi bi-telephone text-primary me-2"></i>
                                                ${locationId === 'acme-supplies' ? '(*************' : '(*************'}
                                            </p>
                                            <p>
                                                <i class="bi bi-envelope text-success me-2"></i>
                                                ${locationId === 'acme-supplies' ? '<EMAIL>' : '<EMAIL>'}
                                            </p>
                                            <hr>
                                            <h6>Supplier Details</h6>
                                            <p>
                                                <strong>Type:</strong> ${locationId === 'acme-supplies' ? 'Raw Materials' : 'Components'}
                                            </p>
                                            <p>
                                                <strong>Lead Time:</strong> ${locationId === 'acme-supplies' ? '7-10 days' : '14-21 days'}
                                            </p>
                                            <p>
                                                <strong>Reliability:</strong>
                                                <span class="text-${locationId === 'acme-supplies' ? 'success' : 'warning'}">
                                                    ${locationId === 'acme-supplies' ? 'High (98%)' : 'Medium (85%)'}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary">Contact Supplier</button>
                                    <button type="button" class="btn btn-success">View Orders</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove any existing modal
                const existingModal = document.getElementById('locationModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // Add modal to the document
                document.body.insertAdjacentHTML('beforeend', modalHTML);

                // Show the modal
                const modal = new bootstrap.Modal(document.getElementById('locationModal'));
                modal.show();
            }

            // Function to view attachments
            function viewAttachment(type, fileName) {
                try {
                    console.log(`Opening attachment: ${fileName} (${type})`);

                    // Remove any existing file viewer modal
                    const existingModal = document.getElementById('fileViewerModal');
                    if (existingModal) {
                        const bsModal = bootstrap.Modal.getInstance(existingModal);
                        if (bsModal) {
                            bsModal.dispose();
                        }
                        existingModal.remove();
                    }

                    // Create a new file viewer modal
                    const fileViewerModal = document.createElement('div');
                    fileViewerModal.className = 'modal fade';
                    fileViewerModal.id = 'fileViewerModal';
                    fileViewerModal.tabIndex = '-1';
                    fileViewerModal.setAttribute('aria-labelledby', 'fileViewerModalLabel');
                    fileViewerModal.setAttribute('aria-hidden', 'true');
                    fileViewerModal.style.zIndex = '1200'; // Ensure it appears above the sidebar

                    // Set the modal content
                    fileViewerModal.innerHTML = `
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                                    <h5 class="modal-title" id="fileViewerTitle">
                                        <i class="bi ${getFileTypeIcon(type)}"></i> ${fileName}
                                    </h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div id="fileViewerContent" class="p-3" style="min-height: 400px;">
                                        ${getAttachmentContent(type, fileName)}
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <div class="action-bar d-flex justify-content-between w-100">
                                        <div>
                                            <button type="button" class="btn btn-outline-success me-1" onclick="downloadCurrentFile()">
                                                <i class="bi bi-download"></i> Download
                                            </button>
                                            <button type="button" class="btn btn-outline-info me-1" onclick="shareCurrentFile()">
                                                <i class="bi bi-share"></i> Share
                                            </button>
                                            <button type="button" class="btn btn-outline-danger me-1" onclick="deleteCurrentFile()">
                                                <i class="bi bi-trash"></i> Delete
                                            </button>
                                        </div>
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add the modal to the document
                    document.body.appendChild(fileViewerModal);

                    // Store the current file information for action buttons
                    window.currentFile = {
                        app: 'attachments',
                        type: type,
                        id: fileName.toLowerCase().replace(/\s+/g, '-'),
                        name: fileName
                    };

                    // Show the modal
                    const modal = new bootstrap.Modal(fileViewerModal);
                    modal.show();

                    console.log(`Viewing ${fileName} directly`);
                } catch (error) {
                    console.error('Error viewing attachment:', error);
                    alert('Could not open the file. Please try again later.');
                }
            }



            // Helper function to get the appropriate icon for file types
            function getFileTypeIcon(type) {
                if (type === 'pdf') return 'bi-file-earmark-pdf text-danger';
                if (type === 'document') return 'bi-file-earmark-text text-primary';
                if (type === 'spreadsheet') return 'bi-file-earmark-spreadsheet text-success';
                if (type === 'image') return 'bi-file-earmark-image text-info';
                if (type === 'presentation') return 'bi-file-earmark-slides text-warning';
                if (type === 'archive') return 'bi-file-earmark-zip text-warning';
                return 'bi-file-earmark text-secondary';
            }

            // Helper function to get content for file viewer based on file type
            function getAttachmentContent(type, fileName) {
                if (type === 'pdf') {
                    return `<div class="text-center">
                        <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 64px;"></i>
                        <h4 class="mt-3">${fileName}</h4>
                        <div class="border p-3 mt-3 text-start" style="background-color: #f8f9fa;">
                            <h5>Document Preview</h5>
                            <p>This is a preview of the PDF document content. The actual content would be displayed here in a real application.</p>
                            <p>The document contains materials planning information and supplier details.</p>
                        </div>
                    </div>`;
                } else if (type === 'document') {
                    return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${fileName}</h4>
                        <hr>
                        <p><strong>Materials Planning Document</strong></p>
                        <p>This document contains production schedules and material requirements for the upcoming quarter.</p>
                        <p>Key points:</p>
                        <ul>
                            <li>Production targets for Q3 2025</li>
                            <li>Material requirements by product line</li>
                            <li>Supplier allocation and delivery schedules</li>
                            <li>Quality control requirements</li>
                        </ul>
                        <p>In a real application, the actual document content would be displayed here.</p>
                    </div>`;
                } else if (type === 'spreadsheet') {
                    return `<div class="border p-3" style="background-color: #f8f9fa;">
                        <h4>${fileName}</h4>
                        <hr>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Material ID</th>
                                        <th>Description</th>
                                        <th>Current Stock</th>
                                        <th>Required</th>
                                        <th>Order Qty</th>
                                        <th>Supplier</th>
                                        <th>Lead Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>MAT-1001</td>
                                        <td>Aluminum Sheet 3mm</td>
                                        <td>250 units</td>
                                        <td>500 units</td>
                                        <td>250 units</td>
                                        <td>Acme Metals</td>
                                        <td>5 days</td>
                                    </tr>
                                    <tr>
                                        <td>MAT-1002</td>
                                        <td>Steel Rod 10mm</td>
                                        <td>120 units</td>
                                        <td>300 units</td>
                                        <td>180 units</td>
                                        <td>Global Materials</td>
                                        <td>7 days</td>
                                    </tr>
                                    <tr>
                                        <td>MAT-1003</td>
                                        <td>Plastic Resin Type A</td>
                                        <td>500 kg</td>
                                        <td>800 kg</td>
                                        <td>300 kg</td>
                                        <td>Polymer Solutions</td>
                                        <td>3 days</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p class="mt-3">In a real application, the actual spreadsheet content would be displayed here.</p>
                    </div>`;
                } else if (type === 'image') {
                    return `<div class="text-center">
                        <img src="https://via.placeholder.com/800x600.png?text=${fileName}" class="img-fluid border" alt="${fileName}">
                        <p class="mt-3">Image preview would be displayed here in a real application.</p>
                    </div>`;
                } else {
                    return `<div class="alert alert-info">No preview available for this file type.</div>`;
                }
            }

            // Function to download a file
            function downloadFile(fileName) {
                alert(`Downloading ${fileName}...`);
                // In a real application, this would trigger a download
            }

            // Function to download the current file
            function downloadCurrentFile() {
                if (window.currentFile) {
                    downloadFile(window.currentFile.name);
                }
            }

            // Function to share a file
            function shareFile(fileName) {
                const email = prompt(`Enter email address to share ${fileName} with:`);
                if (email) {
                    alert(`${fileName} has been shared with ${email}.`);
                }
            }

            // Function to share the current file
            function shareCurrentFile() {
                if (window.currentFile) shareFile(window.currentFile.name);
            }

            // Function to delete an attachment
            function deleteAttachment(fileName) {
                if (confirm(`Are you sure you want to delete ${fileName}?`)) {
                    alert(`${fileName} has been deleted.`);

                    // Close the modal if it's open
                    const modal = bootstrap.Modal.getInstance(document.getElementById('fileViewerModal'));
                    if (modal) {
                        modal.hide();
                    }
                }
            }

            // Function to upload an attachment
            function uploadAttachment() {
                // Create a file input element
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.multiple = true;

                // Trigger the file selection dialog
                fileInput.click();

                // Handle file selection
                fileInput.addEventListener('change', function() {
                    if (fileInput.files.length > 0) {
                        // Close the attachments modal
                        const attachmentsModal = bootstrap.Modal.getInstance(document.getElementById('attachmentsModal'));
                        if (attachmentsModal) {
                            attachmentsModal.hide();
                        }

                        // Show upload progress
                        const fileNames = Array.from(fileInput.files).map(file => file.name).join(', ');
                        alert(`Uploading files: ${fileNames}`);

                        // Simulate upload completion
                        setTimeout(() => {
                            alert('Files uploaded successfully!');

                            // Reopen the attachments modal
                            const modal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
                            modal.show();
                        }, 1500);
                    }
                });
            }

            // Function to delete the current file
            function deleteCurrentFile() {
                if (window.currentFile) deleteAttachment(window.currentFile.name);
            }
        });
    </script>

    <!-- Google Maps Modal -->
    <div class="modal fade" id="mapsModal" tabindex="-1" aria-labelledby="mapsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="mapsModalLabel"><i class="bi bi-geo-alt"></i> Google Maps</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="mapsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations-content" type="button" role="tab" aria-controls="locations-content" aria-selected="true">Locations</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="directions-tab" data-bs-toggle="tab" data-bs-target="#directions-content" type="button" role="tab" aria-controls="directions-content" aria-selected="false">Directions</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="saved-tab" data-bs-toggle="tab" data-bs-target="#saved-content" type="button" role="tab" aria-controls="saved-content" aria-selected="false">Saved Places</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="mapsTabContent">
                        <!-- Locations Tab -->
                        <div class="tab-pane fade show active" id="locations-content" role="tabpanel" aria-labelledby="locations-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="input-group" style="max-width: 300px;">
                                    <input type="text" id="location-search" class="form-control" placeholder="Search locations...">
                                    <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="locationTypeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-filter"></i> Filter
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="locationTypeDropdown">
                                        <li><a class="dropdown-item" href="#">All Locations</a></li>
                                        <li><a class="dropdown-item" href="#">Suppliers</a></li>
                                        <li><a class="dropdown-item" href="#">Warehouses</a></li>
                                        <li><a class="dropdown-item" href="#">Manufacturing Plants</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="ratio ratio-16x9 mb-3">
                                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="list-group">
                                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" onclick="viewLocation('acme-supplies')">
                                            <div>
                                                <i class="bi bi-geo-alt-fill text-danger me-2"></i>
                                                <span>Acme Supplies</span>
                                            </div>
                                            <span class="badge bg-primary rounded-pill">5.2 mi</span>
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" onclick="viewLocation('global-materials')">
                                            <div>
                                                <i class="bi bi-geo-alt-fill text-warning me-2"></i>
                                                <span>Global Materials</span>
                                            </div>
                                            <span class="badge bg-primary rounded-pill">8.7 mi</span>
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="bi bi-geo-alt-fill text-success me-2"></i>
                                                <span>Main Warehouse</span>
                                            </div>
                                            <span class="badge bg-primary rounded-pill">1.3 mi</span>
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="bi bi-geo-alt-fill text-info me-2"></i>
                                                <span>Manufacturing Plant</span>
                                            </div>
                                            <span class="badge bg-primary rounded-pill">0.5 mi</span>
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="bi bi-geo-alt-fill text-secondary me-2"></i>
                                                <span>Distribution Center</span>
                                            </div>
                                            <span class="badge bg-primary rounded-pill">12.8 mi</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Directions Tab -->
                        <div class="tab-pane fade" id="directions-content" role="tabpanel" aria-labelledby="directions-tab">
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="directions-start" class="form-label">Start</label>
                                                <input type="text" class="form-control" id="directions-start" placeholder="Enter starting point">
                                            </div>
                                            <div class="mb-3">
                                                <label for="directions-end" class="form-label">Destination</label>
                                                <input type="text" class="form-control" id="directions-end" placeholder="Enter destination">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Travel Mode</label>
                                                <div class="btn-group w-100" role="group">
                                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-driving" checked>
                                                    <label class="btn btn-outline-primary" for="travel-driving"><i class="bi bi-car-front"></i> Driving</label>

                                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-transit">
                                                    <label class="btn btn-outline-primary" for="travel-transit"><i class="bi bi-bus-front"></i> Transit</label>

                                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-walking">
                                                    <label class="btn btn-outline-primary" for="travel-walking"><i class="bi bi-person-walking"></i> Walking</label>

                                                    <input type="radio" class="btn-check" name="travel-mode" id="travel-cycling">
                                                    <label class="btn btn-outline-primary" for="travel-cycling"><i class="bi bi-bicycle"></i> Cycling</label>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-primary w-100">Get Directions</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ratio ratio-16x9">
                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1682531529270!5m2!1sen!2sca" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                            </div>
                        </div>

                        <!-- Saved Places Tab -->
                        <div class="tab-pane fade" id="saved-content" role="tabpanel" aria-labelledby="saved-tab">
                            <div class="d-flex justify-content-between mb-3">
                                <h6 class="mb-0">Saved Locations</h6>
                                <button class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-plus-circle"></i> Add New
                                </button>
                            </div>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Main Warehouse</h6>
                                        <small class="text-muted">1.3 mi</small>
                                    </div>
                                    <p class="mb-1">123 Warehouse Blvd, New York, NY 10001</p>
                                    <small class="text-muted">Added: 2025-01-15</small>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Manufacturing Plant</h6>
                                        <small class="text-muted">0.5 mi</small>
                                    </div>
                                    <p class="mb-1">456 Factory Ave, New York, NY 10002</p>
                                    <small class="text-muted">Added: 2025-01-10</small>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Acme Supplies</h6>
                                        <small class="text-muted">5.2 mi</small>
                                    </div>
                                    <p class="mb-1">123 Manufacturing Blvd, New York, NY 10001</p>
                                    <small class="text-muted">Added: 2024-12-05</small>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Global Materials</h6>
                                        <small class="text-muted">8.7 mi</small>
                                    </div>
                                    <p class="mb-1">456 Industrial Way, New York, NY 10002</p>
                                    <small class="text-muted">Added: 2024-11-20</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="https://maps.google.com" target="_blank" class="btn btn-primary">Open in Google Maps</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Attachments Modal -->
    <div class="modal fade" id="mrp-attachmentsModal-2" tabindex="-1" aria-labelledby="mrp-attachmentsModalLabel-2" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="attachmentsModalLabel"><i class="bi bi-paperclip"></i> Attachments</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-between mb-3">
                        <button class="btn btn-primary" onclick="uploadAttachment()">
                            <i class="bi bi-upload"></i> Upload Attachment
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Filter By <i class="bi bi-funnel"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="filterDropdown">
                                <li><a class="dropdown-item" href="#">All Files</a></li>
                                <li><a class="dropdown-item" href="#">Documents</a></li>
                                <li><a class="dropdown-item" href="#">Spreadsheets</a></li>
                                <li><a class="dropdown-item" href="#">Images</a></li>
                                <li><a class="dropdown-item" href="#">PDFs</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="list-group attachment-list">
                        <!-- PDF File -->
                        <div class="list-group-item attachment-item" data-filename="Inventory_Report_Q2_2025.pdf" data-filetype="pdf">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                <div>
                                    <h6 class="mb-0">Inventory_Report_Q2_2025.pdf</h6>
                                    <small class="text-muted">2.4 MB · Uploaded: Yesterday</small>
                                </div>
                            </div>
                        </div>

                        <!-- Excel File -->
                        <div class="list-group-item attachment-item" data-filename="Supplier_Pricing_Comparison.xlsx" data-filetype="spreadsheet">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark-excel text-success me-3 fs-4"></i>
                                <div>
                                    <h6 class="mb-0">Supplier_Pricing_Comparison.xlsx</h6>
                                    <small class="text-muted">1.8 MB · Uploaded: 2 weeks ago</small>
                                </div>
                            </div>
                        </div>

                        <!-- Image File -->
                        <div class="list-group-item attachment-item" data-filename="Warehouse_Layout.jpg" data-filetype="image">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark-image text-info me-3 fs-4"></i>
                                <div>
                                    <h6 class="mb-0">Warehouse_Layout.jpg</h6>
                                    <small class="text-muted">3.2 MB · Uploaded: 1 month ago</small>
                                </div>
                            </div>
                        </div>

                        <!-- Word Document -->
                        <div class="list-group-item attachment-item" data-filename="Inventory_Management_Procedures.docx" data-filetype="document">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark-word text-primary me-3 fs-4"></i>
                                <div>
                                    <h6 class="mb-0">Inventory_Management_Procedures.docx</h6>
                                    <small class="text-muted">1.5 MB · Uploaded: 2 months ago</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="download-selected-btn">Download Selected</button>
                </div>
            </div>
        </div>
    </div>
    <script src="/js/file-handlers.js"></script>
    <script>
    // Direct implementation of file action handlers to ensure they work
    function viewFile(filename, fileType) {
        console.log(`Viewing file: ${filename} (${fileType})`);

        // Set current file for other operations
        window.currentFile = {
            name: filename,
            type: fileType
        };
        // The misplaced HTML fragment has been removed to fix the syntax error.
    }

    function getGoogleItemPreviewContent(app, type, itemId) {
        const readableName = itemId.replace(/_/g, ' ');

        if (app === 'drive') {
            if (type === 'folder') {
                return `<div class="text-center">
                    <i class="bi bi-folder-fill text-primary" style="font-size: 64px;"></i>
                    <h4 class="mt-3">${readableName}</h4>
                    <p class="mt-3">Folder contents would be displayed here</p>
                </div>`;
            } else if (type === 'pdf') {
                return `<div class="text-center p-4">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 64px;"></i>
                    <h4 class="mt-3">${readableName}</h4>
                    <div class="border p-3 mt-4 text-start bg-light">
                        <h5>PDF Preview</h5>
                        <p>PDF preview would be displayed here in a production environment.</p>
                    </div>
                </div>`;
            }
        } else if (app === 'docs') {
            return `<div class="border p-4 bg-light h-100">
                <h4 class="mb-4">${readableName}</h4>
                <hr>
                <p>Document content would be displayed here in a production environment.</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
            </div>`;
        } else if (app === 'sheets') {
            return `<div class="border p-3 bg-light h-100">
                <h4 class="mb-3">${readableName}</h4>
                <hr>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Product</th>
                                <th>Quantity</th>
                                <th>Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>MRP-1001</td>
                                <td>Widget A</td>
                                <td>250</td>
                                <td>$10.00</td>
                            </tr>
                            <tr>
                                <td>MRP-1002</td>
                                <td>Widget B</td>
                                <td>175</td>
                                <td>$15.00</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>`;
        }

        return `<div class="alert alert-info p-5">
            <h4 class="alert-heading">${readableName}</h4>
            <p>Preview not available for this file type.</p>
        </div>`;
    }

    function createFileViewerModal() {
        if (document.getElementById('fileViewerModal')) {
            return;
        }

        const modalHtml = `
            <div class="modal fade" id="fileViewerModal" tabindex="-1" aria-labelledby="fileViewerModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="fileViewerTitle"></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="fileViewerContent" style="min-height: 400px; overflow: auto;"></div>
                        </div>
                        <div class="modal-footer">
                            <div class="d-flex justify-content-start gap-2 me-auto">
                                <button type="button" class="btn btn-outline-success" id="download-current-file">
                                    <i class="bi bi-download me-1"></i> Download
                                </button>
                                <button type="button" class="btn btn-outline-info" id="share-current-file">
                                    <i class="bi bi-share me-1"></i> Share
                                </button>
                                <button type="button" class="btn btn-outline-danger" id="delete-current-file">
                                    <i class="bi bi-trash me-1"></i> Delete
                                </button>
                            </div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = modalHtml;
        document.body.appendChild(tempDiv.firstElementChild);

        // Add event listeners to the modal buttons
        document.getElementById('download-current-file').addEventListener('click', () => {
            if (window.currentFile) {
                downloadFile(window.currentFile.name);
            } else if (window.currentGoogleItem) {
                downloadGoogleItem(
                    window.currentGoogleItem.app,
                    window.currentGoogleItem.type,
                    window.currentGoogleItem.id
                );
            }
        });

        document.getElementById('share-current-file').addEventListener('click', () => {
            if (window.currentFile) {
                shareFile(window.currentFile.name);
            } else if (window.currentGoogleItem) {
                shareGoogleItem(
                    window.currentGoogleItem.app,
                    window.currentGoogleItem.type,
                    window.currentGoogleItem.id
                );
            }
        });

        document.getElementById('delete-current-file').addEventListener('click', () => {
            if (window.currentFile) {
                deleteFile(window.currentFile.name);
            } else if (window.currentGoogleItem) {
                deleteGoogleItem(
                    window.currentGoogleItem.app,
                    window.currentGoogleItem.type,
                    window.currentGoogleItem.id
                );
            }
        });
    }

    // Set up file action handlers
    document.addEventListener('DOMContentLoaded', function() {
        // Fix attachments modal sidebar link
        const attachmentsLink = document.querySelector('a[href="#attachments"]');
        if (attachmentsLink) {
            attachmentsLink.addEventListener('click', function(e) {
                e.preventDefault();
                const attachmentsModal = document.getElementById('attachmentsModal');
                if (attachmentsModal) {
                    const modal = new bootstrap.Modal(attachmentsModal);
                    modal.show();
                }
            });
        }

        // Set up handlers for attachment file actions
        document.addEventListener('click', function(event) {
            // View action
            if (event.target.closest('.file-action-view')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    event.preventDefault();
                    const filename = fileItem.getAttribute('data-filename');
                    const fileType = fileItem.getAttribute('data-filetype') || 'unknown';
                    viewFile(filename, fileType);
                }
            }

            // Download action
            if (event.target.closest('.file-action-download')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    event.preventDefault();
                    const filename = fileItem.getAttribute('data-filename');
                    downloadFile(filename);
                }
            }

            // Share action
            if (event.target.closest('.file-action-share')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    event.preventDefault();
                    const filename = fileItem.getAttribute('data-filename');
                    shareFile(filename);
                }
            }

            // Delete action
            if (event.target.closest('.file-action-delete')) {
                const fileItem = event.target.closest('[data-filename]');
                if (fileItem) {
                    event.preventDefault();
                    const filename = fileItem.getAttribute('data-filename');
                    deleteFile(filename);
                }
            }
        });
    });
</script>
<script src="/js/google-modals-handler.js"></script>
<script>
    // Update the file viewer functionality to include proper action buttons
    function viewGoogleItem(app, type, itemId) {
        console.log(`Viewing Google item: ${app} ${type} ${itemId}`);

        // Set current item for other operations
        window.currentGoogleItem = {
            app: app,
            type: type,
            id: itemId
        };

        // Hide any open modals
        const modals = ['driveModal', 'docsModal', 'sheetsModal', 'calendarModal', 'gmailModal', 'mapsModal'];
        modals.forEach(modalId => {
            const modalEl = document.getElementById(modalId);
            if (modalEl) {
                const instance = bootstrap.Modal.getInstance(modalEl);
                if (instance) {
                    instance.hide();
                }
            }
        });

        // Clean up modal artifacts
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });

        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // Get or create the file viewer modal
        let fileViewerModal = document.getElementById('fileViewerModal');

        if (!fileViewerModal) {
            createFileViewerModal();
            fileViewerModal = document.getElementById('fileViewerModal');
        }

        // Update modal content
        const fileTitle = document.getElementById('fileViewerTitle');
        const fileContent = document.getElementById('fileViewerContent');

        if (fileTitle && fileContent) {
            const iconClass = getGoogleIconClass(app, type);
            const readableName = itemId.replace(/_/g, ' ');
            fileTitle.innerHTML = `<i class="bi ${iconClass}"></i> ${readableName}`;

            // Check if this is a folder view, which needs special handling
            if (app === 'drive' && type === 'folder') {
                // Render folder contents with action buttons
                fileContent.innerHTML = `
                    <div class="text-center mb-4">
                        <i class="bi bi-folder-fill text-primary" style="font-size: 48px;"></i>
                        <h4 class="mt-3">${readableName}</h4>
                    </div>
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                                <div>
                                    <h6 class="mb-1">Document 1.pdf</h6>
                                    <small>1.2 MB - Last updated: Yesterday</small>
                                </div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'pdf', 'Document_1.pdf')">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'pdf', 'Document_1.pdf')">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'pdf', 'Document_1.pdf')">
                                    <i class="bi bi-share"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'pdf', 'Document_1.pdf')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                                <div>
                                    <h6 class="mb-1">Document 2.docx</h6>
                                    <small>0.8 MB - Last updated: 2 days ago</small>
                                </div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Document_2.docx')">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Document_2.docx')">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Document_2.docx')">
                                    <i class="bi bi-share"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Document_2.docx')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // For regular files, use the standard preview
                fileContent.innerHTML = getGoogleItemPreviewContent(app, type, itemId);
            }

            // Show the modal
            try {
                let modal = bootstrap.Modal.getInstance(fileViewerModal);
                if (!modal) {
                    modal = new bootstrap.Modal(fileViewerModal);
                }
                modal.show();
            } catch (err) {
                console.error('Error showing file viewer modal:', err);
                alert('Could not open file viewer. Please try again.');
            }
        } else {
            console.error('File viewer elements not found');
            alert('File viewer is not available. Please try again later.');
        }
    }
</script>
<!-- Google Calendar Modal -->
<div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="calendarModalLabel">Google Calendar</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="calendar-events-list">
                    <div class="d-flex justify-content-between mb-3">
                        <button class="btn btn-primary" id="create-new-event-modal">
                            <i class="bi bi-calendar-plus me-2"></i>Create Event
                        </button>
                        <button class="btn btn-outline-secondary" id="refresh-calendar-modal">
                            <i class="bi bi-arrow-clockwise me-2"></i>Refresh
                        </button>
                    </div>
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center" style="cursor: pointer;" onclick="viewCalendarEvent('Inventory_Audit')">
                            <div>
                                <div class="fw-bold">Inventory Audit</div>
                                <div class="small text-muted">
                                    <i class="bi bi-clock me-1"></i>Today, 2:00 PM - 4:00 PM
                                </div>
                            </div>
                            <span class="badge bg-warning rounded-pill">Today</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center" style="cursor: pointer;" onclick="viewCalendarEvent('Supplier_Meeting')">
                            <div>
                                <div class="fw-bold">Supplier Meeting</div>
                                <div class="small text-muted">
                                    <i class="bi bi-clock me-1"></i>Tomorrow, 10:00 AM - 11:00 AM
                                </div>
                            </div>
                            <span class="badge bg-info rounded-pill">Tomorrow</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center" style="cursor: pointer;" onclick="viewCalendarEvent('Production_Planning')">
                            <div>
                                <div class="fw-bold">Production Planning</div>
                                <div class="small text-muted">
                                    <i class="bi bi-clock me-1"></i>May 15, 9:00 AM - 10:30 AM
                                </div>
                            </div>
                            <span class="badge bg-secondary rounded-pill">Next Week</span>
                        </div>
                    </div>
                </div>
                <div id="calendar-event-details" style="display:none;">
                    <div class="card">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <button class="btn btn-sm btn-outline-secondary" onclick="backToCalendarEvents()">
                                <i class="bi bi-arrow-left"></i> Back to Events
                            </button>
                            <div>
                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-pencil"></i> Edit</button>
                                <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i> Delete</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <h4 id="event-title" class="card-title">Event Title</h4>
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-clock me-2 text-primary"></i>
                                    <span id="event-time">Event Time</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-geo-alt me-2 text-danger"></i>
                                    <span id="event-location">Event Location</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-person me-2 text-success"></i>
                                    <span id="event-organizer">Event Organizer</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <strong>Description:</strong>
                                <div id="event-description">Event description goes here.</div>
                            </div>
                            <div class="mb-3">
                                <strong>Attendees:</strong>
                                <ul id="event-attendees" class="mb-0"></ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="https://calendar.google.com" target="_blank" class="btn btn-primary">Open in Calendar</a>
            </div>
        </div>
    </div>
</div>
<script>
// Demo event data
const calendarEvents = {
    'Inventory_Audit': {
        title: 'Inventory Audit',
        time: 'Today, 2:00 PM - 4:00 PM',
        location: 'Warehouse 1',
        organizer: 'Sarah Chen (<EMAIL>)',
        description: 'Quarterly inventory audit with the warehouse team.',
        attendees: ['Sarah Chen', 'John Davis', 'Rachel Miller']
    },
    'Supplier_Meeting': {
        title: 'Supplier Meeting',
        time: 'Tomorrow, 10:00 AM - 11:00 AM',
        location: 'Conference Room B',
        organizer: 'John Davis (<EMAIL>)',
        description: 'Meeting with key suppliers to discuss Q3 requirements.',
        attendees: ['John Davis', 'Rachel Miller', 'Supplier Rep']
    },
    'Production_Planning': {
        title: 'Production Planning',
        time: 'May 15, 9:00 AM - 10:30 AM',
        location: 'Main Office',
        organizer: 'Rachel Miller (<EMAIL>)',
        description: 'Monthly production planning meeting with all department heads.',
        attendees: ['Rachel Miller', 'Sarah Chen', 'John Davis']
    }
};
function viewCalendarEvent(eventId) {
    const event = calendarEvents[eventId];
    if (!event) return;
    document.getElementById('calendar-events-list').style.display = 'none';
    document.getElementById('calendar-event-details').style.display = '';
    document.getElementById('event-title').textContent = event.title;
    document.getElementById('event-time').textContent = event.time;
    document.getElementById('event-location').textContent = event.location;
    document.getElementById('event-organizer').textContent = event.organizer;
    document.getElementById('event-description').textContent = event.description;
    const attendeesList = document.getElementById('event-attendees');
    attendeesList.innerHTML = '';
    event.attendees.forEach(name => {
        const li = document.createElement('li');
        li.textContent = name;
        attendeesList.appendChild(li);
    });
}
function backToCalendarEvents() {
    document.getElementById('calendar-event-details').style.display = 'none';
    document.getElementById('calendar-events-list').style.display = '';
}
</script>
<!-- Gmail Modal -->
<div class="modal fade" id="gmailModal" tabindex="-1" aria-labelledby="gmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                <h5 class="modal-title" id="gmailModalLabel"><i class="bi bi-envelope"></i> Gmail</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-3 border-end">
                        <div class="d-grid gap-2 mb-3">
                            <button class="btn btn-primary" id="compose-new-email" onclick="composeNewEmail()">
                                <i class="bi bi-pencil-square me-2"></i>Compose
                            </button>
                        </div>
                        <div class="list-group mb-4">
                            <a href="#" class="list-group-item list-group-item-action active d-flex justify-content-between align-items-center" id="inbox-link">
                                <div><i class="bi bi-inbox me-2"></i>Inbox</div>
                                <span class="badge bg-primary rounded-pill">3</span>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="starred-link">
                                <div><i class="bi bi-star me-2"></i>Starred</div>
                                <span class="badge bg-secondary rounded-pill">2</span>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="sent-link">
                                <div><i class="bi bi-send me-2"></i>Sent</div>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="drafts-link">
                                <div><i class="bi bi-file-earmark me-2"></i>Drafts</div>
                                <span class="badge bg-secondary rounded-pill">1</span>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="important-link">
                                <div><i class="bi bi-bookmark me-2"></i>Important</div>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <!-- Gmail Tabs -->
                        <ul class="nav nav-tabs mb-3" id="gmailTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox-content" type="button" role="tab" aria-controls="inbox-content" aria-selected="true">Inbox</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent-content" type="button" role="tab" aria-controls="sent-content" aria-selected="false">Sent</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose-content" type="button" role="tab" aria-controls="compose-content" aria-selected="false">Compose</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="read-tab" data-bs-toggle="tab" data-bs-target="#read-content" type="button" role="tab" aria-controls="read-content" aria-selected="false">Read Email</button>
                            </li>
                        </ul>

                        <div class="tab-content" id="gmailTabContent">
                            <!-- Inbox Tab -->
                            <div class="tab-pane fade show active" id="inbox-content" role="tabpanel" aria-labelledby="inbox-tab">
                                <div class="d-flex justify-content-between mb-3">
                                    <div class="input-group" style="max-width: 300px;">
                                        <input type="text" class="form-control" placeholder="Search emails" aria-label="Search emails">
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                    <div>
                                        <button class="btn btn-outline-secondary" id="gmail-refresh-btn">
                                            <i class="bi bi-arrow-clockwise"></i> Refresh
                                        </button>
                                    </div>
                                </div>

                                <!-- Email List -->
                                <div class="list-group mb-3" id="email-list">
                                    <!-- Sarah Chen Email -->
                                    <div class="list-group-item" data-sender="sarah-chen" data-subject="Inventory update: New shipment arrived" style="cursor: pointer;" onclick="openEmail('sarah-chen', 'Inventory update: New shipment arrived')">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">Sarah Chen</h6>
                                            <small>10 minutes ago</small>
                                        </div>
                                        <p class="mb-1 fw-bold">Inventory update: New shipment arrived</p>
                                        <small class="text-muted">The new shipment of Raw Material A has arrived and been logged in the system.</small>
                                    </div>

                                    <!-- John Davis Email -->
                                    <div class="list-group-item" data-sender="john-davis" data-subject="Production schedule for next week" style="cursor: pointer;" onclick="openEmail('john-davis', 'Production schedule for next week')">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">John Davis</h6>
                                            <small>1 hour ago</small>
                                        </div>
                                        <p class="mb-1 fw-bold">Production schedule for next week</p>
                                        <small class="text-muted">Please review the attached production schedule for next week.</small>
                                    </div>

                                    <!-- Rachel Miller Email -->
                                    <div class="list-group-item" data-sender="rachel-miller" data-subject="Supplier price update for Raw Material A" style="cursor: pointer;" onclick="openEmail('rachel-miller', 'Supplier price update for Raw Material A')">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">Rachel Miller</h6>
                                            <small>3 hours ago</small>
                                        </div>
                                        <p class="mb-1 fw-bold">Supplier price update for Raw Material A</p>
                                        <small class="text-muted">Our supplier has informed us of a 5% price increase for Raw Material A starting next month.</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Sent Tab -->
                            <div class="tab-pane fade" id="sent-content" role="tabpanel" aria-labelledby="sent-tab">
                                <div class="d-flex justify-content-between mb-3">
                                    <div class="input-group" style="max-width: 300px;">
                                        <input type="text" class="form-control" placeholder="Search sent emails" aria-label="Search sent emails">
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                    <div>
                                        <button class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-clockwise"></i> Refresh
                                        </button>
                                    </div>
                                </div>

                                <!-- Sent Email List -->
                                <div class="list-group mb-3">
                                    <!-- Sent Email 1 -->
                                    <div class="list-group-item" data-sender="me" data-subject="Re: Material requirements for Q3" style="cursor: pointer;" onclick="openEmail('me', 'Re: Material requirements for Q3')">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">To: Supplier Inc.</h6>
                                            <small>Yesterday</small>
                                        </div>
                                        <p class="mb-1 fw-bold">Re: Material requirements for Q3</p>
                                        <small class="text-muted">Thank you for your quote. We would like to proceed with the order as discussed...</small>
                                    </div>

                                    <!-- Sent Email 2 -->
                                    <div class="list-group-item" data-sender="me" data-subject="Weekly production report" style="cursor: pointer;" onclick="openEmail('me', 'Weekly production report')">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">To: Management Team</h6>
                                            <small>2 days ago</small>
                                        </div>
                                        <p class="mb-1 fw-bold">Weekly production report</p>
                                        <small class="text-muted">Please find attached the weekly production report with all KPIs...</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Compose Tab -->
                            <div class="tab-pane fade" id="compose-content" role="tabpanel" aria-labelledby="compose-tab">
                                <form>
                                    <div class="mb-3">
                                        <label for="email-to" class="form-label">To:</label>
                                        <input type="email" class="form-control" id="email-to" placeholder="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="email-subject" class="form-label">Subject:</label>
                                        <input type="text" class="form-control" id="email-subject">
                                    </div>
                                    <div class="mb-3">
                                        <label for="email-body" class="form-label">Message:</label>
                                        <textarea class="form-control" id="email-body" rows="10"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-outline-secondary me-2">
                                            <i class="bi bi-paperclip"></i> Attach
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary">
                                            <i class="bi bi-save"></i> Save Draft
                                        </button>
                                    </div>
                                    <div class="d-flex justify-content-end">
                                        <button type="button" class="btn btn-secondary me-2" onclick="document.getElementById('inbox-tab').click()">Cancel</button>
                                        <button type="button" class="btn btn-primary">Send</button>
                                    </div>
                                </form>
                            </div>

                            <!-- Read Email Tab -->
                            <div class="tab-pane fade" id="read-content" role="tabpanel" aria-labelledby="read-tab">
                                <!-- This content will be dynamically populated when an email is clicked -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="https://mail.google.com" target="_blank" class="btn btn-primary">Open in Gmail</a>
            </div>
        </div>
    </div>
</div>

<!-- Gmail Modal JS Logic -->
<script>
// Add CSS for Gmail styling
document.head.insertAdjacentHTML('beforeend', `
<style>
#gmailModal .list-group-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

#gmailModal .list-group-item:hover {
    background-color: #f8f9fa;
}

#gmailModal .list-group-item p.fw-bold {
    margin-bottom: 0.25rem;
}

#gmailModal .modal-body {
    max-height: 500px;
    overflow-y: auto;
}

#gmailModal .email-header {
    margin-bottom: 1rem;
}

#gmailModal .email-actions {
    margin-top: 1.5rem;
}

#gmailModal .email-body {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}
</style>
`);

// Initialize Gmail functionality when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Gmail functionality initializing...');

    // Set up refresh button
    const refreshBtn = document.getElementById('gmail-refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            console.log('Refresh button clicked');
            showToast('Emails refreshed successfully');
        });
    }

    // Set up sidebar links
    const inboxLink = document.getElementById('mrp-inbox-link');
    const sentLink = document.getElementById('mrp-sent-link');
    const starredLink = document.getElementById('mrp-starred-link');
    const draftsLink = document.getElementById('mrp-drafts-link');
    const importantLink = document.getElementById('mrp-important-link');

    if (inboxLink) {
        inboxLink.addEventListener('click', function(e) {
            e.preventDefault();
            const inboxTab = document.getElementById('mrp-inbox-tab');
            if (inboxTab) {
                const tabTrigger = new bootstrap.Tab(inboxTab);
                tabTrigger.show();
            }

            // Update active state in sidebar
            document.querySelectorAll('.list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            inboxLink.classList.add('active');
        });
    }

    if (sentLink) {
        sentLink.addEventListener('click', function(e) {
            e.preventDefault();
            const sentTab = document.getElementById('mrp-sent-tab');
            if (sentTab) {
                const tabTrigger = new bootstrap.Tab(sentTab);
                tabTrigger.show();
            }

            // Update active state in sidebar
            document.querySelectorAll('.list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            sentLink.classList.add('active');
        });
    }

    if (starredLink) {
        starredLink.addEventListener('click', function(e) {
            e.preventDefault();
            const inboxTab = document.getElementById('mrp-inbox-tab');
            if (inboxTab) {
                const tabTrigger = new bootstrap.Tab(inboxTab);
                tabTrigger.show();
            }

            // Update active state in sidebar
            document.querySelectorAll('.list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            starredLink.classList.add('active');

            showToast('Starred emails feature coming soon');
        });
    }

    if (draftsLink) {
        draftsLink.addEventListener('click', function(e) {
            e.preventDefault();

            // Update active state in sidebar
            document.querySelectorAll('.list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            draftsLink.classList.add('active');

            showToast('Drafts feature coming soon');
        });
    }

    if (importantLink) {
        importantLink.addEventListener('click', function(e) {
            e.preventDefault();

            // Update active state in sidebar
            document.querySelectorAll('.list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            importantLink.classList.add('active');

            showToast('Important emails feature coming soon');
        });
    }
});

// Function to compose a new email
function composeNewEmail() {
    // Show the Gmail modal if it's not already open
    const gmailModal = document.getElementById('mrp-gmailModal');
    if (gmailModal && !gmailModal.classList.contains('show')) {
        const modal = new bootstrap.Modal(gmailModal);
        modal.show();
    }

    // Switch to the compose tab
    const composeTab = document.getElementById('mrp-compose-tab');
    if (composeTab) {
        const tabTrigger = new bootstrap.Tab(composeTab);
        tabTrigger.show();
    }

    // Clear form fields
    const emailTo = document.getElementById('email-to');
    const emailSubject = document.getElementById('email-subject');
    const emailBody = document.getElementById('email-body');

    if (emailTo) emailTo.value = '';
    if (emailSubject) emailSubject.value = '';
    if (emailBody) emailBody.value = '';
}

// Function to open an email
function openEmail(sender, subject) {
    console.log(`Opening email from ${sender} with subject: ${subject}`);

    // Switch to the read tab and populate it with the email content
    const readTab = document.getElementById('mrp-read-tab');
    const readContent = document.getElementById('mrp-read-content');

    if (!readTab || !readContent) {
        console.error('Read tab or content not found');
        return;
    }

    // Show the Gmail modal if it's not already open
    const gmailModal = document.getElementById('mrp-gmailModal');
    if (gmailModal && !gmailModal.classList.contains('show')) {
        const modal = new bootstrap.Modal(gmailModal);
        modal.show();
    }

    // Activate the read tab
    const tabTrigger = new bootstrap.Tab(readTab);
    tabTrigger.show();

    // Populate the read content based on the sender and subject
    let emailContent = '';

    if (sender === 'Sarah Chen' || sender === 'sarah-chen') {
        emailContent = `
            <div class="email-header mb-3">
                <h5>${subject}</h5>
                <div class="d-flex justify-content-between">
                    <div>
                        <span class="fw-bold">From:</span> Sarah Chen &lt;<EMAIL>&gt;
                    </div>
                    <div>
                        <span class="text-muted">10 minutes ago</span>
                    </div>
                </div>
            </div>
            <div class="email-body">
                <p>Hello,</p>
                <p>I wanted to inform you that the new shipment of Raw Material A has arrived and been logged in the system.</p>
                <p>The shipment includes:</p>
                <ul>
                    <li>500 units of Raw Material A</li>
                    <li>Quality inspection completed and passed</li>
                    <li>All items stored in Warehouse B, Section 3</li>
                </ul>
                <p>The inventory system has been updated to reflect these changes.</p>
                <p>Best regards,<br>Sarah Chen<br>Inventory Manager</p>
            </div>
            <div class="email-actions mt-3">
                <button class="btn btn-primary" onclick="replyEmail('sarah-chen', 'Inventory update: New shipment arrived')">
                    <i class="bi bi-reply me-2"></i>Reply
                </button>
                <button class="btn btn-outline-primary">
                    <i class="bi bi-reply-all me-2"></i>Reply All
                </button>
                <button class="btn btn-outline-primary">
                    <i class="bi bi-forward me-2"></i>Forward
                </button>
                <button class="btn btn-outline-primary" onclick="alert('Attachment feature will be implemented soon')">
                    <i class="bi bi-paperclip me-2"></i>Attach
                </button>
            </div>
        `;
    } else if (sender === 'John Davis' || sender === 'john-davis') {
        emailContent = `
            <div class="email-header mb-3">
                <h5>${subject}</h5>
                <div class="d-flex justify-content-between">
                    <div>
                        <span class="fw-bold">From:</span> John Davis &lt;<EMAIL>&gt;
                    </div>
                    <div>
                        <span class="text-muted">1 hour ago</span>
                    </div>
                </div>
            </div>
            <div class="email-body">
                <p>Hi there,</p>
                <p>Please find attached the production schedule for next week. We need to increase production of Product X by 15% to meet the increased demand.</p>
                <p>Key points:</p>
                <ul>
                    <li>Production line A will run at full capacity</li>
                    <li>We need to schedule overtime for Team B</li>
                    <li>Additional raw materials have been ordered</li>
                </ul>
                <p>Please review and let me know if you have any concerns.</p>
                <p>Regards,<br>John Davis<br>Production Manager</p>
            </div>
            <div class="email-actions mt-3">
                <button class="btn btn-primary" onclick="replyEmail('john-davis', 'Production schedule for next week')">
                    <i class="bi bi-reply me-2"></i>Reply
                </button>
                <button class="btn btn-outline-primary">
                    <i class="bi bi-reply-all me-2"></i>Reply All
                </button>
                <button class="btn btn-outline-primary">
                    <i class="bi bi-forward me-2"></i>Forward
                </button>
                <button class="btn btn-outline-primary" onclick="alert('Attachment feature will be implemented soon')">
                    <i class="bi bi-paperclip me-2"></i>Attach
                </button>
            </div>
        `;
    } else if (sender === 'Rachel Miller' || sender === 'rachel-miller') {
        emailContent = `
            <div class="email-header mb-3">
                <h5>${subject}</h5>
                <div class="d-flex justify-content-between">
                    <div>
                        <span class="fw-bold">From:</span> Rachel Miller &lt;<EMAIL>&gt;
                    </div>
                    <div>
                        <span class="text-muted">3 hours ago</span>
                    </div>
                </div>
            </div>
            <div class="email-body">
                <p>Hello,</p>
                <p>Our supplier has informed us of a 5% price increase for Raw Material A starting next month.</p>
                <p>This will impact our production costs for the following products:</p>
                <ul>
                    <li>Product X - Cost increase of approximately 3%</li>
                    <li>Product Y - Cost increase of approximately 2%</li>
                    <li>Product Z - Cost increase of approximately 4%</li>
                </ul>
                <p>We should schedule a meeting to discuss our options, including:</p>
                <ol>
                    <li>Accepting the price increase and adjusting our pricing</li>
                    <li>Negotiating with the supplier</li>
                    <li>Exploring alternative suppliers</li>
                </ol>
                <p>Best regards,<br>Rachel Miller<br>Procurement Manager</p>
            </div>
            <div class="email-actions mt-3">
                <button class="btn btn-primary" onclick="replyEmail('rachel-miller', 'Supplier price update for Raw Material A')">
                    <i class="bi bi-reply me-2"></i>Reply
                </button>
                <button class="btn btn-outline-primary">
                    <i class="bi bi-reply-all me-2"></i>Reply All
                </button>
                <button class="btn btn-outline-primary">
                    <i class="bi bi-forward me-2"></i>Forward
                </button>
                <button class="btn btn-outline-primary" onclick="alert('Attachment feature will be implemented soon')">
                    <i class="bi bi-paperclip me-2"></i>Attach
                </button>
            </div>
        `;
    } else if (sender === 'me') {
        // Handle sent emails
        if (subject === 'Re: Material requirements for Q3') {
            emailContent = `
                <div class="email-header mb-3">
                    <h5>${subject}</h5>
                    <div class="d-flex justify-content-between">
                        <div>
                            <span class="fw-bold">To:</span> Supplier Inc. &lt;<EMAIL>&gt;
                        </div>
                        <div>
                            <span class="text-muted">Yesterday</span>
                        </div>
                    </div>
                </div>
                <div class="email-body">
                    <p>Dear Supplier,</p>
                    <p>Thank you for your quote. We would like to proceed with the order as discussed.</p>
                    <p>Please confirm the delivery date and provide the necessary documentation.</p>
                    <p>Best regards,<br>MRP Manager</p>
                </div>
                <div class="email-actions mt-3">
                    <button class="btn btn-outline-primary">
                        <i class="bi bi-forward me-2"></i>Forward
                    </button>
                </div>
            `;
        } else if (subject === 'Weekly production report') {
            emailContent = `
                <div class="email-header mb-3">
                    <h5>${subject}</h5>
                    <div class="d-flex justify-content-between">
                        <div>
                            <span class="fw-bold">To:</span> Management Team &lt;<EMAIL>&gt;
                        </div>
                        <div>
                            <span class="text-muted">2 days ago</span>
                        </div>
                    </div>
                </div>
                <div class="email-body">
                    <p>Dear Management Team,</p>
                    <p>Please find attached the weekly production report with all KPIs.</p>
                    <p>Highlights:</p>
                    <ul>
                        <li>Production targets met for all product lines</li>
                        <li>Quality metrics improved by 3% compared to last week</li>
                        <li>One minor equipment issue resolved with minimal downtime</li>
                    </ul>
                    <p>Let me know if you have any questions.</p>
                    <p>Regards,<br>MRP Manager</p>
                </div>
                <div class="email-actions mt-3">
                    <button class="btn btn-outline-primary">
                        <i class="bi bi-forward me-2"></i>Forward
                    </button>
                </div>
            `;
        } else {
            emailContent = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i> Email content not available.
                </div>
            `;
        }
    } else {
        emailContent = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i> Email content not available for sender: ${sender}, subject: ${subject}
            </div>
        `;
    }

    readContent.innerHTML = emailContent;
    console.log('Email content populated');
}

// Function to reply to an email
function replyEmail(sender, subject) {
    // Show the Gmail modal if it's not already open
    const gmailModal = document.getElementById('mrp-gmailModal');
    if (gmailModal && !gmailModal.classList.contains('show')) {
        const modal = new bootstrap.Modal(gmailModal);
        modal.show();
    }

    // Switch to the compose tab and populate it with reply information
    const composeTab = document.getElementById('mrp-compose-tab');
    const emailTo = document.getElementById('email-to');
    const emailSubject = document.getElementById('email-subject');
    const emailBody = document.getElementById('email-body');

    // Activate the compose tab
    const tabTrigger = new bootstrap.Tab(composeTab);
    tabTrigger.show();

    // Set the recipient and subject
    let recipient = '';
    let senderName = '';

    if (sender === 'sarah-chen') {
        recipient = '<EMAIL>';
        senderName = 'Sarah Chen';
    } else if (sender === 'john-davis') {
        recipient = '<EMAIL>';
        senderName = 'John Davis';
    } else if (sender === 'rachel-miller') {
        recipient = '<EMAIL>';
        senderName = 'Rachel Miller';
    } else {
        // Handle generic case
        senderName = sender.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        recipient = `${sender.replace(/-/g, '.')}@example.com`;
    }

    emailTo.value = recipient;
    emailSubject.value = `Re: ${subject}`;

    // Set a reply template in the email body
    emailBody.value = `\n\n-------- Original Message --------\nFrom: ${senderName}\nSubject: ${subject}\n`;

    // Focus on the email body
    emailBody.focus();

    // Show a toast notification
    showToast(`Replying to: ${subject}`);
}

// Function to show a toast notification
function showToast(message, type = 'success') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Create a unique ID for this toast
    const toastId = 'toast-' + Date.now();

    // Create toast element
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi bi-${type === 'success' ? 'check-circle-fill text-success' : 'exclamation-circle-fill text-danger'} me-2"></i>
                <strong class="me-auto">${type === 'success' ? 'Success' : 'Error'}</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // Add toast to container
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialize and show the toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
    toast.show();

    // Remove toast from DOM after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function () {
        toastElement.remove();
    });
}

// Add a global test function that can be called from the console
window.testGmailModal = function() {
    console.log('Testing Gmail modal...');

    // Show the modal
    const gmailModal = document.getElementById('mrp-gmailModal');
    if (gmailModal) {
        console.log('Found Gmail modal, showing it');
        try {
            const modal = new bootstrap.Modal(gmailModal);
            modal.show();
            console.log('Gmail modal shown successfully');
        } catch (error) {
            console.error('Error showing Gmail modal:', error);
        }
    } else {
        console.error('Gmail modal not found');
    }
};

// Initialize when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Gmail functionality initialized');

    // Add event listeners for sort and filter options
    setupGmailSortAndFilter();
});

// Function to set up Gmail sort and filter functionality
function setupGmailSortAndFilter() {
    console.log('Setting up Gmail sort and filter functionality - enhanced version');

    // First, make sure Bootstrap's dropdown functionality is properly applied
    // This is critical for dropdowns to work correctly
    const dropdownElements = document.querySelectorAll('.dropdown-toggle');
    dropdownElements.forEach(element => {
        // Create fresh dropdown instances to avoid any previous binding issues
        try {
            // Dispose any existing dropdown instance
            const dropdownInstance = bootstrap.Dropdown.getInstance(element);
            if (dropdownInstance) {
                dropdownInstance.dispose();
            }

            // Create a new dropdown instance
            new bootstrap.Dropdown(element);
            console.log('Dropdown initialized:', element.id || 'unnamed dropdown');
        } catch (err) {
            console.error('Error initializing dropdown:', err);
        }
    });

    // Search functionality
    const searchInput = document.getElementById('email-search');
    const searchBtn = document.getElementById('search-btn');

    if (searchInput && searchBtn) {
        console.log('Setting up search functionality');
        // Search on button click
        searchBtn.addEventListener('click', function() {
            searchEmails(searchInput.value);
        });

        // Search on Enter key
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                searchEmails(this.value);
            }
        });
    }

    // Sort functionality
    document.querySelectorAll('.sort-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Sort option clicked:', this.getAttribute('data-sort'));
            const sortType = this.getAttribute('data-sort');
            sortEmails(sortType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('sort-dropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-sort-down"></i> ${this.textContent}`;
            }
        });
    });

    // Filter functionality
    document.querySelectorAll('.filter-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Filter option clicked:', this.getAttribute('data-filter'));
            const filterType = this.getAttribute('data-filter');
            filterEmails(filterType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('filter-dropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-funnel"></i> ${this.textContent.trim()}`;
            }
        });
    });

    // Refresh button
    const refreshBtn = document.getElementById('refresh-gmail');
    if (refreshBtn) {
        console.log('Setting up refresh button');
        refreshBtn.addEventListener('click', function() {
            console.log('Refresh button clicked');
            // Reset search input
            if (searchInput) {
                searchInput.value = '';
            }

            // Reset sort dropdown
            const sortDropdown = document.getElementById('sort-dropdown');
            if (sortDropdown) {
                sortDropdown.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
            }

            // Reset filter dropdown
            const filterDropdown = document.getElementById('filter-dropdown');
            if (filterDropdown) {
                filterDropdown.innerHTML = '<i class="bi bi-funnel"></i> Filter';
            }

            // Show all emails
            showAllEmails();

            // Show success message
            showToast('Emails refreshed successfully');
        });
    }
}

// Function to search emails
function searchEmails(query) {
    console.log('Searching emails for:', query);

    if (!query) {
        showAllEmails();
        return;
    }

    // Normalize query
    const normalizedQuery = query.toLowerCase().trim();

    // Get all email items
    const emailItems = document.querySelectorAll('.list-group-item.list-group-item-action');

    // Filter emails
    let matchCount = 0;
    emailItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(normalizedQuery)) {
            item.style.display = '';
            matchCount++;
        } else {
            item.style.display = 'none';
        }
    });

    // Show toast notification
    showToast(`Found ${matchCount} matching emails`);
}

// Function to sort emails
function sortEmails(sortType) {
    console.log('Sorting emails by:', sortType);

    // Get the email list container
    const emailList = document.querySelector('#mrp-inbox-content .list-group');
    if (!emailList) {
        console.error('Email list container not found');
        return;
    }

    // Get all email items
    const emailItems = Array.from(emailList.querySelectorAll('.list-group-item.list-group-item-action'));

    // Sort the emails
    emailItems.sort((a, b) => {
        const [field, direction] = sortType.split('-');
        const isAsc = direction === 'asc';

        let aValue, bValue;

        switch (field) {
            case 'date':
                // Extract date from the time element
                aValue = a.querySelector('small.text-muted').textContent;
                bValue = b.querySelector('small.text-muted').textContent;
                break;
            case 'sender':
                // Extract sender name
                aValue = a.querySelector('h6').textContent;
                bValue = b.querySelector('h6').textContent;
                break;
            case 'subject':
                // Extract subject
                aValue = a.querySelector('p.mb-1').textContent;
                bValue = b.querySelector('p.mb-1').textContent;
                break;
            default:
                aValue = '';
                bValue = '';
        }

        // Compare values
        if (isAsc) {
            return aValue.localeCompare(bValue);
        } else {
            return bValue.localeCompare(aValue);
        }
    });

    // Remove all items from the list
    emailItems.forEach(item => item.remove());

    // Add sorted items back to the list
    emailItems.forEach(item => emailList.appendChild(item));

    // Show toast notification
    showToast(`Emails sorted by ${sortType.split('-')[0]} (${sortType.split('-')[1] === 'asc' ? 'ascending' : 'descending'})`);
}

// Function to filter emails
function filterEmails(filterType) {
    console.log('Filtering emails by:', filterType);

    // Get all email items
    const emailItems = document.querySelectorAll('.list-group-item.list-group-item-action');

    // Show all emails first
    emailItems.forEach(item => {
        item.style.display = '';
    });

    // Apply filter
    if (filterType === 'all') {
        // Already showing all emails
    } else if (filterType === 'unread') {
        // Filter to show only unread emails
        emailItems.forEach(item => {
            const isUnread = item.classList.contains('unread');
            item.style.display = isUnread ? '' : 'none';
        });
    } else if (filterType === 'read') {
        // Filter to show only read emails
        emailItems.forEach(item => {
            const isRead = !item.classList.contains('unread');
            item.style.display = isRead ? '' : 'none';
        });
    } else if (filterType.startsWith('label-')) {
        // Filter by label
        const label = filterType.substring(6); // Remove 'label-' prefix
        emailItems.forEach(item => {
            const badge = item.querySelector('.badge');
            if (badge && badge.textContent.toLowerCase() === label.toLowerCase()) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // Count visible items
    let visibleCount = 0;
    emailItems.forEach(item => {
        if (item.style.display !== 'none') {
            visibleCount++;
        }
    });

    // Show toast notification
    showToast(`Showing ${visibleCount} emails with filter: ${filterType}`);
}

// Function to show all emails
function showAllEmails() {
    // Get all email items
    const emailItems = document.querySelectorAll('.list-group-item.list-group-item-action');

    // Show all emails
    emailItems.forEach(item => {
        item.style.display = '';
    });

    // Show toast notification
    showToast('Showing all emails');
}

// Function to show a toast notification
function showToast(message, type = 'success') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '1050';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    // Set toast content
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Initialize and show the toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}
</script>    <!-- MRP Gmail Implementations -->
<!-- Shared Gmail Integration -->
<script src="../../SharedFeatures/ui/gmail-implementation.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Gmail integration with MRP-specific configuration
        initializeGmail({
            appName: 'MRP',
            appPrefix: 'mrp',
            modalId: 'mrp-gmailModal',
            primaryColor: '#9c27b0', // Purple for MRP
            debug: true
        });
    });
</script>
<script src="js/attachments-handler.js"></script>
<script src="js/mrp-gmail-attachments.js"></script>
<script src="js/mrp-gmail-sort-filter.js"></script>

<!-- Data Utilities -->
<script src="../../shared/js/isa-data-utils.js"></script>
<script src="js/mrp-data-functions.js"></script>
<script src="js/enhanced-search-sort-filter.js"></script>

<!-- Bootstrap components initialization -->
<script>
// Initialize Bootstrap components
document.addEventListener('DOMContentLoaded', function() {
    // Enable all tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Enable all popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    console.log('Bootstrap components initialized');

    // Ensure Gmail button works with enhanced implementation
    setTimeout(function() {
        const openGmailBtn = document.getElementById('mrp-open-gmail-btn');
        if (openGmailBtn) {
            console.log('Adding enhanced click handler to Gmail button');

            // Add click handler if not already added
            if (!openGmailBtn._hasClickHandler) {
                openGmailBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Gmail button clicked - using enhanced implementation');

                    // Show the Gmail modal using the enhanced implementation
                    if (typeof openMrpGmailModalEnhanced === 'function') {
                        openMrpGmailModalEnhanced();
                    } else {
                        // Fallback to basic implementation
                        const gmailModal = document.getElementById('mrp-gmailModal');
                        if (gmailModal) {
                            // First clean up any existing modal backdrops
                            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                                backdrop.remove();
                            });

                            document.body.classList.remove('modal-open');
                            document.body.style.overflow = '';
                            document.body.style.paddingRight = '';

                            // Create and show new modal
                            try {
                                const existingModal = bootstrap.Modal.getInstance(gmailModal);
                                if (existingModal) {
                                    existingModal.dispose();
                                }
                            } catch (err) {
                                console.warn('Error disposing existing modal:', err);
                            }

                            const modal = new bootstrap.Modal(gmailModal);
                            modal.show();

                            // Initialize after modal is shown
                            gmailModal.addEventListener('shown.bs.modal', function() {
                                if (typeof setupGmailSortAndFilter === 'function') {
                                    setupGmailSortAndFilter();
                                }
                            }, { once: true });
                        } else {
                            console.error('Gmail modal not found');
                        }
                    }
                });

                openGmailBtn._hasClickHandler = true;
            }
        }

        // Add enhanced click handler for the View All Sheets button
        const viewAllSheetsBtn = document.getElementById('view-all-sheets-btn');
        if (viewAllSheetsBtn) {
            console.log('Adding enhanced click handler to View All Sheets button');

            if (!viewAllSheetsBtn._hasClickHandler) {
                viewAllSheetsBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('View All Sheets button clicked - opening sheets modal');

                    const sheetsModal = document.getElementById('mrp-sheetsModal');
                    if (sheetsModal) {
                        // First clean up any existing modal backdrops
                        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                            backdrop.remove();
                        });

                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';

                        // Create and show new modal
                        try {
                            const existingModal = bootstrap.Modal.getInstance(sheetsModal);
                            if (existingModal) {
                                existingModal.dispose();
                            }
                        } catch (err) {
                            console.warn('Error disposing existing modal:', err);
                        }

                        const modal = new bootstrap.Modal(sheetsModal);
                        modal.show();
                    } else {
                        console.error('Sheets modal not found');
                    }
                });

                viewAllSheetsBtn._hasClickHandler = true;
            }
        }
    }, 500); // Wait 0.5 seconds to ensure all scripts are loaded
});
</script>
</body>
</html>
