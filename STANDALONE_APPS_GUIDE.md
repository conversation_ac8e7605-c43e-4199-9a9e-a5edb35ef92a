# Ice Systems Australasia - Standalone Applications Guide

This guide explains how to run the Ice Systems Australasia applications in standalone mode, without requiring the Integration Hub to be running.

## Understanding Standalone Mode

By default, the ISA applications are designed to work together with the Integration Hub as a central connection point. However, there may be times when you want to run an application independently without starting the Integration Hub.

Standalone mode allows you to:

- Run applications independently
- Start applications faster
- Reduce resource usage when you only need one application
- Test applications in isolation

## Running Applications in Standalone Mode

There are several ways to run applications in standalone mode:

### 1. Using the Main Launcher

1. Run the `run-from-usb.bat` file
2. Choose from options 21-26 for standalone applications:
   - 21: BMS Web (Standalone)
   - 22: MRP Web (Standalone)
   - 23: CRM Web (Standalone)
   - 24: WMS Web (Standalone)
   - 25: APS Web (Standalone)
   - 26: APM Web (Standalone)

### 2. Using Standalone Shortcuts

1. Run the `create-standalone-shortcuts.bat` file to create desktop shortcuts
2. Use the created shortcuts to launch applications directly in standalone mode

### 3. Using Application Batch Files

You can run any application in standalone mode by passing the "standalone" parameter to its batch file:

```
.\Apps\BMS\start-web.bat standalone
```

## When to Use Standalone Mode vs. Integrated Mode

### Use Standalone Mode When:

- You only need to use a single application
- You're testing or developing a specific application
- You want faster startup times
- You're experiencing issues with the Integration Hub

### Use Integrated Mode When:

- You need data to flow between multiple applications
- You're using features that require the Integration Hub
- You need centralized authentication and user management
- You're using the mobile access portal

## Limitations of Standalone Mode

When running applications in standalone mode, be aware of these limitations:

1. Cross-application data sharing will not work
2. Some advanced features may be disabled
3. Real-time notifications between applications will not function
4. Centralized user management is not available

## Troubleshooting

If you encounter issues with standalone mode:

1. Make sure Node.js is properly installed in the Node directory
2. Check that the application's dependencies are installed
3. Verify that no other application is using the same port
4. Try running the application with the Integration Hub to see if the issue persists

For more help, run the diagnostic tool:

```
.\run-isa-diagnostic.bat
```
