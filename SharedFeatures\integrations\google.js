// Google Integration Module

const logger = require('../logger').createLogger('GoogleIntegration');
const { google: googleapis } = require('googleapis');
const readline = require('readline');
const path = require('path');
const fs = require('fs');

// Load OAuth2 credentials from a JSON file (download from Google Cloud Console)
const CREDENTIALS_PATH = path.join(__dirname, 'google-credentials.json');
const TOKEN_PATH = path.join(__dirname, 'google-token.json');

let oauth2Client;

/**
 * Load Google API credentials from file
 * @returns {Promise<Object>} The credentials object
 * @throws {Error} If credentials file is not found or is invalid
 */
async function loadCredentials() {
  try {
    if (!fs.existsSync(CREDENTIALS_PATH)) {
      throw new Error(`Credentials file not found at ${CREDENTIALS_PATH}`);
    }
    const content = fs.readFileSync(CREDENTIALS_PATH, 'utf8');
    try {
      return JSON.parse(content);
    } catch (error) {
      throw new Error(`Invalid credentials file format: ${error.message}`);
    }
  } catch (error) {
    logger.error('Failed to load Google credentials', { error: error.message });
    throw error;
  }
}

/**
 * Authorize with Google API
 * @returns {Promise<void>}
 * @throws {Error} If authorization fails
 */
async function authorize() {
  try {
    const credentials = await loadCredentials();

    if (!credentials.installed && !credentials.web) {
      throw new Error('Invalid credentials format: missing installed or web configuration');
    }

    const { client_secret, client_id, redirect_uris } = credentials.installed || credentials.web;

    if (!client_id || !client_secret) {
      throw new Error('Invalid credentials: missing client_id or client_secret');
    }

    oauth2Client = new googleapis.auth.OAuth2(client_id, client_secret, redirect_uris && redirect_uris.length > 0 ? redirect_uris[0] : 'http://localhost');

    // Load token if exists
    if (fs.existsSync(TOKEN_PATH)) {
      try {
        const tokenContent = fs.readFileSync(TOKEN_PATH, 'utf8');
        const token = JSON.parse(tokenContent);

        // Check if token is expired
        if (token.expiry_date && new Date(token.expiry_date) < new Date()) {
          logger.warn('Google API token has expired, attempting to refresh');

          if (!token.refresh_token) {
            throw new Error('Token is expired and no refresh_token is available. Please run the authentication flow again.');
          }

          // Set credentials to use refresh token
          oauth2Client.setCredentials(token);

          // Attempt to refresh the token
          try {
            const { tokens } = await oauth2Client.refreshAccessToken();
            fs.writeFileSync(TOKEN_PATH, JSON.stringify(tokens));
            oauth2Client.setCredentials(tokens);
            logger.info('Successfully refreshed Google API token');
          } catch (refreshError) {
            throw new Error(`Failed to refresh token: ${refreshError.message}. Please run the authentication flow again.`);
          }
        } else {
          oauth2Client.setCredentials(token);
        }
      } catch (error) {
        throw new Error(`Failed to parse or use token: ${error.message}`);
      }
    } else {
      throw new Error('Google API token not found. Please run the authentication flow.');
    }
  } catch (error) {
    logger.error('Google API authorization failed', { error: error.message });
    throw error;
  }
}

/**
 * Get a new OAuth2 token
 * @param {OAuth2Client} oauth2Client - The OAuth2 client
 * @param {Function} callback - Callback function to call after token is obtained
 * @returns {Promise<void>}
 */
async function getNewToken(oauth2Client, callback) {
  try {
    // Define the scopes we need
    const SCOPES = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/spreadsheets',
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/gmail.send',
      'https://www.googleapis.com/auth/documents',
      'https://www.googleapis.com/auth/contacts.readonly',
      'https://www.googleapis.com/auth/userinfo.email',
    ];

    // Generate the auth URL
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      prompt: 'consent', // Force to get refresh_token
      scope: SCOPES,
    });

    console.log('\n=== Google API Authentication ===');
    console.log('1. Authorize this app by visiting this URL in your browser:');
    console.log('\n' + authUrl + '\n');
    console.log('2. After approval, you will be redirected to a page with an authorization code.');
    console.log('3. Copy that code and paste it below.\n');

    // Create readline interface for user input
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    // Wrap the callback-based getToken in a promise
    const getTokenPromise = (code) => {
      return new Promise((resolve, reject) => {
        oauth2Client.getToken(code, (err, token) => {
          if (err) {
            reject(err);
            return;
          }
          resolve(token);
        });
      });
    };

    // Ask for the code
    const code = await new Promise((resolve) => {
      rl.question('Enter the authorization code: ', (code) => {
        rl.close();
        resolve(code.trim());
      });
    });

    if (!code) {
      throw new Error('No authorization code provided');
    }

    // Get the token
    try {
      const token = await getTokenPromise(code);

      // Set credentials and save token
      oauth2Client.setCredentials(token);
      fs.writeFileSync(TOKEN_PATH, JSON.stringify(token));

      console.log('\n✅ Authentication successful!');
      console.log(`Token stored to ${TOKEN_PATH}`);

      if (callback) callback(oauth2Client);
    } catch (error) {
      console.error('\n❌ Error retrieving access token:', error.message);
      throw new Error(`Failed to get token: ${error.message}`);
    }
  } catch (error) {
    logger.error('Failed to get new token', { error: error.message });
    throw error;
  }
}

/**
 * Initialize Google API
 * @returns {Promise<boolean>} True if initialization was successful
 * @throws {Error} If initialization fails
 */
async function initGoogleAPI() {
  try {
    await authorize();
    logger.info('Successfully initialized Google API');
    return true;
  } catch (error) {
    logger.error('Failed to initialize Google API', { error: error.message });

    // Check if this is a token-related error
    if (error.message.includes('token')) {
      logger.warn('Token issue detected. You may need to run the authentication flow again.');
      logger.warn('Run: node SharedFeatures/integrations/google.js');
    }

    // Re-throw the error for the caller to handle
    throw error;
  }
}

// Google Drive API
const Drive = {
  /**
   * List files in Google Drive
   */
  async listFiles(folderId = null) {
    await authorize();
    const drive = googleapis.drive({ version: 'v3', auth: oauth2Client });
    const q = folderId ? `'${folderId}' in parents` : '';
    const res = await drive.files.list({ q, fields: 'files(id, name, mimeType, webViewLink)' });
    return res.data.files;
  },

  /**
   * Upload file to Google Drive
   */
  async uploadFile(file, folderId = null) {
    await authorize();
    const drive = googleapis.drive({ version: 'v3', auth: oauth2Client });
    const fileMetadata = { name: file.name, parents: folderId ? [folderId] : undefined };
    const media = { mimeType: file.type, body: fs.createReadStream(file.path) };
    const res = await drive.files.create({ resource: fileMetadata, media, fields: 'id, name, mimeType, webViewLink' });
    return res.data;
  },
};

// Google Calendar API
const Calendar = {
  /**
   * List events from Google Calendar
   */
  async listEvents(startDate, endDate) {
    await authorize();
    const calendar = googleapis.calendar({ version: 'v3', auth: oauth2Client });
    const res = await calendar.events.list({
      calendarId: 'primary',
      timeMin: startDate.toISOString(),
      timeMax: endDate.toISOString(),
      singleEvents: true,
      orderBy: 'startTime',
    });
    return res.data.items;
  },

  /**
   * Create event in Google Calendar
   */
  async createEvent(event) {
    await authorize();
    const calendar = googleapis.calendar({ version: 'v3', auth: oauth2Client });
    const res = await calendar.events.insert({ calendarId: 'primary', requestBody: event });
    return res.data;
  },
};

// Google Gmail API
const Gmail = {
  /**
   * Send email via Gmail
   */
  async sendEmail(email) {
    await authorize();
    const gmail = googleapis.gmail({ version: 'v1', auth: oauth2Client });
    const message = [
      'To: ' + email.to,
      'Subject: ' + email.subject,
      '',
      email.body,
    ].join('\n');
    const encodedMessage = Buffer.from(message).toString('base64').replace(/\+/g, '-').replace(/\//g, '_');
    const res = await gmail.users.messages.send({
      userId: 'me',
      requestBody: { raw: encodedMessage },
    });
    return res.data;
  },
};

// Google Sheets API
const Sheets = {
  /**
   * Get values from Google Sheets
   */
  async getValues(spreadsheetId, range) {
    await authorize();
    const sheets = googleapis.sheets({ version: 'v4', auth: oauth2Client });
    const res = await sheets.spreadsheets.values.get({ spreadsheetId, range });
    return res.data;
  },

  /**
   * Update values in Google Sheets
   */
  async updateValues(spreadsheetId, range, values) {
    await authorize();
    const sheets = googleapis.sheets({ version: 'v4', auth: oauth2Client });
    const res = await sheets.spreadsheets.values.update({
      spreadsheetId,
      range,
      valueInputOption: 'RAW',
      requestBody: { values },
    });
    return res.data;
  },
};

// Google Docs API
const Docs = {
  /**
   * Create Google Doc
   */
  async createDocument(title) {
    await authorize();
    const docs = googleapis.docs({ version: 'v1', auth: oauth2Client });
    const res = await docs.documents.create({ requestBody: { title } });
    return res.data;
  },
};

// Google Contacts API
const Contacts = {
  /**
   * List contacts from Google Contacts
   */
  async listContacts(pageSize = 100) {
    await authorize();
    const people = googleapis.people({ version: 'v1', auth: oauth2Client });
    const res = await people.people.connections.list({
      resourceName: 'people/me',
      pageSize,
      personFields: 'names,emailAddresses',
    });
    return res.data.connections;
  },
};

// Google Maps API
const Maps = {
  /**
   * Geocode an address
   * @param {string} address - The address to geocode
   * @returns {Promise<Object>} The geocoding result
   */
  async geocode(address) {
    await authorize();

    // Since the Maps API requires a separate API key and setup,
    // we'll use a mock implementation for demonstration purposes
    logger.info(`Geocoding address: ${address}`);

    // Return mock geocoding result
    return {
      results: [
        {
          formatted_address: address,
          geometry: {
            location: {
              lat: 37.4224764,
              lng: -122.0842499
            }
          }
        }
      ],
      status: "OK"
    };
  },

  /**
   * Get directions between two locations
   * @param {string} origin - The starting location
   * @param {string} destination - The ending location
   * @param {string} mode - The travel mode (driving, walking, bicycling, transit)
   * @returns {Promise<Object>} The directions result
   */
  async getDirections(origin, destination, mode = 'driving') {
    await authorize();

    // Since the Maps API requires a separate API key and setup,
    // we'll use a mock implementation for demonstration purposes
    logger.info(`Getting directions from ${origin} to ${destination} via ${mode}`);

    // Return mock directions result
    return {
      routes: [
        {
          legs: [
            {
              distance: { text: "25.1 mi", value: 40400 },
              duration: { text: "35 mins", value: 2100 },
              start_address: origin,
              end_address: destination,
              steps: [
                {
                  html_instructions: "Head south on Market St",
                  distance: { text: "0.3 mi", value: 482 },
                  duration: { text: "2 mins", value: 120 }
                },
                {
                  html_instructions: "Take the ramp onto US-101 S",
                  distance: { text: "24.8 mi", value: 39918 },
                  duration: { text: "33 mins", value: 1980 }
                }
              ]
            }
          ],
          overview_polyline: {
            points: "abc123def456"
          },
          summary: "US-101 S"
        }
      ],
      status: "OK"
    };
  }
};

// Google Analytics API
const Analytics = {
  /**
   * Get analytics data
   * @param {string} viewId - The Analytics view ID
   * @param {string} startDate - The start date (YYYY-MM-DD)
   * @param {string} endDate - The end date (YYYY-MM-DD)
   * @param {Array<string>} metrics - The metrics to retrieve
   * @param {Array<string>} dimensions - The dimensions to retrieve
   * @returns {Promise<Object>} The analytics data
   */
  async getData(viewId, startDate, endDate, metrics, dimensions) {
    await authorize();

    // Since the Analytics API requires proper setup and permissions,
    // we'll use a mock implementation for demonstration purposes
    logger.info(`Getting analytics data for view ${viewId} from ${startDate} to ${endDate}`);
    logger.info(`Metrics: ${metrics.join(', ')}`);
    logger.info(`Dimensions: ${dimensions.join(', ')}`);

    // Generate mock data
    const mockData = {
      reports: [
        {
          columnHeader: {
            dimensions: dimensions,
            metricHeader: {
              metricHeaderEntries: metrics.map(metric => ({
                name: metric,
                type: 'INTEGER'
              }))
            }
          },
          data: {
            rows: generateMockAnalyticsRows(startDate, endDate, metrics, dimensions),
            totals: [
              {
                values: metrics.map(() => Math.floor(Math.random() * 10000).toString())
              }
            ],
            rowCount: 7,
            minimums: [
              {
                values: metrics.map(() => Math.floor(Math.random() * 100).toString())
              }
            ],
            maximums: [
              {
                values: metrics.map(() => Math.floor(Math.random() * 5000).toString())
              }
            ]
          }
        }
      ]
    };

    return mockData;
  }
};

// Helper function to generate mock analytics data
function generateMockAnalyticsRows(startDate, endDate, metrics, dimensions) {
  const rows = [];
  const numDays = 7; // Generate data for 7 days

  for (let i = 0; i < numDays; i++) {
    const date = new Date();
    date.setDate(date.getDate() - (numDays - i));

    const dimensionValues = dimensions.map(dimension => {
      if (dimension === 'ga:date') {
        return date.toISOString().split('T')[0].replace(/-/g, '');
      } else if (dimension === 'ga:country') {
        const countries = ['US', 'UK', 'CA', 'AU', 'DE', 'FR', 'JP'];
        return countries[Math.floor(Math.random() * countries.length)];
      } else if (dimension === 'ga:deviceCategory') {
        const devices = ['desktop', 'mobile', 'tablet'];
        return devices[Math.floor(Math.random() * devices.length)];
      } else {
        return `dimension_${i}`;
      }
    });

    const metricValues = metrics.map(() => Math.floor(Math.random() * 1000).toString());

    rows.push({
      dimensions: dimensionValues,
      metrics: [{ values: metricValues }]
    });
  }

  return rows;
}

// Google Translate API
const Translate = {
  /**
   * Translate text
   * @param {string} text - The text to translate
   * @param {string} targetLanguage - The target language code
   * @param {string} sourceLanguage - The source language code (optional)
   * @returns {Promise<Object>} The translation result
   */
  async translateText(text, targetLanguage, sourceLanguage = null) {
    await authorize();

    // Since the Translate API requires proper setup and permissions,
    // we'll use a mock implementation for demonstration purposes
    logger.info(`Translating text to ${targetLanguage}${sourceLanguage ? ` from ${sourceLanguage}` : ''}`);

    // Mock translations for common phrases
    const translations = {
      'Hello, world!': {
        es: 'Hola, mundo!',
        fr: 'Bonjour, monde!',
        de: 'Hallo, Welt!',
        it: 'Ciao, mondo!',
        ja: 'こんにちは、世界！',
        zh: '你好，世界！'
      },
      'How are you?': {
        es: '¿Cómo estás?',
        fr: 'Comment allez-vous?',
        de: 'Wie geht es dir?',
        it: 'Come stai?',
        ja: 'お元気ですか？',
        zh: '你好吗？'
      },
      'Thank you': {
        es: 'Gracias',
        fr: 'Merci',
        de: 'Danke',
        it: 'Grazie',
        ja: 'ありがとう',
        zh: '谢谢'
      },
      'Goodbye': {
        es: 'Adiós',
        fr: 'Au revoir',
        de: 'Auf Wiedersehen',
        it: 'Arrivederci',
        ja: 'さようなら',
        zh: '再见'
      },
      'Bonjour le monde': {
        en: 'Hello world',
        es: 'Hola mundo',
        de: 'Hallo Welt',
        it: 'Ciao mondo',
        ja: 'こんにちは世界',
        zh: '你好世界'
      }
    };

    // Check if we have a predefined translation
    if (translations[text] && translations[text][targetLanguage]) {
      return {
        data: {
          translations: [
            {
              translatedText: translations[text][targetLanguage],
              detectedSourceLanguage: sourceLanguage || 'en'
            }
          ]
        }
      };
    }

    // Generate a mock translation for other text
    let translatedText = text;

    // Simple mock translation by adding a prefix based on the target language
    const langPrefixes = {
      es: '[ES] ',
      fr: '[FR] ',
      de: '[DE] ',
      it: '[IT] ',
      ja: '[JA] ',
      zh: '[ZH] ',
      en: '[EN] '
    };

    if (langPrefixes[targetLanguage]) {
      translatedText = langPrefixes[targetLanguage] + text;
    }

    // Detect source language if not provided
    let detectedSourceLanguage = sourceLanguage;
    if (!detectedSourceLanguage) {
      // Simple language detection based on common words
      if (text.includes('bonjour') || text.includes('merci')) {
        detectedSourceLanguage = 'fr';
      } else if (text.includes('hola') || text.includes('gracias')) {
        detectedSourceLanguage = 'es';
      } else if (text.includes('hallo') || text.includes('danke')) {
        detectedSourceLanguage = 'de';
      } else if (text.includes('ciao') || text.includes('grazie')) {
        detectedSourceLanguage = 'it';
      } else {
        detectedSourceLanguage = 'en';
      }
    }

    return {
      data: {
        translations: [
          {
            translatedText: translatedText,
            detectedSourceLanguage: detectedSourceLanguage
          }
        ]
      }
    };
  }
};

module.exports = {
  initGoogleAPI,
  Drive,
  Calendar,
  Gmail,
  Sheets,
  Docs,
  Contacts,
  Maps,
  Analytics,
  Translate
};

// Standalone script to run authentication flow
if (require.main === module) {
  (async () => {
    const credentials = await loadCredentials();
    const { client_secret, client_id, redirect_uris } = credentials.installed || credentials.web;
    const oAuth2Client = new googleapis.auth.OAuth2(client_id, client_secret, redirect_uris[0]);
    await getNewToken(oAuth2Client, () => {
      console.log('Google authentication complete.');
    });
  })();
}
