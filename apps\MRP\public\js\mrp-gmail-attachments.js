/**
 * MRP Gmail Attachments Handler
 * 
 * This script adds attachment functionality to the MRP Gmail modal
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('MRP Gmail Attachments Handler loaded');
    
    // Set up attachment functionality
    setupMrpGmailAttachments();
});

/**
 * Set up attachment functionality for the MRP Gmail modal
 */
function setupMrpGmailAttachments() {
    // Find the attach button and file input
    const attachBtn = document.getElementById('mrp-attach-btn');
    const fileInput = document.getElementById('mrp-file-input');
    
    if (!attachBtn || !fileInput) {
        console.error('Attach button or file input not found');
        return;
    }
    
    // Add click handler to the attach button
    attachBtn.addEventListener('click', function() {
        fileInput.click();
    });
    
    // Add change handler to the file input
    fileInput.addEventListener('change', function() {
        handleMrpFileSelection(this.files);
    });
}

/**
 * Handle file selection for attachments
 * @param {FileList} files - The selected files
 */
function handleMrpFileSelection(files) {
    if (!files || files.length === 0) {
        console.log('No files selected');
        return;
    }
    
    console.log(`${files.length} file(s) selected`);
    
    // Get the attachments container and list
    const attachmentsContainer = document.getElementById('mrp-attachments-container');
    const attachmentsList = document.getElementById('mrp-attachments-list');
    
    if (!attachmentsContainer || !attachmentsList) {
        console.error('Attachments container or list not found');
        return;
    }
    
    // Show the attachments container
    attachmentsContainer.classList.remove('d-none');
    
    // Add each file to the attachments list
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // Create a unique ID for the file
        const fileId = `attachment-${Date.now()}-${i}`;
        
        // Create the attachment item
        const attachmentItem = document.createElement('div');
        attachmentItem.className = 'attachment-item d-flex justify-content-between align-items-center mb-2';
        attachmentItem.id = fileId;
        
        // Get the appropriate icon based on file type
        const fileIcon = getFileIcon(file.type);
        
        // Format the file size
        const fileSize = formatFileSize(file.size);
        
        // Create the attachment item HTML
        attachmentItem.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="${fileIcon} me-2"></i>
                <div>
                    <div class="fw-bold">${file.name}</div>
                    <small class="text-muted">${fileSize}</small>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeMrpAttachment('${fileId}')">
                <i class="bi bi-x"></i>
            </button>
        `;
        
        // Add the attachment item to the list
        attachmentsList.appendChild(attachmentItem);
    }
}

/**
 * Remove an attachment from the list
 * @param {string} fileId - The ID of the attachment to remove
 */
function removeMrpAttachment(fileId) {
    const attachmentItem = document.getElementById(fileId);
    if (!attachmentItem) {
        console.error(`Attachment with ID ${fileId} not found`);
        return;
    }
    
    // Remove the attachment item
    attachmentItem.remove();
    
    // Check if there are any attachments left
    const attachmentsList = document.getElementById('mrp-attachments-list');
    if (attachmentsList && attachmentsList.children.length === 0) {
        // Hide the attachments container if there are no attachments
        const attachmentsContainer = document.getElementById('mrp-attachments-container');
        if (attachmentsContainer) {
            attachmentsContainer.classList.add('d-none');
        }
    }
}

/**
 * Get the appropriate icon class based on file type
 * @param {string} fileType - The MIME type of the file
 * @returns {string} The icon class
 */
function getFileIcon(fileType) {
    if (fileType.startsWith('image/')) {
        return 'bi bi-file-earmark-image text-info';
    } else if (fileType.includes('pdf')) {
        return 'bi bi-file-earmark-pdf text-danger';
    } else if (fileType.includes('spreadsheet') || fileType.includes('excel') || fileType.includes('sheet')) {
        return 'bi bi-file-earmark-excel text-success';
    } else if (fileType.includes('word') || fileType.includes('document')) {
        return 'bi bi-file-earmark-word text-primary';
    } else {
        return 'bi bi-file-earmark text-secondary';
    }
}

/**
 * Format file size in a human-readable format
 * @param {number} bytes - The file size in bytes
 * @returns {string} The formatted file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Make functions globally available
window.removeMrpAttachment = removeMrpAttachment;
