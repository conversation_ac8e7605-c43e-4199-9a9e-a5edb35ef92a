@echo off
echo Starting Supply Chain Management (SCM)...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Change to the SCM directory
cd /d C:\ISASUITE\apps\SCM\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call C:\ISASUITE\PortableNodeJS\pnpm install
)

REM Close any potentially running instance on the same port
taskkill /F /FI "WINDOWTITLE eq Supply Chain Management*" /T > nul 2>&1
taskkill /F /FI "WINDOWTITLE eq http://localhost:3008*" /T > nul 2>&1

REM Start the application server
echo Starting SCM server...
start "Supply Chain Management" cmd /c "C:\ISASUITE\PortableNodeJS\node.exe server.js"

REM Wait for the server to initialize
timeout /t 2 > nul

REM Open the browser
echo SCM is now running
echo To access SCM, go to: http://localhost:3008
start "" http://localhost:3008

echo.
echo SCM application launched. Close this window to stop the application.