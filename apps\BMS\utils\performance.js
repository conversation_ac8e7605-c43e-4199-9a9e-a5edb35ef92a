/**
 * Performance monitoring utilities for BMS
 */

const os = require('os');
const { performance } = require('perf_hooks');
const express = require('express');

/**
 * Measures execution time of a function
 * @param {Function} fn - Function to measure
 * @param {Array} args - Arguments to pass to the function
 * @returns {Object} Result and execution time
 */
function measureExecutionTime(fn, args = []) {
  const start = performance.now();
  const result = fn(...args);
  const end = performance.now();
  return {
    result,
    executionTime: end - start
  };
}

/**
 * Gets system performance metrics
 * @returns {Object} System metrics
 */
function getSystemMetrics() {
  return {
    cpuUsage: os.loadavg()[0],
    totalMemory: os.totalmem(),
    freeMemory: os.freemem(),
    uptime: os.uptime(),
    timestamp: new Date().toISOString()
  };
}

/**
 * Monitors performance of a function over time
 * @param {Function} fn - Function to monitor
 * @param {Array} args - Arguments to pass to the function
 * @param {number} interval - Monitoring interval in ms
 * @param {number} duration - Total monitoring duration in ms
 * @returns {Promise<Array>} Performance data points
 */
async function monitorPerformance(fn, args = [], interval = 1000, duration = 10000) {
  const dataPoints = [];
  const iterations = Math.floor(duration / interval);

  for (let i = 0; i < iterations; i++) {
    const { executionTime } = measureExecutionTime(fn, args);
    dataPoints.push({
      iteration: i,
      executionTime,
      timestamp: new Date().toISOString(),
      systemMetrics: getSystemMetrics()
    });

    await new Promise(resolve => setTimeout(resolve, interval));
  }

  return dataPoints;
}

/**
 * Configures performance monitoring middleware for Express app
 * @param {Object} app - Express application
 */
function configurePerformance(app) {
  // Add performance monitoring middleware
  app.use((req, res, next) => {
    const start = performance.now();

    // Add response hook to measure request duration
    const originalSend = res.send;
    res.send = function(body) {
      const end = performance.now();
      const duration = end - start;

      // Log performance data
      console.log(`[PERFORMANCE] ${req.method} ${req.url} - ${duration.toFixed(2)}ms`);

      // Add performance headers
      res.set('X-Response-Time', `${duration.toFixed(2)}ms`);

      // Continue with the original send
      return originalSend.call(this, body);
    };

    next();
  });

  // Add system metrics endpoint
  app.get('/metrics', (req, res) => {
    res.json(getSystemMetrics());
  });

  console.log('Performance monitoring configured');
}

/**
 * Configures client-side performance monitoring
 * @param {Object} app - Express application
 */
function configureClientPerformance(app) {
  // Serve client-side performance monitoring script
  app.get('/performance-monitor.js', (req, res) => {
    const script = `
      // Client-side performance monitoring
      (function() {
        // Collect performance metrics
        function collectMetrics() {
          const metrics = {
            navigationTiming: {},
            memory: {},
            deviceInfo: {}
          };

          // Navigation timing
          if (window.performance && window.performance.timing) {
            const timing = window.performance.timing;
            metrics.navigationTiming = {
              loadTime: timing.loadEventEnd - timing.navigationStart,
              domReadyTime: timing.domComplete - timing.domLoading,
              readyStart: timing.fetchStart - timing.navigationStart,
              redirectTime: timing.redirectEnd - timing.redirectStart,
              appcacheTime: timing.domainLookupStart - timing.fetchStart,
              unloadEventTime: timing.unloadEventEnd - timing.unloadEventStart,
              lookupDomainTime: timing.domainLookupEnd - timing.domainLookupStart,
              connectTime: timing.connectEnd - timing.connectStart,
              requestTime: timing.responseEnd - timing.requestStart,
              initDomTreeTime: timing.domInteractive - timing.responseEnd,
              loadEventTime: timing.loadEventEnd - timing.loadEventStart
            };
          }

          // Memory info
          if (window.performance && window.performance.memory) {
            metrics.memory = {
              jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit,
              totalJSHeapSize: window.performance.memory.totalJSHeapSize,
              usedJSHeapSize: window.performance.memory.usedJSHeapSize
            };
          }

          // Device info
          metrics.deviceInfo = {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            screenWidth: window.screen.width,
            screenHeight: window.screen.height,
            devicePixelRatio: window.devicePixelRatio || 1
          };

          return metrics;
        }

        // Send metrics to server
        function sendMetrics() {
          const metrics = collectMetrics();

          fetch('/api/client-metrics', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(metrics)
          }).catch(err => console.error('Failed to send metrics:', err));
        }

        // Send metrics when page loads
        window.addEventListener('load', function() {
          // Wait for everything to finish
          setTimeout(sendMetrics, 0);
        });

        // Expose to window for debugging
        window.performanceMonitor = {
          collectMetrics,
          sendMetrics
        };
      })();
    `;

    res.type('application/javascript');
    res.send(script);
  });

  // Endpoint to receive client metrics
  app.post('/api/client-metrics', express.json(), (req, res) => {
    const metrics = req.body;
    console.log('[CLIENT PERFORMANCE]', JSON.stringify(metrics, null, 2));
    res.status(200).send({ status: 'ok' });
  });

  console.log('Client performance monitoring configured');
}

/**
 * Creates middleware for memory leak detection
 * @returns {Function} Express middleware
 */
function memoryLeakDetection() {
  let memoryUsage = [];
  const maxSamples = 60; // Keep last 60 samples
  const sampleInterval = 60000; // Sample every minute
  let timer = null;

  // Start sampling memory usage
  function startSampling() {
    if (timer) return; // Already started

    timer = setInterval(() => {
      const usage = process.memoryUsage();
      memoryUsage.push({
        timestamp: new Date().toISOString(),
        rss: usage.rss,
        heapTotal: usage.heapTotal,
        heapUsed: usage.heapUsed,
        external: usage.external,
        arrayBuffers: usage.arrayBuffers
      });

      // Keep only the last maxSamples
      if (memoryUsage.length > maxSamples) {
        memoryUsage = memoryUsage.slice(-maxSamples);
      }

      // Check for potential memory leaks
      checkForMemoryLeaks();
    }, sampleInterval);
  }

  // Check for potential memory leaks
  function checkForMemoryLeaks() {
    if (memoryUsage.length < 10) return; // Need at least 10 samples

    // Calculate growth rate over the last 10 samples
    const samples = memoryUsage.slice(-10);
    const firstSample = samples[0];
    const lastSample = samples[samples.length - 1];

    const heapGrowthRate = (lastSample.heapUsed - firstSample.heapUsed) / firstSample.heapUsed;

    // If heap has grown by more than 20% over the last 10 samples, log a warning
    if (heapGrowthRate > 0.2) {
      console.warn('[MEMORY LEAK WARNING] Heap usage has grown by',
        (heapGrowthRate * 100).toFixed(2), '% in the last',
        (samples.length * (sampleInterval / 60000)).toFixed(1), 'minutes');
    }
  }

  // Create middleware
  return (req, res, next) => {
    // Start sampling on first request
    startSampling();

    // Add memory usage endpoint
    if (req.path === '/memory-usage') {
      return res.json({
        current: process.memoryUsage(),
        history: memoryUsage
      });
    }

    next();
  };
}

module.exports = {
  measureExecutionTime,
  getSystemMetrics,
  monitorPerformance,
  configurePerformance,
  configureClientPerformance,
  memoryLeakDetection
};
