<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ISA Suite Integration Hub</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        h1 {
            margin: 0;
        }
        .app-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .app-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid #ccc;
        }
        .app-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* Application-specific colors */
        .app-card.hub {
            border-left-color: #3498db; /* Blue for Hub */
        }
        .app-card.bms {
            border-left-color: #00acc1; /* Teal for BMS */
        }
        .app-card.crm {
            border-left-color: #e74c3c; /* Red for CRM */
        }
        .app-card.mrp {
            border-left-color: #1abc9c; /* Teal for MRP */
        }
        .app-card.wms {
            border-left-color: #2ecc71; /* Green for WMS */
        }
        .app-card.aps {
            border-left-color: #f1c40f; /* Yellow for APS */
        }
        .app-card.apm {
            border-left-color: #34495e; /* Blue-Gray for APM */
        }
        .app-card.pms {
            border-left-color: #9b59b6; /* Purple for PMS */
        }
        .app-card.scm {
            border-left-color: #3498db; /* Light Blue for SCM */
        }
        .app-card.tm {
            border-left-color: #2980b9; /* Dark Blue for TM */
        }
        .app-card h2 {
            margin-top: 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }
        .app-card h2 .app-icon {
            margin-right: 10px;
            font-size: 1.5rem;
            width: 30px;
            text-align: center;
        }
        .app-card p {
            color: #7f8c8d;
            margin-left: 40px;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.8rem;
            margin-top: 10px;
            margin-left: 40px;
        }
        .status.online {
            background-color: #2ecc71;
            color: white;
        }
        .status.offline {
            background-color: #e74c3c;
            color: white;
        }

        /* App-specific status colors */
        .hub .status.online {
            background-color: #3498db;
        }
        .bms .status.online {
            background-color: #e67e22;
        }
        .crm .status.online {
            background-color: #e74c3c;
        }
        .mrp .status.online {
            background-color: #1abc9c;
        }
        .wms .status.online {
            background-color: #2ecc71;
        }
        .aps .status.online {
            background-color: #f1c40f;
        }
        .apm .status.online {
            background-color: #34495e;
        }
        .pms .status.online {
            background-color: #9b59b6;
        }
        .scm .status.online {
            background-color: #3498db;
        }
        .tm .status.online {
            background-color: #2980b9;
        }
        .app-link {
            display: inline-block;
            margin-top: 15px;
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .app-link:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Application-specific button colors */
        .hub .app-link {
            background-color: #3498db;
        }
        .hub .app-link:hover {
            background-color: #2980b9;
        }
        .bms .app-link {
            background-color: #e67e22;
        }
        .bms .app-link:hover {
            background-color: #d35400;
        }
        .crm .app-link {
            background-color: #e74c3c;
        }
        .crm .app-link:hover {
            background-color: #c0392b;
        }
        .mrp .app-link {
            background-color: #1abc9c;
        }
        .mrp .app-link:hover {
            background-color: #16a085;
        }
        .wms .app-link {
            background-color: #2ecc71;
        }
        .wms .app-link:hover {
            background-color: #27ae60;
        }
        .aps .app-link {
            background-color: #f1c40f;
        }
        .aps .app-link:hover {
            background-color: #f39c12;
        }
        .apm .app-link {
            background-color: #34495e;
        }
        .apm .app-link:hover {
            background-color: #2c3e50;
        }
        .pms .app-link {
            background-color: #9b59b6;
        }
        .pms .app-link:hover {
            background-color: #8e44ad;
        }
        .scm .app-link {
            background-color: #3498db;
        }
        .scm .app-link:hover {
            background-color: #2980b9;
        }
        .tm .app-link {
            background-color: #2980b9;
        }
        .tm .app-link:hover {
            background-color: #1f618d;
        }
        .system-metrics {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }
        .metric-card {
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .metric-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <header>
        <h1>ISA Suite Integration Hub</h1>
    </header>

    <div class="banner" style="background-color: #a8d8ff; padding: 15px; text-align: center; border-bottom: 2px solid #3498db;">
        <h3 style="margin: 0; color: #2c3e50;">Welcome to the ISA Suite Integration Hub</h3>
        <p style="margin: 5px 0 0 0; color: #34495e;">Your central command center for all integrated applications</p>
    </div>

    <div class="container">
        <div class="system-metrics">
            <h2>System Metrics</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="cpu-usage">0%</div>
                    <div class="metric-label">CPU Usage</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-usage">0 MB</div>
                    <div class="metric-label">Memory Usage</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="uptime">0h 0m</div>
                    <div class="metric-label">Uptime</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="active-users">0</div>
                    <div class="metric-label">Active Users</div>
                </div>
            </div>
        </div>

        <h2>Applications</h2>
        <div class="app-grid">
            <div class="app-card hub">
                <h2><span class="app-icon"><i class="bi bi-hdd-network"></i></span> Integration Hub</h2>
                <p>Central connection point for all systems</p>
                <div class="status online">Online</div>
                <a href="http://localhost:8000" class="app-link" target="_self">Open</a>
            </div>

            <div class="app-card bms">
                <h2><span class="app-icon"><i class="bi bi-briefcase"></i></span> Business Management System</h2>
                <p>Core business operations and financial tracking</p>
                <div class="status" id="bms-status">Checking...</div>
                <a href="http://localhost:3001" class="app-link" target="_blank">Open</a>
            </div>

            <div class="app-card crm">
                <h2><span class="app-icon"><i class="bi bi-people"></i></span> Customer Relationship Management</h2>
                <p>Customer and sales management</p>
                <div class="status" id="crm-status">Checking...</div>
                <a href="http://localhost:3003" class="app-link" target="_blank">Open</a>
            </div>

            <div class="app-card mrp">
                <h2><span class="app-icon"><i class="bi bi-box-seam"></i></span> Materials Requirements Planning</h2>
                <p>Inventory and materials planning</p>
                <div class="status" id="mrp-status">Checking...</div>
                <a href="http://localhost:3002" class="app-link" target="_blank">Open</a>
            </div>

            <div class="app-card wms">
                <h2><span class="app-icon"><i class="bi bi-building"></i></span> Warehouse Management System</h2>
                <p>Warehouse operations and inventory control</p>
                <div class="status" id="wms-status">Checking...</div>
                <a href="http://localhost:3004" class="app-link" target="_blank">Open</a>
            </div>

            <div class="app-card aps">
                <h2><span class="app-icon"><i class="bi bi-calendar-check"></i></span> Advanced Planning and Scheduling</h2>
                <p>Production scheduling and resource allocation</p>
                <div class="status" id="aps-status">Checking...</div>
                <a href="http://localhost:3005" class="app-link" target="_blank">Open</a>
            </div>

            <div class="app-card apm">
                <h2><span class="app-icon"><i class="bi bi-gear"></i></span> Asset Performance Management</h2>
                <p>Asset monitoring and maintenance</p>
                <div class="status" id="apm-status">Checking...</div>
                <a href="http://localhost:3006" class="app-link" target="_blank">Open</a>
            </div>

            <div class="app-card pms">
                <h2><span class="app-icon"><i class="bi bi-kanban"></i></span> Project Management System</h2>
                <p>Project planning and tracking</p>
                <div class="status" id="pms-status">Checking...</div>
                <a href="http://localhost:3007" class="app-link" target="_blank">Open</a>
            </div>

            <div class="app-card scm">
                <h2><span class="app-icon"><i class="bi bi-truck"></i></span> Supply Chain Management</h2>
                <p>Supply chain operations and logistics</p>
                <div class="status" id="scm-status">Checking...</div>
                <a href="http://localhost:3008" class="app-link" target="_blank">Open</a>
            </div>

            <div class="app-card tm">
                <h2><span class="app-icon"><i class="bi bi-check2-square"></i></span> Task Management System</h2>
                <p>Task assignment and tracking</p>
                <div class="status" id="tm-status">Checking...</div>
                <a href="http://localhost:3009" class="app-link" target="_blank">Open</a>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        // Connect to socket.io
        const socket = io();

        // Function to check application status
        async function checkAppStatus(url, statusElementId) {
            const statusElement = document.getElementById(statusElementId);

            try {
                const response = await fetch(`${url}/health`, { timeout: 2000 });
                if (response.ok) {
                    statusElement.textContent = 'Online';
                    statusElement.className = 'status online';
                } else {
                    statusElement.textContent = 'Error';
                    statusElement.className = 'status offline';
                }
            } catch (error) {
                statusElement.textContent = 'Offline';
                statusElement.className = 'status offline';
            }
        }

        // Function to update system metrics
        function updateSystemMetrics() {
            // Simulate metrics for demo
            document.getElementById('cpu-usage').textContent = Math.floor(Math.random() * 30) + '%';
            document.getElementById('memory-usage').textContent = Math.floor(Math.random() * 1000) + ' MB';

            // Calculate uptime
            const now = new Date();
            const startTime = new Date(now - Math.floor(Math.random() * 86400000)); // Random time in the last 24 hours
            const hours = Math.floor((now - startTime) / 3600000);
            const minutes = Math.floor(((now - startTime) % 3600000) / 60000);
            document.getElementById('uptime').textContent = `${hours}h ${minutes}m`;

            document.getElementById('active-users').textContent = Math.floor(Math.random() * 50);
        }

        // Check application statuses
        function checkAllApps() {
            checkAppStatus('http://localhost:3001', 'bms-status');
            checkAppStatus('http://localhost:3003', 'crm-status');
            checkAppStatus('http://localhost:3002', 'mrp-status');
            checkAppStatus('http://localhost:3004', 'wms-status');
            checkAppStatus('http://localhost:3005', 'aps-status');
            checkAppStatus('http://localhost:3006', 'apm-status');
            checkAppStatus('http://localhost:3007', 'pms-status');
            checkAppStatus('http://localhost:3008', 'scm-status');
            checkAppStatus('http://localhost:3009', 'tm-status');
        }

        // Update metrics and check apps on load
        document.addEventListener('DOMContentLoaded', () => {
            updateSystemMetrics();
            checkAllApps();

            // Update metrics every 5 seconds
            setInterval(updateSystemMetrics, 5000);

            // Check app status every 30 seconds
            setInterval(checkAllApps, 30000);
        });

        // Listen for real-time updates from server
        socket.on('metrics-update', (data) => {
            console.log('Received metrics update:', data);
            // Update metrics with real data when available
        });

        socket.on('app-status-change', (data) => {
            console.log('App status changed:', data);
            // Update app status when notified by server
            const statusElement = document.getElementById(`${data.app}-status`);
            if (statusElement) {
                statusElement.textContent = data.status;
                statusElement.className = `status ${data.status.toLowerCase()}`;
            }
        });
    </script>
</body>
</html>
