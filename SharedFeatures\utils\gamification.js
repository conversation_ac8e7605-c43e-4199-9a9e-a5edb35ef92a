// Gamification Module

// Mock implementation for demonstration purposes
// In a real app, this would use a database

const logger = require('../logger').createLogger('Gamification');

// In-memory storage for user progress
const userProgress = new Map();

/**
 * Record tutorial completion
 */
function recordTutorialCompletion(userId, tutorialId) {
  logger.info('Recording tutorial completion', { userId, tutorialId });

  // Get or initialize user progress
  const progress = getUserProgress(userId);

  // Add tutorial to completed list if not already there
  if (!progress.completedTutorials.includes(tutorialId)) {
    progress.completedTutorials.push(tutorialId);
    progress.points += 10; // Award points for completing tutorial
    progress.level = calculateLevel(progress.points);
  }

  // Update user progress
  userProgress.set(userId, progress);

  return progress;
}

/**
 * Record transaction
 */
function recordTransaction(userId) {
  logger.info('Recording transaction', { userId });

  // Get or initialize user progress
  const progress = getUserProgress(userId);

  // Increment transaction count and award points
  progress.transactionCount += 1;
  progress.points += 5; // Award points for each transaction
  progress.level = calculateLevel(progress.points);

  // Update user progress
  userProgress.set(userId, progress);

  return progress;
}

/**
 * Get user progress
 */
function getUserProgress(userId) {
  // Get existing progress or initialize new progress
  if (!userProgress.has(userId)) {
    userProgress.set(userId, {
      userId,
      completedTutorials: [],
      transactionCount: 0,
      points: 0,
      level: 1,
      badges: [],
    });
  }

  return userProgress.get(userId);
}

/**
 * Get leaderboard
 */
function getLeaderboard(limit = 10) {
  // Convert Map to array and sort by points
  const users = Array.from(userProgress.values())
    .sort((a, b) => b.points - a.points)
    .slice(0, limit);

  return users;
}

/**
 * Calculate level based on points
 */
function calculateLevel(points) {
  // Simple level calculation: 1 level per 100 points
  return Math.floor(points / 100) + 1;
}

module.exports = {
  recordTutorialCompletion,
  recordTransaction,
  getUserProgress,
  getLeaderboard,
};
