# Ice Systems Australasia Applications Suite

This USB drive contains the complete suite of Ice Systems Australasia applications, organized in a modular structure for easy maintenance and development.

## Directory Structure

- **Apps/** - Contains all application modules
  - **BMS/** - Business Intelligence System
  - **MRP/** - Materials Requirements Planning
  - **CRM/** - Customer Relationship Management
  - **hub/** - Central integration hub (runs on port 8000)
  - **WMS/** - Warehouse Management System
  - **APS/** - Advanced Planning and Scheduling
  - **APM/** - Asset Performance Management
- **SharedFeatures/** - Common code and features shared across applications
- **docs/** - Documentation for all applications
- **tools/** - Utility scripts and tools

## Getting Started

1. To launch any application, run the `launcher.bat` file in the root directory.
2. Select the application you want to run from the menu.
3. Each application can be run as either a web application or a desktop application.

## Port Configuration

- Integration Hub: http://localhost:8000
- BMS: http://localhost:3001
- MRP: http://localhost:3002
- CRM: http://localhost:3003
- WMS: http://localhost:3004
- APS: http://localhost:3005
- APM: http://localhost:3006

## Features

- All applications are available as both PWA and standalone .exe with desktop/mobile icons
- Real-time data synchronization between applications
- Google Apps integration in all modules
- Consistent implementation of features across all applications

## Maintenance

To update or modify an application:

1. Navigate to the appropriate application folder
2. Make your changes
3. Test the application using the start scripts
4. If changes affect shared features, update the SharedFeatures repository

## Support

For support or questions, please contact Ice Systems Australasia support team.
