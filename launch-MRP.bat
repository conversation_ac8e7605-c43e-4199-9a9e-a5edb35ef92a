@echo off
echo Starting Materials Requirements Planning (MRP)...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Change to the MRP directory
cd /d C:\ISASUITE\apps\MRP\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call pnpm install
)

REM Run the application with production mode by default
echo Starting MRP in production mode...
echo To access MRP, go to: http://localhost:3002
start "" http://localhost:3002

REM Start using pnpm instead of directly invoking Node
pnpm start

echo.
echo MRP application has been closed.
pause