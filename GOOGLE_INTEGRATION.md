# Google Integration for ISA Suite

This document explains how to use and maintain the Google integration features in the ISA Suite.

## Overview

The ISA Suite integrates with various Google services:

- **Google Drive**: File storage and sharing
- **Google Calendar**: Scheduling and event management
- **Gmail**: Email communication
- **Google Sheets**: Data analysis and reporting
- **Google Docs**: Document creation and editing
- **Google Contacts**: Contact management
- **Google Maps**: Location services and mapping
- **Google Analytics**: Website analytics and reporting
- **Google Translate**: Language translation services

## Authentication

The Google integration uses OAuth2 authentication with the following components:

1. **Credentials file**: `SharedFeatures/integrations/google-credentials.json` contains the OAuth client ID and secret
2. **Token file**: `SharedFeatures/integrations/google-token.json` stores the OAuth token after authentication

## Token Management

Google access tokens expire after a certain period (typically 60 minutes). To ensure uninterrupted access to Google services, you need to manage the token lifecycle:

### Checking Token Status

Run `check-google-token-status.bat` to check the current status of your Google token. This will show:
- Whether the token exists
- When it will expire
- If it needs to be refreshed

### Manual Token Generation

If the token has expired or doesn't exist, you can generate a new one manually:

1. Run `node SharedFeatures/integrations/google.js`
2. Follow the prompts to authorize the application
3. Copy the authorization code from the browser
4. Paste it into the terminal when prompted

### Automatic Token Monitoring

To avoid having to manually refresh the token, you can use the token monitor:

1. Run `start-token-monitor.bat`
2. The monitor will run in the background and automatically refresh the token before it expires
3. Logs are written to `logs/google-token-monitor.log`

## Google Services

### Core Services

- **Google Drive**: File storage and sharing
  - List files and folders
  - Upload and download files
  - Share files with others

- **Google Calendar**: Scheduling and event management
  - List events
  - Create and update events
  - Set reminders

- **Gmail**: Email communication
  - Send emails
  - Attach files

- **Google Sheets**: Data analysis and reporting
  - Read and write spreadsheet data
  - Create charts and reports

- **Google Docs**: Document creation and editing
  - Create documents
  - Edit document content

- **Google Contacts**: Contact management
  - List contacts
  - Get contact details

### New Services

- **Google Maps**: Location services and mapping
  - Geocode addresses
  - Get directions between locations
  - Display maps

- **Google Analytics**: Website analytics and reporting
  - Get website traffic data
  - Analyze user behavior
  - Generate reports

- **Google Translate**: Language translation services
  - Translate text between languages
  - Detect language

## Testing Google Integration

You can test the Google integration features using the provided test script:

```
node test-google-features.js
```

This script tests all Google services and reports which ones are working correctly.

You can also view the Google integration demo page to see the UI components in action:

```
open-browser google-integration-demo.html
```

## Troubleshooting

### Token Expired

If you see "Failed to initialize Google API" errors in the application logs, the token may have expired. Run `check-google-token-status.bat` to check and follow the instructions to refresh it.

### Authentication Failed

If authentication fails:
1. Check that the credentials file (`google-credentials.json`) exists and contains valid credentials
2. Ensure you have internet access
3. Try generating a new token manually

### API Limits

Google APIs have usage limits. If you encounter quota errors:
1. Wait a while before trying again
2. Consider implementing rate limiting in your application
3. Check the Google Cloud Console for your current usage and limits

## UI Components

The ISA Suite includes pre-built UI components for Google integration that can be used in any application. These components provide a consistent user experience across all applications.

### Available Components

- **Google Drive UI**: Enhanced file browser with search capability, visual progress indicators, and improved navigation
- **Google Calendar UI**: Calendar view and event management
- **Gmail UI**: Email composition and sending
- **Google Sheets UI**: Spreadsheet viewer and editor
- **Google Docs UI**: Document viewer and editor
- **Google Maps UI**: Map viewer and location search
- **Google Translate UI**: Text translation interface

### Using UI Components

To use these components in your application:

1. Import the UI components module:

```javascript
const googleUI = require('../../SharedFeatures/ui/google-integration-ui');
```

2. Add the component to your HTML:

```javascript
app.get('/dashboard', (req, res) => {
  res.render('dashboard', {
    googleDriveUI: googleUI.GoogleDriveUI,
    googleCalendarUI: googleUI.GoogleCalendarUI,
    googleIntegrationCSS: googleUI.GoogleIntegrationCSS
  });
});
```

3. Initialize the component handlers in your client-side JavaScript:

```javascript
const googleHandlers = require('../../SharedFeatures/ui/google-integration-handlers');

// Initialize Google Drive UI
googleHandlers.GoogleDriveHandlers.init('google-drive-container');
```

### Google Drive UI Features

The Google Drive integration includes several enhanced features:

1. **File Search**: Users can search for files within the Drive modal
   - Real-time filtering of files based on search terms
   - Highlighted search matches for better visibility
   - Automatic reset when changing tabs or folders

2. **Progress Indicators**: Visual feedback for file operations
   - Loading indicators when opening files
   - Progress bars for file downloads
   - Success/error notifications

3. **Improved Navigation**:
   - "Root Directory" button for quick navigation back to the top level
   - Improved folder structure visualization
   - Better handling of deep folder hierarchies

4. **Sidebar Integration**:
   - Automatic detection of Google Drive sidebar links
   - Dynamic handling of sidebar clicks to open the Drive modal
   - Support for various sidebar layouts and structures

For detailed implementation documentation, see `apps/BMS/public/docs/google-drive-integration.md`.

### Demo Page

A demo page showing all Google integration UI components is available at `google-integration-demo.html`. You can use this as a reference for implementing the components in your application.

## Development

When developing features that use Google integration:

1. Always check if the Google API is initialized before using it
2. Handle authentication errors gracefully
3. Use the shared Google integration module (`SharedFeatures/integrations/google.js`) rather than implementing your own
4. Use the provided UI components for a consistent user experience

### Backend Example

```javascript
const google = require('../../SharedFeatures/integrations/google');

// Initialize Google API
google.initGoogleAPI().catch((err) => {
  logger.error('Failed to initialize Google API', { error: err.message });
});

// Use Google services
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger.error('Failed to list Google Drive files', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});
```

### Frontend Example

```javascript
// Add Google Drive UI to the page
document.getElementById('google-drive-container').innerHTML = googleUI.GoogleDriveUI;

// Initialize the UI
googleHandlers.GoogleDriveHandlers.init('google-drive-container');

// Add event listener for custom actions
document.getElementById('custom-drive-action').addEventListener('click', async () => {
  try {
    const response = await fetch('/api/google-drive/files');
    const data = await response.json();

    if (data.status === 'success') {
      // Process files
      console.log('Files:', data.data);
    } else {
      console.error('Error:', data.message);
    }
  } catch (error) {
    console.error('Error:', error.message);
  }
});
```
