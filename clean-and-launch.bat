@echo off
setlocal enabledelayedexpansion

REM Set PATH to include PortableNodeJS
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Check if the ISASUITE directory exists
if not exist "C:\ISASUITE" (
    echo ERROR: C:\ISASUITE directory not found.
    echo Please make sure the ISA Suite is installed correctly.
    pause
    exit /b 1
)

REM Create a log directory if it doesn't exist
if not exist "C:\ISASUITE\logs" mkdir C:\ISASUITE\logs

REM Define colors for console output
set "GREEN=0A"
set "YELLOW=0E"
set "RED=0C"
set "WHITE=0F"
set "CYAN=0B"
set "MAGENTA=0D"

REM Define application information
set "apps[1]=Integration Hub|hub|8000|index.js"
set "apps[2]=Business Management System|BMS|3001|index.js"
set "apps[3]=Materials Requirements Planning|MRP|3002|index.js"
set "apps[4]=Customer Relationship Management|CRM|3003|index.js"
set "apps[5]=Warehouse Management System|WMS|3004|index.js"
set "apps[6]=Advanced Planning and Scheduling|APS|3005|index.js"
set "apps[7]=Asset Performance Management|APM|3006|index.js"
set "apps[8]=Project Management System|PMS|3007|index.js"
set "apps[9]=Supply Chain Management|SCM|3008|server.js"
set "apps[10]=Task Management System|TM|3009|server.js"

REM Clean up any existing Node.js processes
echo Cleaning up existing processes...
taskkill /F /IM node.exe > nul 2>&1
echo Waiting for ports to be released...
timeout /t 5 > nul

REM Select application mode
cls
color %WHITE%
echo ===================================
color %CYAN%
echo    ISA Suite - Single Window Launcher
color %WHITE%
echo ===================================
echo.
echo Select application mode:
echo.
color %GREEN%
echo  1. Production Mode
color %YELLOW%
echo  2. Sandbox/Training Mode
color %MAGENTA%
echo  3. Demo Mode
color %WHITE%
echo.
set /p mode_choice=Enter your choice (1-3):

if "%mode_choice%"=="1" (
    set "APP_MODE=production"
    set "MODE_COLOR=%GREEN%"
    set "MODE_NAME=Production"
) else if "%mode_choice%"=="2" (
    set "APP_MODE=sandbox"
    set "MODE_COLOR=%YELLOW%"
    set "MODE_NAME=Sandbox/Training"
) else if "%mode_choice%"=="3" (
    set "APP_MODE=demo"
    set "MODE_COLOR=%MAGENTA%"
    set "MODE_NAME=Demo"
) else (
    set "APP_MODE=production"
    set "MODE_COLOR=%GREEN%"
    set "MODE_NAME=Production"
    echo Invalid choice. Using default Production mode.
    timeout /t 2 > nul
)

cls
color %MODE_COLOR%
echo ===================================
echo    ISA Suite - %MODE_NAME% Mode
echo ===================================
color %WHITE%
echo.
echo Starting all ISA Suite applications in a single window...
echo.

REM Create a log file for this session
set "LOG_FILE=C:\ISASUITE\logs\isasuite-%APP_MODE%-%DATE:~-4,4%%DATE:~-7,2%%DATE:~-10,2%.log"
echo ISA Suite started in %MODE_NAME% mode at %TIME% on %DATE% > "%LOG_FILE%"
echo. >> "%LOG_FILE%"

REM Start all applications in the background
echo Starting applications...
echo.

REM Set environment variable for all applications
set "NODE_ENV=%APP_MODE%"

REM Start Integration Hub
echo Starting Integration Hub...
start /b cmd /c "cd C:\ISASUITE\apps\hub && node index.js >> "%LOG_FILE%" 2>&1"
echo Integration Hub started on port 8000.

REM Wait for Integration Hub to start
timeout /t 5 > nul

REM Start all other applications
for /L %%i in (2,1,10) do (
    for /F "tokens=1-4 delims=|" %%a in ("!apps[%%i]!") do (
        echo Starting %%a...
        start /b cmd /c "cd C:\ISASUITE\apps\%%b && node %%d >> "%LOG_FILE%" 2>&1"
        echo %%a started on port %%c.
    )
    timeout /t 1 > nul
)

echo.
echo All applications started successfully in %MODE_NAME% mode.
echo.
echo Application URLs:
echo Integration Hub: http://localhost:8000
echo Business Management System: http://localhost:3001
echo Materials Requirements Planning: http://localhost:3002
echo Customer Relationship Management: http://localhost:3003
echo Warehouse Management System: http://localhost:3004
echo Advanced Planning and Scheduling: http://localhost:3005
echo Asset Performance Management: http://localhost:3006
echo Project Management System: http://localhost:3007
echo Supply Chain Management: http://localhost:3008
echo Task Management System: http://localhost:3009
echo.
echo All application logs are saved in %LOG_FILE%
echo.

REM Open Integration Hub in browser
echo Opening Integration Hub in browser...
start http://localhost:8000

echo.
echo Press any key to stop all applications and exit.
pause > nul

REM Stop all applications
echo.
echo Stopping all applications...
taskkill /F /IM node.exe > nul 2>&1
echo All applications stopped.
echo.
echo Thank you for using ISA Suite.
timeout /t 3 > nul
