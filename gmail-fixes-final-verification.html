<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Fixes Verification - Final Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 10px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        .test-card h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .status.running {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .test-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .summary {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .manual-test {
            background: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛠️ BMS Gmail Integration - Final Verification</h1>
        <p>Testing all Gmail functionality fixes and enhancements</p>
    </div>

    <div class="test-grid">
        <div class="test-card">
            <h3>🌐 BMS Server Status</h3>
            <div id="server-status">
                <span class="status running">Checking...</span>
            </div>
            <button class="test-btn" onclick="testServerStatus()">Recheck Server</button>
            <div id="server-details"></div>
        </div>

        <div class="test-card">
            <h3>📧 Gmail Modal Configuration</h3>
            <div id="modal-status">
                <span class="status running">Checking...</span>
            </div>
            <button class="test-btn" onclick="testModalConfig()">Test Modal Config</button>
            <div id="modal-details"></div>
        </div>

        <div class="test-card">
            <h3>🔗 Dashboard Button Fix</h3>
            <div id="button-status">
                <span class="status running">Checking...</span>
            </div>
            <button class="test-btn" onclick="testDashboardButton()">Test Button</button>
            <div id="button-details"></div>
        </div>

        <div class="test-card">
            <h3>⚙️ Enhanced Gmail Integration</h3>
            <div id="integration-status">
                <span class="status running">Checking...</span>
            </div>
            <button class="test-btn" onclick="testIntegration()">Test Integration</button>
            <div id="integration-details"></div>
        </div>

        <div class="test-card">
            <h3>🧹 Conflict Resolution</h3>
            <div id="conflict-status">
                <span class="status running">Checking...</span>
            </div>
            <button class="test-btn" onclick="testConflicts()">Check Conflicts</button>
            <div id="conflict-details"></div>
        </div>

        <div class="test-card">
            <h3>📱 Manual Tests</h3>
            <div class="manual-test">
                <strong>To manually verify:</strong><br>
                1. <a href="http://localhost:3001" target="_blank">Open BMS Dashboard</a><br>
                2. Click "View All Emails" button<br>
                3. Verify Gmail modal opens<br>
                4. Test sidebar Gmail link<br>
                5. Check all tabs work in modal
            </div>
            <button class="test-btn" onclick="openBMS()">Open BMS</button>
        </div>
    </div>

    <div class="summary">
        <h2>📊 Test Summary</h2>
        <div id="overall-status">
            <span class="status running">Running tests...</span>
        </div>
        <div id="test-log" class="log"></div>
        <button class="test-btn" onclick="runAllTests()">Run All Tests</button>
        <button class="test-btn" onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        let testResults = {};
        let logEntries = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logEntries.push(`[${timestamp}] ${message}`);
            updateLog();
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateLog() {
            const logElement = document.getElementById('test-log');
            logElement.innerHTML = logEntries.slice(-20).join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        function setStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            const statusClass = status ? 'success' : 'error';
            element.innerHTML = `<span class="status ${statusClass}">${status ? '✅ PASS' : '❌ FAIL'}</span>`;
            testResults[elementId] = status;
            log(`${elementId}: ${message}`, status ? 'success' : 'error');
            updateOverallStatus();
        }

        function setWarning(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<span class="status warning">⚠️ WARNING</span>`;
            log(`${elementId}: ${message}`, 'warning');
        }

        function setDetails(elementId, details) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="log">${details}</div>`;
        }

        function updateOverallStatus() {
            const passed = Object.values(testResults).filter(r => r === true).length;
            const failed = Object.values(testResults).filter(r => r === false).length;
            const total = Object.keys(testResults).length;
            
            const overallElement = document.getElementById('overall-status');
            if (total === 0) {
                overallElement.innerHTML = '<span class="status running">Running tests...</span>';
            } else if (failed === 0) {
                overallElement.innerHTML = `<span class="status success">✅ ALL TESTS PASSED (${passed}/${total})</span>`;
            } else {
                overallElement.innerHTML = `<span class="status error">❌ ${failed} FAILED, ${passed} PASSED (${total} total)</span>`;
            }
        }

        async function testServerStatus() {
            log('Testing BMS server status...');
            try {
                const response = await fetch('http://localhost:3001', { 
                    method: 'GET',
                    cache: 'no-cache'
                });
                
                if (response.ok) {
                    setStatus('server-status', true, 'BMS server is running on localhost:3001');
                    setDetails('server-details', `Status: ${response.status} ${response.statusText}<br>Content-Type: ${response.headers.get('content-type')}`);
                } else {
                    setStatus('server-status', false, `Server returned status ${response.status}`);
                    setDetails('server-details', `Error: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                setStatus('server-status', false, 'Server not accessible: ' + error.message);
                setDetails('server-details', `Error: ${error.message}<br>Make sure BMS is running on localhost:3001`);
            }
        }

        async function testModalConfig() {
            log('Testing Gmail modal configuration...');
            try {
                const response = await fetch('http://localhost:3001');
                const html = await response.text();
                
                const checks = {
                    gmailModal: html.includes('id="gmailModal"'),
                    modalConfig: html.includes("modalId: 'gmailModal'"),
                    viewAllButton: html.includes('data-bs-target="#gmailModal"'),
                    noOldConfig: !html.includes("modalId: 'bms-gmailModal'")
                };
                
                const passed = Object.values(checks).every(c => c);
                setStatus('modal-status', passed, passed ? 'Modal configuration is correct' : 'Modal configuration has issues');
                
                let details = 'Configuration checks:\n';
                details += `• Gmail Modal Element: ${checks.gmailModal ? '✅' : '❌'}\n`;
                details += `• Correct Modal ID Config: ${checks.modalConfig ? '✅' : '❌'}\n`;
                details += `• View All Button Target: ${checks.viewAllButton ? '✅' : '❌'}\n`;
                details += `• No Old Config: ${checks.noOldConfig ? '✅' : '❌'}`;
                
                setDetails('modal-details', details);
                
            } catch (error) {
                setStatus('modal-status', false, 'Error testing modal config: ' + error.message);
                setDetails('modal-details', `Error: ${error.message}`);
            }
        }

        async function testDashboardButton() {
            log('Testing dashboard "View All Emails" button...');
            try {
                const response = await fetch('http://localhost:3001');
                const html = await response.text();
                
                const checks = {
                    bootstrapTrigger: html.includes('data-bs-toggle="modal" data-bs-target="#gmailModal"'),
                    noOldOnclick: !html.includes('onclick="document.getElementById(\'gmail-link\').click();"'),
                    hasViewAllText: html.includes('View All Emails')
                };
                
                const passed = Object.values(checks).every(c => c);
                setStatus('button-status', passed, passed ? 'Dashboard button is fixed' : 'Dashboard button has issues');
                
                let details = 'Button checks:\n';
                details += `• Bootstrap Modal Trigger: ${checks.bootstrapTrigger ? '✅' : '❌'}\n`;
                details += `• No Old onclick Handler: ${checks.noOldOnclick ? '✅' : '❌'}\n`;
                details += `• Has "View All Emails" Text: ${checks.hasViewAllText ? '✅' : '❌'}`;
                
                setDetails('button-details', details);
                
            } catch (error) {
                setStatus('button-status', false, 'Error testing button: ' + error.message);
                setDetails('button-details', `Error: ${error.message}`);
            }
        }

        async function testIntegration() {
            log('Testing enhanced Gmail integration...');
            try {
                const response = await fetch('http://localhost:3001');
                const html = await response.text();
                
                const checks = {
                    enhancedScript: html.includes('gmail-integration-enhanced.js'),
                    correctTrigger: html.includes("triggerId: 'gmail-link'"),
                    hasGmailLink: html.includes('id="gmail-link"'),
                    correctModalId: html.includes("modalId: 'gmailModal'"),
                    noBadTrigger: !html.includes("triggerId: 'open-gmail-btn'")
                };
                
                const passed = Object.values(checks).every(c => c);
                setStatus('integration-status', passed, passed ? 'Enhanced integration is configured correctly' : 'Integration has configuration issues');
                
                let details = 'Integration checks:\n';
                details += `• Enhanced Script Loaded: ${checks.enhancedScript ? '✅' : '❌'}\n`;
                details += `• Correct Trigger ID: ${checks.correctTrigger ? '✅' : '❌'}\n`;
                details += `• Gmail Link Exists: ${checks.hasGmailLink ? '✅' : '❌'}\n`;
                details += `• Correct Modal ID: ${checks.correctModalId ? '✅' : '❌'}\n`;
                details += `• No Bad Trigger: ${checks.noBadTrigger ? '✅' : '❌'}`;
                
                setDetails('integration-details', details);
                
            } catch (error) {
                setStatus('integration-status', false, 'Error testing integration: ' + error.message);
                setDetails('integration-details', `Error: ${error.message}`);
            }
        }

        async function testConflicts() {
            log('Testing for remaining conflicts...');
            try {
                const response = await fetch('http://localhost:3001');
                const html = await response.text();
                
                const checks = {
                    noDuplicateInit: !html.includes('// Initialize Gmail integration') || 
                                   (html.match(/Initialize Gmail integration/g) || []).length <= 1,
                    noConflictingScripts: !html.includes('conflicting-gmail') && 
                                         !html.includes('duplicate-gmail'),
                    cleanEnhancedConfig: html.includes('Enhanced Gmail integration initialized for BMS'),
                    noOldModalId: !html.includes("'bms-gmailModal'")
                };
                
                const passed = Object.values(checks).every(c => c);
                setStatus('conflict-status', passed, passed ? 'No conflicts detected' : 'Conflicts still present');
                
                let details = 'Conflict checks:\n';
                details += `• No Duplicate Initialization: ${checks.noDuplicateInit ? '✅' : '❌'}\n`;
                details += `• No Conflicting Scripts: ${checks.noConflictingScripts ? '✅' : '❌'}\n`;
                details += `• Clean Enhanced Config: ${checks.cleanEnhancedConfig ? '✅' : '❌'}\n`;
                details += `• No Old Modal ID: ${checks.noOldModalId ? '✅' : '❌'}`;
                
                setDetails('conflict-details', details);
                
            } catch (error) {
                setStatus('conflict-status', false, 'Error testing conflicts: ' + error.message);
                setDetails('conflict-details', `Error: ${error.message}`);
            }
        }

        function openBMS() {
            window.open('http://localhost:3001', '_blank');
            log('Opened BMS in new tab for manual testing');
        }

        function clearLog() {
            logEntries = [];
            updateLog();
            log('Log cleared');
        }

        async function runAllTests() {
            log('Running all automated tests...');
            testResults = {};
            updateOverallStatus();
            
            await testServerStatus();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testModalConfig();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testDashboardButton();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testIntegration();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testConflicts();
            
            log('All automated tests completed');
        }

        // Auto-run tests when page loads
        window.addEventListener('load', function() {
            log('Gmail Fixes Verification started');
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
