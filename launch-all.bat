@echo off
echo ===================================================
echo ISA Suite - Integrated Systems Administration Suite
echo ===================================================
echo.
echo This launcher will start all ISA Suite applications.
echo.
echo Starting Hub first...

REM Start the Hub (always start first)
start "" "C:\ISASUITE\launch-hub.bat"

REM Wait for Hub to initialize
timeout /t 5 > nul

echo.
echo Starting all other applications...
echo.

REM Start the remaining applications
start "" "C:\ISASUITE\launch-BMS.bat" 
start "" "C:\ISASUITE\launch-MRP.bat"
start "" "C:\ISASUITE\launch-CRM.bat"
start "" "C:\ISASUITE\launch-WMS.bat"
start "" "C:\ISASUITE\launch-APS.bat"
start "" "C:\ISASUITE\launch-APM.bat"
start "" "C:\ISASUITE\launch-PMS.bat"
start "" "C:\ISASUITE\launch-SCM.bat"
start "" "C:\ISASUITE\launch-TM.bat"

echo.
echo All ISA Suite applications have been launched.
echo To access the main Hub interface, go to: http://localhost:8000
echo.
echo Note: Close this window to manage applications individually.
echo To stop all applications at once, use stop-apps.bat
echo.
pause