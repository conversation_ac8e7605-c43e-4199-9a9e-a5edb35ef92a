/**
 * Enhanced MRP Gmail Fix
 * This file provides comprehensive fixes for Gmail modal functionality in the MRP application
 * - Fixes dropdown menus not opening
 * - Fixes modal backdrop not being removed when closing the modal
 * - Properly initializes sort and filter functionality
 */

// Execute when the document is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced MRP Gmail Fix loaded');

    // Find the Open Gmail button
    const openGmailBtn = document.getElementById('mrp-open-gmail-btn');
    if (openGmailBtn) {
        console.log('Found Open Gmail button, adding enhanced event listener');

        // Remove any existing click handlers
        const newBtn = openGmailBtn.cloneNode(true);
        openGmailBtn.parentNode.replaceChild(newBtn, openGmailBtn);

        // Add our enhanced click handler
        newBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Open Gmail button clicked - using enhanced implementation');
            openMrpGmailModalEnhanced();
        });
    } else {
        console.warn('Could not find Open Gmail button by ID "mrp-open-gmail-btn"');
        
        // Try alternative selector
        const gmailLinks = document.querySelectorAll('[href="#gmail"], [data-bs-target="#mrp-gmailModal"]');
        gmailLinks.forEach(link => {
            console.log('Found alternative Gmail link, adding enhanced event listener');
            
            // Remove any existing click handlers
            const newLink = link.cloneNode(true);
            link.parentNode.replaceChild(newLink, link);
            
            // Add our enhanced click handler
            newLink.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Gmail link clicked - using enhanced implementation');
                openMrpGmailModalEnhanced();
            });
        });
    }

    // Make sure we clear any lingering modal backdrops when the page loads
    cleanupModalBackdrops();
});

/**
 * Opens the Gmail modal with enhanced error handling and cleanup
 */
function openMrpGmailModalEnhanced() {
    // First clean up any existing modal backdrops
    cleanupModalBackdrops();
    
    console.log('Opening MRP Gmail modal with enhanced implementation');
    
    // Get the Gmail modal element
    const gmailModal = document.getElementById('mrp-gmailModal');
    if (!gmailModal) {
        console.error('Gmail modal not found with ID "mrp-gmailModal"');
        return;
    }

    try {
        // Ensure any existing modal instance is properly disposed
        try {
            const existingModal = bootstrap.Modal.getInstance(gmailModal);
            if (existingModal) {
                console.log('Disposing existing modal instance');
                existingModal.dispose();
            }
        } catch (err) {
            console.warn('Error while disposing existing modal:', err);
        }
        
        // Create a new modal instance with proper options
        const modal = new bootstrap.Modal(gmailModal, {
            backdrop: true,
            keyboard: true,
            focus: true
        });
        
        // Show the modal
        modal.show();
        
        // Add event listener to properly clean up when modal is hidden
        gmailModal.addEventListener('hidden.bs.modal', function onModalHidden() {
            console.log('Gmail modal hidden, cleaning up');
            cleanupModalBackdrops();
            
            // Remove this specific listener to avoid memory leaks
            gmailModal.removeEventListener('hidden.bs.modal', onModalHidden);
        });
        
        // Initialize dropdown functionality after modal is shown
        gmailModal.addEventListener('shown.bs.modal', function onModalShown() {
            console.log('Gmail modal shown, initializing dropdowns');
            initializeGmailDropdowns();
            
            // Setup sort and filter functionality
            if (typeof setupGmailSortAndFilter === 'function') {
                setupGmailSortAndFilter();
            }
            
            // Remove this specific listener to avoid memory leaks
            gmailModal.removeEventListener('shown.bs.modal', onModalShown);
        }, { once: true });
        
    } catch (err) {
        console.error('Error creating or showing modal:', err);
        // As a fallback, try the data-bs-toggle approach
        gmailModal.setAttribute('data-bs-toggle', 'modal');
        gmailModal.click();
    }
}

/**
 * Initializes Gmail dropdown menus with proper event handlers
 */
function initializeGmailDropdowns() {
    console.log('Initializing Gmail dropdowns');
    
    // Get all dropdown toggle elements within the Gmail modal
    const gmailModal = document.getElementById('mrp-gmailModal');
    if (!gmailModal) return;
    
    const dropdownElements = gmailModal.querySelectorAll('.dropdown-toggle');
    console.log(`Found ${dropdownElements.length} dropdown toggles`);
    
    dropdownElements.forEach((element, index) => {
        console.log(`Initializing dropdown #${index + 1}:`, element.id || 'unnamed dropdown');
        
        try {
            // Dispose any existing dropdown instance
            try {
                const dropdownInstance = bootstrap.Dropdown.getInstance(element);
                if (dropdownInstance) {
                    dropdownInstance.dispose();
                }
            } catch (err) {
                console.warn(`Error disposing dropdown #${index + 1}:`, err);
            }
            
            // Create new dropdown instance
            const dropdown = new bootstrap.Dropdown(element);
            
            // Add direct click handler as a fallback
            element.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                dropdown.toggle();
            });
            
            console.log(`Dropdown #${index + 1} initialized successfully`);
        } catch (err) {
            console.error(`Error initializing dropdown #${index + 1}:`, err);
            
            // Emergency fallback - add direct data attributes
            element.setAttribute('data-bs-toggle', 'dropdown');
            element.setAttribute('aria-expanded', 'false');
        }
    });
    
    // Add direct click handlers to dropdown items as well
    const dropdownItems = gmailModal.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        // Get the action type (sort or filter) and value
        const sortValue = item.getAttribute('data-sort');
        const filterValue = item.getAttribute('data-filter');
        
        if (sortValue) {
            // This is a sort dropdown item
            item.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Sort dropdown item clicked:', sortValue);
                if (typeof sortEmails === 'function') {
                    sortEmails(sortValue);
                }
            });
        } else if (filterValue) {
            // This is a filter dropdown item
            item.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Filter dropdown item clicked:', filterValue);
                if (typeof filterEmails === 'function') {
                    filterEmails(filterValue);
                }
            });
        }
    });
}

/**
 * Cleans up any lingering modal backdrops and resets body styles
 */
function cleanupModalBackdrops() {
    console.log('Cleaning up modal backdrops');
    
    // Remove any lingering backdrop elements
    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
        console.log('Removing modal backdrop');
        backdrop.remove();
    });
    
    // Reset body styles
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
}

/**
 * Global function to setup Gmail sort and filter functionality
 * This function is called when the Gmail modal is opened
 */
window.setupGmailSortAndFilter = function() {
    console.log('Setting up Gmail sort and filter functionality - enhanced version');

    // First, make sure Bootstrap's dropdown functionality is properly initialized
    initializeGmailDropdowns();
    
    // Setup search functionality
    const searchInput = document.getElementById('email-search');
    const searchBtn = document.getElementById('search-btn');
    
    if (searchInput && searchBtn) {
        // Search on button click
        searchBtn.addEventListener('click', function() {
            if (typeof searchEmails === 'function') {
                searchEmails(searchInput.value);
            } else {
                console.error('searchEmails function is not defined');
            }
        });
        
        // Search on Enter key
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter' && typeof searchEmails === 'function') {
                searchEmails(this.value);
            }
        });
    }
    
    // Setup refresh functionality
    const refreshBtn = document.getElementById('refresh-gmail');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            console.log('Refresh button clicked');
            if (typeof showAllEmails === 'function') {
                showAllEmails();
            } else {
                console.error('showAllEmails function is not defined');
                // Fallback implementation
                document.querySelectorAll('#email-list .list-group-item').forEach(item => {
                    item.style.display = '';
                });
                alert('Emails refreshed successfully!');
            }
        });
    }
    
    console.log('Gmail sort and filter setup complete');
    
    // Return true to indicate success
    return true;
};

/**
 * Fallback sort function if the main one isn't defined
 */
window.sortEmails = window.sortEmails || function(sortType) {
    console.log('Sorting emails by:', sortType);
    alert('Sorting by ' + sortType);
};

/**
 * Fallback filter function if the main one isn't defined
 */
window.filterEmails = window.filterEmails || function(filterType) {
    console.log('Filtering emails by:', filterType);
    alert('Filtering by ' + filterType);
};

/**
 * Fallback search function if the main one isn't defined
 */
window.searchEmails = window.searchEmails || function(query) {
    console.log('Searching emails for:', query);
    alert('Searching for: ' + query);
};

/**
 * Global handler to ensure we can fix modals from anywhere
 */
window.fixGmailModal = function() {
    cleanupModalBackdrops();
    initializeGmailDropdowns();
    return 'Gmail modal fixed';
};
