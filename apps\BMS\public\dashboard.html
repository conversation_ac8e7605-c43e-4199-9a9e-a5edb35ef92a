<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMS System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <style>
        :root {
            --app-primary-color: #00acc1;
            --app-primary-dark: #007c91;
            --app-primary-light: #5ddef4;
            --app-secondary-color: #26a69a;
            --app-accent-color: #ff5722;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding-top: 56px;
            overflow-x: hidden;
        }

        .app-navbar {
            background-color: var(--app-primary-color) !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .back-to-hub-btn {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            border: none;
        }

        .back-to-hub-btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .app-navbar-bell-btn {
            color: white;
            background-color: transparent;
            border: none;
        }

        .app-navbar-bell-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar {
            position: fixed;
            top: 56px;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 0;
            width: 250px;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #343a40;
            transition: all 0.3s;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            transition: all 0.2s;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: white;
            background-color: var(--app-primary-color);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
        }

        .sidebar-heading {
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.5);
        }

        .main-content {
            margin-left: 250px;
            padding: 2rem;
            transition: all 0.3s;
        }

        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }

        .dashboard-card-primary {
            background-color: var(--app-primary-color);
            color: white;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
        }

        .card-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .card-border-primary {
            border-top: 4px solid var(--app-primary-color);
        }

        .notification-item {
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }

        .notification-item.high {
            border-left-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }

        .notification-item.medium {
            border-left-color: #ffc107;
            background-color: rgba(255, 193, 7, 0.1);
        }

        .notification-item.low {
            border-left-color: #17a2b8;
            background-color: rgba(23, 162, 184, 0.1);
        }

        /* Google Integration Components Styles */
        .google-integration-component {
            border: 1px solid #ddd;
            border-radius: 10px;
            margin-bottom: 20px;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .google-integration-component:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .component-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--app-primary-color);
            color: white;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .component-body {
            padding: 20px;
            min-height: 100px;
            background-color: #fff;
        }

        .component-footer {
            padding: 12px 20px;
            border-top: 1px solid #eee;
            text-align: right;
            background-color: #f8f9fa;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .input-group-max-300 {
            max-width: 300px;
        }

        .max-width-300 {
            max-width: 300px;
        }
    </style>
</head>
<body>
    <!-- Top Navbar -->
    <nav class="navbar navbar-dark fixed-top app-navbar">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <a class="navbar-brand" href="/">BMS System</a>
            </div>
            <div class="d-flex">
                <a href="javascript:void(0);" onclick="window.close(); window.opener.focus();" class="btn me-3 back-to-hub-btn">
                    Back to Hub
                </a>
                <button class="btn position-relative me-2 app-navbar-bell-btn" title="Notifications">
                    <i class="bi bi-bell-fill"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        3
                    </span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="pt-2">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#">
                        <i class="bi bi-speedometer2"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#financial">
                        <i class="bi bi-cash-coin"></i> Financial Management
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#hr">
                        <i class="bi bi-people"></i> Human Resources
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#operations">
                        <i class="bi bi-gear"></i> Operations
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#reports">
                        <i class="bi bi-file-earmark-text"></i> Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#settings">
                        <i class="bi bi-sliders"></i> Settings
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#attachmentsModal">
                        <i class="bi bi-paperclip"></i> Attachments
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                <span>Integrations</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="#google-calendar" data-bs-toggle="modal" data-bs-target="#calendarModal">
                        <i class="bi bi-calendar3"></i> Google Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-drive" data-bs-toggle="modal" data-bs-target="#driveModal">
                        <i class="bi bi-folder"></i> Google Drive
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-docs" data-bs-toggle="modal" data-bs-target="#docsModal">
                        <i class="bi bi-file-earmark-text"></i> Google Docs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-sheets" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                        <i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-maps" data-bs-toggle="modal" data-bs-target="#mapsModal">
                        <i class="bi bi-map"></i> Google Maps
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#gmail" data-bs-toggle="modal" data-bs-target="#gmailModal">
                        <i class="bi bi-envelope"></i> Gmail
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#google-translate" data-bs-toggle="modal" data-bs-target="#translateModal">
                        <i class="bi bi-translate"></i> Google Translate
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0);" onclick="window.close(); window.opener.focus();">
                        <i class="bi bi-box-arrow-left"></i> Back to Hub
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">Business Management Dashboard</h1>
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                </div>
                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle">
                    <i class="bi bi-calendar3"></i> This month
                </button>
            </div>
        </div>

        <!-- AI Insights Card -->
        <div class="card mb-4 card-border-primary">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-2">AI Business Insights</h5>
                    <span class="badge bg-primary">AI Powered</span>
                </div>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="refresh-insights-btn" title="Refresh AI Insights">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                    <div class="dropdown d-inline-block ms-2">
                        <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" id="insightsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="insightsDropdown">
                            <li><a class="dropdown-item" href="#" id="expand-all-insights">Expand All</a></li>
                            <li><a class="dropdown-item" href="#" id="collapse-all-insights">Collapse All</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="export-insights">Export Insights</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="text-muted">Based on your business data, here are some insights and recommendations:</p>
                <div class="row" id="ai-insights-container">
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-graph-up text-primary me-2"></i>
                                    Revenue Insights
                                </h6>
                                <p class="card-text">Revenue is up 15% compared to last quarter. Key growth areas include software services and consulting.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                                    Expense Alert
                                </h6>
                                <p class="card-text">Travel expenses have increased by 23%. Consider reviewing travel policies and implementing cost-saving measures.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-lightbulb text-success me-2"></i>
                                    Opportunity
                                </h6>
                                <p class="card-text">Based on current trends, expanding into the healthcare sector could increase revenue by an estimated 12%.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Load all modals from the original page -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load modals from the original page
            fetch('index.html')
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    
                    // Find all modals
                    const modals = doc.querySelectorAll('.modal');
                    
                    // Append them to the current document
                    modals.forEach(modal => {
                        document.body.appendChild(document.importNode(modal, true));
                    });
                    
                    console.log('Modals loaded successfully');
                    
                    // Load scripts
                    loadScripts();
                })
                .catch(error => {
                    console.error('Error loading modals:', error);
                });
        });
        
        function loadScripts() {
            // Load essential scripts
            const scripts = [
                'js/google-drive-handler.js',
                'js/bms-drive-fix.js',
                'js/file-actions.js'
            ];
            
            scripts.forEach(script => {
                const scriptElement = document.createElement('script');
                scriptElement.src = script;
                document.body.appendChild(scriptElement);
            });
            
            console.log('Scripts loaded successfully');
        }
    </script>
</body>
</html>
