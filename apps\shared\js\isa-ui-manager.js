/**
 * ISA UI Manager - Shared UI component management for ISASUITE applications
 * Used across CRM, BMS, WMS, MRP and other applications
 */

const ISAUIManager = {
    /**
     * Opens a modal by ID with proper error handling
     * @param {string} modalId - The ID of the modal to open
     * @returns {boolean} - Success status
     */
    openModal: function(modalId) {
        console.log(`ISAUIManager: Opening modal ${modalId}`);
        
        try {
            // Check if Bootstrap is available
            if (typeof bootstrap === 'undefined') {
                console.error("Bootstrap not defined. Make sure bootstrap.bundle.min.js is loaded.");
                alert("UI Framework initialization error. Please refresh the page.");
                return false;
            }
            
            // Find modal element
            const modalElement = document.getElementById(modalId);
            if (!modalElement) {
                console.error(`Modal with ID ${modalId} not found in DOM`);
                return false;
            }
            
            // Close any open modals first
            document.querySelectorAll('.modal.show').forEach(modal => {
                const instance = bootstrap.Modal.getInstance(modal);
                if (instance) {
                    console.log(`Closing open modal: ${modal.id}`);
                    instance.hide();
                }
            });
            
            // Remove any stray backdrops
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());
            
            // Reset body state
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            // Create new modal instance
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: false,
                focus: true
            });
            
            // Add events for debugging
            modalElement.addEventListener('shown.bs.modal', function() {
                console.log(`Modal ${modalId} shown successfully`);
            });
            
            // Show the modal
            modal.show();
            
            // Double-check modal is displayed (forcing if needed)
            setTimeout(() => {
                if (!modalElement.classList.contains('show')) {
                    console.warn(`Modal ${modalId} not showing properly, forcing display`);
                    modalElement.classList.add('show');
                    modalElement.style.display = 'block';
                    document.body.classList.add('modal-open');
                    
                    // Create backdrop manually if needed
                    if (!document.querySelector('.modal-backdrop')) {
                        const backdrop = document.createElement('div');
                        backdrop.className = 'modal-backdrop fade show';
                        document.body.appendChild(backdrop);
                    }
                }
            }, 300);
            
            return true;
        } catch (error) {
            console.error(`Error opening modal ${modalId}:`, error);
            return false;
        }
    },
    
    /**
     * Closes a modal by ID
     * @param {string} modalId - The ID of the modal to close
     * @returns {boolean} - Success status
     */
    closeModal: function(modalId) {
        try {
            const modalElement = document.getElementById(modalId);
            if (!modalElement) return false;
            
            const instance = bootstrap.Modal.getInstance(modalElement);
            if (instance) {
                instance.hide();
                return true;
            }
            return false;
        } catch (error) {
            console.error(`Error closing modal ${modalId}:`, error);
            return false;
        }
    },
    
    /**
     * Initialize UI Manager for the application
     * Sets up all modal event handlers and creates necessary mount points
     */
    init: function() {
        console.log("Initializing ISA UI Manager");
        
        // Create global modal mount point if it doesn't exist
        if (!document.getElementById('modal-mount-point')) {
            const mountPoint = document.createElement('div');
            mountPoint.id = 'modal-mount-point';
            document.body.appendChild(mountPoint);
        }
        
        // Ensure all system modals are properly mounted
        const modalMountPoint = document.getElementById('modal-mount-point');
        document.querySelectorAll('.modal').forEach(modal => {
            console.log(`Processing modal: ${modal.id}`);
            // Move modal to mount point to avoid z-index issues
            modalMountPoint.appendChild(modal);
        });
        
        // Set up system event handlers
        document.addEventListener('click', function(event) {
            // Handle attachments links
            if (event.target.matches('[data-isa-action="open-attachments"]') || 
                (event.target.parentElement && event.target.parentElement.matches('[data-isa-action="open-attachments"]'))) {
                event.preventDefault();
                ISAUIManager.openModal('attachmentsModal');
            }
            
            // Handle maps links
            if (event.target.matches('[data-isa-action="open-maps"]') || 
                (event.target.parentElement && event.target.parentElement.matches('[data-isa-action="open-maps"]'))) {
                event.preventDefault();
                ISAUIManager.openModal('mapsModal');
            }
        });
        
        // Special handler for older onclick links (for backward compatibility)
        window.openAttachmentsModal = function() {
            return ISAUIManager.openModal('attachmentsModal');
        };
        
        window.openMapsModal = function() {
            return ISAUIManager.openModal('mapsModal');
        };
    }
};

// Initialize when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    ISAUIManager.init();
});
