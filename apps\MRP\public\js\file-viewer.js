/**
 * File Viewer for MRP Application
 * Handles display of file contents and folder contents with proper action buttons
 */

function getGoogleItemPreviewContent(app, type, itemId) {
    const readableName = itemId.replace(/_/g, ' ');
    
    if (app === 'drive' && type === 'folder') {
        // Generate folder contents with action buttons
        return `
            <div class="text-center mb-4">
                <i class="bi bi-folder-fill text-primary" style="font-size: 48px;"></i>
                <h4 class="mt-3">${readableName}</h4>
            </div>
            <div class="list-group">
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-file-earmark-pdf text-danger me-3 fs-4"></i>
                        <div>
                            <h6 class="mb-1">Document 1.pdf</h6>
                            <small>1.2 MB - Last updated: Yesterday</small>
                        </div>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('drive', 'pdf', 'Document_1.pdf')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('drive', 'pdf', 'Document_1.pdf')">
                            <i class="bi bi-download"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('drive', 'pdf', 'Document_1.pdf')">
                            <i class="bi bi-share"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('drive', 'pdf', 'Document_1.pdf')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-file-earmark-text text-primary me-3 fs-4"></i>
                        <div>
                            <h6 class="mb-1">Document 2.docx</h6>
                            <small>0.8 MB - Last updated: 2 days ago</small>
                        </div>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('docs', 'document', 'Document_2.docx')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('docs', 'document', 'Document_2.docx')">
                            <i class="bi bi-download"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('docs', 'document', 'Document_2.docx')">
                            <i class="bi bi-share"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('docs', 'document', 'Document_2.docx')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-4"></i>
                        <div>
                            <h6 class="mb-1">Spreadsheet.xlsx</h6>
                            <small>1.5 MB - Last updated: 3 days ago</small>
                        </div>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewGoogleItem('sheets', 'spreadsheet', 'Spreadsheet.xlsx')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="downloadGoogleItem('sheets', 'spreadsheet', 'Spreadsheet.xlsx')">
                            <i class="bi bi-download"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="shareGoogleItem('sheets', 'spreadsheet', 'Spreadsheet.xlsx')">
                            <i class="bi bi-share"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteGoogleItem('sheets', 'spreadsheet', 'Spreadsheet.xlsx')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Other content types will remain the same
    // ... existing code ...
}

// Add this script to the main index.html
document.addEventListener('DOMContentLoaded', function() {
    // Make sure the folder view displays proper action buttons for contained files
    window.viewGoogleFolder = function(folderId) {
        viewGoogleItem('drive', 'folder', folderId);
    };
});
