name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Lint code
        run: npm run lint

      - name: Run tests
        run: npm test

      - name: Build web app
        run: npm run build

      - name: Upload web build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: web-build-artifacts
          path: build/

  build-electron:
    needs: test
    runs-on: windows-latest

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build Electron app
        run: npm run electron-pack

      - name: Upload Electron build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: electron-build-artifacts
          path: dist/

  deploy-staging:
    needs: [test, build-electron]
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Download web build artifacts
        uses: actions/download-artifact@v3
        with:
          name: web-build-artifacts
          path: build/

      - name: Download Electron build artifacts
        uses: actions/download-artifact@v3
        with:
          name: electron-build-artifacts
          path: electron-dist/

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment"
          # Add your deployment script here
          # For example, using AWS CLI to deploy to S3 or Elastic Beanstalk
          # or using SSH to deploy to a server

  deploy-production:
    needs: [test, build-electron]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Download web build artifacts
        uses: actions/download-artifact@v3
        with:
          name: web-build-artifacts
          path: build/

      - name: Download Electron build artifacts
        uses: actions/download-artifact@v3
        with:
          name: electron-build-artifacts
          path: electron-dist/

      - name: Deploy to production
        run: |
          echo "Deploying to production environment"
          # Add your production deployment script here
          # This would typically involve more safeguards and possibly manual approval
