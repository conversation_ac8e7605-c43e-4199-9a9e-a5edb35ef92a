const http = require('http');
const fs = require('fs');
const path = require('path');

// Simple static file server
function serveStaticFile(res, filePath, contentType) {
  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404);
      res.end('File not found');
      return;
    }

    res.writeHead(200, { 'Content-Type': contentType });
    res.end(data);
  });
}

let customers = [
  {
    id: 1,
    name: 'Acme Corp',
    status: 'Active',
    lastContact: '2024-05-01',
    value: 50000,
    interactions: 12,
  },
  {
    id: 2,
    name: 'Globex Inc',
    status: 'Prospect',
    lastContact: '2024-04-15',
    value: 25000,
    interactions: 5,
  },
  {
    id: 3,
    name: 'Initech',
    status: 'Active',
    lastContact: '2024-05-05',
    value: 75000,
    interactions: 18,
  },
];

let interactions = [
  {
    id: 1,
    customer: 'Acme Corp',
    type: 'Meeting',
    date: '2024-05-01',
    outcome: 'Positive',
    nextAction: 'Follow-up call',
  },
  {
    id: 2,
    customer: 'Globex Inc',
    type: 'Email',
    date: '2024-04-15',
    outcome: 'Pending',
    nextAction: 'Schedule demo',
  },
  {
    id: 3,
    customer: 'Initech',
    type: 'Call',
    date: '2024-05-05',
    outcome: 'Positive',
    nextAction: 'Send proposal',
  },
];

const server = http.createServer((req, res) => {
  // Parse the URL
  const url = new URL(req.url, `http://${req.headers.host}`);
  const pathname = url.pathname;

  // Serve static files
  if (pathname.startsWith('/js/')) {
    const filePath = path.join(__dirname, 'public', pathname);
    serveStaticFile(res, filePath, 'text/javascript');
    return;
  }

  // Serve shared files
  if (pathname.startsWith('/shared/')) {
    const sharedPath = pathname.replace('/shared/', '');
    const filePath = path.join(__dirname, '..', '..', 'shared', sharedPath);

    // Determine content type based on file extension
    const ext = path.extname(filePath);
    let contentType = 'text/plain';

    switch (ext) {
      case '.js':
        contentType = 'text/javascript';
        break;
      case '.css':
        contentType = 'text/css';
        break;
      case '.json':
        contentType = 'application/json';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.jpg':
        contentType = 'image/jpg';
        break;
      case '.html':
        contentType = 'text/html';
        break;
    }

    serveStaticFile(res, filePath, contentType);
    return;
  }

  // Serve index.html for root path
  if (pathname === '/') {
    const filePath = path.join(__dirname, 'public', 'index.html');
    if (fs.existsSync(filePath)) {
      serveStaticFile(res, filePath, 'text/html');
      return;
    }

    // If index.html doesn't exist, serve the default HTML
    const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>CRM Dashboard</title>
                <style>
                    body { font-family: Arial; margin: 20px; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .dashboard-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }
                    .card {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        padding: 20px;
                        margin-bottom: 20px;
                    }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
                    th { background: #f4f4f4; }
                    .actions { margin: 20px 0; }
                    button { padding: 10px; margin: 5px; cursor: pointer; }
                    .status {
                        padding: 5px 10px;
                        border-radius: 3px;
                        color: white;
                        font-weight: bold;
                    }
                    .active { background: #28a745; }
                    .prospect { background: #ffc107; }
                    .positive { background: #28a745; }
                    .pending { background: #ffc107; }
                    .metrics {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 20px;
                        margin-bottom: 20px;
                    }
                    .metric-card {
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 8px;
                        text-align: center;
                    }
                    .metric-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: #007bff;
                    }
                    .value {
                        font-weight: bold;
                        color: #28a745;
                    }
                    .interaction-type {
                        background: #f8f9fa;
                        padding: 3px 8px;
                        border-radius: 3px;
                        font-size: 0.9em;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Customer Relationship Management</h1>
                    <div class="actions">
                        <button onclick="window.location.href='http://localhost:3000'">Back to Hub</button>
                    </div>

                    <div class="metrics">
                        <div class="metric-card">
                            <div class="metric-value">3</div>
                            <div>Active Customers</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">$150,000</div>
                            <div>Total Value</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">35</div>
                            <div>Total Interactions</div>
                        </div>
                    </div>

                    <div class="dashboard-grid">
                        <div class="card">
                            <h2>Customer Overview</h2>
                            <table>
                                <tr>
                                    <th>Customer</th>
                                    <th>Status</th>
                                    <th>Last Contact</th>
                                    <th>Value</th>
                                    <th>Interactions</th>
                                </tr>
                                ${customers
                                  .map(
                                    (customer) => `
                                    <tr>
                                        <td>${customer.name}</td>
                                        <td>
                                            <span class="status ${customer.status.toLowerCase()}">
                                                ${customer.status}
                                            </span>
                                        </td>
                                        <td>${customer.lastContact}</td>
                                        <td class="value">$${customer.value.toLocaleString()}</td>
                                        <td>${customer.interactions}</td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>

                        <div class="card">
                            <h2>Recent Interactions</h2>
                            <table>
                                <tr>
                                    <th>Customer</th>
                                    <th>Type</th>
                                    <th>Date</th>
                                    <th>Outcome</th>
                                    <th>Next Action</th>
                                </tr>
                                ${interactions
                                  .map(
                                    (interaction) => `
                                    <tr>
                                        <td>${interaction.customer}</td>
                                        <td>
                                            <span class="interaction-type">
                                                ${interaction.type}
                                            </span>
                                        </td>
                                        <td>${interaction.date}</td>
                                        <td>
                                            <span class="status ${interaction.outcome.toLowerCase()}">
                                                ${interaction.outcome}
                                            </span>
                                        </td>
                                        <td>${interaction.nextAction}</td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `;
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
    return;
  }

  // API endpoints
  if (pathname === '/api/customers') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(customers));
    return;
  }

  if (pathname === '/api/interactions') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(interactions));
    return;
  }

  // 404 for everything else
  res.writeHead(404);
  res.end('Not found');
});

server.listen(3003, () => {
  console.log('Customer Relationship Management System running on http://localhost:3003');
  console.log('Shared features loaded successfully');
});
