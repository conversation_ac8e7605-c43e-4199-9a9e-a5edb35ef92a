version: '3.8'

services:
  hub:
    build:
      context: ../../apps/hub
      dockerfile: ../../infrastructure/docker/hub/Dockerfile
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
    networks:
      - isa-network

  bms:
    build:
      context: ../../apps/bms
      dockerfile: ../../infrastructure/docker/bms/Dockerfile
    ports:
      - '3001:3000'
    environment:
      - NODE_ENV=production
    networks:
      - isa-network

  crm:
    build:
      context: ../../apps/crm
      dockerfile: ../../infrastructure/docker/crm/Dockerfile
    ports:
      - '3002:3000'
    environment:
      - NODE_ENV=production
    networks:
      - isa-network

  pms:
    build:
      context: ../../apps/pms
      dockerfile: ../../infrastructure/docker/pms/Dockerfile
    ports:
      - '3003:3000'
    environment:
      - NODE_ENV=production
    networks:
      - isa-network

  tms:
    build:
      context: ../../apps/tms
      dockerfile: ../../infrastructure/docker/tms/Dockerfile
    ports:
      - '3004:3000'
    environment:
      - NODE_ENV=production
    networks:
      - isa-network

  scm:
    build:
      context: ../../apps/scm
      dockerfile: ../../infrastructure/docker/scm/Dockerfile
    ports:
      - '3005:3000'
    environment:
      - NODE_ENV=production
    networks:
      - isa-network

  pdm:
    build:
      context: ../../apps/pdm
      dockerfile: ../../infrastructure/docker/pdm/Dockerfile
    ports:
      - '3006:3000'
    environment:
      - NODE_ENV=production
    networks:
      - isa-network

  bi:
    build:
      context: ../../apps/bi
      dockerfile: ../../infrastructure/docker/bi/Dockerfile
    ports:
      - '3007:3000'
    environment:
      - NODE_ENV=production
    networks:
      - isa-network

networks:
  isa-network:
    driver: bridge
