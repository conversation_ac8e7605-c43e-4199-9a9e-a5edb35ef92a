// GMAIL DROPDOWN FORCE FIX - Copy/Paste into Browser Console
// Run this at http://localhost:3002 after opening Gmail modal

console.clear();
console.log('🔧 GMAIL DROPDOWN FORCE FIX');
console.log('===========================');

function forceFixGmailDropdowns() {
    // Step 1: Ensure modal is open
    const modal = document.getElementById('mrp-gmailModal');
    if (!modal) {
        console.log('❌ Gmail modal not found');
        return false;
    }
    
    if (!modal.classList.contains('show')) {
        console.log('📧 Opening Gmail modal...');
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
        
        setTimeout(() => {
            console.log('⏰ Modal opened, run forceFixGmailDropdowns() again');
        }, 1000);
        return false;
    }
    
    console.log('✅ Gmail modal is open');
    
    // Step 2: Get dropdown elements
    const sortBtn = document.getElementById('sort-dropdown');
    const filterBtn = document.getElementById('filter-dropdown');
    
    if (!sortBtn || !filterBtn) {
        console.log('❌ Dropdown buttons not found');
        return false;
    }
    
    console.log('✅ Found dropdown buttons');
    
    // Step 3: Clean up existing instances and conflicting attributes
    console.log('🧹 Cleaning up existing dropdown instances...');
    
    [sortBtn, filterBtn].forEach(btn => {
        // Remove any existing Bootstrap dropdown instance
        try {
            const existingInstance = bootstrap.Dropdown.getInstance(btn);
            if (existingInstance) {
                existingInstance.dispose();
                console.log(`✅ Disposed existing instance for ${btn.id}`);
            }
        } catch (e) {
            console.log(`⚠️ No existing instance for ${btn.id}`);
        }
        
        // Remove conflicting onclick handlers
        btn.removeAttribute('onclick');
        
        // Ensure proper Bootstrap attributes
        btn.setAttribute('data-bs-toggle', 'dropdown');
        btn.setAttribute('aria-expanded', 'false');
    });
    
    // Step 4: Create fresh Bootstrap dropdown instances
    console.log('🆕 Creating fresh dropdown instances...');
    
    try {
        const sortDropdown = new bootstrap.Dropdown(sortBtn, {
            autoClose: true,
            boundary: modal
        });
        
        const filterDropdown = new bootstrap.Dropdown(filterBtn, {
            autoClose: true,
            boundary: modal
        });
        
        console.log('✅ Bootstrap dropdowns created successfully');
        
        // Step 5: Add click event listeners with debugging
        sortBtn.addEventListener('click', function(e) {
            console.log('🖱️ Sort dropdown clicked');
        });
        
        filterBtn.addEventListener('click', function(e) {
            console.log('🖱️ Filter dropdown clicked');
        });
        
        // Step 6: Test the dropdowns
        console.log('🧪 Testing dropdowns...');
        
        // Test sort dropdown
        setTimeout(() => {
            console.log('Testing sort dropdown...');
            sortBtn.click();
            
            setTimeout(() => {
                const sortMenu = sortBtn.nextElementSibling;
                const sortIsOpen = sortMenu && sortMenu.classList.contains('show');
                console.log('Sort dropdown test:', sortIsOpen ? '✅ WORKING' : '❌ FAILED');
                
                if (sortIsOpen) {
                    // Close it
                    sortBtn.click();
                }
                
                // Test filter dropdown
                setTimeout(() => {
                    console.log('Testing filter dropdown...');
                    filterBtn.click();
                    
                    setTimeout(() => {
                        const filterMenu = filterBtn.nextElementSibling;
                        const filterIsOpen = filterMenu && filterMenu.classList.contains('show');
                        console.log('Filter dropdown test:', filterIsOpen ? '✅ WORKING' : '❌ FAILED');
                        
                        if (filterIsOpen) {
                            // Close it
                            filterBtn.click();
                        }
                        
                        // Final result
                        const bothWorking = sortIsOpen && filterIsOpen;
                        console.log('\n🎯 FINAL RESULT:', bothWorking ? '✅ DROPDOWNS FIXED!' : '❌ STILL HAVE ISSUES');
                        
                        if (bothWorking) {
                            console.log('🎉 You can now use the sort and filter dropdowns normally!');
                        } else {
                            console.log('🔧 Try manual fallback by running: setupManualDropdowns()');
                        }
                        
                    }, 300);
                }, 500);
            }, 300);
        }, 500);
        
        return true;
        
    } catch (error) {
        console.log('❌ Error creating Bootstrap dropdowns:', error);
        return false;
    }
}

// Manual fallback function
function setupManualDropdowns() {
    console.log('🔧 Setting up manual dropdown fallback...');
    
    const modal = document.getElementById('mrp-gmailModal');
    const sortBtn = document.getElementById('sort-dropdown');
    const filterBtn = document.getElementById('filter-dropdown');
    
    if (!modal || !sortBtn || !filterBtn) {
        console.log('❌ Required elements not found');
        return;
    }
    
    // Remove Bootstrap attributes to avoid conflicts
    [sortBtn, filterBtn].forEach(btn => {
        btn.removeAttribute('data-bs-toggle');
        btn.removeAttribute('aria-expanded');
    });
    
    // Manual click handlers
    sortBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('📊 Manual sort dropdown clicked');
        
        const menu = this.nextElementSibling;
        const isOpen = menu.classList.contains('show');
        
        // Close all dropdowns first
        modal.querySelectorAll('.dropdown-menu.show').forEach(m => {
            m.classList.remove('show');
        });
        
        // Toggle this one
        if (!isOpen) {
            menu.classList.add('show');
            console.log('✅ Sort dropdown opened manually');
        }
    });
    
    filterBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('🔍 Manual filter dropdown clicked');
        
        const menu = this.nextElementSibling;
        const isOpen = menu.classList.contains('show');
        
        // Close all dropdowns first
        modal.querySelectorAll('.dropdown-menu.show').forEach(m => {
            m.classList.remove('show');
        });
        
        // Toggle this one
        if (!isOpen) {
            menu.classList.add('show');
            console.log('✅ Filter dropdown opened manually');
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            modal.querySelectorAll('.dropdown-menu.show').forEach(m => {
                m.classList.remove('show');
            });
        }
    });
    
    console.log('✅ Manual dropdown handlers set up. Try clicking the dropdowns now!');
}

// Make functions globally available
window.forceFixGmailDropdowns = forceFixGmailDropdowns;
window.setupManualDropdowns = setupManualDropdowns;

// Auto-run the fix
console.log('🚀 Starting automatic fix...');
forceFixGmailDropdowns();
