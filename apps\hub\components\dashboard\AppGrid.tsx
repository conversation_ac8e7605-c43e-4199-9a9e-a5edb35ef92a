import { useState, useEffect } from 'react';
import Link from 'next/link';

interface App {
  name: string;
  url: string;
  description: string;
  status: 'online' | 'offline' | 'maintenance';
}

export default function AppGrid() {
  const [apps, setApps] = useState<App[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchApps = async () => {
      try {
        const response = await fetch('/api/apps');
        const data = await response.json();
        setApps(data);
      } catch (error) {
        console.error('Error fetching apps:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchApps();
  }, []);

  if (loading) {
    return <div>Loading applications...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {apps.map((app) => (
        <Link key={app.name} href={app.url} passHref>
          <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">{app.name}</h2>
              <span
                className={`px-2 py-1 rounded-full text-xs ${
                  app.status === 'online'
                    ? 'bg-green-100 text-green-800'
                    : app.status === 'offline'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                }`}
              >
                {app.status}
              </span>
            </div>
            <p className="text-gray-600">{app.description}</p>
          </div>
        </Link>
      ))}
    </div>
  );
}
