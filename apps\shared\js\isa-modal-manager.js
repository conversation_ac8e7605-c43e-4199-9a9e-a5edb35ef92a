/**
 * ISA Modal Manager
 * Central manager for modal dialogs across ISASUITE applications
 */
class ISAModalManager {
    constructor() {
        this.activeModals = new Map();
        this.modalMountPoint = null;
        this.debug = true; // Set to false in production
    }

    /**
     * Initialize the modal manager
     */
    init() {
        this.log("Initializing ISA Modal Manager");
        this.createModalMountPoint();
        this.setupEventListeners();
        this.moveModalsToMountPoint();
    }

    /**
     * Create a mount point for modals if it doesn't exist
     */
    createModalMountPoint() {
        if (!document.getElementById('isa-modal-mount')) {
            this.log("Creating modal mount point");
            this.modalMountPoint = document.createElement('div');
            this.modalMountPoint.id = 'isa-modal-mount';
            document.body.appendChild(this.modalMountPoint);
        } else {
            this.modalMountPoint = document.getElementById('isa-modal-mount');
        }
    }

    /**
     * Move all modals to the mount point
     */
    moveModalsToMountPoint() {
        if (!this.modalMountPoint) return;

        const modals = document.querySelectorAll('.modal');
        this.log(`Found ${modals.length} modals to move to mount point`);
        
        modals.forEach(modal => {
            const modalId = modal.id;
            if (modalId) {
                this.log(`Moving modal ${modalId} to mount point`);
                this.modalMountPoint.appendChild(modal);
            }
        });
    }

    /**
     * Set up global event listeners for modals
     */
    setupEventListeners() {
        document.addEventListener('click', (event) => {
            // Handle data-isa-action attributes
            const target = event.target.closest('[data-isa-action]');
            if (target) {
                const action = target.getAttribute('data-isa-action');
                this.log(`Modal action triggered: ${action}`);
                
                if (action === 'open-maps') {
                    event.preventDefault();
                    this.openModal('mapsModal');
                } else if (action === 'open-warehouse-maps') {
                    event.preventDefault();
                    this.openModal('warehouseMapsModal');
                } else if (action === 'open-attachments') {
                    event.preventDefault();
                    this.openModal('attachmentsModal');
                }
            }
        });

        // Register modal events for debugging
        document.querySelectorAll('.modal').forEach(modal => {
            ['show', 'shown', 'hide', 'hidden'].forEach(eventName => {
                modal.addEventListener(`${eventName}.bs.modal`, (event) => {
                    this.log(`Modal ${modal.id} - ${eventName} event`);
                });
            });
        });
    }

    /**
     * Open a modal by ID
     * @param {string} modalId - ID of the modal to open
     * @returns {boolean} Success status
     */
    openModal(modalId) {
        this.log(`Opening modal: ${modalId}`);
        
        try {
            // Check if Bootstrap is available
            if (typeof bootstrap === 'undefined') {
                console.error("Bootstrap not defined. Make sure bootstrap.bundle.min.js is loaded.");
                alert("UI Framework initialization error. Please refresh the page.");
                return false;
            }

            const modalEl = document.getElementById(modalId);
            if (!modalEl) {
                console.error(`Modal element with ID ${modalId} not found`);
                return false;
            }

            // Close any open modals
            document.querySelectorAll('.modal.show').forEach(openModal => {
                const instance = bootstrap.Modal.getInstance(openModal);
                if (instance) {
                    this.log(`Closing open modal: ${openModal.id}`);
                    instance.hide();
                }
            });

            // Clean up any leftover backdrops
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });

            // Reset body state
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // Create a fresh modal instance
            const modalInstance = new bootstrap.Modal(modalEl, {
                backdrop: 'static',
                keyboard: false,
                focus: true
            });

            // Show the modal
            modalInstance.show();

            // Store the active modal
            this.activeModals.set(modalId, modalInstance);

            // Double-check modal is displayed (with a delay to catch race conditions)
            setTimeout(() => {
                if (!modalEl.classList.contains('show')) {
                    this.log(`Modal ${modalId} not showing properly, forcing display`);
                    modalEl.classList.add('show');
                    modalEl.style.display = 'block';
                    document.body.classList.add('modal-open');
                    
                    // Create backdrop manually if needed
                    if (!document.querySelector('.modal-backdrop')) {
                        const backdrop = document.createElement('div');
                        backdrop.className = 'modal-backdrop fade show';
                        document.body.appendChild(backdrop);
                    }
                }
            }, 300);

            return true;
        } catch (error) {
            console.error(`Error opening modal ${modalId}:`, error);
            return false;
        }
    }

    /**
     * Close a modal by ID
     * @param {string} modalId - ID of the modal to close
     * @returns {boolean} Success status
     */
    closeModal(modalId) {
        this.log(`Closing modal: ${modalId}`);
        
        try {
            const modalEl = document.getElementById(modalId);
            if (!modalEl) {
                console.error(`Modal element with ID ${modalId} not found`);
                return false;
            }

            const modalInstance = bootstrap.Modal.getInstance(modalEl);
            if (modalInstance) {
                modalInstance.hide();
                this.activeModals.delete(modalId);
                return true;
            } else {
                console.warn(`No Bootstrap modal instance found for ${modalId}`);
                return false;
            }
        } catch (error) {
            console.error(`Error closing modal ${modalId}:`, error);
            return false;
        }
    }

    /**
     * Logger function with debug mode check
     * @param {string} message - Message to log
     */
    log(message) {
        if (this.debug) {
            console.log(`ISAModalManager: ${message}`);
        }
    }
}

// Create a singleton instance
const isaModalManager = new ISAModalManager();

// Initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', () => {
    isaModalManager.init();
});

// Global access methods (for backwards compatibility)
window.openWarehouseMapsModal = function() {
    return isaModalManager.openModal('warehouseMapsModal');
}

window.openMapsModal = function() {
    return isaModalManager.openModal('mapsModal');
}

window.openAttachmentsModal = function() {
    return isaModalManager.openModal('attachmentsModal');
}
