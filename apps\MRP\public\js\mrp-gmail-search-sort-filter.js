/**
 * MRP Gmail Search, Sort, and Filter Functionality
 * Implements BMS-style search, sort, and filter for the MRP Gmail modal
 */

// Configuration for MRP Gmail
const MRP_GMAIL_LABELS = [
    { id: 'important', name: 'Important', color: '#dc3545' },
    { id: 'production', name: 'Production', color: '#28a745' },
    { id: 'inventory', name: 'Inventory', color: '#17a2b8' },
    { id: 'suppliers', name: 'Suppliers', color: '#fd7e14' }
];

// Initialize when the document is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('MRP Gmail Search, Sort, and Filter functionality loaded');

    // Add the search, sort, and filter controls when the Gmail modal is opened
    const openGmailBtn = document.getElementById('mrp-open-gmail-btn');
    if (openGmailBtn) {
        console.log('Found Open Gmail button, adding event listener');

        openGmailBtn.addEventListener('click', function() {
            console.log('Open Gmail button clicked');
            // Enhance immediately without delay
            enhanceMrpGmailInbox();
        });
    } else {
        console.warn('Open Gmail button not found, trying alternative approach');

        // Alternative approach: Use MutationObserver for immediate detection
        const modalObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const gmailModal = node.querySelector && (
                            node.querySelector('#mrp-gmail-modal') ||
                            node.querySelector('#enhanced-gmail-modal') ||
                            node.querySelector('.modal.show')
                        ) || (node.id === 'mrp-gmail-modal' || 
                             node.id === 'enhanced-gmail-modal' || 
                             node.classList && node.classList.contains('modal'));

                        if (gmailModal) {
                            console.log('Gmail modal detected, enhancing inbox');
                            enhanceMrpGmailInbox();
                            modalObserver.disconnect();
                        }
                    }
                });
            });
        });

        // Start observing
        modalObserver.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Stop observing after 10 seconds to avoid infinite monitoring
        setTimeout(function() {
            modalObserver.disconnect();
        }, 10000);
    }

    // Add a global function that can be called from other scripts
    window.enhanceMrpGmailInbox = enhanceMrpGmailInbox;
});

/**
 * Enhance the MRP Gmail inbox with search, sort, and filter controls
 */
function enhanceMrpGmailInbox() {
    console.log('Enhancing MRP Gmail inbox with search, sort, and filter controls');

    // Find the inbox content - try both IDs since there might be different implementations
    let inboxContent = document.getElementById('mrp-inbox-content');
    if (!inboxContent) {
        inboxContent = document.querySelector('.tab-pane.active');
        console.log('Using active tab pane as inbox content');
    }

    if (!inboxContent) {
        console.error('MRP Gmail inbox content not found');
        // Try again after a short delay
        setTimeout(enhanceMrpGmailInbox, 500);
        return;
    }

    // Check if controls already exist
    if (inboxContent.querySelector('.mrp-gmail-controls')) {
        console.log('MRP Gmail controls already exist');
        return;
    }

    // Create the controls HTML
    const controlsHTML = `
    <div class="d-flex justify-content-between mb-3 mrp-gmail-controls">
        <div class="input-group" style="max-width: 300px;">
            <input type="text" class="form-control" id="mrp-gmail-search" placeholder="Search emails" aria-label="Search emails">
            <button class="btn btn-outline-secondary" id="mrp-gmail-search-btn" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
        <div>
            <div class="dropdown d-inline-block me-2">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="mrpGmailSortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-sort-down"></i> Sort
                </button>
                <ul class="dropdown-menu" aria-labelledby="mrpGmailSortDropdown">
                    <li><a class="dropdown-item mrp-gmail-sort-option" href="#" data-sort="date-desc">Newest first</a></li>
                    <li><a class="dropdown-item mrp-gmail-sort-option" href="#" data-sort="date-asc">Oldest first</a></li>
                    <li><a class="dropdown-item mrp-gmail-sort-option" href="#" data-sort="sender-asc">Sender A-Z</a></li>
                    <li><a class="dropdown-item mrp-gmail-sort-option" href="#" data-sort="sender-desc">Sender Z-A</a></li>
                    <li><a class="dropdown-item mrp-gmail-sort-option" href="#" data-sort="subject-asc">Subject A-Z</a></li>
                    <li><a class="dropdown-item mrp-gmail-sort-option" href="#" data-sort="subject-desc">Subject Z-A</a></li>
                </ul>
            </div>
            <div class="dropdown d-inline-block me-2">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="mrpGmailFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-funnel"></i> Filter
                </button>
                <ul class="dropdown-menu" aria-labelledby="mrpGmailFilterDropdown">
                    <li><a class="dropdown-item mrp-gmail-filter-option" href="#" data-filter="all">All emails</a></li>
                    <li><a class="dropdown-item mrp-gmail-filter-option" href="#" data-filter="unread">Unread</a></li>
                    <li><a class="dropdown-item mrp-gmail-filter-option" href="#" data-filter="read">Read</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header">By Label</h6></li>
                    ${MRP_GMAIL_LABELS.map(label => `
                        <li><a class="dropdown-item mrp-gmail-filter-option" href="#" data-filter="label-${label.id}">
                            <span class="badge" style="background-color: ${label.color};">${label.name}</span>
                        </a></li>
                    `).join('')}
                </ul>
            </div>
            <button class="btn btn-outline-secondary" id="mrp-gmail-refresh-btn">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
    </div>
    `;

    // Insert the controls at the top of the inbox content
    inboxContent.insertAdjacentHTML('afterbegin', controlsHTML);

    // Add data attributes to email items for sorting and filtering
    addDataAttributesToEmails();

    // Set up event listeners for the controls
    setupMrpGmailControlEventListeners();
}

/**
 * Add data attributes to email items for sorting and filtering
 */
function addDataAttributesToEmails() {
    const emailItems = document.querySelectorAll('.mrp-email-item');

    emailItems.forEach((item, index) => {
        // Add date attribute if it doesn't exist
        if (!item.hasAttribute('data-date')) {
            // Create dates that decrease as index increases (newest first)
            const date = new Date();
            date.setMinutes(date.getMinutes() - (index * 30)); // 30 minutes between emails
            item.setAttribute('data-date', date.toISOString());
        }

        // Add label attribute if it doesn't exist
        if (!item.hasAttribute('data-label')) {
            // Assign random labels
            const randomLabel = MRP_GMAIL_LABELS[Math.floor(Math.random() * MRP_GMAIL_LABELS.length)].id;
            item.setAttribute('data-label', randomLabel);

            // Add label badge if it doesn't exist
            if (!item.querySelector('.badge')) {
                const labelObj = MRP_GMAIL_LABELS.find(l => l.id === randomLabel);
                const labelBadge = document.createElement('span');
                labelBadge.className = 'badge ms-2';
                labelBadge.style.backgroundColor = labelObj.color;
                labelBadge.textContent = labelObj.name;

                const titleElement = item.querySelector('h6');
                if (titleElement) {
                    titleElement.appendChild(labelBadge);
                }
            }
        }

        // Add read/unread status if not already set
        if (!item.classList.contains('unread') && !item.classList.contains('read')) {
            // Make some emails unread
            if (index % 3 === 0) {
                item.classList.add('unread');
            } else {
                item.classList.add('read');
            }
        }
    });
}

/**
 * Set up event listeners for the MRP Gmail controls
 */
function setupMrpGmailControlEventListeners() {
    console.log('Setting up MRP Gmail control event listeners');

    // Search functionality
    const searchInput = document.getElementById('mrp-gmail-search');
    const searchBtn = document.getElementById('mrp-gmail-search-btn');

    if (searchInput && searchBtn) {
        // Remove previous event listeners if any
        const newSearchBtn = searchBtn.cloneNode(true);
        const newSearchInput = searchInput.cloneNode(true);
        searchBtn.parentNode.replaceChild(newSearchBtn, searchBtn);
        searchInput.parentNode.replaceChild(newSearchInput, searchInput);
        // Search on button click
        newSearchBtn.addEventListener('click', function() {
            searchMrpGmailEmails(newSearchInput.value);
        });
        // Search on Enter key
        newSearchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                searchMrpGmailEmails(this.value);
            }
        });
    } else {
        console.warn('Search input or button not found', { searchInput, searchBtn });
    }

    // Sort functionality
    const sortOptions = document.querySelectorAll('.mrp-gmail-sort-option');
    console.log(`Found ${sortOptions.length} sort options`);

    sortOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const sortType = this.getAttribute('data-sort');
            console.log('Sort option clicked:', sortType);
            sortMrpGmailEmails(sortType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('mrpGmailSortDropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-sort-down"></i> ${this.textContent}`;
            } else {
                console.warn('Sort dropdown button not found');
            }
        });
    });

    // Filter functionality
    const filterOptions = document.querySelectorAll('.mrp-gmail-filter-option');
    console.log(`Found ${filterOptions.length} filter options`);

    filterOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const filterType = this.getAttribute('data-filter');
            console.log('Filter option clicked:', filterType);
            filterMrpGmailEmails(filterType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('mrpGmailFilterDropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-funnel"></i> ${this.textContent.trim()}`;
            } else {
                console.warn('Filter dropdown button not found');
            }
        });
    });

    // Refresh button
    const refreshBtn = document.getElementById('mrp-gmail-refresh-btn');
    if (refreshBtn) {
        console.log('Found refresh button');

        refreshBtn.addEventListener('click', function() {
            console.log('Refresh button clicked');

            // Reset search input
            if (searchInput) {
                searchInput.value = '';
            }

            // Reset sort dropdown
            const sortDropdown = document.getElementById('mrpGmailSortDropdown');
            if (sortDropdown) {
                sortDropdown.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
            } else {
                console.warn('Sort dropdown button not found for reset');
            }

            // Reset filter dropdown
            const filterDropdown = document.getElementById('mrpGmailFilterDropdown');
            if (filterDropdown) {
                filterDropdown.innerHTML = '<i class="bi bi-funnel"></i> Filter';
            } else {
                console.warn('Filter dropdown button not found for reset');
            }

            // Show all emails
            showAllMrpGmailEmails();

            // Show success message
            showToast('Emails refreshed successfully');
        });
    } else {
        console.warn('Refresh button not found');
    }
}

/**
 * Search MRP Gmail emails by query
 * @param {string} query - The search query
 */
function searchMrpGmailEmails(query) {
    if (!query) {
        showAllMrpGmailEmails();
        return;
    }

    const emailList = document.getElementById('mrp-email-list');
    if (!emailList) return;

    const emailItems = emailList.querySelectorAll('.mrp-email-item');
    if (!emailItems.length) return;

    // Normalize query
    const normalizedQuery = query.toLowerCase().trim();

    // Show/hide based on search query
    emailItems.forEach(item => {
        const sender = item.getAttribute('data-sender') || '';
        const subject = item.getAttribute('data-subject') || '';
        const content = item.querySelector('small.text-muted')?.textContent.toLowerCase() || '';

        if (sender.includes(normalizedQuery) ||
            subject.toLowerCase().includes(normalizedQuery) ||
            content.includes(normalizedQuery)) {
            item.style.display = '';

            // Highlight matching text
            highlightMrpGmailText(item, normalizedQuery);
        } else {
            item.style.display = 'none';
        }
    });

    // Show toast with results count
    const visibleCount = Array.from(emailItems).filter(item => item.style.display !== 'none').length;
    showToast(`Found ${visibleCount} email${visibleCount !== 1 ? 's' : ''} matching "${query}"`);
}

/**
 * Highlight matching text in email items
 * @param {HTMLElement} item - The email item element
 * @param {string} query - The search query
 */
function highlightMrpGmailText(item, query) {
    // Remove existing highlights
    item.querySelectorAll('.highlight').forEach(el => {
        const parent = el.parentNode;
        parent.replaceChild(document.createTextNode(el.textContent), el);
        parent.normalize();
    });

    // Highlight text in subject
    const subjectEl = item.querySelector('p.mb-1');
    if (subjectEl) {
        const subject = subjectEl.textContent;
        const normalizedSubject = subject.toLowerCase();
        const index = normalizedSubject.indexOf(query);

        if (index >= 0) {
            const before = subject.substring(0, index);
            const match = subject.substring(index, index + query.length);
            const after = subject.substring(index + query.length);

            subjectEl.innerHTML = before + '<span class="highlight" style="background-color: yellow;">' + match + '</span>' + after;
        }
    }

    // Highlight text in content
    const contentEl = item.querySelector('small.text-muted');
    if (contentEl) {
        const content = contentEl.textContent;
        const normalizedContent = content.toLowerCase();
        const index = normalizedContent.indexOf(query);

        if (index >= 0) {
            const before = content.substring(0, index);
            const match = content.substring(index, index + query.length);
            const after = content.substring(index + query.length);

            contentEl.innerHTML = before + '<span class="highlight" style="background-color: yellow;">' + match + '</span>' + after;
        }
    }
}

/**
 * Sort MRP Gmail emails by specified criteria
 * @param {string} sortType - The sort type (date-desc, date-asc, sender-asc, sender-desc, subject-asc, subject-desc)
 */
function sortMrpGmailEmails(sortType) {
    const emailList = document.getElementById('mrp-email-list');
    if (!emailList) return;

    // Get all email items
    const emailItems = Array.from(emailList.querySelectorAll('.mrp-email-item'));
    if (!emailItems.length) return;

    // Sort based on criteria
    emailItems.sort((a, b) => {
        switch (sortType) {
            case 'date-desc':
                // Newest first (default)
                const dateA = a.getAttribute('data-date') || '';
                const dateB = b.getAttribute('data-date') || '';
                return dateB.localeCompare(dateA);
            case 'date-asc':
                // Oldest first
                const dateC = a.getAttribute('data-date') || '';
                const dateD = b.getAttribute('data-date') || '';
                return dateC.localeCompare(dateD);
            case 'sender-asc':
                // Sender A-Z
                const senderA = a.getAttribute('data-sender') || '';
                const senderB = b.getAttribute('data-sender') || '';
                return senderA.localeCompare(senderB);
            case 'sender-desc':
                // Sender Z-A
                const senderC = a.getAttribute('data-sender') || '';
                const senderD = b.getAttribute('data-sender') || '';
                return senderD.localeCompare(senderC);
            case 'subject-asc':
                // Subject A-Z
                const subjectA = a.getAttribute('data-subject') || '';
                const subjectB = b.getAttribute('data-subject') || '';
                return subjectA.toLowerCase().localeCompare(subjectB.toLowerCase());
            case 'subject-desc':
                // Subject Z-A
                const subjectC = a.getAttribute('data-subject') || '';
                const subjectD = b.getAttribute('data-subject') || '';
                return subjectD.toLowerCase().localeCompare(subjectC.toLowerCase());
            default:
                return 0;
        }
    });

    // Reorder the DOM elements
    emailItems.forEach(item => {
        emailList.appendChild(item);
    });

    // Show toast
    showToast(`Emails sorted by ${sortType.replace('-', ' ')}`);
}

/**
 * Filter MRP Gmail emails by specified criteria
 * @param {string} filterType - The filter type (all, unread, read, label-*)
 */
function filterMrpGmailEmails(filterType) {
    const emailList = document.getElementById('mrp-email-list');
    if (!emailList) return;

    // Get all email items
    const emailItems = emailList.querySelectorAll('.mrp-email-item');
    if (!emailItems.length) return;

    // Show/hide based on filter
    emailItems.forEach(item => {
        if (filterType === 'all') {
            // Show all emails
            item.style.display = '';
        } else if (filterType === 'unread') {
            // Show only unread emails
            item.style.display = item.classList.contains('unread') ? '' : 'none';
        } else if (filterType === 'read') {
            // Show only read emails
            item.style.display = !item.classList.contains('unread') ? '' : 'none';
        } else if (filterType.startsWith('label-')) {
            // Show emails with specific label
            const label = filterType.replace('label-', '');
            const itemLabel = item.getAttribute('data-label');
            item.style.display = itemLabel === label ? '' : 'none';
        }
    });

    // Show toast
    const visibleCount = Array.from(emailItems).filter(item => item.style.display !== 'none').length;
    showToast(`Showing ${visibleCount} email${visibleCount !== 1 ? 's' : ''} with filter: ${filterType}`);
}

/**
 * Show all MRP Gmail emails
 */
function showAllMrpGmailEmails() {
    const emailList = document.getElementById('mrp-email-list');
    if (!emailList) return;

    // Get all email items
    const emailItems = emailList.querySelectorAll('.mrp-email-item');
    if (!emailItems.length) return;

    // Show all emails
    emailItems.forEach(item => {
        item.style.display = '';

        // Remove highlights
        item.querySelectorAll('.highlight').forEach(el => {
            const parent = el.parentNode;
            parent.replaceChild(document.createTextNode(el.textContent), el);
            parent.normalize();
        });
    });
}