<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Performance Real-Time Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .performance-stats {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .timing {
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gmail Performance Real-Time Test</h1>
        <p>This tool tests the actual performance of Gmail interactions in the BMS system to verify optimization effectiveness.</p>
        
        <div class="test-section">
            <h3>Real-Time Performance Monitoring</h3>
            <button class="test-button" onclick="startPerformanceTest()">Start Real-Time Test</button>
            <button class="test-button" onclick="testBMSGmail()">Test BMS Gmail</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
            <div id="performance-results"></div>
        </div>

        <div class="test-section">
            <h3>Response Time Analysis</h3>
            <button class="test-button" onclick="measureClickResponse()">Test Click Response</button>
            <button class="test-button" onclick="measureModalOpen()">Test Modal Opening</button>
            <button class="test-button" onclick="measureTabSwitch()">Test Tab Switching</button>
            <div id="timing-results"></div>
        </div>

        <div class="test-section">
            <h3>Timeout Detection</h3>
            <button class="test-button" onclick="scanForTimeouts()">Scan for setTimeout Calls</button>
            <button class="test-button" onclick="scanForIntervals()">Scan for setInterval Calls</button>
            <div id="timeout-results"></div>
        </div>
    </div>

    <script>
        // Performance monitoring variables
        let performanceData = {
            clickTimes: [],
            modalTimes: [],
            tabSwitchTimes: [],
            timeoutCount: 0,
            intervalCount: 0
        };

        // Override setTimeout to track usage
        const originalSetTimeout = window.setTimeout;
        window.setTimeout = function(...args) {
            performanceData.timeoutCount++;
            const delay = args[1] || 0;
            if (delay > 100) {
                addResult('timeout-results', `setTimeout detected: ${delay}ms delay`, 'warning');
            }
            return originalSetTimeout.apply(this, args);
        };

        // Override setInterval to track usage
        const originalSetInterval = window.setInterval;
        window.setInterval = function(...args) {
            performanceData.intervalCount++;
            const delay = args[1] || 0;
            if (delay < 5000) {
                addResult('timeout-results', `setInterval detected: ${delay}ms interval (potential performance issue)`, 'warning');
            }
            return originalSetInterval.apply(this, args);
        };

        function addResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        function measureTime(operation) {
            const start = performance.now();
            return {
                start: start,
                end: () => {
                    const end = performance.now();
                    const duration = end - start;
                    addResult('timing-results', `${operation}: ${duration.toFixed(2)}ms`, 
                        duration < 50 ? 'success' : duration < 200 ? 'warning' : 'error');
                    return duration;
                }
            };
        }

        function startPerformanceTest() {
            addResult('performance-results', 'Starting real-time performance monitoring...', 'success');
            
            // Test if BMS server is running
            fetch('http://localhost:3001/health')
                .then(response => response.json())
                .then(data => {
                    addResult('performance-results', 'BMS server is running and responsive', 'success');
                })
                .catch(error => {
                    addResult('performance-results', 'BMS server not accessible - please start BMS first', 'error');
                });

            // Monitor for Gmail-related elements
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) {
                            if (node.id && node.id.includes('gmail')) {
                                addResult('performance-results', `Gmail element detected: ${node.id}`, 'success');
                            }
                            if (node.classList && node.classList.contains('modal')) {
                                const timer = measureTime('Modal creation');
                                setTimeout(timer.end, 0);
                            }
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Stop monitoring after 30 seconds
            setTimeout(() => {
                observer.disconnect();
                addResult('performance-results', 'Performance monitoring stopped', 'success');
            }, 30000);
        }

        function testBMSGmail() {
            addResult('performance-results', 'Testing BMS Gmail integration...', 'success');
            
            // Try to open BMS Gmail in a new window/tab
            const timer = measureTime('BMS Gmail page load');
            
            try {
                // Test if we can access the BMS Gmail page
                fetch('http://localhost:3001/')
                    .then(response => {
                        timer.end();
                        if (response.ok) {
                            addResult('performance-results', 'BMS Gmail page accessible - test clicking Gmail button manually', 'success');
                            // Open BMS in new tab for manual testing
                            window.open('http://localhost:3001/', '_blank');
                        } else {
                            addResult('performance-results', 'BMS Gmail page not accessible', 'error');
                        }
                    })
                    .catch(error => {
                        timer.end();
                        addResult('performance-results', 'Error accessing BMS: ' + error.message, 'error');
                    });
            } catch (error) {
                timer.end();
                addResult('performance-results', 'Error testing BMS Gmail: ' + error.message, 'error');
            }
        }

        function measureClickResponse() {
            addResult('timing-results', 'Measuring click response time...', 'success');
            
            // Create a test button
            const testButton = document.createElement('button');
            testButton.textContent = 'Test Click Response';
            testButton.className = 'test-button';
            
            const timer = measureTime('Click response');
            testButton.onclick = () => {
                timer.end();
                testButton.remove();
                addResult('timing-results', 'Click response test completed', 'success');
            };
            
            document.getElementById('timing-results').appendChild(testButton);
            addResult('timing-results', 'Click the test button above to measure response time', 'success');
        }

        function measureModalOpen() {
            addResult('timing-results', 'Testing modal opening performance...', 'success');
            
            const timer = measureTime('Modal opening');
            
            // Create a test modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border: 1px solid #ccc;
                border-radius: 5px;
                z-index: 9999;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            `;
            modal.innerHTML = `
                <h3>Test Modal</h3>
                <p>This modal opened in ${timer.end()}ms</p>
                <button onclick="this.parentElement.remove()">Close</button>
            `;
            
            document.body.appendChild(modal);
        }

        function measureTabSwitch() {
            addResult('timing-results', 'Testing tab switching performance...', 'success');
            
            const timer = measureTime('Tab switching');
            
            // Simulate tab switching by toggling visibility
            const testDiv = document.createElement('div');
            testDiv.innerHTML = `
                <div style="margin: 10px 0;">
                    <button onclick="switchTab(1)" class="test-button">Tab 1</button>
                    <button onclick="switchTab(2)" class="test-button">Tab 2</button>
                </div>
                <div id="tab1" style="display: block; padding: 10px; border: 1px solid #ddd;">Tab 1 Content</div>
                <div id="tab2" style="display: none; padding: 10px; border: 1px solid #ddd;">Tab 2 Content</div>
            `;
            
            window.switchTab = function(tabNum) {
                const timer = measureTime(`Tab ${tabNum} switch`);
                document.getElementById('tab1').style.display = tabNum === 1 ? 'block' : 'none';
                document.getElementById('tab2').style.display = tabNum === 2 ? 'block' : 'none';
                timer.end();
            };
            
            document.getElementById('timing-results').appendChild(testDiv);
        }

        function scanForTimeouts() {
            addResult('timeout-results', `setTimeout calls detected: ${performanceData.timeoutCount}`, 
                performanceData.timeoutCount > 10 ? 'warning' : 'success');
            
            // Reset counter
            performanceData.timeoutCount = 0;
        }

        function scanForIntervals() {
            addResult('timeout-results', `setInterval calls detected: ${performanceData.intervalCount}`, 
                performanceData.intervalCount > 5 ? 'warning' : 'success');
            
            // Reset counter
            performanceData.intervalCount = 0;
        }

        function clearResults() {
            document.getElementById('performance-results').innerHTML = '';
            document.getElementById('timing-results').innerHTML = '';
            document.getElementById('timeout-results').innerHTML = '';
            performanceData = {
                clickTimes: [],
                modalTimes: [],
                tabSwitchTimes: [],
                timeoutCount: 0,
                intervalCount: 0
            };
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addResult('performance-results', 'Gmail Performance Real-Time Test initialized', 'success');
            addResult('performance-results', 'Ready to test Gmail performance optimizations', 'success');
        });
    </script>
</body>
</html>
