// Asset Performance Management System - Main Entry Point

const express = require('express');
const cors = require('cors');
const app = express();
const port = 3006;

// Import shared features
const sharedFeatures = require('../../SharedFeatures');
const auth = sharedFeatures.auth;
const logger = sharedFeatures.logger.createLogger('APM');
const google = require('../../SharedFeatures/integrations/google');
const slack = sharedFeatures.slack;

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));

// Initialize integrations
google.initGoogleAPI().catch((err) => {
  logger && logger.error ? logger.error('Failed to initialize Google API', { error: err.message }) : console.error('Failed to initialize Google API', err);
});

// Define routes
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/api/assets', (req, res) => {
  res.json({
    status: 'success',
    data: {
      assets: [
        {
          id: 1,
          name: 'Production Machine A',
          status: 'operational',
          lastMaintenance: '2025-04-15',
          nextMaintenance: '2025-05-15',
        },
        {
          id: 2,
          name: 'Forklift B',
          status: 'maintenance',
          lastMaintenance: '2025-03-10',
          nextMaintenance: '2025-04-30',
        },
        {
          id: 3,
          name: 'Conveyor System C',
          status: 'operational',
          lastMaintenance: '2025-04-01',
          nextMaintenance: '2025-05-01',
        },
      ],
    },
  });
});

// Google Calendar Integration for Maintenance Schedules
app.get('/api/maintenance/schedule', auth.authenticate, async (req, res) => {
  try {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 6); // Six months ahead

    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger.error('Failed to fetch maintenance schedules from calendar', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Sheets Integration for Asset Performance Tracking
app.get('/api/performance/spreadsheet/:spreadsheetId', auth.authenticate, async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Assets!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger.error('Failed to fetch asset performance spreadsheet', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration for Asset Documentation
app.get('/api/assets/documents', auth.authenticate, async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger.error('Failed to fetch asset documentation', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration for Maintenance Reports
app.post('/api/maintenance/report', auth.authenticate, async (req, res) => {
  try {
    const { assetId, assetName, maintenanceDate, technician, findings, actions } = req.body;

    // Create a new Google Doc for the maintenance report
    const title = `Maintenance Report - ${assetName} - ${maintenanceDate}`;
    const document = await google.Docs.createDocument(title);

    // In a real implementation, you would update the document with the report content

    res.json({ status: 'success', data: { documentId: document.id, title: document.title } });
  } catch (error) {
    logger.error('Failed to create maintenance report', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Slack Integration for Asset Alerts
app.post('/api/asset/alert', auth.authenticate, async (req, res) => {
  try {
    const { assetId, assetName, issue, priority, channel } = req.body;

    const message = `*ASSET ALERT*: Issue with ${assetName} (ID: ${assetId})\nIssue: ${issue}\nPriority: ${priority}`;
    const result = await slack.sendMessage(channel, message);

    res.json({ status: 'success', data: { alerted: true, result } });
  } catch (error) {
    logger.error('Failed to send asset alert', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Sample Google Sheets endpoint
app.get('/api/google-sheets/:spreadsheetId', async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Sheets data', { error: error.message }) : console.error('Failed to fetch Google Sheets data', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Drive files', { error: error.message }) : console.error('Failed to fetch Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration
app.get('/api/google-calendar/events', async (req, res) => {
  try {
    const startDate = new Date(req.query.startDate || Date.now());
    const endDate = new Date(req.query.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Calendar events', { error: error.message }) : console.error('Failed to fetch Google Calendar events', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/google-gmail/send', async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to send Gmail email', { error: error.message }) : console.error('Failed to send Gmail email', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration
app.post('/api/google-docs/create', async (req, res) => {
  try {
    const { title } = req.body;
    const doc = await google.Docs.createDocument(title);
    res.json({ status: 'success', data: doc });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to create Google Doc', { error: error.message }) : console.error('Failed to create Google Doc', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google-contacts', async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Contacts', { error: error.message }) : console.error('Failed to fetch Google Contacts', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Start server
app.listen(port, () => {
  console.log(`APM running at http://localhost:${port}`);
  console.log('Connected to IntegrationHub');
});
