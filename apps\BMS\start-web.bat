@echo off
setlocal

REM Set path to local Node.js installation
set PATH=%CD%\..\..\Node\node-v22.15.0-win-x64;%PATH%

REM Check if a parameter was provided to skip hub check
if "%1"=="standalone" goto standalone

REM Check if Integration Hub is running
echo Checking if Integration Hub is running...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8000"') do set HUB_RUNNING=1
if not defined HUB_RUNNING (
    echo Integration Hub is not running.
    choice /C YN /M "Do you want to start the Integration Hub"
    if errorlevel 2 goto standalone
    if errorlevel 1 (
        echo Starting Integration Hub...
        start cmd /k "cd /d "%CD%\..\..\Apps\IntegrationHub" && "%CD%\..\..\Node\node-v22.15.0-win-x64\npm.cmd" start production"
        timeout /t 10 /nobreak > nul
    )
)
goto start_app

:standalone
echo Running BMS in standalone mode (without Integration Hub)...

:start_app
REM Start the application
echo Starting Business Management System...
start "" "http://localhost:3001"
call %CD%\..\..\Node\node-v22.15.0-win-x64\npm.cmd start production

:end
exit
