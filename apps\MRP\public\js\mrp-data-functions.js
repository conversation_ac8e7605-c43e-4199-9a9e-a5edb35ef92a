/**
 * MRP Data Functions
 * 
 * This file contains functions specific to the MRP application for handling data,
 * including search, sort, filter, and refresh functionality.
 */

/**
 * MRPDataFunctions - Namespace for MRP-specific data functions
 */
const MRPDataFunctions = {
    /**
     * Initialize all data functionality for the MRP application
     */
    init: function() {
        // Initialize inventory table data
        this.initInventoryTable();
        
        // Initialize search, sort, and filter for inventory table
        this.initInventoryTableControls();
        
        // Initialize refresh functionality
        this.initRefreshControls();
        
        // Fix navigation links
        this.fixNavigationLinks();
        
        console.log('MRP Data Functions initialized');
    },
    
    /**
     * Initialize the inventory table with data
     */
    initInventoryTable: function() {
        const inventoryData = [
            { id: 'RM-001', name: 'Aluminum Sheet', category: 'Raw Material', quantity: 1200, reorderLevel: 500, status: 'Good' },
            { id: 'RM-002', name: 'Steel Rod', category: 'Raw Material', quantity: 850, reorderLevel: 400, status: 'Good' },
            { id: 'RM-003', name: 'Copper Wire', category: 'Raw Material', quantity: 320, reorderLevel: 350, status: 'Warning' },
            { id: 'RM-004', name: 'Plastic Pellets', category: 'Raw Material', quantity: 1500, reorderLevel: 600, status: 'Good' },
            { id: 'RM-005', name: 'Glass Panel', category: 'Raw Material', quantity: 75, reorderLevel: 100, status: 'Warning' },
            { id: 'RM-006', name: 'Rubber Gasket', category: 'Raw Material', quantity: 450, reorderLevel: 200, status: 'Good' },
            { id: 'RM-007', name: 'Silicon Wafer', category: 'Raw Material', quantity: 0, reorderLevel: 50, status: 'OutOfStock' },
            { id: 'CP-001', name: 'Circuit Board', category: 'Component', quantity: 230, reorderLevel: 100, status: 'Good' },
            { id: 'CP-002', name: 'Power Supply', category: 'Component', quantity: 120, reorderLevel: 50, status: 'Good' },
            { id: 'CP-003', name: 'LCD Screen', category: 'Component', quantity: 45, reorderLevel: 60, status: 'Warning' },
            { id: 'CP-004', name: 'Keyboard', category: 'Component', quantity: 0, reorderLevel: 30, status: 'OutOfStock' },
            { id: 'CP-005', name: 'Battery', category: 'Component', quantity: 180, reorderLevel: 75, status: 'Good' },
            { id: 'FG-001', name: 'Laptop Model A', category: 'Finished Good', quantity: 35, reorderLevel: 20, status: 'Good' },
            { id: 'FG-002', name: 'Desktop Model B', category: 'Finished Good', quantity: 28, reorderLevel: 15, status: 'Good' },
            { id: 'FG-003', name: 'Tablet Model C', category: 'Finished Good', quantity: 12, reorderLevel: 25, status: 'Warning' },
            { id: 'FG-004', name: 'Smartphone Model D', category: 'Finished Good', quantity: 0, reorderLevel: 10, status: 'OutOfStock' }
        ];
        
        const tableBody = document.getElementById('inventoryTableBody');
        if (!tableBody) {
            console.error('Inventory table body not found');
            return;
        }
        
        // Clear existing rows
        tableBody.innerHTML = '';
        
        // Add data rows
        inventoryData.forEach(item => {
            const row = document.createElement('tr');
            row.setAttribute('data-id', item.id);
            
            // Determine status class and icon
            let statusClass = '';
            let statusIcon = '';
            
            switch (item.status) {
                case 'Good':
                    statusClass = 'status-good';
                    statusIcon = 'bi-check-circle-fill text-success';
                    break;
                case 'Warning':
                    statusClass = 'status-warning';
                    statusIcon = 'bi-exclamation-triangle-fill text-warning';
                    break;
                case 'OutOfStock':
                    statusClass = 'status-danger';
                    statusIcon = 'bi-x-circle-fill text-danger';
                    break;
            }
            
            row.innerHTML = `
                <td>${item.id}</td>
                <td>${item.name}</td>
                <td>${item.category}</td>
                <td>${item.quantity.toLocaleString()}</td>
                <td>${item.reorderLevel.toLocaleString()}</td>
                <td>
                    <span class="status-indicator ${statusClass}"></span>
                    <i class="bi ${statusIcon} me-1"></i>
                    ${item.status === 'OutOfStock' ? 'Out of Stock' : item.status}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary view-item" data-id="${item.id}">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary edit-item" data-id="${item.id}">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger delete-item" data-id="${item.id}">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // Add event listeners for action buttons
        this.initInventoryTableActions();
        
        console.log('Inventory table initialized with data');
    },
    
    /**
     * Initialize action buttons in the inventory table
     */
    initInventoryTableActions: function() {
        // View item buttons
        document.querySelectorAll('.view-item').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                alert(`Viewing item ${itemId}`);
                // In a real application, this would open a modal or navigate to a detail page
            });
        });
        
        // Edit item buttons
        document.querySelectorAll('.edit-item').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                alert(`Editing item ${itemId}`);
                // In a real application, this would open an edit form
            });
        });
        
        // Delete item buttons
        document.querySelectorAll('.delete-item').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                if (confirm(`Are you sure you want to delete item ${itemId}?`)) {
                    alert(`Item ${itemId} deleted`);
                    // In a real application, this would send a delete request to the server
                    // and remove the row from the table
                }
            });
        });
    },
    
    /**
     * Initialize search, sort, and filter controls for the inventory table
     */
    initInventoryTableControls: function() {
        // Add search input above the table if it doesn't exist
        const tableContainer = document.querySelector('.inventory-table').parentNode;
        
        if (!document.getElementById('inventorySearchInput')) {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'row mb-3';
            searchContainer.innerHTML = `
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="inventorySearchInput" placeholder="Search inventory...">
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="d-flex justify-content-end">
                        <div class="me-2">
                            <select class="form-select" id="categoryFilter">
                                <option value="all">All Categories</option>
                                <option value="raw material">Raw Material</option>
                                <option value="component">Component</option>
                                <option value="finished good">Finished Good</option>
                            </select>
                        </div>
                        <div class="me-2">
                            <select class="form-select" id="statusFilter">
                                <option value="all">All Statuses</option>
                                <option value="good">Good</option>
                                <option value="warning">Warning</option>
                                <option value="out of stock">Out of Stock</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" id="refreshInventoryBtn">
                            <i class="bi bi-arrow-repeat"></i> Refresh
                        </button>
                    </div>
                </div>
            `;
            
            tableContainer.insertBefore(searchContainer, tableContainer.firstChild);
        }
        
        // Initialize search
        ISADataUtils.initTableSearch('inventorySearchInput', 'inventoryTable', [0, 1, 2]); // Search ID, Name, and Category columns
        
        // Initialize sorting
        ISADataUtils.initTableSort('inventoryTable', {
            excludeColumns: [6], // Exclude Actions column
            numericColumns: [3, 4] // Quantity and Reorder Level are numeric
        });
        
        // Initialize filtering
        ISADataUtils.initTableFilter('inventoryTable', [
            {
                filterId: 'categoryFilter',
                columnIndex: 2, // Category column
            },
            {
                filterId: 'statusFilter',
                columnIndex: 5, // Status column
                filterFn: (cell, filterValue, row) => {
                    if (filterValue === 'all') return true;
                    const cellText = cell.textContent.toLowerCase();
                    return cellText.includes(filterValue);
                }
            }
        ]);
    },
    
    /**
     * Initialize refresh controls
     */
    initRefreshControls: function() {
        // Refresh inventory table
        ISADataUtils.initRefresh('refreshInventoryBtn', 'inventoryTableBody', () => {
            return new Promise((resolve, reject) => {
                // Simulate network delay
                setTimeout(() => {
                    try {
                        this.initInventoryTable();
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                }, 1000);
            });
        });
        
        // Refresh AI insights
        ISADataUtils.initRefresh('refresh-insights-btn', 'ai-insights-container', () => {
            return new Promise((resolve, reject) => {
                // Simulate network delay
                setTimeout(() => {
                    try {
                        this.refreshAIInsights();
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                }, 1500);
            });
        });
    },
    
    /**
     * Refresh AI insights content
     */
    refreshAIInsights: function() {
        const container = document.getElementById('ai-insights-container');
        if (!container) return;
        
        // Show loading spinner
        container.innerHTML = `
            <div class="d-flex justify-content-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `;
        
        // Simulate loading delay
        setTimeout(() => {
            container.innerHTML = `
                <div class="alert alert-primary" role="alert">
                    <h5><i class="bi bi-lightbulb"></i> Inventory Optimization</h5>
                    <p>Based on current demand patterns, consider reducing reorder levels for Steel Rod (RM-002) by 15% to optimize inventory costs.</p>
                </div>
                <div class="alert alert-warning" role="alert">
                    <h5><i class="bi bi-exclamation-triangle"></i> Stock Alert</h5>
                    <p>Copper Wire (RM-003) is approaching reorder level. Based on production schedule, place order within 5 days to avoid delays.</p>
                </div>
                <div class="alert alert-danger" role="alert">
                    <h5><i class="bi bi-x-circle"></i> Critical Shortage</h5>
                    <p>Silicon Wafer (RM-007) is out of stock. This will impact production of Circuit Boards (CP-001) within 7 days.</p>
                </div>
                <div class="alert alert-success" role="alert">
                    <h5><i class="bi bi-graph-up"></i> Demand Forecast</h5>
                    <p>Predicted 20% increase in demand for Laptop Model A (FG-001) next quarter based on historical data and market trends.</p>
                </div>
            `;
        }, 1500);
    },
    
    /**
     * Fix navigation links to ensure they work properly
     */
    fixNavigationLinks: function() {
        // Fix sidebar navigation links
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            const href = link.getAttribute('href');
            
            // Skip links that are already properly configured
            if (href.startsWith('#') && href.length > 1 && !link.hasAttribute('data-bs-toggle')) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all links
                    document.querySelectorAll('.sidebar .nav-link').forEach(l => {
                        l.classList.remove('active');
                    });
                    
                    // Add active class to clicked link
                    this.classList.add('active');
                    
                    // Update page title
                    const linkText = this.textContent.trim();
                    document.querySelector('.main-content h1').textContent = linkText + ' Dashboard';
                    
                    // In a real application, this would load the appropriate content
                    alert(`Navigating to ${linkText}`);
                });
            }
        });
    }
};

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    MRPDataFunctions.init();
});
