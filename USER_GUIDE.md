# ISA APPS User Guide

This guide will help you get started with the Ice Systems Australasia Applications Suite.

## Getting Started

### Starting the Applications

You have two options to start the applications:

#### Option 1: Using the Launcher

1. Double-click on `ice-systems-launcher.bat` in the root directory
2. Select the application you want to run from the menu
3. Each application can be run as either a web application or a desktop application

#### Option 2: Starting All Applications at Once

1. Double-click on `start-and-open.bat` in the root directory
2. This will start all applications in separate command windows
3. Wait for all applications to start (this may take a minute or two)

> **Note:** Always run PowerShell scripts (like `setup-and-run.ps1`) with PowerShell, not Notepad.

### Accessing the Applications

Once the applications are running, you can access them at the following URLs:

- **Integration Hub**: http://localhost:8000
- **BMS** (Business Management System): http://localhost:3001
- **MRP** (Materials Requirements Planning): http://localhost:3002
- **CRM** (Customer Relationship Management): http://localhost:3003
- **WMS** (Warehouse Management System): http://localhost:3004
- **APS** (Advanced Planning and Scheduling): http://localhost:3005
- **APM** (Asset Performance Management): http://localhost:3006
- **PMS**: http://localhost:3007
- **SCM**: http://localhost:3008
- **TM**: http://localhost:3009

## Using the Applications

### Integration Hub

The Integration Hub is the central component that connects all applications. It provides:

- System status monitoring
- Data synchronization between applications
- Integration with external services (Google, Microsoft, etc.)

### Business Management System (BMS)

The BMS handles core business operations:

- User management
- Financial management
- Reporting
- Settings and configuration

### Materials Requirements Planning (MRP)

The MRP system manages inventory and production:

- Inventory management
- Production planning
- Material forecasting
- Supplier management

### Customer Relationship Management (CRM)

The CRM system manages customer interactions:

- Contact management
- Sales pipeline tracking
- Customer communication
- Lead management

### Warehouse Management System (WMS)

The WMS handles warehouse operations:

- Inventory tracking
- Order fulfillment
- Shipping and receiving
- Warehouse layout

### Advanced Planning and Scheduling (APS)

The APS system optimizes production scheduling:

- Production scheduling
- Resource allocation
- Capacity planning
- Optimization algorithms

### Asset Performance Management (APM)

The APM system monitors and manages assets:

- Asset tracking
- Maintenance scheduling
- Performance monitoring
- Lifecycle management

### Project Management System (PMS)

The PMS system manages projects, tasks, and timelines.

- Project tracking
- Task assignment
- Timeline management
- Collaboration tools

### Supply Chain Management (SCM)

The SCM system manages supply chain operations.

- Supplier management
- Inventory tracking
- Order processing
- Logistics coordination

### Transport Management (TM)

The TM system manages transport and logistics.

- Fleet management
- Route optimization
- Shipment tracking
- Delivery scheduling

## Getting Help

Each application includes an AI-powered help system that can assist you with:

- Answering questions about how to use the application
- Providing step-by-step guides for common tasks
- Suggesting best practices
- Finding relevant external resources

To access the help system, click on the "Help" button in any application.

## Troubleshooting

### Application Won't Start

If an application fails to start:

1. Make sure the Integration Hub is running first
2. Check that the port is not already in use
3. Try restarting the application

### Connection Issues

If applications can't communicate with each other:

1. Verify that the Integration Hub is running
2. Check that all applications are using the correct ports
3. Make sure your firewall is not blocking the connections

### Other Issues

For other issues, please refer to the `docs/TROUBLESHOOTING_GUIDE.md` file for more detailed troubleshooting steps.
