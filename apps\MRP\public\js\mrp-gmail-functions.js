/**
 * MRP Gmail Functions
 * This file provides essential functions for the Gmail functionality in the MRP application
 */

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of toast (success, info, warning, error)
 */
function showToast(message, type = 'success') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '1050';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    // Set toast content
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Initialize and show the toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

/**
 * Open an email in the read tab
 * @param {string} sender - The email sender
 * @param {string} subject - The email subject
 */
function openEmail(sender, subject) {
    console.log(`Opening email: "${subject}" from ${sender}`);
    
    // Find the read tab
    const readTab = document.getElementById('mrp-read-tab');
    if (!readTab) {
        console.error('Read tab not found');
        return;
    }
    
    // Switch to the read tab
    const tabTrigger = new bootstrap.Tab(readTab);
    tabTrigger.show();
    
    // Call the existing populateMrpEmailContent function
    if (typeof populateMrpEmailContent === 'function') {
        populateMrpEmailContent(sender, subject);
    } else {
        console.error('populateMrpEmailContent function not found');
    }
}

// Make the openEmail function available globally
window.openEmail = openEmail;
