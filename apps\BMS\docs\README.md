# BMS Documentation

Welcome to the Business Management System (BMS) documentation. This documentation is organized into the following sections:

## API Documentation

The [API Documentation](./api/README.md) provides detailed information about the BMS API endpoints, including request and response formats, authentication, and examples.

## Architecture Documentation

The [Architecture Documentation](./architecture/README.md) describes the system architecture, including components, data flow, and design decisions.

## User Guides

The [User Guides](./user-guides/README.md) provide step-by-step instructions for using the BMS application, including tutorials and how-to guides.

## Getting Started

To get started with BMS, follow these steps:

1. Clone the repository
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`
4. Open your browser to `http://localhost:3001`

## Contributing to Documentation

We welcome contributions to the documentation. Please follow these guidelines:

1. Use Markdown for all documentation files
2. Follow the existing structure and style
3. Include examples and screenshots where appropriate
4. Submit a pull request with your changes
