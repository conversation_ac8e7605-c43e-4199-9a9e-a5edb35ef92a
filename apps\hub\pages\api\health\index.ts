import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/mongodb';
import Redis from 'redis';

const redisClient = Redis.createClient({ url: process.env.REDIS_URL });

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const [dbStatus, redisStatus] = await Promise.all([checkDatabase(), checkRedis()]);

    const status = {
      status: dbStatus && redisStatus ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      services: {
        database: dbStatus ? 'healthy' : 'unhealthy',
        redis: redisStatus ? 'healthy' : 'unhealthy',
      },
    };

    res.status(200).json(status);
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
}

async function checkDatabase() {
  try {
    const { db } = await connectToDatabase();
    await db.command({ ping: 1 });
    return true;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}

function checkRedis() {
  return new Promise((resolve) => {
    redisClient.ping((err) => {
      if (err) {
        console.error('Redis health check failed:', err);
        resolve(false);
      } else {
        resolve(true);
      }
    });
  });
}
