// Customer Relationship Management System - Main Entry Point

const express = require('express');
const cors = require('cors');
const app = express();
const port = 3003;

// Import shared features
const sharedFeatures = require('../../SharedFeatures');
const auth = sharedFeatures.auth;
const logger = sharedFeatures.logger.createLogger('CRM');
const salesforce = sharedFeatures.salesforce;
const google = require('../../SharedFeatures/integrations/google');
const slack = sharedFeatures.slack;

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));

// Initialize integrations
salesforce.initSalesforceAPI().catch((err) => {
  logger.error('Failed to initialize Salesforce API', { error: err.message });
});

google.initGoogleAPI().catch((err) => {
  logger && logger.error ? logger.error('Failed to initialize Google API', { error: err.message }) : console.error('Failed to initialize Google API', err);
});

// Define routes
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Dashboard API endpoint
app.get('/api/dashboard', (req, res) => {
  // Simulate dashboard data
  const dashboardData = {
    kpis: {
      totalCustomers: {
        value: 256,
        trend: 12,
        trendDirection: 'up'
      },
      activeDeals: {
        value: 28,
        trend: 5,
        trendDirection: 'up'
      },
      conversionRate: {
        value: 24,
        trend: 3,
        trendDirection: 'up'
      },
      customerSatisfaction: {
        value: 92,
        trend: 2,
        trendDirection: 'up'
      }
    },
    salesTrends: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      data: [45000, 52000, 49000, 60000, 55000, 70000]
    },
    leadSources: {
      labels: ['Website', 'Referral', 'Social Media', 'Email', 'Trade Show', 'Other'],
      data: [35, 25, 15, 10, 10, 5]
    },
    recentCustomers: [
      {
        id: 101,
        name: 'Global Tech Solutions',
        contact: 'Michael Chen',
        email: '<EMAIL>',
        phone: '(*************',
        status: 'Active',
        lastContact: '2025-04-28'
      },
      {
        id: 102,
        name: 'Innovative Systems Inc',
        contact: 'Sarah Williams',
        email: '<EMAIL>',
        phone: '(*************',
        status: 'Active',
        lastContact: '2025-04-27'
      },
      {
        id: 103,
        name: 'Premier Solutions',
        contact: 'David Rodriguez',
        email: '<EMAIL>',
        phone: '(*************',
        status: 'Active',
        lastContact: '2025-04-25'
      },
      {
        id: 104,
        name: 'Elite Enterprises',
        contact: 'Jennifer Lee',
        email: '<EMAIL>',
        phone: '(*************',
        status: 'Inactive',
        lastContact: '2025-04-20'
      },
      {
        id: 105,
        name: 'Summit Industries',
        contact: 'Robert Taylor',
        email: '<EMAIL>',
        phone: '(*************',
        status: 'Active',
        lastContact: '2025-04-26'
      }
    ],
    upcomingDeals: [
      {
        id: 201,
        customer: 'Global Tech Solutions',
        description: 'Enterprise Software Package',
        value: 75000,
        probability: 80,
        expectedCloseDate: '2025-05-15'
      },
      {
        id: 202,
        customer: 'Innovative Systems Inc',
        description: 'Cloud Migration Services',
        value: 120000,
        probability: 65,
        expectedCloseDate: '2025-05-30'
      },
      {
        id: 203,
        customer: 'Summit Industries',
        description: 'Security Infrastructure Upgrade',
        value: 95000,
        probability: 75,
        expectedCloseDate: '2025-06-10'
      }
    ],
    activities: [
      {
        id: 301,
        type: 'Call',
        customer: 'Global Tech Solutions',
        contact: 'Michael Chen',
        date: '2025-04-28',
        notes: 'Discussed upcoming software needs and potential upgrade.'
      },
      {
        id: 302,
        type: 'Meeting',
        customer: 'Innovative Systems Inc',
        contact: 'Sarah Williams',
        date: '2025-04-27',
        notes: 'Presented cloud migration proposal. Client showed strong interest.'
      },
      {
        id: 303,
        type: 'Email',
        customer: 'Premier Solutions',
        contact: 'David Rodriguez',
        date: '2025-04-25',
        notes: 'Sent follow-up information about service packages.'
      },
      {
        id: 304,
        type: 'Demo',
        customer: 'Summit Industries',
        contact: 'Robert Taylor',
        date: '2025-04-26',
        notes: 'Product demonstration went well. Client requested pricing information.'
      }
    ]
  };

  res.json({ status: 'success', data: dashboardData });
});

app.get('/api/customers', (req, res) => {
  res.json({
    status: 'success',
    data: {
      customers: [
        { id: 1, name: 'Acme Corp', contact: 'John Doe', email: '<EMAIL>', status: 'active' },
        {
          id: 2,
          name: 'Widget Inc',
          contact: 'Jane Smith',
          email: '<EMAIL>',
          status: 'active',
        },
        {
          id: 3,
          name: 'XYZ Company',
          contact: 'Bob Johnson',
          email: '<EMAIL>',
          status: 'inactive',
        },
      ],
    },
  });
});

// Salesforce Account Integration
app.get('/api/salesforce/accounts', auth.authenticate, async (req, res) => {
  try {
    const query = 'SELECT Id, Name, Industry FROM Account LIMIT 10';
    const accounts = await salesforce.Account.query(query);
    res.json({ status: 'success', data: accounts });
  } catch (error) {
    logger.error('Failed to fetch Salesforce accounts', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Salesforce Contact Integration
app.get('/api/salesforce/contacts', auth.authenticate, async (req, res) => {
  try {
    const query = 'SELECT Id, FirstName, LastName, Email FROM Contact LIMIT 10';
    const contacts = await salesforce.Contact.query(query);
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger.error('Failed to fetch Salesforce contacts', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Salesforce Opportunity Integration
app.get('/api/salesforce/opportunities', auth.authenticate, async (req, res) => {
  try {
    const query = 'SELECT Id, Name, StageName, Amount, CloseDate FROM Opportunity LIMIT 10';
    const opportunities = await salesforce.Opportunity.query(query);
    res.json({ status: 'success', data: opportunities });
  } catch (error) {
    logger.error('Failed to fetch Salesforce opportunities', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/email/send', auth.authenticate, async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger.error('Failed to send email', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google/contacts', auth.authenticate, async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger.error('Failed to fetch Google contacts', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Sample Google Sheets endpoint
app.get('/api/google-sheets/:spreadsheetId', async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Sheets data', { error: error.message }) : console.error('Failed to fetch Google Sheets data', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Slack Integration for Customer Updates
app.post('/api/customer/notify', auth.authenticate, async (req, res) => {
  try {
    const { customerId, message, channel } = req.body;
    // Get customer details
    const customer = { id: customerId, name: 'Example Customer' }; // In real app, fetch from database

    // Send notification to Slack
    const slackMessage = `*Customer Update*: ${customer.name} (ID: ${customer.id})\n${message}`;
    const result = await slack.sendMessage(channel, slackMessage);

    res.json({ status: 'success', data: result });
  } catch (error) {
    logger.error('Failed to send customer notification', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Drive files', { error: error.message }) : console.error('Failed to fetch Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration
app.get('/api/google-calendar/events', async (req, res) => {
  try {
    const startDate = new Date(req.query.startDate || Date.now());
    const endDate = new Date(req.query.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Calendar events', { error: error.message }) : console.error('Failed to fetch Google Calendar events', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/google-gmail/send', async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to send Gmail email', { error: error.message }) : console.error('Failed to send Gmail email', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration
app.post('/api/google-docs/create', async (req, res) => {
  try {
    const { title } = req.body;
    const doc = await google.Docs.createDocument(title);
    res.json({ status: 'success', data: doc });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to create Google Doc', { error: error.message }) : console.error('Failed to create Google Doc', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google-contacts', async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Contacts', { error: error.message }) : console.error('Failed to fetch Google Contacts', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Start server
app.listen(port, () => {
  console.log(`CRM running at http://localhost:${port}`);
  console.log('Connected to IntegrationHub');
});
