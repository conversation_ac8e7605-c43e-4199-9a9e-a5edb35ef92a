// <PERSON>ript to completely fix the BMS index.html file
const fs = require('fs');
const path = require('path');

// Read the PMS index.html file as a reference
const pmsIndexPath = path.join(__dirname, '..', 'PMS', 'public', 'index.html');
let pmsContent = '';
try {
    pmsContent = fs.readFileSync(pmsIndexPath, 'utf8');
} catch (error) {
    console.error('Error reading PMS index.html file:', error);
}

// Extract the Google Sheets Modal from PMS
let pmsGoogleSheetsModal = '';
if (pmsContent) {
    const sheetsModalStart = pmsContent.indexOf('<!-- Google Sheets Modal -->');
    if (sheetsModalStart > -1) {
        const sheetsModalEnd = pmsContent.indexOf('</div>\n\n', sheetsModalStart) + 6;
        pmsGoogleSheetsModal = pmsContent.substring(sheetsModalStart, sheetsModalEnd);
    }
}

// Read the current BMS index.html file
const indexPath = path.join(__dirname, 'public', 'index.html');
let content = fs.readFileSync(indexPath, 'utf8');

// Create a backup of the original file
fs.writeFileSync(indexPath + '.bak2', content, 'utf8');

// Remove all duplicate h2 elements with $125,000
content = content.replace(/(<h2 class="card-text">\$125,000<\/h2>\s*)+/g, '');

// Remove broken HTML tags
content = content.replace(/<\s*(?!\/|br|hr|img|input|link|meta|area|base|col|command|embed|keygen|param|source|track|wbr)[a-z]+[^>]*>\s*<\s*\/\s*[a-z]+>/g, '');

// Remove single < characters that are not part of tags
content = content.replace(/([^<])<([^a-zA-Z\/])/g, '$1$2');

// Replace the Google Sheets Modal if we have a reference from PMS
if (pmsGoogleSheetsModal) {
    // Find all occurrences of Google Sheets Modal
    const firstSheetsModalStart = content.indexOf('<!-- Google Sheets Modal -->');
    if (firstSheetsModalStart > -1) {
        // Find the end of the first modal
        let firstSheetsModalEnd = content.indexOf('</div>\n\n', firstSheetsModalStart);
        if (firstSheetsModalEnd === -1) {
            firstSheetsModalEnd = content.indexOf('</div>\n    </div>\n</div>', firstSheetsModalStart);
        }
        if (firstSheetsModalEnd === -1) {
            firstSheetsModalEnd = content.indexOf('</div>\n\n    <!-- Google Docs Modal -->', firstSheetsModalStart);
        }
        if (firstSheetsModalEnd > -1) {
            firstSheetsModalEnd += 6;
            // Replace the first modal with the PMS version
            content = content.substring(0, firstSheetsModalStart) + 
                      pmsGoogleSheetsModal.replace(/pink/g, 'var(--app-primary-color)') + 
                      content.substring(firstSheetsModalEnd);
            
            // Find and remove any duplicate modals
            const secondSheetsModalStart = content.indexOf('<!-- Google Sheets Modal -->', firstSheetsModalStart + 100);
            if (secondSheetsModalStart > -1) {
                let secondSheetsModalEnd = content.indexOf('</div>\n\n', secondSheetsModalStart);
                if (secondSheetsModalEnd === -1) {
                    secondSheetsModalEnd = content.indexOf('</div>\n    </div>\n</div>', secondSheetsModalStart);
                }
                if (secondSheetsModalEnd === -1) {
                    secondSheetsModalEnd = content.indexOf('</div>\n\n    <!-- Google Docs Modal -->', secondSheetsModalStart);
                }
                if (secondSheetsModalEnd > -1) {
                    secondSheetsModalEnd += 6;
                    content = content.substring(0, secondSheetsModalStart) + content.substring(secondSheetsModalEnd);
                }
            }
        }
    }
}

// Fix any missing icons in buttons
content = content.replace(/<button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment\([^)]+\)"><\/button>/g, 
                         '<button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment($1)"><i class="bi bi-eye"></i></button>');
content = content.replace(/<button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadAttachment\([^)]+\)"><\/button>/g, 
                         '<button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadAttachment($1)"><i class="bi bi-download"></i></button>');
content = content.replace(/<button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile\([^)]+\)"><\/button>/g, 
                         '<button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile($1)"><i class="bi bi-share"></i></button>');
content = content.replace(/<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment\([^)]+\)"><\/button>/g, 
                         '<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment($1)"><i class="bi bi-trash"></i></button>');

// Fix missing icons in other elements
content = content.replace(/<i class="bi bi-file-earmark-spreadsheet text-success me-2"><\/i>/g, 
                         '<i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>');
content = content.replace(/<i class="bi bi-file-earmark-text text-primary me-2"><\/i>/g, 
                         '<i class="bi bi-file-earmark-text text-primary me-2"></i>');
content = content.replace(/<i class="bi bi-file-earmark-spreadsheet me-2"><\/i>/g, 
                         '<i class="bi bi-file-earmark-spreadsheet me-2"></i>');
content = content.replace(/<i class="bi bi-file-earmark-text me-2"><\/i>/g, 
                         '<i class="bi bi-file-earmark-text me-2"></i>');

// Write the fixed content back to the file
fs.writeFileSync(indexPath, content, 'utf8');

console.log('BMS index.html file completely fixed!');
