"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "../../node_modules/.pnpm/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/.pnpm/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f/node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/.pnpm/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/.pnpm/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"../../node_modules/.pnpm/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f/node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"../../node_modules/.pnpm/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f/node_modules/next/dist/pages/_app.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"./pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: (private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default()),\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Home = ()=>{\n    const [apps, setApps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"bms\",\n            name: \"Business Management System\",\n            description: \"Business operations and management\",\n            url: \"http://localhost:3001\",\n            icon: \"\\uD83D\\uDCCA\",\n            status: \"loading\"\n        },\n        {\n            id: \"mrp\",\n            name: \"Materials Requirements Planning\",\n            description: \"Inventory and materials management\",\n            url: \"http://localhost:3002\",\n            icon: \"\\uD83D\\uDCE6\",\n            status: \"loading\"\n        },\n        {\n            id: \"crm\",\n            name: \"Customer Relationship Management\",\n            description: \"Customer data and interactions\",\n            url: \"http://localhost:3003\",\n            icon: \"\\uD83D\\uDC65\",\n            status: \"loading\"\n        },\n        {\n            id: \"wms\",\n            name: \"Warehouse Management System\",\n            description: \"Warehouse operations and logistics\",\n            url: \"http://localhost:3004\",\n            icon: \"\\uD83C\\uDFED\",\n            status: \"loading\"\n        },\n        {\n            id: \"aps\",\n            name: \"Advanced Planning and Scheduling\",\n            description: \"Production planning and scheduling\",\n            url: \"http://localhost:3005\",\n            icon: \"\\uD83D\\uDCC5\",\n            status: \"loading\"\n        },\n        {\n            id: \"apm\",\n            name: \"Asset Performance Management\",\n            description: \"Equipment maintenance and reliability\",\n            url: \"http://localhost:3006\",\n            icon: \"⚙️\",\n            status: \"loading\"\n        },\n        {\n            id: \"pms\",\n            name: \"Project Management System\",\n            description: \"Project planning and execution\",\n            url: \"http://localhost:3007\",\n            icon: \"\\uD83D\\uDCDD\",\n            status: \"loading\"\n        },\n        {\n            id: \"scm\",\n            name: \"Supply Chain Management\",\n            description: \"Supply chain visibility and optimization\",\n            url: \"http://localhost:3008\",\n            icon: \"\\uD83D\\uDD04\",\n            status: \"loading\"\n        },\n        {\n            id: \"tm\",\n            name: \"Task Management System\",\n            description: \"Task assignment and tracking\",\n            url: \"http://localhost:3009\",\n            icon: \"✓\",\n            status: \"loading\"\n        }\n    ]);\n    // Check application status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAppStatus = async ()=>{\n            const updatedApps = [\n                ...apps\n            ];\n            for(let i = 0; i < updatedApps.length; i++){\n                try {\n                    const response = await fetch(`${updatedApps[i].url}/api/health`, {\n                        method: \"GET\",\n                        mode: \"no-cors\",\n                        cache: \"no-cache\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        }\n                    });\n                    updatedApps[i].status = \"online\";\n                } catch (error) {\n                    updatedApps[i].status = \"offline\";\n                }\n            }\n            setApps(updatedApps);\n        };\n        checkAppStatus();\n        const interval = setInterval(checkAppStatus, 30000); // Check every 30 seconds\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"Arial, sans-serif\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"ISA Suite Hub - Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"ISA Suite Integration Hub Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    marginBottom: \"30px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"2.5rem\",\n                            color: \"#333\",\n                            marginBottom: \"10px\"\n                        },\n                        children: \"ISA Suite Hub\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: \"1.2rem\",\n                            color: \"#666\",\n                            maxWidth: \"800px\",\n                            textAlign: \"center\"\n                        },\n                        children: \"Welcome to the Integrated Systems Administration Suite - Your central dashboard for manufacturing operations management\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fill, minmax(300px, 1fr))\",\n                    gap: \"20px\",\n                    maxWidth: \"1200px\",\n                    margin: \"0 auto\"\n                },\n                children: apps.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: app.url,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        style: {\n                            textDecoration: \"none\",\n                            color: \"inherit\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                border: \"1px solid #e0e0e0\",\n                                borderRadius: \"8px\",\n                                padding: \"20px\",\n                                transition: \"transform 0.2s, box-shadow 0.2s\",\n                                height: \"100%\",\n                                boxShadow: \"0 2px 5px rgba(0,0,0,0.1)\",\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                backgroundColor: \"#fff\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"5px\",\n                                        fontSize: \"3rem\",\n                                        textAlign: \"center\"\n                                    },\n                                    children: app.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: \"1.3rem\",\n                                        margin: \"10px 0\",\n                                        color: \"#333\",\n                                        textAlign: \"center\"\n                                    },\n                                    children: app.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        margin: \"0 0 15px 0\",\n                                        color: \"#666\",\n                                        flexGrow: 1,\n                                        fontSize: \"0.9rem\",\n                                        textAlign: \"center\"\n                                    },\n                                    children: app.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        marginTop: \"auto\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                display: \"inline-block\",\n                                                width: \"10px\",\n                                                height: \"10px\",\n                                                borderRadius: \"50%\",\n                                                backgroundColor: app.status === \"online\" ? \"#28a745\" : app.status === \"offline\" ? \"#dc3545\" : \"#ffc107\",\n                                                marginRight: \"5px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"0.8rem\",\n                                                color: app.status === \"online\" ? \"#28a745\" : app.status === \"offline\" ? \"#dc3545\" : \"#ffc107\"\n                                            },\n                                            children: app.status === \"online\" ? \"Online\" : app.status === \"offline\" ? \"Offline\" : \"Loading\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, undefined)\n                    }, app.id, false, {\n                        fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                style: {\n                    marginTop: \"50px\",\n                    textAlign: \"center\",\n                    color: \"#666\",\n                    fontSize: \"0.8rem\",\n                    padding: \"20px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"ISA Suite v1.0.0 \\xa9 2025\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\ISASUITE\\\\apps\\\\hub\\\\pages\\\\index.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f","vendor-chunks/@swc+helpers@0.5.2"], () => (__webpack_exec__("../../node_modules/.pnpm/next@14.1.0_@babel+core@7.2_737465f182756f719c273dd9e085df4f/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();