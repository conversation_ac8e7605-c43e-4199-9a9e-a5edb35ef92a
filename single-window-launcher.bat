@echo off
setlocal enabledelayedexpansion

REM Set PATH to include PortableNodeJS
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Check if the ISASUITE directory exists
if not exist "C:\ISASUITE" (
    echo ERROR: C:\ISASUITE directory not found.
    echo Please make sure the ISA Suite is installed correctly.
    pause
    exit /b 1
)

REM Create a log directory if it doesn't exist
if not exist "C:\ISASUITE\logs" mkdir C:\ISASUITE\logs

REM Define colors for console output
set "GREEN=0A"
set "YELLOW=0E"
set "RED=0C"
set "WHITE=0F"
set "CYAN=0B"
set "MAGENTA=0D"

REM Define application information - name|folder|port|start_command|main_file
set "apps[1]=Integration Hub|hub|8000|pnpm dev -- -p 8000|next.js"
set "apps[2]=Business Management System|BMS|3001|pnpm start|index.js"
set "apps[3]=Materials Requirements Planning|MRP|3002|pnpm start|index.js"
set "apps[4]=Customer Relationship Management|CRM|3003|pnpm start|index.js"
set "apps[5]=Warehouse Management System|WMS|3004|pnpm start|index.js"
set "apps[6]=Advanced Planning and Scheduling|APS|3005|pnpm start|index.js"
set "apps[7]=Asset Performance Management|APM|3006|pnpm start|index.js"
set "apps[8]=Project Management System|PMS|3007|pnpm start|server.js"
set "apps[9]=Supply Chain Management|SCM|3008|pnpm start|server.js"
set "apps[10]=Task Management System|TM|3009|pnpm start|server.js"

REM Select application mode
cls
color %WHITE%
echo ===================================
color %CYAN%
echo    ISA Suite - Single Window Launcher
color %WHITE%
echo ===================================
echo.
echo Select application mode:
echo.
color %GREEN%
echo  1. Production Mode
color %YELLOW%
echo  2. Sandbox/Training Mode
color %MAGENTA%
echo  3. Demo Mode
color %WHITE%
echo.
set /p mode_choice=Enter your choice (1-3):

if "%mode_choice%"=="1" (
    set "APP_MODE=production"
    set "MODE_COLOR=%GREEN%"
    set "MODE_NAME=Production"
) else if "%mode_choice%"=="2" (
    set "APP_MODE=sandbox"
    set "MODE_COLOR=%YELLOW%"
    set "MODE_NAME=Sandbox/Training"
) else if "%mode_choice%"=="3" (
    set "APP_MODE=demo"
    set "MODE_COLOR=%MAGENTA%"
    set "MODE_NAME=Demo"
) else (
    set "APP_MODE=production"
    set "MODE_COLOR=%GREEN%"
    set "MODE_NAME=Production"
    echo Invalid choice. Using default Production mode.
    timeout /t 2 > nul
)

cls
color %MODE_COLOR%
echo ===================================
echo    ISA Suite - %MODE_NAME% Mode
echo ===================================
color %WHITE%
echo.
echo Starting all ISA Suite applications in a single window...
echo.

REM Create logs directory if it doesn't exist
if not exist "C:\ISASUITE\logs" mkdir "C:\ISASUITE\logs"

REM Create a log file for this session
set "LOG_FILE=C:\ISASUITE\logs\isasuite-%APP_MODE%-%DATE:~-4,4%%DATE:~-7,2%%DATE:~-10,2%.log"
echo ISA Suite started in %MODE_NAME% mode at %TIME% on %DATE% > "%LOG_FILE%"
echo. >> "%LOG_FILE%"

REM Start all applications in the background
echo Starting applications...
echo.

REM Set environment variable for all applications
set "NODE_ENV=%APP_MODE%"

REM Clean up any existing Node processes to avoid port conflicts
echo Cleaning up any existing Node processes...
taskkill /F /IM node.exe > nul 2>&1

REM Install common dependencies in the shared features
echo Installing SharedFeatures dependencies...
cd C:\ISASUITE\SharedFeatures
call pnpm install >> "%LOG_FILE%" 2>&1
cd C:\ISASUITE

REM Start each application in the background (no separate windows)
for /L %%i in (1,1,10) do (
    for /F "tokens=1-5 delims=|" %%a in ("!apps[%%i]!") do (
        echo Starting %%a...

        REM Start the application in the background without opening a new window
        cd /d C:\ISASUITE\apps\%%b

        REM Install dependencies if needed
        if not exist node_modules\. (
            echo Installing dependencies for %%a...
            call pnpm install >> "%LOG_FILE%" 2>&1
        )

        REM Special handling for specific applications
        if "%%b"=="WMS" (
            echo Starting WMS with direct node execution...
            start /b cmd /c "cd /d C:\ISASUITE\apps\%%b && set NODE_ENV=%APP_MODE% && node server.js > C:\ISASUITE\logs\%%b.log 2>&1"
        ) else if "%%b"=="PMS" (
            echo Starting PMS with direct node execution...
            start /b cmd /c "cd /d C:\ISASUITE\apps\%%b && set NODE_ENV=%APP_MODE% && node server.js > C:\ISASUITE\logs\%%b.log 2>&1"
        ) else if "%%b"=="TM" (
            echo Starting TM with direct node execution...
            REM Install node-fetch if it doesn't exist
            if not exist C:\ISASUITE\apps\TM\node_modules\node-fetch (
                echo Installing node-fetch for TM...
                cd /d C:\ISASUITE\apps\TM && call npm install node-fetch@2.6.7 cors express --save
            )
            start /b cmd /c "cd /d C:\ISASUITE\apps\%%b && set NODE_ENV=%APP_MODE% && node server.js > C:\ISASUITE\logs\%%b.log 2>&1"
        ) else if "%%b"=="SCM" (
            echo Starting SCM with direct node execution...
            REM Install node-fetch if it doesn't exist
            if not exist C:\ISASUITE\apps\SCM\node_modules\node-fetch (
                echo Installing node-fetch for SCM...
                cd /d C:\ISASUITE\apps\SCM && call npm install node-fetch@2.6.7 cors express --save
            )
            start /b cmd /c "cd /d C:\ISASUITE\apps\%%b && set NODE_ENV=%APP_MODE% && node server.js > C:\ISASUITE\logs\%%b.log 2>&1"
        ) else (
            REM Start the application in the background using pnpm
            echo Starting %%a in background...
            start /b cmd /c "cd /d C:\ISASUITE\apps\%%b && set NODE_ENV=%APP_MODE% && call pnpm %%d > C:\ISASUITE\logs\%%b.log 2>&1"
        )

        REM Log the startup
        echo %%a started on port %%c. >> "%LOG_FILE%"
        echo %%a started on port %%c.

        REM Small delay between starting apps to avoid resource contention
        timeout /t 2 > nul
    )
)

echo.
echo All applications started successfully in %MODE_NAME% mode.
echo.
echo Application URLs:
echo Integration Hub: http://localhost:8000
echo Business Management System: http://localhost:3001
echo Materials Requirements Planning: http://localhost:3002
echo Customer Relationship Management: http://localhost:3003
echo Warehouse Management System: http://localhost:3004
echo Advanced Planning and Scheduling: http://localhost:3005
echo Asset Performance Management: http://localhost:3006
echo Project Management System: http://localhost:3007
echo Supply Chain Management: http://localhost:3008
echo Task Management System: http://localhost:3009
echo.
echo All application logs are saved in %LOG_FILE%
echo.

REM Open Integration Hub in browser
echo Opening Integration Hub in browser...
start http://localhost:8000

echo.
echo Press any key to stop all applications and exit.
pause > nul

REM Stop all applications
echo.
echo Stopping all applications...
taskkill /F /IM node.exe > nul 2>&1
echo All applications stopped.
echo.
echo Thank you for using ISA Suite.
timeout /t 3 > nul
