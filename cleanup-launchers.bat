@echo off
echo ===================================
echo    ISA Suite - Cleaning Up Redundant Files
echo ===================================
echo.

echo The following files will be deleted as they are redundant or obsolete:
echo.
echo - fix-and-start-apps.bat (replaced by single-window-launcher.bat)
echo - force-clean-and-launch.bat (redundant with single-window-launcher.bat)
echo - ice-systems-launcher.bat (redundant)
echo - open-apps.bat (redundant with single-window-launcher.bat)
echo - simple-launch.bat (redundant with single-window-launcher.bat)
echo - start-and-open.bat (redundant with single-window-launcher.bat)
echo - start-apps-consolidated.bat (redundant with single-window-launcher.bat)
echo - start-apps.bat (redundant with single-window-launcher.bat)
echo - start.bat (redundant with app-specific launchers)
echo.

set /p confirm=Do you want to proceed with cleanup? (Y/N): 

if /i "%confirm%" == "Y" (
    echo.
    echo Deleting redundant launcher files...
    
    if exist fix-and-start-apps.bat del fix-and-start-apps.bat
    if exist force-clean-and-launch.bat del force-clean-and-launch.bat
    if exist ice-systems-launcher.bat del ice-systems-launcher.bat
    if exist open-apps.bat del open-apps.bat
    if exist simple-launch.bat del simple-launch.bat
    if exist start-and-open.bat del start-and-open.bat
    if exist start-apps-consolidated.bat del start-apps-consolidated.bat
    if exist start-apps.bat del start-apps.bat
    if exist start.bat del start.bat
    
    echo.
    echo Cleanup complete. Your workspace now has a cleaner structure with standardized launchers.
    echo Please use single-window-launcher.bat as your primary launcher.
) else (
    echo.
    echo Cleanup canceled. No files were deleted.
)

echo.
pause