// Task Management System - Main Entry Point

const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const app = express();
const port = 3009;

// Import shared features if available
try {
  const sharedFeatures = require('../../SharedFeatures');
  const auth = sharedFeatures.auth;
  const logger = sharedFeatures.logger.createLogger('TM');
} catch (err) {
  console.log('Shared features not available, continuing without them');
}

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Sample data
let tasks = [
  {
    id: 1,
    title: 'Implement User Authentication',
    description: 'Add user authentication to the system using OAuth 2.0',
    assignee: '<PERSON>',
    dueDate: '2024-05-20',
    status: 'In Progress',
    priority: 'High',
    progress: 60,
    category: 'Development'
  },
  {
    id: 2,
    title: 'Database Optimization',
    description: 'Optimize database queries to improve performance',
    assignee: '<PERSON>',
    dueDate: '2024-05-25',
    status: 'Planned',
    priority: 'Medium',
    progress: 0,
    category: 'Database'
  },
  {
    id: 3,
    title: 'UI/UX Redesign',
    description: 'Redesign the user interface for better user experience',
    assignee: '<PERSON>',
    dueDate: '2024-05-30',
    status: 'Completed',
    priority: 'Low',
    progress: 100,
    category: 'Design'
  },
  {
    id: 4,
    title: 'Order new inventory supplies',
    description: 'Place orders for new inventory items that are running low',
    assignee: 'Sarah Williams',
    dueDate: '2024-05-22',
    status: 'In Progress',
    priority: 'High',
    progress: 30,
    category: 'Supply Chain'
  },
  {
    id: 5,
    title: 'Review supplier contracts',
    description: 'Review and renew contracts with key suppliers',
    assignee: 'Robert Brown',
    dueDate: '2024-05-28',
    status: 'Planned',
    priority: 'Medium',
    progress: 0,
    category: 'Supply Chain'
  },
  {
    id: 6,
    title: 'Update inventory tracking system',
    description: 'Implement updates to the inventory tracking system',
    assignee: 'Jennifer Davis',
    dueDate: '2024-06-05',
    status: 'Planned',
    priority: 'High',
    progress: 0,
    category: 'Supply Chain'
  },
  {
    id: 7,
    title: 'Optimize warehouse layout',
    description: 'Redesign warehouse layout for better efficiency',
    assignee: 'Michael Wilson',
    dueDate: '2024-06-10',
    status: 'Planned',
    priority: 'Medium',
    progress: 0,
    category: 'Supply Chain'
  },
  {
    id: 8,
    title: 'Implement cross-docking procedures',
    description: 'Develop and implement cross-docking procedures to reduce storage time',
    assignee: 'Sarah Williams',
    dueDate: '2024-06-15',
    status: 'Planned',
    priority: 'Medium',
    progress: 0,
    category: 'Supply Chain'
  }
];

let teamMembers = [
  { id: 1, name: 'John Doe', role: 'Backend Developer', tasks: 3, completed: 2 },
  { id: 2, name: 'Jane Smith', role: 'Database Engineer', tasks: 2, completed: 1 },
  { id: 3, name: 'Mike Johnson', role: 'Frontend Developer', tasks: 4, completed: 3 },
  { id: 4, name: 'Sarah Williams', role: 'Supply Chain Manager', tasks: 5, completed: 2 },
  { id: 5, name: 'Robert Brown', role: 'Procurement Specialist', tasks: 3, completed: 1 },
  { id: 6, name: 'Jennifer Davis', role: 'Inventory Manager', tasks: 4, completed: 2 },
  { id: 7, name: 'Michael Wilson', role: 'Warehouse Manager', tasks: 3, completed: 1 },
];

// Define routes
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// Health check endpoint for integration with the hub
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API endpoint for dashboard data
app.get('/api/dashboard', (req, res) => {
  // Simulate dashboard data
  const dashboardData = {
    metrics: {
      activeTasks: tasks.length,
      overallProgress: Math.round(tasks.reduce((sum, task) => sum + task.progress, 0) / tasks.length),
      teamMembers: teamMembers.length
    },
    tasks: tasks,
    teamMembers: teamMembers,
    tasksByStatus: {
      labels: ['Planned', 'In Progress', 'Completed'],
      data: [
        tasks.filter(task => task.status === 'Planned').length,
        tasks.filter(task => task.status === 'In Progress').length,
        tasks.filter(task => task.status === 'Completed').length
      ]
    },
    tasksByPriority: {
      labels: ['High', 'Medium', 'Low'],
      data: [
        tasks.filter(task => task.priority === 'High').length,
        tasks.filter(task => task.priority === 'Medium').length,
        tasks.filter(task => task.priority === 'Low').length
      ]
    },
    progressTrend: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
      data: [25, 40, 45, 60, 75, 85]
    }
  };

  res.json({ status: 'success', data: dashboardData });
});

// API endpoint for tasks data
app.get('/api/tasks', (req, res) => {
  // Check if category filter is provided
  const category = req.query.category;

  if (category) {
    // Filter tasks by category
    const filteredTasks = tasks.filter(task =>
      task.category && task.category.toLowerCase() === category.toLowerCase()
    );
    res.json({ status: 'success', data: { tasks: filteredTasks } });
  } else {
    // Return all tasks
    res.json({ status: 'success', data: { tasks } });
  }
});

// API endpoint for a specific task
app.get('/api/tasks/:id', (req, res) => {
  const taskId = parseInt(req.params.id);
  const task = tasks.find(t => t.id === taskId);

  if (!task) {
    return res.status(404).json({ status: 'error', message: 'Task not found' });
  }

  res.json({ status: 'success', data: { task } });
});

// API endpoint for team members data
app.get('/api/team', (req, res) => {
  res.json({ status: 'success', data: { teamMembers } });
});

// API endpoint for team members by role
app.get('/api/team/by-role', (req, res) => {
  const role = req.query.role;

  if (role) {
    // Filter team members by role
    const filteredMembers = teamMembers.filter(member =>
      member.role && member.role.toLowerCase().includes(role.toLowerCase())
    );
    res.json({ status: 'success', data: { teamMembers: filteredMembers } });
  } else {
    // Return all team members
    res.json({ status: 'success', data: { teamMembers } });
  }
});

// API endpoint for legacy HTML page (for backward compatibility)
app.get('/legacy', (req, res) => {
  // Create a simple HTML page for the dashboard
  const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Task Manager</title>
                <style>
                    body { font-family: Arial; margin: 20px; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .dashboard-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }
                    .card {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        padding: 20px;
                        margin-bottom: 20px;
                    }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
                    th { background: #f4f4f4; }
                    .actions { margin: 20px 0; }
                    button { padding: 10px; margin: 5px; cursor: pointer; }
                    .status {
                        padding: 5px 10px;
                        border-radius: 3px;
                        color: white;
                        font-weight: bold;
                    }
                    .in-progress { background: #ffc107; }
                    .planned { background: #17a2b8; }
                    .completed { background: #28a745; }
                    .metrics {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 20px;
                        margin-bottom: 20px;
                    }
                    .metric-card {
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 8px;
                        text-align: center;
                    }
                    .metric-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: #007bff;
                    }
                    .priority {
                        padding: 3px 8px;
                        border-radius: 3px;
                        font-size: 0.9em;
                    }
                    .high { background: #dc3545; color: white; }
                    .medium { background: #ffc107; color: black; }
                    .low { background: #28a745; color: white; }
                    .progress-bar {
                        width: 100%;
                        background: #f0f0f0;
                        border-radius: 5px;
                        margin: 5px 0;
                    }
                    .progress {
                        height: 20px;
                        background: #007bff;
                        border-radius: 5px;
                        text-align: center;
                        line-height: 20px;
                        color: white;
                    }
                    .team-stats {
                        font-weight: bold;
                    }
                    .high-workload { color: #dc3545; }
                    .medium-workload { color: #ffc107; }
                    .low-workload { color: #28a745; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Task Manager</h1>
                    <div class="actions">
                        <button onclick="window.location.href='http://localhost:8000'">Back to Hub</button>
                    </div>

                    <div class="metrics">
                        <div class="metric-card">
                            <div class="metric-value">3</div>
                            <div>Active Tasks</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">53%</div>
                            <div>Overall Progress</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">3</div>
                            <div>Team Members</div>
                        </div>
                    </div>

                    <div class="dashboard-grid">
                        <div class="card">
                            <h2>Task List</h2>
                            <table>
                                <tr>
                                    <th>Title</th>
                                    <th>Assignee</th>
                                    <th>Due Date</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Progress</th>
                                </tr>
                                ${tasks
                                  .map(
                                    (task) => `
                                    <tr>
                                        <td>${task.title}</td>
                                        <td>${task.assignee}</td>
                                        <td>${task.dueDate}</td>
                                        <td>
                                            <span class="status ${task.status.toLowerCase().replace(' ', '-')}">
                                                ${task.status}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="priority ${task.priority.toLowerCase()}">
                                                ${task.priority}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="progress-bar">
                                                <div class="progress" style="width: ${task.progress}%">
                                                    ${task.progress}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>

                        <div class="card">
                            <h2>Team Overview</h2>
                            <table>
                                <tr>
                                    <th>Name</th>
                                    <th>Role</th>
                                    <th>Tasks</th>
                                    <th>Completed</th>
                                </tr>
                                ${teamMembers
                                  .map(
                                    (member) => `
                                    <tr>
                                        <td>${member.name}</td>
                                        <td>${member.role}</td>
                                        <td>
                                            <span class="team-stats ${member.tasks > 3 ? 'high-workload' : member.tasks > 2 ? 'medium-workload' : 'low-workload'}">
                                                ${member.tasks}
                                            </span>
                                        </td>
                                        <td>${member.completed}</td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `;
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end(html);
});

// Create an index.html file in the public directory
app.use((req, res) => {
  res.status(404).send('Not found');
});

// Start the server
app.listen(port, () => {
  console.log(`Task Management System running on http://localhost:${port}`);
});
