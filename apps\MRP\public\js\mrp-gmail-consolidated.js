/**
 * MRP Gmail Consolidated
 * This file is kept minimal to avoid conflicts with the direct implementation in index.html
 */

console.log('MRP Gmail consolidated script loaded - using direct implementation in index.html');

// Make the openEmail function globally available if needed
window.openMrpGmailModal = function() {
    console.log('Using direct implementation in index.html');
    
    // Show the Gmail modal
    const gmailModal = document.getElementById('mrp-gmailModal');
    if (gmailModal) {
        const modal = new bootstrap.Modal(gmailModal);
        modal.show();
        
        // Initialize sort, filter and attachment functionality
        if (typeof setupGmailSortAndFilter === 'function') {
            setupGmailSortAndFilter();
        } else {
            console.error('setupGmailSortAndFilter function not found');
        }
    } else {
        console.error('Gmail modal not found');
    }
};

/**
 * Set up Gmail sort and filter functionality
 * This function is called from index.html when the Gmail modal is opened
 */
window.setupGmailSortAndFilter = function() {
    console.log('Setting up Gmail sort and filter functionality from consolidated.js');
    
    // Wait a brief moment for the modal to fully render
    setTimeout(() => {
        // Set up sort functionality
        document.querySelectorAll('.dropdown-item[data-sort]').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const sortType = this.getAttribute('data-sort');
                console.log('Sort option clicked:', sortType);
                
                if (typeof sortEmails === 'function') {
                    sortEmails(sortType);
                    
                    // Update dropdown button text
                    const dropdownButton = this.closest('.dropdown').querySelector('.dropdown-toggle');
                    if (dropdownButton) {
                        dropdownButton.innerHTML = `<i class="bi bi-sort-down"></i> ${this.textContent}`;
                    }
                } else {
                    console.error('sortEmails function not found');
                }
            });
        });
        
        // Set up filter functionality
        document.querySelectorAll('.dropdown-item[data-filter]').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const filterType = this.getAttribute('data-filter');
                console.log('Filter option clicked:', filterType);
                
                if (typeof filterEmails === 'function') {
                    filterEmails(filterType);
                    
                    // Update dropdown button text
                    const dropdownButton = this.closest('.dropdown').querySelector('.dropdown-toggle');
                    if (dropdownButton) {
                        dropdownButton.innerHTML = `<i class="bi bi-funnel"></i> ${this.textContent.trim()}`;
                    }
                } else {
                    console.error('filterEmails function not found');
                }
            });
        });
        
        // Set up refresh button
        const refreshBtn = document.querySelector('#mrp-refresh-btn, [id$="refresh-btn"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                console.log('Refresh button clicked');
                
                // Reset search input
                const searchInput = document.querySelector('input[id$="search"]');
                if (searchInput) {
                    searchInput.value = '';
                }
                
                // Reset sort dropdown
                const sortDropdown = document.querySelector('[id$="sort-dropdown"]');
                if (sortDropdown) {
                    sortDropdown.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
                }
                
                // Reset filter dropdown
                const filterDropdown = document.querySelector('[id$="filter-dropdown"]');
                if (filterDropdown) {
                    filterDropdown.innerHTML = '<i class="bi bi-funnel"></i> Filter';
                }
                
                // Show all emails
                if (typeof showAllEmails === 'function') {
                    showAllEmails();
                    showToast('Emails refreshed successfully');
                } else {
                    console.error('showAllEmails function not found');
                }
            });
        }
        
        // Set up attachment handlers
        document.querySelectorAll('.view-btn, .download-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get the file name from the parent container
                const fileElement = this.closest('.d-flex');
                if (!fileElement) return;
                
                const fileName = fileElement.querySelector('strong')?.textContent || 'document';
                const action = button.classList.contains('view-btn') ? 'view' : 'download';
                console.log(`${action} button clicked for ${fileName}`);
                
                if (action === 'view' && typeof showFileViewer === 'function') {
                    showFileViewer(fileName);
                } else if (action === 'download' && typeof simulateFileDownload === 'function') {
                    simulateFileDownload(fileName);
                } else {
                    showToast(`${action === 'view' ? 'Viewing' : 'Downloading'} ${fileName}...`);
                }
            });
        });
        
        console.log('Gmail sort, filter and attachment functionality initialized');
    }, 100);
};
