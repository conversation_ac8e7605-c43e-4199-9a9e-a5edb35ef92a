// Google Drive Integration
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Drive files', { error: error.message }) : console.error('Failed to fetch Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration
app.get('/api/google-calendar/events', async (req, res) => {
  try {
    const startDate = new Date(req.query.startDate || Date.now());
    const endDate = new Date(req.query.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Calendar events', { error: error.message }) : console.error('Failed to fetch Google Calendar events', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/google-gmail/send', async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to send Gmail email', { error: error.message }) : console.error('Failed to send Gmail email', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Sheets Integration
app.get('/api/google-sheets/:spreadsheetId', async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Sheets data', { error: error.message }) : console.error('Failed to fetch Google Sheets data', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration
app.post('/api/google-docs/create', async (req, res) => {
  try {
    const { title } = req.body;
    const doc = await google.Docs.createDocument(title);
    res.json({ status: 'success', data: doc });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to create Google Doc', { error: error.message }) : console.error('Failed to create Google Doc', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google-contacts', async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Contacts', { error: error.message }) : console.error('Failed to fetch Google Contacts', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Maps Integration
app.get('/api/google-maps/geocode', async (req, res) => {
  try {
    const { address } = req.query;
    if (!address) {
      return res.status(400).json({ status: 'error', message: 'Address is required' });
    }
    const result = await google.Maps.geocode(address);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to geocode address', { error: error.message }) : console.error('Failed to geocode address', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

app.get('/api/google-maps/directions', async (req, res) => {
  try {
    const { origin, destination, mode } = req.query;
    if (!origin || !destination) {
      return res.status(400).json({ status: 'error', message: 'Origin and destination are required' });
    }
    const result = await google.Maps.getDirections(origin, destination, mode || 'driving');
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to get directions', { error: error.message }) : console.error('Failed to get directions', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Analytics Integration
app.get('/api/google-analytics/data', async (req, res) => {
  try {
    const { viewId, startDate, endDate, metrics, dimensions } = req.query;
    if (!viewId || !startDate || !endDate || !metrics) {
      return res.status(400).json({ status: 'error', message: 'ViewId, startDate, endDate, and metrics are required' });
    }

    const metricsArray = metrics.split(',');
    const dimensionsArray = dimensions ? dimensions.split(',') : [];

    const result = await google.Analytics.getData(viewId, startDate, endDate, metricsArray, dimensionsArray);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to get analytics data', { error: error.message }) : console.error('Failed to get analytics data', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Translate Integration
app.post('/api/google-translate', async (req, res) => {
  try {
    const { text, targetLanguage, sourceLanguage } = req.body;
    if (!text || !targetLanguage) {
      return res.status(400).json({ status: 'error', message: 'Text and targetLanguage are required' });
    }

    const result = await google.Translate.translateText(text, targetLanguage, sourceLanguage);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to translate text', { error: error.message }) : console.error('Failed to translate text', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});
