# Shared Gmail Integration for ISA Suite

This directory contains a shared implementation of Gmail functionality that can be used across all applications in the ISA Suite.

## Features

- Consistent modal structure and styling
- Email viewing, composing, replying, and forwarding
- Search, sort, and filter functionality
- Attachment handling
- Error handling and diagnostics
- Customizable per application (colors, prefixes, etc.)

## Files

- `gmail-integration.js` - The main Gmail integration class
- `gmail-integration.css` - Shared styles for Gmail functionality
- `gmail-implementation.js` - Helper script for easy implementation

## Implementation

To implement the shared Gmail integration in an application, add the following code to your HTML file:

```html
<!-- Shared Gmail Integration -->
<script src="../../SharedFeatures/ui/gmail-implementation.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Gmail integration with app-specific configuration
        initializeGmail({
            appName: 'YourAppName',
            appPrefix: 'your-app-prefix',
            modalId: 'gmailModal',
            primaryColor: '#your-app-color',
            debug: true // Set to false in production
        });
    });
</script>
```

## Configuration Options

The `initializeGmail` function accepts the following configuration options:

| Option | Type | Description | Default |
|--------|------|-------------|---------|
| `appName` | String | The name of the application | 'App' |
| `appPrefix` | String | Prefix for element IDs | '' |
| `modalId` | String | ID of the Gmail modal | 'gmailModal' |
| `primaryColor` | String | Primary color for styling | '#4285f4' |
| `secondaryColor` | String | Secondary color for styling | '#ea4335' |
| `debug` | Boolean | Enable debug logging | false |

## Modal Structure

The Gmail modal has the following structure:

- Modal header with title and close button
- Modal body with:
  - Sidebar (left column) containing:
    - Compose button
    - Folder links (Inbox, Starred, Sent, Drafts, Important)
    - Labels section
  - Main content (right column) containing:
    - Search, sort, and filter controls
    - Tabs (Inbox, Sent, Read, Compose)
    - Tab content
- Modal footer with close button and "Open in Gmail" link

## Email Functionality

The integration provides the following email functionality:

- View emails in the inbox
- Read emails in the read tab
- Compose new emails
- Reply to emails
- Forward emails
- Send emails
- Save drafts
- Discard drafts
- Attach files

## Customization

You can customize the appearance and behavior of the Gmail integration by:

1. Modifying the configuration options when initializing
2. Overriding the CSS styles in your application's stylesheet
3. Extending the `GmailIntegration` class for advanced customization

## Example Implementations

The Gmail integration has been implemented in the following applications:

- MRP (Materials Requirements Planning)
- BMS (Business Management System)
- CRM (Customer Relationship Management)

## Troubleshooting

If you encounter issues with the Gmail integration:

1. Check the browser console for error messages
2. Ensure the paths to the shared files are correct
3. Verify that the modal ID matches the one in your HTML
4. Make sure the application prefix is consistent throughout your code

## Future Enhancements

Planned enhancements for the Gmail integration:

- Real-time email notifications
- Offline support
- Advanced search capabilities
- Email templates
- Contact suggestions when composing
- Drag and drop for attachments
- Integration with other Google services
