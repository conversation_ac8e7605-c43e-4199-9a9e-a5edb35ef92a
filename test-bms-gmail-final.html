<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMS Gmail Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>BMS Gmail Functionality Test</h1>
    <p>This test will verify that the Gmail fixes are working properly in the BMS application.</p>

    <div class="test-section">
        <h2>Test 1: Check BMS Application Status</h2>
        <button onclick="testBMSStatus()">Test BMS Status</button>
        <div id="bms-status-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Verify Gmail Modal Elements</h2>
        <button onclick="testGmailModal()">Test Gmail Modal</button>
        <div id="gmail-modal-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Test Dashboard "View All Emails" Button</h2>
        <button onclick="testViewAllEmailsButton()">Test View All Emails</button>
        <div id="view-emails-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Test Sidebar Gmail Link</h2>
        <button onclick="testSidebarGmailLink()">Test Sidebar Gmail Link</button>
        <div id="sidebar-gmail-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 5: Check Enhanced Gmail Integration</h2>
        <button onclick="testEnhancedGmail()">Test Enhanced Gmail</button>
        <div id="enhanced-gmail-result"></div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function testBMSStatus() {
            try {
                const response = await fetch('http://localhost:3001');
                if (response.ok) {
                    showResult('bms-status-result', '✅ BMS application is running on localhost:3001', 'success');
                } else {
                    showResult('bms-status-result', '❌ BMS application returned status: ' + response.status, 'error');
                }
            } catch (error) {
                showResult('bms-status-result', '❌ BMS application is not accessible: ' + error.message, 'error');
            }
        }

        async function testGmailModal() {
            try {
                const response = await fetch('http://localhost:3001');
                const html = await response.text();
                
                const hasGmailModal = html.includes('id="gmailModal"');
                const hasViewAllEmailsLink = html.includes('data-bs-target="#gmailModal"');
                const hasCorrectModalConfig = html.includes("modalId: 'gmailModal'");
                
                let results = [];
                if (hasGmailModal) {
                    results.push('✅ Gmail modal with id="gmailModal" found');
                } else {
                    results.push('❌ Gmail modal with id="gmailModal" NOT found');
                }
                
                if (hasViewAllEmailsLink) {
                    results.push('✅ "View All Emails" button correctly targets #gmailModal');
                } else {
                    results.push('❌ "View All Emails" button does NOT target #gmailModal');
                }
                
                if (hasCorrectModalConfig) {
                    results.push('✅ Enhanced Gmail config has correct modalId');
                } else {
                    results.push('❌ Enhanced Gmail config has incorrect modalId');
                }
                
                const allPassed = hasGmailModal && hasViewAllEmailsLink && hasCorrectModalConfig;
                showResult('gmail-modal-result', results.join('<br>'), allPassed ? 'success' : 'error');
                
            } catch (error) {
                showResult('gmail-modal-result', '❌ Error testing Gmail modal: ' + error.message, 'error');
            }
        }

        async function testViewAllEmailsButton() {
            try {
                const response = await fetch('http://localhost:3001');
                const html = await response.text();
                
                // Check for the exact implementation
                const hasCorrectButton = html.includes('data-bs-toggle="modal" data-bs-target="#gmailModal"');
                const hasOldButton = html.includes('onclick="document.getElementById(\'gmail-link\').click();"');
                
                let results = [];
                if (hasCorrectButton) {
                    results.push('✅ "View All Emails" button uses correct Bootstrap modal trigger');
                } else {
                    results.push('❌ "View All Emails" button does NOT use Bootstrap modal trigger');
                }
                
                if (!hasOldButton) {
                    results.push('✅ Old onclick handler has been removed');
                } else {
                    results.push('❌ Old onclick handler is still present');
                }
                
                const allPassed = hasCorrectButton && !hasOldButton;
                showResult('view-emails-result', results.join('<br>'), allPassed ? 'success' : 'error');
                
            } catch (error) {
                showResult('view-emails-result', '❌ Error testing View All Emails button: ' + error.message, 'error');
            }
        }

        async function testSidebarGmailLink() {
            try {
                const response = await fetch('http://localhost:3001');
                const html = await response.text();
                
                const hasSidebarGmailLink = html.includes('id="gmail-link"');
                const hasCorrectTriggerConfig = html.includes("triggerId: 'gmail-link'");
                
                let results = [];
                if (hasSidebarGmailLink) {
                    results.push('✅ Sidebar Gmail link with id="gmail-link" found');
                } else {
                    results.push('❌ Sidebar Gmail link with id="gmail-link" NOT found');
                }
                
                if (hasCorrectTriggerConfig) {
                    results.push('✅ Enhanced Gmail config targets correct triggerId');
                } else {
                    results.push('❌ Enhanced Gmail config has incorrect triggerId');
                }
                
                const allPassed = hasSidebarGmailLink && hasCorrectTriggerConfig;
                showResult('sidebar-gmail-result', results.join('<br>'), allPassed ? 'success' : 'error');
                
            } catch (error) {
                showResult('sidebar-gmail-result', '❌ Error testing sidebar Gmail link: ' + error.message, 'error');
            }
        }

        async function testEnhancedGmail() {
            try {
                const response = await fetch('http://localhost:3001');
                const html = await response.text();
                
                const hasEnhancedGmail = html.includes('gmail-integration-enhanced.js');
                const hasNoDuplicateInit = !html.includes('// Initialize Gmail integration') || 
                                          (html.match(/Initialize Gmail integration/g) || []).length === 1;
                const hasCleanConfig = html.includes("modalId: 'gmailModal'") && 
                                      html.includes("triggerId: 'gmail-link'");
                
                let results = [];
                if (hasEnhancedGmail) {
                    results.push('✅ Enhanced Gmail integration script is loaded');
                } else {
                    results.push('❌ Enhanced Gmail integration script is NOT loaded');
                }
                
                if (hasNoDuplicateInit) {
                    results.push('✅ No duplicate Gmail initialization detected');
                } else {
                    results.push('❌ Duplicate Gmail initialization detected');
                }
                
                if (hasCleanConfig) {
                    results.push('✅ Enhanced Gmail configuration is correct');
                } else {
                    results.push('❌ Enhanced Gmail configuration has issues');
                }
                
                const allPassed = hasEnhancedGmail && hasNoDuplicateInit && hasCleanConfig;
                showResult('enhanced-gmail-result', results.join('<br>'), allPassed ? 'success' : 'error');
                
            } catch (error) {
                showResult('enhanced-gmail-result', '❌ Error testing enhanced Gmail: ' + error.message, 'error');
            }
        }

        // Auto-run tests when page loads
        window.addEventListener('load', function() {
            setTimeout(testBMSStatus, 500);
            setTimeout(testGmailModal, 1000);
            setTimeout(testViewAllEmailsButton, 1500);
            setTimeout(testSidebarGmailLink, 2000);
            setTimeout(testEnhancedGmail, 2500);
        });
    </script>
</body>
</html>
