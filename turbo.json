{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build", "^health"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "lint": {"outputs": []}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"], "env": ["NODE_ENV", "PORT", "APP_MODE"]}, "health": {"cache": false, "persistent": true, "outputs": ["health.json"], "env": ["HEALTH_CHECK_TIMEOUT", "HEALTH_CHECK_INTERVAL", "NODE_ENV"]}, "clean": {"cache": false}, "start:production": {"cache": false, "persistent": true, "dependsOn": ["^build", "health"]}, "start:development": {"cache": false, "persistent": true, "dependsOn": ["^build", "health"]}}}