/**
 * Google Integration UI Components
 * 
 * This module provides UI components for Google integration features.
 * These components can be used in any application to provide a consistent
 * user interface for Google services.
 */

// Google Drive UI Component
const GoogleDriveUI = `
<div class="google-integration-component google-drive-component">
  <div class="component-header">
    <h3><i class="fas fa-folder"></i> Google Drive</h3>
    <div class="component-actions">
      <button id="refresh-drive" class="btn btn-sm btn-outline-primary"><i class="fas fa-sync"></i></button>
      <button id="upload-drive" class="btn btn-sm btn-outline-success"><i class="fas fa-upload"></i></button>
    </div>
  </div>
  <div class="component-body">
    <div class="input-group mb-3">
      <input type="text" id="drive-search" class="form-control" placeholder="Search files...">
      <div class="input-group-append">
        <button id="drive-search-btn" class="btn btn-outline-secondary"><i class="fas fa-search"></i></button>
      </div>
    </div>
    <div id="drive-files-container" class="files-container">
      <div class="loading">Loading files...</div>
    </div>
  </div>
  <div class="component-footer">
    <a href="https://drive.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Drive</a>
  </div>
</div>
`;

// Google Calendar UI Component
const GoogleCalendarUI = `
<div class="google-integration-component google-calendar-component">
  <div class="component-header">
    <h3><i class="fas fa-calendar-alt"></i> Google Calendar</h3>
    <div class="component-actions">
      <button id="refresh-calendar" class="btn btn-sm btn-outline-primary"><i class="fas fa-sync"></i></button>
      <button id="add-event" class="btn btn-sm btn-outline-success"><i class="fas fa-plus"></i></button>
    </div>
  </div>
  <div class="component-body">
    <div class="date-selector mb-3">
      <button id="prev-week" class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-left"></i></button>
      <span id="date-range">This Week</span>
      <button id="next-week" class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-right"></i></button>
    </div>
    <div id="calendar-events-container" class="events-container">
      <div class="loading">Loading events...</div>
    </div>
  </div>
  <div class="component-footer">
    <a href="https://calendar.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Calendar</a>
  </div>
</div>
`;

// Google Gmail UI Component
const GoogleGmailUI = `
<div class="google-integration-component google-gmail-component">
  <div class="component-header">
    <h3><i class="fas fa-envelope"></i> Gmail</h3>
    <div class="component-actions">
      <button id="compose-email" class="btn btn-sm btn-outline-success"><i class="fas fa-pen"></i> Compose</button>
    </div>
  </div>
  <div class="component-body">
    <div id="email-compose-form" class="email-compose-form" style="display: none;">
      <div class="form-group">
        <input type="text" id="email-to" class="form-control" placeholder="To">
      </div>
      <div class="form-group">
        <input type="text" id="email-subject" class="form-control" placeholder="Subject">
      </div>
      <div class="form-group">
        <textarea id="email-body" class="form-control" rows="5" placeholder="Message"></textarea>
      </div>
      <div class="form-group">
        <button id="send-email" class="btn btn-primary">Send</button>
        <button id="cancel-email" class="btn btn-secondary">Cancel</button>
      </div>
    </div>
  </div>
  <div class="component-footer">
    <a href="https://mail.google.com" target="_blank" class="btn btn-link btn-sm">Open in Gmail</a>
  </div>
</div>
`;

// Google Sheets UI Component
const GoogleSheetsUI = `
<div class="google-integration-component google-sheets-component">
  <div class="component-header">
    <h3><i class="fas fa-table"></i> Google Sheets</h3>
    <div class="component-actions">
      <button id="refresh-sheets" class="btn btn-sm btn-outline-primary"><i class="fas fa-sync"></i></button>
      <button id="create-sheet" class="btn btn-sm btn-outline-success"><i class="fas fa-plus"></i></button>
    </div>
  </div>
  <div class="component-body">
    <div class="input-group mb-3">
      <input type="text" id="sheet-id" class="form-control" placeholder="Spreadsheet ID">
      <div class="input-group-append">
        <button id="load-sheet" class="btn btn-outline-secondary">Load</button>
      </div>
    </div>
    <div id="sheet-data-container" class="sheet-container">
      <div class="placeholder">Enter a spreadsheet ID and click Load</div>
    </div>
  </div>
  <div class="component-footer">
    <a href="https://sheets.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Sheets</a>
  </div>
</div>
`;

// Google Docs UI Component
const GoogleDocsUI = `
<div class="google-integration-component google-docs-component">
  <div class="component-header">
    <h3><i class="fas fa-file-alt"></i> Google Docs</h3>
    <div class="component-actions">
      <button id="refresh-docs" class="btn btn-sm btn-outline-primary"><i class="fas fa-sync"></i></button>
      <button id="create-doc" class="btn btn-sm btn-outline-success"><i class="fas fa-plus"></i></button>
    </div>
  </div>
  <div class="component-body">
    <div id="create-doc-form" class="create-doc-form" style="display: none;">
      <div class="form-group">
        <input type="text" id="doc-title" class="form-control" placeholder="Document Title">
      </div>
      <div class="form-group">
        <button id="confirm-create-doc" class="btn btn-primary">Create</button>
        <button id="cancel-create-doc" class="btn btn-secondary">Cancel</button>
      </div>
    </div>
    <div id="docs-container" class="docs-container">
      <div class="loading">Loading documents...</div>
    </div>
  </div>
  <div class="component-footer">
    <a href="https://docs.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Docs</a>
  </div>
</div>
`;

// Google Maps UI Component
const GoogleMapsUI = `
<div class="google-integration-component google-maps-component">
  <div class="component-header">
    <h3><i class="fas fa-map-marker-alt"></i> Google Maps</h3>
  </div>
  <div class="component-body">
    <div class="input-group mb-3">
      <input type="text" id="location-search" class="form-control" placeholder="Search location...">
      <div class="input-group-append">
        <button id="search-location" class="btn btn-outline-secondary"><i class="fas fa-search"></i></button>
      </div>
    </div>
    <div id="map-container" class="map-container" style="height: 300px;">
      <div class="placeholder">Search for a location to display the map</div>
    </div>
  </div>
  <div class="component-footer">
    <a href="https://maps.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Maps</a>
  </div>
</div>
`;

// Google Translate UI Component
const GoogleTranslateUI = `
<div class="google-integration-component google-translate-component">
  <div class="component-header">
    <h3><i class="fas fa-language"></i> Google Translate</h3>
  </div>
  <div class="component-body">
    <div class="row">
      <div class="col-md-5">
        <select id="source-language" class="form-control mb-2">
          <option value="">Detect language</option>
          <option value="en">English</option>
          <option value="es">Spanish</option>
          <option value="fr">French</option>
          <option value="de">German</option>
          <option value="it">Italian</option>
          <option value="ja">Japanese</option>
          <option value="ko">Korean</option>
          <option value="zh">Chinese</option>
        </select>
        <textarea id="source-text" class="form-control" rows="5" placeholder="Enter text to translate"></textarea>
      </div>
      <div class="col-md-2 text-center my-auto">
        <button id="translate-btn" class="btn btn-primary"><i class="fas fa-exchange-alt"></i></button>
      </div>
      <div class="col-md-5">
        <select id="target-language" class="form-control mb-2">
          <option value="en">English</option>
          <option value="es">Spanish</option>
          <option value="fr">French</option>
          <option value="de">German</option>
          <option value="it">Italian</option>
          <option value="ja">Japanese</option>
          <option value="ko">Korean</option>
          <option value="zh">Chinese</option>
        </select>
        <textarea id="target-text" class="form-control" rows="5" placeholder="Translation will appear here" readonly></textarea>
      </div>
    </div>
  </div>
  <div class="component-footer">
    <a href="https://translate.google.com" target="_blank" class="btn btn-link btn-sm">Open in Google Translate</a>
  </div>
</div>
`;

// CSS for Google Integration Components
const GoogleIntegrationCSS = `
<style>
  .google-integration-component {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .component-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .component-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }
  
  .component-body {
    padding: 15px;
    min-height: 100px;
  }
  
  .component-footer {
    padding: 10px 15px;
    border-top: 1px solid #eee;
    text-align: right;
  }
  
  .loading {
    text-align: center;
    color: #666;
    padding: 20px;
  }
  
  .placeholder {
    text-align: center;
    color: #999;
    padding: 20px;
  }
  
  .files-container, .events-container, .docs-container {
    max-height: 300px;
    overflow-y: auto;
  }
</style>
`;

// Export all components
module.exports = {
  GoogleDriveUI,
  GoogleCalendarUI,
  GoogleGmailUI,
  GoogleSheetsUI,
  GoogleDocsUI,
  GoogleMapsUI,
  GoogleTranslateUI,
  GoogleIntegrationCSS
};
