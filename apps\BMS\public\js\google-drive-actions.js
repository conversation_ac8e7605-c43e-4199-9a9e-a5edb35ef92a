/**
 * Google Drive Action Buttons Handler for BMS
 * Handles view, download, share, and delete actions for files in the Google Drive modal
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Google Drive Actions initialized');

    // Initialize action button handlers
    initializeDriveActionButtons();

    // Initialize search, sort, and filter functionality when modal is shown
    const driveModal = document.getElementById('driveModal');
    if (driveModal) {
        driveModal.addEventListener('shown.bs.modal', function() {
            console.log('Drive modal shown, initializing controls');
            initializeSearchSortFilter();
        });
    }

    // Also try to initialize immediately if modal exists
    initializeSearchSortFilter();

    // Initialize Gmail search when Gmail modal is shown
    const gmailModal = document.getElementById('gmailModal');
    if (gmailModal) {
        gmailModal.addEventListener('shown.bs.modal', function() {
            console.log('Gmail modal shown, initializing Gmail search');
            setTimeout(() => {
                initializeGmailSearch();
            }, 100);
        });
    }
});

/**
 * Initialize event listeners for all Google Drive action buttons
 */
function initializeDriveActionButtons() {
    // Use event delegation to handle dynamically added buttons
    document.addEventListener('click', function(event) {
        const target = event.target;
        
        // Check if the click is on an action button or its icon
        const button = target.closest('.btn-group .btn');
        if (!button) return;
        
        // Make sure we're in the drive modal, sheets modal, docs modal, or gmail modal
        const driveModal = button.closest('#driveModal');
        const sheetsModal = button.closest('#sheetsModal');
        const docsModal = button.closest('#docsModal');
        const gmailModal = button.closest('#gmailModal');
        if (!driveModal && !sheetsModal && !docsModal && !gmailModal) return;
        
        // Get the file information
        const row = button.closest('tr');
        const attachmentItem = button.closest('.attachment-item');

        // Handle Gmail attachments differently
        if (gmailModal && attachmentItem) {
            handleGmailAttachment(button, attachmentItem);
            return;
        }

        if (!row) return;
        
        const fileName = getFileNameFromRow(row);
        const fileType = getFileTypeFromRow(row);
        
        // Determine action based on button classes and icons
        const icon = button.querySelector('i');
        if (!icon) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        if (icon.classList.contains('bi-eye')) {
            handleViewAction(fileName, fileType);
        } else if (icon.classList.contains('bi-download')) {
            handleDownloadAction(fileName, fileType);
        } else if (icon.classList.contains('bi-share')) {
            handleShareAction(fileName, fileType);
        } else if (icon.classList.contains('bi-trash')) {
            handleDeleteAction(fileName, fileType);
        }
    });
}

/**
 * Extract file name from table row
 */
function getFileNameFromRow(row) {
    const nameCell = row.querySelector('td:first-child span');
    return nameCell ? nameCell.textContent.trim() : 'Unknown File';
}

/**
 * Extract file type from table row based on icon
 */
function getFileTypeFromRow(row) {
    const icon = row.querySelector('td:first-child i');
    if (!icon) return 'unknown';
    
    if (icon.classList.contains('bi-folder-fill')) return 'folder';
    if (icon.classList.contains('bi-file-earmark-pdf')) return 'pdf';
    if (icon.classList.contains('bi-file-earmark-spreadsheet')) return 'spreadsheet';
    if (icon.classList.contains('bi-file-earmark-text')) return 'document';
    if (icon.classList.contains('bi-file-earmark-slides')) return 'presentation';
    if (icon.classList.contains('bi-file-earmark-image')) return 'image';
    
    return 'file';
}

/**
 * Handle view action
 */
function handleViewAction(fileName, fileType) {
    console.log(`Viewing ${fileType}: ${fileName}`);
    
    // Create and show file preview modal
    showFilePreviewModal(fileName, fileType);
}

/**
 * Handle download action
 */
function handleDownloadAction(fileName, fileType) {
    console.log(`Downloading ${fileType}: ${fileName}`);
    
    // Show download confirmation
    showDownloadModal(fileName, fileType);
}

/**
 * Handle share action
 */
function handleShareAction(fileName, fileType) {
    console.log(`Sharing ${fileType}: ${fileName}`);
    
    // Show share modal
    showShareModal(fileName, fileType);
}

/**
 * Handle delete action
 */
function handleDeleteAction(fileName, fileType) {
    console.log(`Deleting ${fileType}: ${fileName}`);
    
    // Show delete confirmation
    showDeleteConfirmationModal(fileName, fileType);
}

/**
 * Show file preview modal
 */
function showFilePreviewModal(fileName, fileType) {
    const modal = createModal('filePreviewModal', 'File Preview', `
        <div class="text-center">
            <div class="mb-4">
                ${getFileIcon(fileType, 'display-1')}
            </div>
            <h5>${fileName}</h5>
            <p class="text-muted">Type: ${fileType.charAt(0).toUpperCase() + fileType.slice(1)}</p>
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                This is a preview. Click "Open in Google Drive" to view the full content.
            </div>
        </div>
    `, [
        { text: 'Close', class: 'btn-secondary', dismiss: true },
        { text: 'Open in Google Drive', class: 'btn-primary', action: () => window.open('https://drive.google.com', '_blank') }
    ]);

    // Check if any Google modal is open and set appropriate z-index
    const driveModal = document.getElementById('driveModal');
    const sheetsModal = document.getElementById('sheetsModal');
    const docsModal = document.getElementById('docsModal');
    const gmailModal = document.getElementById('gmailModal');

    const isGoogleModalOpen = (driveModal && driveModal.classList.contains('show')) ||
                              (sheetsModal && sheetsModal.classList.contains('show')) ||
                              (docsModal && docsModal.classList.contains('show')) ||
                              (gmailModal && gmailModal.classList.contains('show'));

    if (isGoogleModalOpen) {
        // Set high z-index for modal opened from Google modals
        modal.style.setProperty('z-index', '1400', 'important');
        console.log('File preview modal opened from Google modal - using elevated z-index');

        // Set backdrop z-index when modal is shown
        modal.addEventListener('shown.bs.modal', function() {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => {
                backdrop.style.setProperty('z-index', '1399', 'important');
            });
        });
    }

    showModal(modal);
}

/**
 * Show download modal
 */
function showDownloadModal(fileName, fileType) {
    const fileIcon = getFileIcon(fileType);
    const fileSize = getEstimatedFileSize(fileName);

    const modal = createModal('downloadModal', 'Download File', `
        <div class="text-center">
            <div class="mb-4">
                <i class="bi bi-download display-1 text-success"></i>
            </div>
            <div class="file-info mb-4">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="${fileIcon} me-2 fs-3"></i>
                    <h5 class="mb-0">${fileName}</h5>
                </div>
                <p class="text-muted">Size: ${fileSize}</p>
            </div>
            <div class="download-options">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio" name="downloadFormat" id="original" value="original" checked>
                    <label class="form-check-label" for="original">
                        Original format (${getFileExtension(fileName)})
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio" name="downloadFormat" id="pdf" value="pdf">
                    <label class="form-check-label" for="pdf">
                        PDF format
                    </label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="radio" name="downloadFormat" id="excel" value="excel">
                    <label class="form-check-label" for="excel">
                        Excel format (.xlsx)
                    </label>
                </div>
            </div>
            <p class="text-muted small">The file will be downloaded to your default download folder.</p>
        </div>
    `, [
        { text: 'Cancel', class: 'btn-secondary', dismiss: true },
        { text: 'Download', class: 'btn-success', action: () => {
            const selectedFormat = document.querySelector('input[name="downloadFormat"]:checked')?.value || 'original';
            performDownload(fileName, fileType, selectedFormat);
        }}
    ]);

    showModal(modal);
}

/**
 * Show share modal
 */
function showShareModal(fileName, fileType) {
    const fileIcon = getFileIcon(fileType);

    const modal = createModal('shareModal', 'Share File', `
        <div class="mb-4">
            <div class="d-flex align-items-center mb-3">
                <i class="bi bi-file-earmark-spreadsheet text-success me-3 fs-3"></i>
                <div>
                    <h5 class="mb-0">${fileName}</h5>
                    <small class="text-muted">${fileType.charAt(0).toUpperCase() + fileType.slice(1)}</small>
                </div>
            </div>
        </div>

        <!-- Share Link Section -->
        <div class="mb-4">
            <label class="form-label">Share Link</label>
            <div class="input-group">
                <input type="text" class="form-control" id="shareLink" value="https://sheets.google.com/share/${fileName.replace(/\s+/g, '-').toLowerCase()}" readonly>
                <button class="btn btn-outline-secondary" type="button" onclick="copyShareLink()">
                    <i class="bi bi-clipboard"></i> Copy
                </button>
            </div>
            <div class="form-text">Anyone with this link can view the file</div>
        </div>

        <!-- Share with Specific People -->
        <form id="shareForm">
            <h6 class="mb-3">Share with specific people</h6>
            <div class="mb-3">
                <label for="shareEmail" class="form-label">Email Address</label>
                <input type="email" class="form-control" id="shareEmail" placeholder="Enter email address">
            </div>
            <div class="mb-3">
                <label for="sharePermission" class="form-label">Permission Level</label>
                <select class="form-select" id="sharePermission">
                    <option value="view">Can view</option>
                    <option value="comment">Can comment</option>
                    <option value="edit">Can edit</option>
                </select>
            </div>
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="notifyPeople" checked>
                    <label class="form-check-label" for="notifyPeople">
                        Notify people via email
                    </label>
                </div>
            </div>
            <div class="mb-3">
                <label for="shareMessage" class="form-label">Message (optional)</label>
                <textarea class="form-control" id="shareMessage" rows="3" placeholder="Add a message..."></textarea>
            </div>
        </form>

        <!-- Current Sharing Status -->
        <div class="mt-4">
            <h6 class="mb-2">People with access</h6>
            <div class="list-group list-group-flush">
                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                    <div class="d-flex align-items-center">
                        <div class="avatar-circle bg-primary text-white me-2" style="width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-person"></i>
                        </div>
                        <div>
                            <div class="fw-bold">You</div>
                            <small class="text-muted">Owner</small>
                        </div>
                    </div>
                    <span class="badge bg-success">Owner</span>
                </div>
            </div>
        </div>
    `, [
        { text: 'Done', class: 'btn-secondary', dismiss: true },
        { text: 'Share', class: 'btn-info', action: () => {
            const email = document.getElementById('shareEmail').value;
            const permission = document.getElementById('sharePermission').value;
            const message = document.getElementById('shareMessage').value;
            const notify = document.getElementById('notifyPeople').checked;

            if (email) {
                performShare(fileName, fileType, email, permission, message, notify);
            } else {
                // If no email, just copy the link
                copyShareLink();
                return true;
            }
        }}
    ]);

    showModal(modal);
}

/**
 * Show delete confirmation modal
 */
function showDeleteConfirmationModal(fileName, fileType) {
    const modal = createModal('deleteModal', 'Delete File', `
        <div class="text-center">
            <div class="mb-4">
                <i class="bi bi-trash display-1 text-danger"></i>
            </div>
            <h5>Delete ${fileName}?</h5>
            <p class="text-muted">This action cannot be undone. The file will be moved to trash.</p>
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Are you sure you want to delete this ${fileType}?
            </div>
        </div>
    `, [
        { text: 'Cancel', class: 'btn-secondary', dismiss: true },
        { text: 'Delete', class: 'btn-danger', action: () => {
            // Simulate delete
            // Remove the row from the table
            const modal = document.getElementById('deleteModal');
            if (modal) {
                const driveModal = document.getElementById('driveModal');
                if (driveModal) {
                    const rows = driveModal.querySelectorAll('tbody tr');
                    rows.forEach(row => {
                        const rowFileName = getFileNameFromRow(row);
                        if (rowFileName === fileName) {
                            row.remove();
                        }
                    });
                }
            }
        }}
    ]);
    
    showModal(modal);
}

/**
 * Get appropriate icon for file type
 */
function getFileIcon(fileType, sizeClass = '') {
    const icons = {
        folder: `<i class="bi bi-folder-fill text-primary ${sizeClass}"></i>`,
        pdf: `<i class="bi bi-file-earmark-pdf text-danger ${sizeClass}"></i>`,
        spreadsheet: `<i class="bi bi-file-earmark-spreadsheet text-success ${sizeClass}"></i>`,
        document: `<i class="bi bi-file-earmark-text text-primary ${sizeClass}"></i>`,
        presentation: `<i class="bi bi-file-earmark-slides text-warning ${sizeClass}"></i>`,
        image: `<i class="bi bi-file-earmark-image text-info ${sizeClass}"></i>`,
        file: `<i class="bi bi-file-earmark text-secondary ${sizeClass}"></i>`
    };
    
    return icons[fileType] || icons.file;
}

/**
 * Create a modal element
 */
function createModal(id, title, body, buttons = []) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = id;
    modal.tabIndex = -1;
    
    const buttonsHtml = buttons.map(btn => 
        `<button type="button" class="btn ${btn.class}" ${btn.dismiss ? 'data-bs-dismiss="modal"' : ''}>
            ${btn.text}
        </button>`
    ).join('');
    
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${body}
                </div>
                <div class="modal-footer">
                    ${buttonsHtml}
                </div>
            </div>
        </div>
    `;
    
    // Add click handlers for action buttons
    buttons.forEach((btn, index) => {
        if (btn.action) {
            const buttonElement = modal.querySelectorAll('.modal-footer .btn')[index];
            buttonElement.addEventListener('click', () => {
                const result = btn.action();
                if (result !== false) {
                    const bootstrapModal = bootstrap.Modal.getInstance(modal);
                    if (bootstrapModal) {
                        bootstrapModal.hide();
                    }
                }
            });
        }
    });
    
    return modal;
}

/**
 * Show a modal
 */
function showModal(modal) {
    // Remove any existing modal with the same ID
    const existing = document.getElementById(modal.id);
    if (existing) {
        existing.remove();
    }
    
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    // Clean up when modal is hidden
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}



/**
 * Initialize search, sort, and filter functionality
 */
function initializeSearchSortFilter() {
    // Google Drive - Files tab search, sort, and filter
    initializeTabControls('files', 'drive-files-list');

    // Google Drive - Shared tab search, sort, and filter
    initializeTabControls('shared', 'shared-files-list');

    // Google Sheets - Search functionality
    initializeSheetsSearch();

    // Google Docs - Search functionality
    initializeDocsSearch();

    // Gmail - Search functionality
    initializeGmailSearch();
}

/**
 * Initialize controls for a specific tab
 */
function initializeTabControls(tabPrefix, tableBodyId) {
    const searchInput = document.getElementById(`${tabPrefix === 'files' ? 'file-search-drive' : 'shared-search'}`);
    const searchButton = document.getElementById(`${tabPrefix === 'files' ? 'search-files-btn' : 'search-shared-btn'}`);
    const sortSelect = document.getElementById(`${tabPrefix === 'files' ? 'file-sort' : 'shared-sort'}`);
    const filterSelect = document.getElementById(`${tabPrefix === 'files' ? 'file-filter' : 'shared-filter'}`);

    if (!searchInput || !sortSelect || !filterSelect) {
        console.warn(`Controls not found for tab: ${tabPrefix}`);
        return;
    }

    // Search functionality
    const performSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        filterAndSortTable(tableBodyId, searchTerm, sortSelect.value, filterSelect.value);
    };

    // Search on input change (real-time search)
    searchInput.addEventListener('input', performSearch);

    // Search on button click
    if (searchButton) {
        searchButton.addEventListener('click', performSearch);
    }

    // Search on Enter key
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // Sort functionality
    sortSelect.addEventListener('change', () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        filterAndSortTable(tableBodyId, searchTerm, sortSelect.value, filterSelect.value);
    });

    // Filter functionality
    filterSelect.addEventListener('change', () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        filterAndSortTable(tableBodyId, searchTerm, sortSelect.value, filterSelect.value);
    });
}

/**
 * Filter and sort table based on search term, sort option, and filter option
 */
function filterAndSortTable(tableBodyId, searchTerm, sortValue, filterValue) {
    const tableBody = document.getElementById(tableBodyId);
    if (!tableBody) {
        console.warn(`Table body not found: ${tableBodyId}`);
        return;
    }

    // Store original data if not already stored
    if (!tableBody.dataset.originalRows) {
        const originalRows = Array.from(tableBody.querySelectorAll('tr'));
        tableBody.dataset.originalRows = JSON.stringify(originalRows.map(row => row.outerHTML));
    }

    // If we need to reset, restore from original HTML and re-add to DOM
    if (tableBody.querySelectorAll('tr').length === 0 ||
        (searchTerm === '' && sortValue === 'name-asc' && filterValue === 'all')) {

        // Restore original content
        const originalRowsData = JSON.parse(tableBody.dataset.originalRows);
        tableBody.innerHTML = originalRowsData.join('');
    }

    // Now work with the current DOM rows
    const rows = Array.from(tableBody.querySelectorAll('tr'));

    // Filter rows
    const filteredRows = rows.filter(row => {
        const nameCell = row.querySelector('td:first-child span');
        const iconCell = row.querySelector('td:first-child i');

        if (!nameCell || !iconCell) return false;

        const fileName = nameCell.textContent.toLowerCase();
        const fileType = getFileTypeFromIcon(iconCell);

        // Apply search filter
        const matchesSearch = !searchTerm || fileName.includes(searchTerm);

        // Apply type filter
        const matchesFilter = filterValue === 'all' ||
                             (filterValue === 'folders' && fileType === 'folder') ||
                             (filterValue === 'documents' && ['document', 'pdf'].includes(fileType)) ||
                             (filterValue === 'spreadsheets' && fileType === 'spreadsheet') ||
                             (filterValue === 'presentations' && fileType === 'presentation') ||
                             (filterValue === 'images' && fileType === 'image') ||
                             (filterValue === 'pdfs' && fileType === 'pdf');

        return matchesSearch && matchesFilter;
    });

    // Sort rows
    filteredRows.sort((a, b) => {
        const aName = a.querySelector('td:first-child span')?.textContent || '';
        const bName = b.querySelector('td:first-child span')?.textContent || '';
        const aDate = a.querySelector('td:nth-child(2)')?.textContent || '';
        const bDate = b.querySelector('td:nth-child(2)')?.textContent || '';

        switch (sortValue) {
            case 'name-asc':
                return aName.localeCompare(bName);
            case 'name-desc':
                return bName.localeCompare(aName);
            case 'date-desc':
                return compareDates(aDate, bDate); // Fixed: newer dates first
            case 'date-asc':
                return compareDates(bDate, aDate); // Fixed: older dates first
            case 'size-desc':
                return getFileSize(bName) - getFileSize(aName); // Largest first
            case 'size-asc':
                return getFileSize(aName) - getFileSize(bName); // Smallest first
            case 'shared-desc':
            case 'shared-asc':
                // For shared files, sort by the shared date
                return sortValue === 'shared-desc' ? compareDates(aDate, bDate) : compareDates(bDate, aDate);
            default:
                return 0;
        }
    });

    // Clear table and add filtered/sorted rows
    tableBody.innerHTML = '';

    if (filteredRows.length === 0) {
        // Show no results message
        const noResultsRow = document.createElement('tr');
        noResultsRow.innerHTML = `
            <td colspan="3" class="text-center text-muted py-4">
                <i class="bi bi-search display-4 mb-2"></i>
                <p>No files found matching your criteria</p>
                <small>Try adjusting your search or filter settings</small>
            </td>
        `;
        tableBody.appendChild(noResultsRow);
    } else {
        filteredRows.forEach(row => tableBody.appendChild(row));
    }
}

/**
 * Get file type from icon classes
 */
function getFileTypeFromIcon(iconElement) {
    if (iconElement.classList.contains('bi-folder-fill')) return 'folder';
    if (iconElement.classList.contains('bi-file-earmark-pdf')) return 'pdf';
    if (iconElement.classList.contains('bi-file-earmark-spreadsheet')) return 'spreadsheet';
    if (iconElement.classList.contains('bi-file-earmark-text')) return 'document';
    if (iconElement.classList.contains('bi-file-earmark-slides')) return 'presentation';
    if (iconElement.classList.contains('bi-file-earmark-image')) return 'image';
    return 'file';
}

/**
 * Compare dates for sorting (simple text comparison for demo)
 */
function compareDates(dateA, dateB) {
    // Simple comparison - in a real app you'd parse actual dates
    const dateOrder = {
        'yesterday': 1,
        '2 days ago': 2,
        '3 days ago': 3,
        '1 week ago': 7,
        '2 weeks ago': 14
    };

    const aValue = dateOrder[dateA.toLowerCase()] || 999;
    const bValue = dateOrder[dateB.toLowerCase()] || 999;

    return aValue - bValue;
}

/**
 * Get estimated file size for sorting (demo purposes)
 */
function getFileSize(fileName) {
    // Simple file size estimation based on file type and name length
    const name = fileName.toLowerCase();
    let baseSize = name.length * 10; // Base size on name length

    if (name.includes('.pdf')) baseSize += 500;
    if (name.includes('.xlsx') || name.includes('.xls')) baseSize += 300;
    if (name.includes('.docx') || name.includes('.doc')) baseSize += 200;
    if (name.includes('.pptx') || name.includes('.ppt')) baseSize += 800;
    if (name.includes('report')) baseSize += 400;
    if (name.includes('budget')) baseSize += 600;
    if (name.includes('strategy')) baseSize += 350;

    return baseSize;
}

/**
 * Initialize Google Sheets search functionality
 */
function initializeSheetsSearch() {
    const searchInput = document.getElementById('sheets-search');
    const searchButton = document.getElementById('search-sheets-btn');
    const sortSelect = document.getElementById('sheets-sort');
    const filterSelect = document.getElementById('sheets-filter');

    if (!searchInput) {
        console.warn('Google Sheets search input not found');
        return;
    }

    // Search functionality for Google Sheets
    const performSheetsSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const sortValue = sortSelect?.value || 'name-asc';
        const filterValue = filterSelect?.value || 'all';
        filterAndSortSheetsTable(searchTerm, sortValue, filterValue);
    };

    // Search on input change (real-time search)
    searchInput.addEventListener('input', performSheetsSearch);

    // Search on button click
    if (searchButton) {
        searchButton.addEventListener('click', performSheetsSearch);
    }

    // Search on Enter key
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSheetsSearch();
        }
    });

    // Sort functionality
    if (sortSelect) {
        sortSelect.addEventListener('change', performSheetsSearch);
    }

    // Filter functionality
    if (filterSelect) {
        filterSelect.addEventListener('change', performSheetsSearch);
    }
}

/**
 * Filter and sort Google Sheets table based on search term, sort option, and filter option
 */
function filterAndSortSheetsTable(searchTerm, sortValue, filterValue) {
    const tableBody = document.getElementById('sheets-list');
    if (!tableBody) {
        console.warn('Google Sheets table body not found');
        return;
    }

    // Store original data if not already stored
    if (!tableBody.dataset.originalRows) {
        const originalRows = Array.from(tableBody.querySelectorAll('tr'));
        tableBody.dataset.originalRows = JSON.stringify(originalRows.map(row => row.outerHTML));
    }

    // If we need to reset, restore from original HTML and re-add to DOM
    if (tableBody.querySelectorAll('tr').length === 0 ||
        (searchTerm === '' && sortValue === 'name-asc' && filterValue === 'all')) {

        // Restore original content
        const originalRowsData = JSON.parse(tableBody.dataset.originalRows);
        tableBody.innerHTML = originalRowsData.join('');
    }

    // Now work with the current DOM rows
    const rows = Array.from(tableBody.querySelectorAll('tr'));

    // Filter rows
    const filteredRows = rows.filter(row => {
        const nameCell = row.querySelector('td:first-child span');
        const iconCell = row.querySelector('td:first-child i');

        if (!nameCell || !iconCell) return false;

        const fileName = nameCell.textContent.toLowerCase();
        const fileType = getSheetsFileType(iconCell);

        // Apply search filter
        const matchesSearch = !searchTerm || fileName.includes(searchTerm);

        // Apply type filter
        const matchesFilter = filterValue === 'all' ||
                             (filterValue === 'spreadsheets' && fileType === 'spreadsheet') ||
                             (filterValue === 'templates' && fileName.includes('template')) ||
                             (filterValue === 'shared' && fileName.includes('shared'));

        return matchesSearch && matchesFilter;
    });

    // Sort rows
    filteredRows.sort((a, b) => {
        const aName = a.querySelector('td:first-child span')?.textContent || '';
        const bName = b.querySelector('td:first-child span')?.textContent || '';
        const aDate = a.querySelector('td:nth-child(2)')?.textContent || '';
        const bDate = b.querySelector('td:nth-child(2)')?.textContent || '';

        switch (sortValue) {
            case 'name-asc':
                return aName.localeCompare(bName);
            case 'name-desc':
                return bName.localeCompare(aName);
            case 'date-desc':
                return parseDateString(bDate) - parseDateString(aDate);
            case 'date-asc':
                return parseDateString(aDate) - parseDateString(bDate);
            case 'size-desc':
                return getSheetsFileSize(bName) - getSheetsFileSize(aName);
            case 'size-asc':
                return getSheetsFileSize(aName) - getSheetsFileSize(bName);
            default:
                return 0;
        }
    });

    // Clear table and add filtered/sorted rows
    tableBody.innerHTML = '';

    if (filteredRows.length === 0) {
        // Show no results message
        const noResultsRow = document.createElement('tr');
        noResultsRow.innerHTML = `
            <td colspan="3" class="text-center text-muted py-4">
                <i class="bi bi-search display-4 mb-2"></i>
                <p>No sheets found matching your criteria</p>
                <small>Try adjusting your search or filter settings</small>
            </td>
        `;
        tableBody.appendChild(noResultsRow);
    } else {
        filteredRows.forEach(row => tableBody.appendChild(row));
    }
}

/**
 * Get file type from Google Sheets icon
 */
function getSheetsFileType(iconElement) {
    if (iconElement.classList.contains('bi-file-earmark-spreadsheet')) {
        return 'spreadsheet';
    }
    return 'unknown';
}

/**
 * Get estimated file size for Google Sheets files (for demo purposes)
 */
function getSheetsFileSize(name) {
    let baseSize = 1000; // Base size in KB

    // Add size based on file name characteristics
    if (name.includes('financial')) baseSize += 500;
    if (name.includes('budget')) baseSize += 300;
    if (name.includes('expense')) baseSize += 200;
    if (name.includes('tracking')) baseSize += 150;

    return baseSize;
}

/**
 * Get estimated file size for display
 */
function getEstimatedFileSize(fileName) {
    const name = fileName.toLowerCase();
    let sizeKB = 1000; // Base size

    if (name.includes('financial')) sizeKB += 500;
    if (name.includes('budget')) sizeKB += 300;
    if (name.includes('expense')) sizeKB += 200;
    if (name.includes('tracking')) sizeKB += 150;

    if (sizeKB > 1024) {
        return `${(sizeKB / 1024).toFixed(1)} MB`;
    }
    return `${sizeKB} KB`;
}

/**
 * Get file extension from filename
 */
function getFileExtension(fileName) {
    const parts = fileName.split('.');
    return parts.length > 1 ? `.${parts[parts.length - 1]}` : '.xlsx';
}

/**
 * Parse date string for sorting
 */
function parseDateString(dateStr) {
    const dateMap = {
        'yesterday': 1,
        '2 days ago': 2,
        '3 days ago': 3,
        '1 week ago': 7,
        '2 weeks ago': 14
    };

    return dateMap[dateStr.toLowerCase()] || 999;
}

/**
 * Perform download action
 */
function performDownload(fileName, fileType, format) {
    console.log(`Downloading ${fileName} as ${format}`);

    // Show download progress
    const progressModal = createModal('downloadProgressModal', 'Downloading...', `
        <div class="text-center">
            <div class="mb-3">
                <i class="bi bi-download display-4 text-success"></i>
            </div>
            <h5>Downloading ${fileName}</h5>
            <p class="text-muted">Format: ${format}</p>
            <div class="progress mb-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
            </div>
            <p class="small text-muted">Please wait while we prepare your download...</p>
        </div>
    `, []);

    showModal(progressModal);

    // Simulate download progress
    let progress = 0;
    const progressBar = progressModal.querySelector('.progress-bar');
    const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 100) progress = 100;

        progressBar.style.width = `${progress}%`;

        if (progress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(progressModal);
                if (modal) modal.hide();

                // Show success message
                showDownloadSuccess(fileName, format);
            }, 500);
        }
    }, 200);
}

/**
 * Show download success message
 */
function showDownloadSuccess(fileName, format) {
    const successModal = createModal('downloadSuccessModal', 'Download Complete', `
        <div class="text-center">
            <div class="mb-3">
                <i class="bi bi-check-circle display-4 text-success"></i>
            </div>
            <h5>Download Complete!</h5>
            <p class="text-muted">${fileName} has been downloaded as ${format} format.</p>
            <p class="small">Check your Downloads folder.</p>
        </div>
    `, [
        { text: 'Open Folder', class: 'btn-outline-primary', action: () => {
            // In a real app, this would open the downloads folder
            alert('Opening Downloads folder...');
        }},
        { text: 'Done', class: 'btn-success', dismiss: true }
    ]);

    showModal(successModal);
}

/**
 * Perform share action
 */
function performShare(fileName, fileType, email, permission, message, notify) {
    console.log(`Sharing ${fileName} with ${email} (${permission})`);

    // Show sharing progress
    const progressModal = createModal('shareProgressModal', 'Sharing...', `
        <div class="text-center">
            <div class="mb-3">
                <i class="bi bi-share display-4 text-info"></i>
            </div>
            <h5>Sharing ${fileName}</h5>
            <p class="text-muted">with ${email}</p>
            <div class="spinner-border text-info" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `, []);

    showModal(progressModal);

    // Simulate sharing process
    setTimeout(() => {
        const modal = bootstrap.Modal.getInstance(progressModal);
        if (modal) modal.hide();

        // Show success message
        showShareSuccess(fileName, email, permission, notify);
    }, 2000);
}

/**
 * Show share success message
 */
function showShareSuccess(fileName, email, permission, notify) {
    const successModal = createModal('shareSuccessModal', 'Shared Successfully', `
        <div class="text-center">
            <div class="mb-3">
                <i class="bi bi-check-circle display-4 text-success"></i>
            </div>
            <h5>File Shared!</h5>
            <p class="text-muted">${fileName} has been shared with ${email}</p>
            <p class="small">Permission level: <strong>${permission}</strong></p>
            ${notify ? '<p class="small text-info"><i class="bi bi-envelope"></i> Email notification sent</p>' : ''}
        </div>
    `, [
        { text: 'Share with Others', class: 'btn-outline-info', action: () => {
            // Reopen share modal
            showShareModal(fileName, 'spreadsheet');
        }},
        { text: 'Done', class: 'btn-success', dismiss: true }
    ]);

    showModal(successModal);
}

/**
 * Copy share link to clipboard
 */
function copyShareLink() {
    const shareLink = document.getElementById('shareLink');
    if (shareLink) {
        shareLink.select();
        shareLink.setSelectionRange(0, 99999); // For mobile devices

        try {
            document.execCommand('copy');

            // Show temporary success message
            const button = shareLink.parentElement.querySelector('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check"></i> Copied!';
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-success');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);

        } catch (err) {
            alert('Link copied to clipboard!');
        }
    }
}

/**
 * Initialize Google Docs search functionality
 */
function initializeDocsSearch() {
    const searchInput = document.getElementById('docs-search');
    const searchButton = document.getElementById('search-docs-btn');
    const sortSelect = document.getElementById('docs-sort');
    const filterSelect = document.getElementById('docs-filter');

    if (!searchInput) {
        console.warn('Google Docs search input not found');
        return;
    }

    // Search functionality for Google Docs
    const performDocsSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const sortValue = sortSelect?.value || 'name-asc';
        const filterValue = filterSelect?.value || 'all';
        filterAndSortDocsTable(searchTerm, sortValue, filterValue);
    };

    // Search on input change (real-time search)
    searchInput.addEventListener('input', performDocsSearch);

    // Search on button click
    if (searchButton) {
        searchButton.addEventListener('click', performDocsSearch);
    }

    // Search on Enter key
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performDocsSearch();
        }
    });

    // Sort functionality
    if (sortSelect) {
        sortSelect.addEventListener('change', performDocsSearch);
    }

    // Filter functionality
    if (filterSelect) {
        filterSelect.addEventListener('change', performDocsSearch);
    }
}

/**
 * Filter and sort Google Docs table based on search term, sort option, and filter option
 */
function filterAndSortDocsTable(searchTerm, sortValue, filterValue) {
    const tableBody = document.getElementById('docs-list');
    if (!tableBody) {
        console.warn('Google Docs table body not found');
        return;
    }

    // Store original data if not already stored
    if (!tableBody.dataset.originalRows) {
        const originalRows = Array.from(tableBody.querySelectorAll('tr'));
        tableBody.dataset.originalRows = JSON.stringify(originalRows.map(row => row.outerHTML));
    }

    // If we need to reset, restore from original HTML and re-add to DOM
    if (tableBody.querySelectorAll('tr').length === 0 ||
        (searchTerm === '' && sortValue === 'name-asc' && filterValue === 'all')) {

        // Restore original content
        const originalRowsData = JSON.parse(tableBody.dataset.originalRows);
        tableBody.innerHTML = originalRowsData.join('');
    }

    // Now work with the current DOM rows
    const rows = Array.from(tableBody.querySelectorAll('tr'));

    // Filter rows
    const filteredRows = rows.filter(row => {
        const nameCell = row.querySelector('td:first-child span');
        const iconCell = row.querySelector('td:first-child i');

        if (!nameCell || !iconCell) return false;

        const fileName = nameCell.textContent.toLowerCase();
        const fileType = getDocsFileType(iconCell);

        // Apply search filter
        const matchesSearch = !searchTerm || fileName.includes(searchTerm);

        // Apply type filter
        const matchesFilter = filterValue === 'all' ||
                             (filterValue === 'documents' && fileType === 'document') ||
                             (filterValue === 'templates' && fileName.includes('template')) ||
                             (filterValue === 'shared' && fileName.includes('shared')) ||
                             (filterValue === 'recent' && isRecentFile(row));

        return matchesSearch && matchesFilter;
    });

    // Sort rows
    filteredRows.sort((a, b) => {
        const aName = a.querySelector('td:first-child span')?.textContent || '';
        const bName = b.querySelector('td:first-child span')?.textContent || '';
        const aDate = a.querySelector('td:nth-child(2)')?.textContent || '';
        const bDate = b.querySelector('td:nth-child(2)')?.textContent || '';

        switch (sortValue) {
            case 'name-asc':
                return aName.localeCompare(bName);
            case 'name-desc':
                return bName.localeCompare(aName);
            case 'date-desc':
                return parseDateString(bDate) - parseDateString(aDate);
            case 'date-asc':
                return parseDateString(aDate) - parseDateString(bDate);
            case 'size-desc':
                return getDocsFileSize(bName) - getDocsFileSize(aName);
            case 'size-asc':
                return getDocsFileSize(aName) - getDocsFileSize(bName);
            default:
                return 0;
        }
    });

    // Clear table and add filtered/sorted rows
    tableBody.innerHTML = '';

    if (filteredRows.length === 0) {
        // Show no results message
        const noResultsRow = document.createElement('tr');
        noResultsRow.innerHTML = `
            <td colspan="3" class="text-center text-muted py-4">
                <i class="bi bi-search display-4 mb-2"></i>
                <p>No documents found matching your criteria</p>
                <small>Try adjusting your search or filter settings</small>
            </td>
        `;
        tableBody.appendChild(noResultsRow);
    } else {
        filteredRows.forEach(row => tableBody.appendChild(row));
    }
}

/**
 * Get file type from Google Docs icon
 */
function getDocsFileType(iconElement) {
    if (iconElement.classList.contains('bi-file-earmark-text')) {
        return 'document';
    }
    return 'unknown';
}

/**
 * Get estimated file size for Google Docs files (for demo purposes)
 */
function getDocsFileSize(name) {
    let baseSize = 800; // Base size in KB

    // Add size based on file name characteristics
    if (name.includes('business')) baseSize += 400;
    if (name.includes('marketing')) baseSize += 300;
    if (name.includes('annual')) baseSize += 600;
    if (name.includes('report')) baseSize += 200;
    if (name.includes('plan')) baseSize += 350;

    return baseSize;
}

/**
 * Check if file is recent (for demo purposes)
 */
function isRecentFile(row) {
    const dateCell = row.querySelector('td:nth-child(2)');
    if (!dateCell) return false;

    const dateText = dateCell.textContent.toLowerCase();
    return dateText.includes('yesterday') || dateText.includes('days ago');
}

/**
 * Initialize Gmail search functionality
 */
function initializeGmailSearch() {
    const searchInput = document.getElementById('email-search');
    const searchButton = document.getElementById('search-btn');
    const sortSelect = document.getElementById('email-sort');
    const filterSelect = document.getElementById('email-filter');

    if (!searchInput) {
        console.warn('Gmail search input not found');
        return;
    }

    // Search functionality for Gmail
    const performGmailSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const sortValue = sortSelect?.value || 'date-desc';
        const filterValue = filterSelect?.value || 'all';
        filterAndSortGmailEmails(searchTerm, sortValue, filterValue);
    };

    // Search on input change (real-time search)
    searchInput.addEventListener('input', performGmailSearch);

    // Search on button click
    if (searchButton) {
        searchButton.addEventListener('click', performGmailSearch);
    }

    // Search on Enter key
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performGmailSearch();
        }
    });

    // Sort functionality
    if (sortSelect) {
        sortSelect.addEventListener('change', performGmailSearch);
    }

    // Filter functionality
    if (filterSelect) {
        filterSelect.addEventListener('change', performGmailSearch);
    }
}

/**
 * Filter and sort Gmail emails based on search term, sort option, and filter option
 */
function filterAndSortGmailEmails(searchTerm, sortValue, filterValue) {
    const emailList = document.querySelector('#email-list .list-group');
    if (!emailList) {
        console.warn('Gmail email list not found');
        return;
    }

    // Store original data if not already stored
    if (!emailList.dataset.originalEmails) {
        const originalEmails = Array.from(emailList.querySelectorAll('.email-item'));
        emailList.dataset.originalEmails = JSON.stringify(originalEmails.map(email => email.outerHTML));
    }

    // If we need to reset, restore from original HTML and re-add to DOM
    if (emailList.querySelectorAll('.email-item').length === 0 ||
        (searchTerm === '' && sortValue === 'date-desc' && filterValue === 'all')) {

        // Restore original content
        const originalEmailsData = JSON.parse(emailList.dataset.originalEmails);
        emailList.innerHTML = originalEmailsData.join('');
    }

    // Now work with the current DOM emails
    const emails = Array.from(emailList.querySelectorAll('.email-item'));

    // Filter emails
    const filteredEmails = emails.filter(email => {
        const sender = email.getAttribute('data-sender')?.toLowerCase() || '';
        const subject = email.getAttribute('data-subject')?.toLowerCase() || '';
        const isRead = email.getAttribute('data-read') === 'true';

        // Apply search filter
        const matchesSearch = !searchTerm ||
                             sender.includes(searchTerm) ||
                             subject.includes(searchTerm);

        // Apply type filter
        let matchesFilter = true;
        switch (filterValue) {
            case 'unread':
                matchesFilter = !isRead;
                break;
            case 'read':
                matchesFilter = isRead;
                break;
            case 'important':
                matchesFilter = sender.includes('john davis');
                break;
            case 'starred':
                matchesFilter = sender.includes('david chen');
                break;
            default:
                matchesFilter = true;
        }

        return matchesSearch && matchesFilter;
    });

    // Sort emails
    filteredEmails.sort((a, b) => {
        const aDate = new Date(a.getAttribute('data-date') || '');
        const bDate = new Date(b.getAttribute('data-date') || '');
        const aSender = a.getAttribute('data-sender') || '';
        const bSender = b.getAttribute('data-sender') || '';
        const aSubject = a.getAttribute('data-subject') || '';
        const bSubject = b.getAttribute('data-subject') || '';

        switch (sortValue) {
            case 'date-desc':
                return bDate - aDate;
            case 'date-asc':
                return aDate - bDate;
            case 'sender-asc':
                return aSender.localeCompare(bSender);
            case 'sender-desc':
                return bSender.localeCompare(aSender);
            case 'subject-asc':
                return aSubject.localeCompare(bSubject);
            case 'subject-desc':
                return bSubject.localeCompare(aSubject);
            default:
                return 0;
        }
    });

    // Clear email list and add filtered/sorted emails
    emailList.innerHTML = '';

    if (filteredEmails.length === 0) {
        // Show no results message
        const noResultsDiv = document.createElement('div');
        noResultsDiv.className = 'list-group-item text-center text-muted py-4';
        noResultsDiv.innerHTML = `
            <i class="bi bi-search display-4 mb-2"></i>
            <p>No emails found matching your criteria</p>
            <small>Try adjusting your search or filter settings</small>
        `;
        emailList.appendChild(noResultsDiv);
    } else {
        filteredEmails.forEach(email => emailList.appendChild(email));
    }
}

/**
 * Handle Gmail attachment actions
 */
function handleGmailAttachment(button, attachmentItem) {
    // Get attachment information
    const fileNameElement = attachmentItem.querySelector('.fw-bold');
    const fileName = fileNameElement ? fileNameElement.textContent : 'Unknown File';

    // Determine file type from icon
    const iconElement = attachmentItem.querySelector('i');
    let fileType = 'unknown';
    if (iconElement) {
        if (iconElement.classList.contains('bi-file-earmark-pdf')) {
            fileType = 'pdf';
        } else if (iconElement.classList.contains('bi-file-earmark-excel')) {
            fileType = 'spreadsheet';
        } else if (iconElement.classList.contains('bi-file-earmark-image')) {
            fileType = 'image';
        } else if (iconElement.classList.contains('bi-file-earmark-word')) {
            fileType = 'document';
        }
    }

    // Determine action based on button type
    const buttonClasses = button.className;

    if (buttonClasses.includes('btn-outline-primary')) {
        // View action
        showFilePreviewModal(fileName, fileType);
    } else if (buttonClasses.includes('btn-outline-success')) {
        // Download action
        showDownloadModal(fileName, fileType);
    } else if (buttonClasses.includes('btn-outline-info')) {
        // Share action
        showShareModal(fileName, fileType);
    } else if (buttonClasses.includes('btn-outline-danger')) {
        // Delete action
        showDeleteConfirmationModal(fileName, fileType);
    }
}
