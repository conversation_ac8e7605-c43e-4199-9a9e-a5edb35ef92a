/**
 * Enhanced Gmail Implementation for MRP Application
 * Implements the BMS-style search, sort, and filter functionality
 */

// Configuration
const MRP_GMAIL_CONFIG = {
    // MRP color scheme
    colors: {
        primary: '#9c27b0',       // Primary color (purple)
        secondary: '#6c757d',     // Secondary color (gray)
        success: '#28a745',       // Success color (green)
        danger: '#dc3545',        // Danger color (red)
        warning: '#ffc107',       // Warning color (yellow)
        info: '#17a2b8',          // Info color (teal/blue)
        light: '#f8f9fa',         // Light color (off-white)
        dark: '#343a40'           // Dark color (dark gray)
    },
    // Email categories/labels
    labels: [
        { id: 'important', name: 'Important', icon: 'bookmark', count: 2, color: '#dc3545' },
        { id: 'production', name: 'Production', icon: 'gear', count: 5, color: '#28a745' },
        { id: 'inventory', name: 'Inventory', icon: 'box', count: 3, color: '#17a2b8' },
        { id: 'orders', name: 'Orders', icon: 'cart', count: 4, color: '#9c27b0' }
    ]
};

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('MRP Enhanced Gmail Implementation loaded');

    // Find the Gmail modal
    const gmailModal = document.getElementById('gmailModal');
    if (gmailModal) {
        console.log('Found Gmail modal, enhancing it');
        enhanceGmailModal(gmailModal);
    }

    // Find the Open Gmail button
    const openGmailBtn = document.getElementById('mrp-open-gmail-btn');
    if (openGmailBtn) {
        console.log('Found Open Gmail button, adding enhanced event listener');

        // Add our direct click handler
        openGmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Open Gmail button clicked - using enhanced implementation');

            // If the modal is already enhanced, just open it
            const enhancedModal = document.getElementById('gmailModal');
            if (enhancedModal) {
                const modal = new bootstrap.Modal(enhancedModal);
                modal.show();
            }
        });
    }

    // Make the openEmail function available globally
    window.openEmail = openEmail;
});

/**
 * Enhance the existing Gmail modal with BMS-style search, sort, and filter functionality
 */
function enhanceGmailModal(modal) {
    console.log('Enhancing Gmail modal with BMS-style functionality');

    // Find the inbox tab content
    const inboxContent = modal.querySelector('.tab-pane[id*="inbox"]');
    if (!inboxContent) {
        console.error('Inbox tab content not found');
        return;
    }

    // Add search, sort, and filter controls
    const controlsHtml = `
    <div class="d-flex justify-content-between mb-3">
        <div class="input-group" style="max-width: 300px;">
            <input type="text" class="form-control" id="mrp-email-search" placeholder="Search emails" aria-label="Search emails">
            <button class="btn btn-outline-secondary" id="mrp-email-search-btn" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
        <div>
            <div class="dropdown d-inline-block me-2">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="mrpEmailSortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-sort-down"></i> Sort
                </button>
                <ul class="dropdown-menu" aria-labelledby="mrpEmailSortDropdown">
                    <li><a class="dropdown-item mrp-sort-option" href="#" data-sort="date-desc">Newest first</a></li>
                    <li><a class="dropdown-item mrp-sort-option" href="#" data-sort="date-asc">Oldest first</a></li>
                    <li><a class="dropdown-item mrp-sort-option" href="#" data-sort="sender-asc">Sender A-Z</a></li>
                    <li><a class="dropdown-item mrp-sort-option" href="#" data-sort="sender-desc">Sender Z-A</a></li>
                    <li><a class="dropdown-item mrp-sort-option" href="#" data-sort="subject-asc">Subject A-Z</a></li>
                    <li><a class="dropdown-item mrp-sort-option" href="#" data-sort="subject-desc">Subject Z-A</a></li>
                </ul>
            </div>
            <div class="dropdown d-inline-block me-2">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="mrpEmailFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-funnel"></i> Filter
                </button>
                <ul class="dropdown-menu" aria-labelledby="mrpEmailFilterDropdown">
                    <li><a class="dropdown-item mrp-filter-option" href="#" data-filter="all">All emails</a></li>
                    <li><a class="dropdown-item mrp-filter-option" href="#" data-filter="unread">Unread</a></li>
                    <li><a class="dropdown-item mrp-filter-option" href="#" data-filter="read">Read</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header">By Label</h6></li>
                    ${MRP_GMAIL_CONFIG.labels.map(label => `
                        <li><a class="dropdown-item mrp-filter-option" href="#" data-filter="label-${label.id}">
                            <span class="badge" style="background-color: ${label.color};">${label.name}</span>
                        </a></li>
                    `).join('')}
                </ul>
            </div>
            <button class="btn btn-outline-secondary" id="mrp-gmail-refresh-btn">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
    </div>
    `;

    // Insert the controls at the top of the inbox content
    inboxContent.insertAdjacentHTML('afterbegin', controlsHtml);

    // Add data attributes to email items for sorting and filtering
    const emailItems = inboxContent.querySelectorAll('.list-group-item');
    emailItems.forEach((item, index) => {
        // Add data attributes if they don't exist
        if (!item.hasAttribute('data-sender')) {
            const sender = item.querySelector('h6, .fw-bold')?.textContent.trim() || `Sender ${index + 1}`;
            item.setAttribute('data-sender', sender.toLowerCase().replace(/\s+/g, '-'));
        }

        if (!item.hasAttribute('data-subject')) {
            const subject = item.querySelector('p, .mb-1')?.textContent.trim() || `Subject ${index + 1}`;
            item.setAttribute('data-subject', subject);
        }

        if (!item.hasAttribute('data-date')) {
            const date = new Date();
            date.setDate(date.getDate() - index); // Simulate different dates
            item.setAttribute('data-date', date.toISOString());
        }

        if (!item.hasAttribute('data-label')) {
            // Assign random labels
            const labels = MRP_GMAIL_CONFIG.labels;
            const randomLabel = labels[Math.floor(Math.random() * labels.length)].id;
            item.setAttribute('data-label', randomLabel);

            // Add label badge if it doesn't exist
            if (!item.querySelector('.badge')) {
                const labelObj = MRP_GMAIL_CONFIG.labels.find(l => l.id === randomLabel);
                const labelBadge = document.createElement('span');
                labelBadge.className = 'badge ms-2';
                labelBadge.style.backgroundColor = labelObj.color;
                labelBadge.textContent = labelObj.name;

                const titleElement = item.querySelector('h6, .fw-bold');
                if (titleElement) {
                    titleElement.appendChild(labelBadge);
                }
            }
        }

        // Add unread class to some emails
        if (index % 3 === 0) {
            item.classList.add('unread');
            item.style.backgroundColor = 'rgba(156, 39, 176, 0.05)';
        }

        // Add click event listener to open email
        item.style.cursor = 'pointer';
        item.addEventListener('click', function(e) {
            // Don't trigger if clicking on buttons or links
            if (e.target.closest('.btn') || e.target.closest('a') || e.target.closest('.dropdown-menu')) {
                console.log('Clicked on a button or link, not opening email');
                return;
            }

            console.log('Email item clicked');
            const sender = this.getAttribute('data-sender');
            const subject = this.getAttribute('data-subject');

            // Mark as read (remove unread indicator)
            this.classList.remove('unread');
            this.style.backgroundColor = '';

            // Call the openEmail function directly
            openEmail(sender, subject);
        });
    });

    // Set up event listeners
    setupMrpGmailEventListeners();
}

/**
 * Set up event listeners for the enhanced Gmail modal
 */
function setupMrpGmailEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('mrp-email-search');
    const searchBtn = document.getElementById('mrp-email-search-btn');

    if (searchInput && searchBtn) {
        // Search on button click
        searchBtn.addEventListener('click', function() {
            searchMrpEmails(searchInput.value);
        });

        // Search on Enter key
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                searchMrpEmails(this.value);
            }
        });
    }

    // Sort functionality
    document.querySelectorAll('.mrp-sort-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const sortType = this.getAttribute('data-sort');
            sortMrpEmails(sortType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('mrpEmailSortDropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-sort-down"></i> ${this.textContent}`;
            }
        });
    });

    // Filter functionality
    document.querySelectorAll('.mrp-filter-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const filterType = this.getAttribute('data-filter');
            filterMrpEmails(filterType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('mrpEmailFilterDropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-funnel"></i> ${this.textContent.trim()}`;
            }
        });
    });

    // Refresh button
    const refreshBtn = document.getElementById('mrp-gmail-refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // Reset search input
            if (searchInput) {
                searchInput.value = '';
            }

            // Reset sort dropdown
            const sortDropdown = document.getElementById('mrpEmailSortDropdown');
            if (sortDropdown) {
                sortDropdown.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
            }

            // Reset filter dropdown
            const filterDropdown = document.getElementById('mrpEmailFilterDropdown');
            if (filterDropdown) {
                filterDropdown.innerHTML = '<i class="bi bi-funnel"></i> Filter';
            }

            // Show all emails
            showAllMrpEmails();

            // Show success message
            showToast('Emails refreshed successfully');
        });
    }
}

/**
 * Search emails by query
 */
function searchMrpEmails(query) {
    if (!query) {
        showAllMrpEmails();
        return;
    }

    const inboxContent = document.querySelector('#gmailModal .tab-pane[id*="inbox"]');
    if (!inboxContent) return;

    const emailItems = inboxContent.querySelectorAll('.list-group-item');
    if (!emailItems.length) return;

    // Normalize query
    const normalizedQuery = query.toLowerCase().trim();

    // Show/hide based on search query
    emailItems.forEach(item => {
        const sender = item.getAttribute('data-sender') || '';
        const subject = item.getAttribute('data-subject') || '';
        const content = item.querySelector('small, .text-muted')?.textContent.toLowerCase() || '';

        if (sender.includes(normalizedQuery) || subject.toLowerCase().includes(normalizedQuery) || content.includes(normalizedQuery)) {
            item.style.display = '';

            // Highlight matching text
            highlightMrpText(item, normalizedQuery);
        } else {
            item.style.display = 'none';
        }
    });

    // Show toast with results count
    const visibleCount = Array.from(emailItems).filter(item => item.style.display !== 'none').length;
    showToast(`Found ${visibleCount} email${visibleCount !== 1 ? 's' : ''} matching "${query}"`);
}

/**
 * Highlight matching text in email items
 */
function highlightMrpText(item, query) {
    // Remove existing highlights
    item.querySelectorAll('.highlight').forEach(el => {
        const parent = el.parentNode;
        parent.replaceChild(document.createTextNode(el.textContent), el);
        parent.normalize();
    });

    // Highlight text in subject
    const subjectEl = item.querySelector('p, .mb-1');
    if (subjectEl) {
        const subject = subjectEl.textContent;
        const normalizedSubject = subject.toLowerCase();
        const index = normalizedSubject.indexOf(query);

        if (index >= 0) {
            const before = subject.substring(0, index);
            const match = subject.substring(index, index + query.length);
            const after = subject.substring(index + query.length);

            subjectEl.innerHTML = before + '<span class="highlight" style="background-color: yellow;">' + match + '</span>' + after;
        }
    }

    // Highlight text in content
    const contentEl = item.querySelector('small, .text-muted');
    if (contentEl) {
        const content = contentEl.textContent;
        const normalizedContent = content.toLowerCase();
        const index = normalizedContent.indexOf(query);

        if (index >= 0) {
            const before = content.substring(0, index);
            const match = content.substring(index, index + query.length);
            const after = content.substring(index + query.length);

            contentEl.innerHTML = before + '<span class="highlight" style="background-color: yellow;">' + match + '</span>' + after;
        }
    }
}

/**
 * Sort emails by specified criteria
 */
function sortMrpEmails(sortType) {
    const inboxContent = document.querySelector('#gmailModal .tab-pane[id*="inbox"]');
    if (!inboxContent) return;

    const emailList = inboxContent.querySelector('.list-group');
    if (!emailList) return;

    // Get all email items
    const emailItems = Array.from(emailList.querySelectorAll('.list-group-item'));
    if (!emailItems.length) return;

    // Sort based on criteria
    emailItems.sort((a, b) => {
        switch (sortType) {
            case 'date-desc':
                // Newest first (default)
                const dateA = a.getAttribute('data-date') || '';
                const dateB = b.getAttribute('data-date') || '';
                return dateB.localeCompare(dateA);
            case 'date-asc':
                // Oldest first
                const dateC = a.getAttribute('data-date') || '';
                const dateD = b.getAttribute('data-date') || '';
                return dateC.localeCompare(dateD);
            case 'sender-asc':
                // Sender A-Z
                const senderA = a.getAttribute('data-sender') || '';
                const senderB = b.getAttribute('data-sender') || '';
                return senderA.localeCompare(senderB);
            case 'sender-desc':
                // Sender Z-A
                const senderC = a.getAttribute('data-sender') || '';
                const senderD = b.getAttribute('data-sender') || '';
                return senderD.localeCompare(senderC);
            case 'subject-asc':
                // Subject A-Z
                const subjectA = a.getAttribute('data-subject') || '';
                const subjectB = b.getAttribute('data-subject') || '';
                return subjectA.toLowerCase().localeCompare(subjectB.toLowerCase());
            case 'subject-desc':
                // Subject Z-A
                const subjectC = a.getAttribute('data-subject') || '';
                const subjectD = b.getAttribute('data-subject') || '';
                return subjectD.toLowerCase().localeCompare(subjectC.toLowerCase());
            default:
                return 0;
        }
    });

    // Reorder the DOM elements
    emailItems.forEach(item => {
        emailList.appendChild(item);
    });

    // Show toast
    showToast(`Emails sorted by ${sortType.replace('-', ' ')}`);
}

/**
 * Filter emails by specified criteria
 */
function filterMrpEmails(filterType) {
    const inboxContent = document.querySelector('#gmailModal .tab-pane[id*="inbox"]');
    if (!inboxContent) return;

    // Get all email items
    const emailItems = inboxContent.querySelectorAll('.list-group-item');
    if (!emailItems.length) return;

    // Show/hide based on filter
    emailItems.forEach(item => {
        if (filterType === 'all') {
            // Show all emails
            item.style.display = '';
        } else if (filterType === 'unread') {
            // Show only unread emails
            item.style.display = item.classList.contains('unread') ? '' : 'none';
        } else if (filterType === 'read') {
            // Show only read emails
            item.style.display = !item.classList.contains('unread') ? '' : 'none';
        } else if (filterType.startsWith('label-')) {
            // Show emails with specific label
            const label = filterType.replace('label-', '');
            const itemLabel = item.getAttribute('data-label');
            item.style.display = itemLabel === label ? '' : 'none';
        }
    });

    // Show toast
    const visibleCount = Array.from(emailItems).filter(item => item.style.display !== 'none').length;
    showToast(`Showing ${visibleCount} email${visibleCount !== 1 ? 's' : ''} with filter: ${filterType}`);
}

/**
 * Show all emails
 */
function showAllMrpEmails() {
    const inboxContent = document.querySelector('#gmailModal .tab-pane[id*="inbox"]');
    if (!inboxContent) return;

    // Get all email items
    const emailItems = inboxContent.querySelectorAll('.list-group-item');
    if (!emailItems.length) return;

    // Show all emails
    emailItems.forEach(item => {
        item.style.display = '';

        // Remove highlights
        item.querySelectorAll('.highlight').forEach(el => {
            const parent = el.parentNode;
            parent.replaceChild(document.createTextNode(el.textContent), el);
            parent.normalize();
        });
    });
}

/**
 * Open an email in the read tab
 * @param {string} sender - The email sender
 * @param {string} subject - The email subject
 */
function openEmail(sender, subject) {
    console.log(`Opening email: "${subject}" from ${sender}`);

    // Find the read tab
    const readTab = document.getElementById('read-tab');
    if (!readTab) {
        console.error('Read tab not found');
        return;
    }

    // Switch to the read tab
    const tabTrigger = new bootstrap.Tab(readTab);
    tabTrigger.show();

    // Find the read content container
    const readContent = document.getElementById('read-content');
    if (!readContent) {
        console.error('Read content container not found');
        return;
    }

    // Generate a random email body based on the subject
    const emailBody = generateEmailBody(subject);

    // Create email content HTML
    const emailHTML = `
        <div class="email-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <button class="btn btn-sm btn-outline-secondary back-to-inbox-btn">
                    <i class="bi bi-arrow-left"></i> Back to Inbox
                </button>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-secondary reply-btn">
                        <i class="bi bi-reply"></i> Reply
                    </button>
                    <button class="btn btn-sm btn-outline-secondary forward-btn">
                        <i class="bi bi-forward"></i> Forward
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-btn">
                        <i class="bi bi-trash"></i> Delete
                    </button>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">${subject}</h5>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>From:</strong> ${sender.replace(/-/g, ' ')}
                            <span class="badge bg-${MRP_GMAIL_CONFIG.colors.primary}" style="background-color: ${MRP_GMAIL_CONFIG.colors.primary};">
                                ${MRP_GMAIL_CONFIG.labels[Math.floor(Math.random() * MRP_GMAIL_CONFIG.labels.length)].name}
                            </span>
                        </div>
                        <small class="text-muted">${new Date().toLocaleString()}</small>
                    </div>
                </div>
                <div class="card-body">
                    <div class="email-body">
                        ${emailBody}
                    </div>

                    <hr>

                    <div class="attachments">
                        <h6><i class="bi bi-paperclip"></i> Attachments (${Math.floor(Math.random() * 3) + 1})</h6>
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <div class="card">
                                    <div class="card-body p-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-text me-2 fs-4"></i>
                                            <div>
                                                <small>Document.docx</small>
                                                <div class="btn-group btn-group-sm mt-1">
                                                    <button class="btn btn-sm btn-outline-primary view-btn" data-file-name="Document.docx" data-file-type="document">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-primary download-btn" data-file-name="Document.docx" data-file-type="document">
                                                        <i class="bi bi-download"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="card">
                                    <div class="card-body p-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet me-2 fs-4"></i>
                                            <div>
                                                <small>Spreadsheet.xlsx</small>
                                                <div class="btn-group btn-group-sm mt-1">
                                                    <button class="btn btn-sm btn-outline-primary view-btn" data-file-name="Spreadsheet.xlsx" data-file-type="spreadsheet">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-primary download-btn" data-file-name="Spreadsheet.xlsx" data-file-type="spreadsheet">
                                                        <i class="bi bi-download"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="reply-section">
                <div class="card">
                    <div class="card-body">
                        <h6>Reply</h6>
                        <textarea class="form-control mb-2" rows="3" placeholder="Type your reply here..."></textarea>
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-paperclip"></i> Attach
                            </button>
                            <button class="btn btn-sm btn-primary" style="background-color: ${MRP_GMAIL_CONFIG.colors.primary}; border-color: ${MRP_GMAIL_CONFIG.colors.primary};">
                                <i class="bi bi-send"></i> Send
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Set the content
    readContent.innerHTML = emailHTML;

    // Add event listeners to buttons
    setupEmailButtonListeners(readContent);

    // Make this function available globally
    window.openEmail = openEmail;
}

/**
 * Generate a random email body based on the subject
 */
function generateEmailBody(subject) {
    const paragraphs = [
        `<p>Hello,</p>
        <p>I wanted to follow up on our discussion about ${subject}. We've made significant progress and I'd like to share some updates with you.</p>
        <p>The team has been working diligently to address all the requirements, and we're on track to meet our deadlines. Please review the attached documents for more details.</p>
        <p>Let me know if you have any questions or concerns.</p>
        <p>Best regards,<br>The MRP Team</p>`,

        `<p>Dear Team,</p>
        <p>Regarding ${subject}, I'm pleased to inform you that we've completed the initial phase of the project. The results are promising, and we're ready to move on to the next steps.</p>
        <p>I've attached the latest reports and projections for your review. Please take a look and provide your feedback by the end of the week.</p>
        <p>Thank you for your continued support.</p>
        <p>Regards,<br>Production Manager</p>`,

        `<p>Hi there,</p>
        <p>I'm writing to provide an update on ${subject}. We've encountered some challenges that require immediate attention.</p>
        <p>The attached documents outline the issues and proposed solutions. I'd appreciate your input on how to proceed.</p>
        <p>Can we schedule a meeting to discuss this further?</p>
        <p>Thanks,<br>Inventory Specialist</p>`
    ];

    return paragraphs[Math.floor(Math.random() * paragraphs.length)];
}

/**
 * Set up event listeners for email buttons
 */
function setupEmailButtonListeners(container) {
    // Back to inbox button
    const backButton = container.querySelector('.back-to-inbox-btn');
    if (backButton) {
        backButton.addEventListener('click', function() {
            // Switch back to inbox tab
            const inboxTab = document.getElementById('inbox-tab');
            if (inboxTab) {
                const tabTrigger = new bootstrap.Tab(inboxTab);
                tabTrigger.show();
            }
        });
    }

    // Reply button
    const replyButton = container.querySelector('.reply-btn');
    if (replyButton) {
        replyButton.addEventListener('click', function() {
            // Focus on reply textarea
            const replyTextarea = container.querySelector('.reply-section textarea');
            if (replyTextarea) {
                replyTextarea.focus();
            }
        });
    }

    // Forward button
    const forwardButton = container.querySelector('.forward-btn');
    if (forwardButton) {
        forwardButton.addEventListener('click', function() {
            showToast('Forward functionality will be implemented soon', 'info');
        });
    }

    // Delete button
    const deleteButton = container.querySelector('.delete-btn');
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete this email?')) {
                showToast('Email deleted successfully');

                // Switch back to inbox tab
                const inboxTab = document.getElementById('inbox-tab');
                if (inboxTab) {
                    const tabTrigger = new bootstrap.Tab(inboxTab);
                    tabTrigger.show();
                }
            }
        });
    }

    // View attachment buttons
    const viewButtons = container.querySelectorAll('.view-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const fileName = this.getAttribute('data-file-name');
            const fileType = this.getAttribute('data-file-type');
            showToast(`Viewing ${fileName}`, 'info');
        });
    });

    // Download attachment buttons
    const downloadButtons = container.querySelectorAll('.download-btn');
    downloadButtons.forEach(button => {
        button.addEventListener('click', function() {
            const fileName = this.getAttribute('data-file-name');
            showToast(`Downloading ${fileName}`, 'success');
        });
    });

    // Send reply button
    const sendButton = container.querySelector('.reply-section .btn-primary');
    if (sendButton) {
        sendButton.addEventListener('click', function() {
            const replyTextarea = container.querySelector('.reply-section textarea');
            if (replyTextarea && replyTextarea.value.trim()) {
                showToast('Reply sent successfully', 'success');
                replyTextarea.value = '';
            } else {
                showToast('Please enter a reply before sending', 'warning');
            }
        });
    }
}



/**
 * Show a toast notification
 */
function showToast(message, type = 'success') {
    // Use the ISADataUtils.showToast function if available
    if (window.ISADataUtils && typeof window.ISADataUtils.showToast === 'function') {
        window.ISADataUtils.showToast(message, type);
        return;
    }

    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Create a unique ID for this toast
    const toastId = 'toast-' + Date.now();

    // Determine icon and title based on type
    let icon, title;
    switch (type) {
        case 'success':
            icon = 'check-circle-fill text-success';
            title = 'Success';
            break;
        case 'danger':
        case 'error':
            icon = 'exclamation-circle-fill text-danger';
            title = 'Error';
            break;
        case 'warning':
            icon = 'exclamation-triangle-fill text-warning';
            title = 'Warning';
            break;
        case 'info':
            icon = 'info-circle-fill text-info';
            title = 'Information';
            break;
        default:
            icon = 'bell-fill text-primary';
            title = 'Notification';
    }

    // Create toast element
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi bi-${icon} me-2"></i>
                <strong class="me-auto">${title}</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // Add toast to container
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialize and show the toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
    toast.show();

    // Remove toast from DOM after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function () {
        toastElement.remove();
    });
}