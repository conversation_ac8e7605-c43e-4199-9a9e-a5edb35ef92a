@echo off
echo Starting Task Management (TM)...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%
set NODE_ENV=production

REM Change to the TM directory
cd /d C:\ISASUITE\apps\TM\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call C:\ISASUITE\PortableNodeJS\pnpm install
)

REM Close any potentially running instance on the same port
taskkill /F /FI "WINDOWTITLE eq Task Management*" /T > nul 2>&1
taskkill /F /FI "WINDOWTITLE eq http://localhost:3009*" /T > nul 2>&1

REM Start the application
echo Starting TM...
echo To access Task Management, go to: http://localhost:3009
start "Task Management" cmd /c "C:\ISASUITE\PortableNodeJS\node.exe server.js"

REM Wait for server to initialize
timeout /t 2 > nul

REM Open browser
start "" http://localhost:3009

echo.
echo Task Management application launched. Close this window to stop the application.