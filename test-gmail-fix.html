<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMS Gmail Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .test-button:hover {
            background-color: #138496;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>BMS Gmail Modal Fix Test</h1>
        <p>This page tests whether the Gmail modal fix is working correctly in BMS.</p>
        
        <div class="status info" id="status">
            <strong>Status:</strong> Click the test button to check the Gmail modal...
        </div>
        
        <button class="test-button" onclick="testGmailModalFix()">Test Gmail Modal Fix</button>
        <button class="test-button" onclick="openBMSInNewWindow()">Open BMS in New Window</button>
        
        <div id="results"></div>
        
        <h2>Test Instructions:</h2>
        <ol>
            <li>Make sure BMS is running at <a href="http://localhost:3001" target="_blank">http://localhost:3001</a></li>
            <li>Open BMS and navigate to the Gmail section</li>
            <li>Click on "View All Emails" link</li>
            <li>Check if the enhanced Gmail modal opens with the labels sidebar</li>
            <li>Verify that the modal shows "Gmail" in the title with proper styling</li>
        </ol>
        
        <h2>Expected Behavior:</h2>
        <ul>
            <li>✅ "View All Emails" should open an enhanced Gmail modal</li>
            <li>✅ Modal should have a left sidebar with labels (Inventory, Urgent, Follow-up, Production)</li>
            <li>✅ Modal should have tabs (Inbox, Sent, Compose, Read Email)</li>
            <li>✅ Modal should use BMS color scheme (#17a2b8)</li>
            <li>❌ Should NOT open the basic/old Gmail modal</li>
        </ul>
    </div>

    <script>
        function testGmailModalFix() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<strong>Testing...</strong> Checking Gmail modal fix...';
            statusDiv.className = 'status info';
            
            // Test if we can reach BMS
            fetch('http://localhost:3001')
                .then(response => {
                    if (response.ok) {
                        statusDiv.innerHTML = '<strong>Success:</strong> BMS is running at localhost:3001. Now test the Gmail modal manually.';
                        statusDiv.className = 'status success';
                    } else {
                        statusDiv.innerHTML = '<strong>Error:</strong> BMS responded with status ' + response.status;
                        statusDiv.className = 'status error';
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = '<strong>Error:</strong> Cannot reach BMS at localhost:3001. Make sure BMS is running.';
                    statusDiv.className = 'status error';
                });
        }
        
        function openBMSInNewWindow() {
            window.open('http://localhost:3001', '_blank');
        }
        
        // Auto-test on page load
        setTimeout(testGmailModalFix, 1000);
    </script>
</body>
</html>
