/**
 * MRP Modal Backdrop Fix
 * This script addresses the issue where closing modals
 * leaves the screen dimmed due to lingering modal backdrops
 */

(function() {
    // Run on DOM content loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('MRP Modal Backdrop Fix loaded');
        setupModalBackdropFix();
    });

    /**
     * Sets up event listeners to fix the modal backdrop issue
     */
    function setupModalBackdropFix() {
        // Set up fix for Gmail modal
        setupModalFix('mrp-gmailModal', 'gmail');
        
        // Set up fix for Sheets modal
        setupModalFix('mrp-sheetsModal', 'sheets');
    }
    
    /**
     * Sets up modal backdrop fix for a specific modal
     * @param {string} modalId - The ID of the modal element
     * @param {string} modalType - The type of modal (for logging)
     */
    function setupModalFix(modalId, modalType) {
        // Find the modal
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.warn(`${modalType} modal not found with ID "${modalId}"`);
            return;
        }        // Add event listener to fix the backdrop issue when the modal is hidden
        modal.addEventListener('hidden.bs.modal', function() {
            console.log(`${modalType} modal hidden, cleaning up backdrops`);
            cleanupModalBackdrops();
        });

        // Add modal-specific initialization for shown event
        modal.addEventListener('shown.bs.modal', function() {
            console.log(`${modalType} modal shown`);
            
            // Gmail-specific functionality
            if (modalType === 'gmail' && typeof setupGmailSortAndFilter === 'function') {
                console.log('Initializing Gmail sort and filter');
                setupGmailSortAndFilter();
            }
        });

        // Find all links and buttons that open this modal
        const modalOpeners = document.querySelectorAll(`[data-bs-target="#${modalId}"], [href="#${modalType}"]`);
        modalOpeners.forEach(opener => {
            opener.addEventListener('click', function(e) {
                // Clean up any existing backdrops before opening
                cleanupModalBackdrops();
            });
        });

        console.log(`${modalType} modal backdrop fix setup complete`);
    }

    /**
     * Cleans up any lingering modal backdrops and resets body styles
     */
    function cleanupModalBackdrops() {
        // Remove any lingering backdrop elements
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });
        
        // Reset body styles
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }
})();
