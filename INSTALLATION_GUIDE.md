# ISA Suite Installation Guide

This guide provides instructions for installing and running the ISA Suite on your laptop or other computers.

## System Requirements

- Windows 10 or later
- 4GB RAM minimum (8GB recommended)
- 1GB free disk space
- Internet connection for initial setup

## Installation Options

There are two installation options:

1. **Full Installation (Admin Version)**: Includes all applications, development tools, and admin features.
2. **User Installation (User Version)**: Includes only the applications needed for regular users.

## Installation Steps

### Option 1: Using the Installer

1. Download the ISA Suite installer package.
2. Right-click on `install-isasuite.bat` and select "Run as administrator".
3. Follow the on-screen instructions to complete the installation.
4. Select the installation type (Full or User).
5. Wait for the installation to complete.

### Option 2: Manual Installation

1. Create a directory at `C:\ISASUITE`.
2. Copy all files from the ISA Suite package to this directory.
3. Run `service-manager.bat` to start the ISA Suite Service Manager.

## Running ISA Suite

After installation, you have several options to run the ISA Suite:

### Option 1: Service Manager (Recommended)

1. Double-click the "ISA Suite Service Manager" shortcut on your desktop.
2. Select the desired mode (Production, Sandbox/Training, or Demo).
3. Choose "Start all applications" from the menu.
4. The Integration Hub will open in your browser automatically.

### Option 2: Single Window Launcher

1. Navigate to the ISA Suite installation directory (`C:\ISASUITE`).
2. Run `single-window-launcher.bat`.
3. Select the desired mode.
4. All applications will start in a single window.
5. The Integration Hub will open in your browser automatically.

### Option 3: Individual Applications

You can also start individual applications using the Service Manager:

1. Open the Service Manager.
2. Select "Start individual application".
3. Choose the application you want to start.

## Application Modes

The ISA Suite supports three different modes:

1. **Production Mode**: For normal business operations with real data.
2. **Sandbox/Training Mode**: For training and testing without affecting production data.
3. **Demo Mode**: For demonstrations with sample data.

## Accessing Applications

Once the applications are running, you can access them through:

1. **Integration Hub**: http://localhost:8000
   - This is the central dashboard for all applications.
   - You can access all other applications from here.

2. **Direct URLs**:
   - Business Management System: http://localhost:3001
   - Materials Requirements Planning: http://localhost:3002
   - Customer Relationship Management: http://localhost:3003
   - Warehouse Management System: http://localhost:3004
   - Advanced Planning and Scheduling: http://localhost:3005
   - Asset Performance Management: http://localhost:3006
   - Project Management System: http://localhost:3007
   - Supply Chain Management: http://localhost:3008
   - Task Management System: http://localhost:3009

## Stopping Applications

To stop all applications:

1. Open the Service Manager.
2. Select "Stop all applications".

Or, if using the Single Window Launcher, press any key to stop all applications and exit.

## Troubleshooting

### Applications Not Starting

1. Check if Node.js is installed correctly.
2. Verify that all required files are in the correct locations.
3. Check the log files in `C:\ISASUITE\logs` for error messages.

### Cannot Access Applications in Browser

1. Verify that the applications are running using the Service Manager's "Check application status" option.
2. Check if your firewall is blocking the applications.
3. Try accessing the applications using a different browser.

## Support

For additional support, please contact your system administrator or the ISA Suite support team.

---

© 2024 ISA Suite. All rights reserved.
