// Script to fix the Google Sheets Modal in BMS index.html
const fs = require('fs');
const path = require('path');

// Read the current index.html file
const indexPath = path.join(__dirname, 'public', 'index.html');
let content = fs.readFileSync(indexPath, 'utf8');

// Find the Google Sheets Modal section
const sheetsModalStart = content.indexOf('<!-- Google Sheets Modal -->');
const sheetsModalEnd = content.indexOf('</div>', sheetsModalStart + 1000) + 6;

// Extract the current modal
const currentModal = content.substring(sheetsModalStart, sheetsModalEnd);

// Create a fixed version of the Google Sheets Modal
const fixedSheetsModal = `<!-- Google Sheets Modal -->
    <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="sheetsModalLabel"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="sheetsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="recent-tab" data-bs-toggle="tab" data-bs-target="#recent-sheets" type="button" role="tab" aria-controls="recent-sheets" aria-selected="true">Recent Sheets</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create-sheet" type="button" role="tab" aria-controls="create-sheet" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import-sheet" type="button" role="tab" aria-controls="import-sheet" aria-selected="false">Import Data</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="sheetsTabContent">
                        <div class="tab-pane fade show active" id="recent-sheets" role="tabpanel" aria-labelledby="recent-tab">
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <strong>Financial Report Q1 2025</strong>
                                            <p class="mb-1 small text-muted">Last edited: 2 days ago <span class="badge bg-primary rounded-pill">Shared</span></p>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('spreadsheet', 'Financial_Report_Q1_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadAttachment('Financial_Report_Q1_2025.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Financial_Report_Q1_2025.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Financial_Report_Q1_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <strong>Budget Planning 2025</strong>
                                            <p class="mb-1 small text-muted">Last edited: 1 week ago <span class="badge bg-secondary rounded-pill">Private</span></p>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('spreadsheet', 'Budget_Planning_2025.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadAttachment('Budget_Planning_2025.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Budget_Planning_2025.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Budget_Planning_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                            <strong>Revenue Forecast 2025-2026</strong>
                                            <p class="mb-1 small text-muted">Last edited: 2 weeks ago <span class="badge bg-primary rounded-pill">Shared</span></p>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewAttachment('spreadsheet', 'Revenue_Forecast_2025-2026.xlsx')"><i class="bi bi-eye"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadAttachment('Revenue_Forecast_2025-2026.xlsx')"><i class="bi bi-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Revenue_Forecast_2025-2026.xlsx')"><i class="bi bi-share"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAttachment('Revenue_Forecast_2025-2026.xlsx')"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-grid gap-2 mt-3">
                                <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#sheetsModal">
                                    <i class="bi bi-file-earmark-spreadsheet me-2"></i>View All Sheets
                                </button>
                            </div>
                        </div>`;

// Replace the current modal with the fixed one
const fixedContent = content.replace(currentModal, fixedSheetsModal);

// Write the fixed content back to the file
fs.writeFileSync(indexPath, fixedContent, 'utf8');

console.log('Google Sheets Modal fixed successfully!');
