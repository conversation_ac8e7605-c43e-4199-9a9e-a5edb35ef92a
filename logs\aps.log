
> aps@1.0.0 start C:\ISASUITE\apps\APS
> node index.js production

APS running at http://localhost:3005
Connected to IntegrationHub
[WARN] [GoogleIntegration] Google API token has expired, attempting to refresh {}
Notifications database initialized
[ERROR] [GoogleIntegration] Google API authorization failed {
  error: 'Failed to parse or use token: Failed to refresh token: The "data" argument must be of type string or an instance of <PERSON>uffer, TypedArray, or DataView. Received undefined. Please run the authentication flow again.'
}
[ERROR] [GoogleIntegration] Failed to initialize Google API {
  error: 'Failed to parse or use token: Failed to refresh token: The "data" argument must be of type string or an instance of <PERSON>uffer, TypedArray, or DataView. Received undefined. Please run the authentication flow again.'
}
[WARN] [GoogleIntegration] Token issue detected. You may need to run the authentication flow again. {}
[WARN] [GoogleIntegration] Run: node SharedFeatures/integrations/google.js {}
[ERROR] [APS] Failed to initialize Google API {
  error: 'Failed to parse or use token: Failed to refresh token: The "data" argument must be of type string or an instance of <PERSON><PERSON><PERSON>, Typed<PERSON>rray, or DataView. Received undefined. Please run the authentication flow again.'
}
