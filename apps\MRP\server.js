const http = require('http');
const fs = require('fs');
const path = require('path');

// Simple static file server
function serveStaticFile(res, filePath, contentType) {
  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404);
      res.end('File not found');
      return;
    }

    res.writeHead(200, { 'Content-Type': contentType });
    res.end(data);
  });
}

// Create the server
const server = http.createServer((req, res) => {
  // Parse the URL
  const url = new URL(req.url, `http://${req.headers.host}`);
  const pathname = url.pathname;

  // Serve static files
  if (pathname.startsWith('/js/')) {
    const filePath = path.join(__dirname, 'public', pathname);
    serveStaticFile(res, filePath, 'text/javascript');
    return;
  }

  // Serve shared files
  if (pathname.startsWith('/shared/')) {
    const sharedPath = pathname.replace('/shared/', '');
    const filePath = path.join(__dirname, '..', '..', 'shared', sharedPath);

    // Determine content type based on file extension
    const ext = path.extname(filePath);
    let contentType = 'text/plain';

    switch (ext) {
      case '.js':
        contentType = 'text/javascript';
        break;
      case '.css':
        contentType = 'text/css';
        break;
      case '.json':
        contentType = 'application/json';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.jpg':
        contentType = 'image/jpg';
        break;
      case '.html':
        contentType = 'text/html';
        break;
    }

    serveStaticFile(res, filePath, contentType);
    return;
  }

  // Serve index.html for root path
  if (pathname === '/') {
    const filePath = path.join(__dirname, 'public', 'index.html');
    serveStaticFile(res, filePath, 'text/html');
    return;
  }

  // API endpoints
  if (pathname === '/api/production-orders') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(productionOrders));
    return;
  }

  if (pathname === '/api/resources') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(resources));
    return;
  }

  // 404 for everything else
  res.writeHead(404);
  res.end('Not found');
});

let productionOrders = [
  {
    id: 1,
    product: 'Widget A',
    quantity: 1000,
    status: 'In Progress',
    dueDate: '2024-05-15',
    resources: ['Line 1', 'Robot A'],
    priority: 'High',
    progress: 60,
  },
  {
    id: 2,
    product: 'Widget B',
    quantity: 500,
    status: 'Scheduled',
    dueDate: '2024-05-20',
    resources: ['Line 2', 'Robot B'],
    priority: 'Medium',
    progress: 0,
  },
  {
    id: 3,
    product: 'Widget C',
    quantity: 2000,
    status: 'Planned',
    dueDate: '2024-05-30',
    resources: ['Line 3', 'Robot C'],
    priority: 'Low',
    progress: 0,
  },
];

let resources = [
  {
    id: 1,
    name: 'Line 1',
    type: 'Production Line',
    status: 'Operational',
    utilization: 85,
    nextMaintenance: '2024-06-01',
    capacity: 100,
  },
  {
    id: 2,
    name: 'Line 2',
    type: 'Production Line',
    status: 'Maintenance',
    utilization: 0,
    nextMaintenance: '2024-05-20',
    capacity: 100,
  },
  {
    id: 3,
    name: 'Robot A',
    type: 'Automation',
    status: 'Operational',
    utilization: 75,
    nextMaintenance: '2024-06-15',
    capacity: 100,
  },
];

// Start the server
const PORT = 3002;
server.listen(PORT, () => {
  console.log(`Materials Requirements Planning System running on http://localhost:${PORT}`);
  console.log('Shared features loaded successfully');
});
