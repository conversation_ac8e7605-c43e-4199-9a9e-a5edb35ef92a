{"name": "TM", "version": "1.0.0", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "start-web": "webpack serve --mode development --open --port 3009", "start-electron": "electron .", "build": "webpack --mode production"}, "dependencies": {"express": "^4.18.2", "react": "^18.0.0", "react-dom": "^18.0.0", "node-fetch": "^2.6.7", "cors": "^2.8.5"}, "devDependencies": {"webpack": "^5.0.0", "webpack-cli": "^4.0.0", "webpack-dev-server": "^4.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-react": "^7.0.0", "babel-loader": "^8.0.0"}}