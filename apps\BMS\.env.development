# Development Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bms_dev
DB_USER=postgres
DB_PASSWORD=password

# JWT Configuration
JWT_SECRET=dev_jwt_secret
JWT_EXPIRATION=1d

# Integration Hub Configuration
INTEGRATION_HUB_URL=http://localhost:8000
INTEGRATION_HUB_API_KEY=dev_api_key

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE_PATH=logs/bms-dev.log
