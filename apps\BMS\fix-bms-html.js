// Script to fix the BMS index.html file
const fs = require('fs');
const path = require('path');

// Read the current index.html file
const indexPath = path.join(__dirname, 'public', 'index.html');
let content = fs.readFileSync(indexPath, 'utf8');

// Remove all duplicate h2 elements with $125,000
content = content.replace(/(<h2 class="card-text">\$125,000<\/h2>\s*)+/g, '');

// Remove broken HTML tags
content = content.replace(/<\s*(?!\/|br|hr|img|input|link|meta|area|base|col|command|embed|keygen|param|source|track|wbr)[a-z]+[^>]*>\s*<\s*\/\s*[a-z]+>/g, '');

// Remove duplicate Google Sheets Modal
const firstSheetsModalStart = content.indexOf('<!-- Google Sheets Modal -->');
const secondSheetsModalStart = content.indexOf('<!-- Google Sheets Modal -->', firstSheetsModalStart + 100);

if (secondSheetsModalStart > -1) {
    const secondSheetsModalEnd = content.indexOf('</div>', secondSheetsModalStart + 1000) + 6;
    content = content.substring(0, secondSheetsModalStart) + content.substring(secondSheetsModalEnd);
}

// Fix broken HTML in the Attachments Modal
content = content.replace(/ad" disabled>Upload Files<\/button>/g, '<button class="btn btn-primary" type="button" id="start-upload" disabled>Upload Files</button>');

// Fix any other broken HTML
content = content.replace(/<\s*(?!\/|br|hr|img|input|link|meta|area|base|col|command|embed|keygen|param|source|track|wbr)[a-z]+[^>]*>\s*$/g, '');

// Write the fixed content back to the file
fs.writeFileSync(indexPath, content, 'utf8');

console.log('BMS index.html file fixed successfully!');
