// <PERSON>ript to completely fix the Google Sheets Modal in BMS index.html
const fs = require('fs');
const path = require('path');

// Read the current BMS index.html file
const indexPath = path.join(__dirname, 'public', 'index.html');
let content = fs.readFileSync(indexPath, 'utf8');

// Create a backup of the original file
fs.writeFileSync(indexPath + '.bak4', content, 'utf8');

// Define the fixed Google Sheets Modal
const fixedSheetsModal = `    <!-- Google Sheets Modal -->
    <div class="modal fade" id="sheetsModal" tabindex="-1" aria-labelledby="sheetsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: var(--app-primary-color); color: white;">
                    <h5 class="modal-title" id="sheetsModalLabel"><i class="bi bi-file-earmark-spreadsheet"></i> Google Sheets</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="sheetsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="sheets-tab" data-bs-toggle="tab" data-bs-target="#sheets-content" type="button" role="tab" aria-controls="sheets-content" aria-selected="true">My Sheets</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create-content" type="button" role="tab" aria-controls="create-content" aria-selected="false">Create New</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import-content" type="button" role="tab" aria-controls="import-content" aria-selected="false">Import</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="sheetsTabContent">
                        <!-- My Sheets Tab -->
                        <div class="tab-pane fade show active" id="sheets-content" role="tabpanel" aria-labelledby="sheets-tab">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" placeholder="Search sheets..." id="sheets-search">
                                <button class="btn btn-outline-secondary" type="button"><i class="bi bi-search"></i></button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sheets-list">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Financial_Report_Q1_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'financial-report-q1-2025')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Financial_Report_Q1_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Financial_Report_Q1_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Financial_Report_Q1_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Budget_Planning_2025.xlsx</span>
                                                </div>
                                            </td>
                                            <td>1 week ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'budget-planning-2025')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Budget_Planning_2025.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Budget_Planning_2025.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Budget_Planning_2025.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-spreadsheet text-success me-2"></i>
                                                    <span>Revenue_Forecast_2025-2026.xlsx</span>
                                                </div>
                                            </td>
                                            <td>2 weeks ago</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openGoogleItem('sheets', 'spreadsheet', 'revenue-forecast-2025-2026')"><i class="bi bi-eye"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadFile('Revenue_Forecast_2025-2026.xlsx')"><i class="bi bi-download"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="shareFile('Revenue_Forecast_2025-2026.xlsx')"><i class="bi bi-share"></i></button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCurrentFile('Revenue_Forecast_2025-2026.xlsx')"><i class="bi bi-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Create New Tab -->
                        <div class="tab-pane fade" id="create-content" role="tabpanel" aria-labelledby="create-tab">
                            <form id="create-sheet-form">
                                <div class="mb-3">
                                    <label for="sheet-title" class="form-label">Sheet Title</label>
                                    <input type="text" class="form-control" id="sheet-title" placeholder="Enter a title for your sheet">
                                </div>
                                <div class="mb-3">
                                    <label for="sheet-template" class="form-label">Template</label>
                                    <select class="form-select" id="sheet-template">
                                        <option value="blank">Blank</option>
                                        <option value="project">Project Timeline</option>
                                        <option value="budget">Budget Tracker</option>
                                        <option value="resources">Resource Allocation</option>
                                        <option value="tasks">Task Tracker</option>
                                    </select>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="create-sheet-btn">
                                        <i class="bi bi-plus-circle me-2"></i>Create Sheet
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Import Tab -->
                        <div class="tab-pane fade" id="import-content" role="tabpanel" aria-labelledby="import-tab">
                            <form id="import-sheet-form">
                                <div class="mb-3">
                                    <label for="import-title" class="form-label">Sheet Title</label>
                                    <input type="text" class="form-control" id="import-title" placeholder="Enter a title for your imported sheet">
                                </div>
                                <div class="mb-3">
                                    <label for="import-file" class="form-label">File to Import</label>
                                    <input class="form-control" type="file" id="import-file" accept=".xlsx,.xls,.csv,.ods">
                                    <div class="form-text">Supported formats: Excel (.xlsx, .xls), CSV, OpenDocument Spreadsheet (.ods)</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="import-first-row">
                                        <label class="form-check-label" for="import-first-row">
                                            First row contains column headers
                                        </label>
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="import-sheet-btn">
                                        <i class="bi bi-upload me-2"></i>Import Sheet
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>`;

// Find the Google Sheets Modal in the file
const sheetsModalStart = content.indexOf('<!-- Google Sheets Modal -->');
if (sheetsModalStart > -1) {
    // Find the end of the modal
    let sheetsModalEnd = content.indexOf('</div>\n\n', sheetsModalStart);
    if (sheetsModalEnd === -1) {
        sheetsModalEnd = content.indexOf('</div>\n    </div>\n</div>', sheetsModalStart);
    }
    if (sheetsModalEnd === -1) {
        sheetsModalEnd = content.indexOf('</div>\n\n    <!-- Google Docs Modal -->', sheetsModalStart);
    }
    if (sheetsModalEnd === -1) {
        // Try to find the end of the modal by looking for the next modal
        const nextModalStart = content.indexOf('<!-- ', sheetsModalStart + 100);
        if (nextModalStart > -1) {
            sheetsModalEnd = content.lastIndexOf('</div>', nextModalStart);
        }
    }
    
    if (sheetsModalEnd > -1) {
        // Add 6 characters to include the closing </div> tag
        sheetsModalEnd += 6;
        
        // Replace the modal with the fixed version
        content = content.substring(0, sheetsModalStart) + fixedSheetsModal + content.substring(sheetsModalEnd);
        
        // Write the fixed content back to the file
        fs.writeFileSync(indexPath, content, 'utf8');
        console.log('Google Sheets Modal fixed successfully!');
    } else {
        console.error('Could not find the end of the Google Sheets Modal.');
    }
} else {
    console.error('Could not find the Google Sheets Modal.');
}
