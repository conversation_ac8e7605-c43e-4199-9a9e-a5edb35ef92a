# Production Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=production

# Database Configuration
DB_HOST=db.example.com
DB_PORT=5432
DB_NAME=bms_prod
DB_USER=bms_user
DB_PASSWORD=secure_password

# JWT Configuration
JWT_SECRET=prod_jwt_secret
JWT_EXPIRATION=1d

# Integration Hub Configuration
INTEGRATION_HUB_URL=https://hub.example.com
INTEGRATION_HUB_API_KEY=prod_api_key

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=logs/bms-prod.log
