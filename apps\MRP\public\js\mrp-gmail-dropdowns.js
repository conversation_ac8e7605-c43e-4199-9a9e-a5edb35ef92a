// Gmail Dropdown Fix for MRP Application
console.log('Loading Gmail Dropdown Fix...');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Setting up Gmail dropdown handlers');
    
    // Setup immediately for better responsiveness
    setupGmailDropdowns();
});

function setupGmailDropdowns() {
    console.log('Setting up Gmail dropdowns...');
    
    // Check if modal exists
    const modal = document.getElementById('mrp-gmailModal');
    if (!modal) {
        console.log('Gmail modal not found, setting up observer for when it appears...');
        
        // Use MutationObserver instead of setTimeout for better performance
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.id === 'mrp-gmailModal' || node.querySelector('#mrp-gmailModal')) {
                            console.log('Gmail modal detected, setting up dropdowns...');
                            observer.disconnect();
                            setupGmailDropdowns();
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
        return;
    }
    
    // Setup modal shown event listener
    modal.addEventListener('shown.bs.modal', function () {
        console.log('Gmail modal shown, initializing dropdowns...');
        initializeDropdownsInModal();
    });
    
    // Also try to initialize immediately if modal is already visible
    if (modal.classList.contains('show')) {
        console.log('Modal already visible, initializing dropdowns...');
        initializeDropdownsInModal();
    }
}

function initializeDropdownsInModal() {
    const modal = document.getElementById('mrp-gmailModal');
    if (!modal) return;
    
    console.log('Initializing dropdowns in modal...');
    
    // Find dropdown elements
    const sortButton = modal.querySelector('#sort-dropdown');
    const filterButton = modal.querySelector('#filter-dropdown');
    
    console.log('Found elements:', {
        sortButton: !!sortButton,
        filterButton: !!filterButton
    });
    
    if (!sortButton || !filterButton) {
        console.error('Dropdown buttons not found');
        return;
    }
    
    // Remove any existing event listeners by cloning elements
    cleanupDropdowns(modal);
    
    // Try Bootstrap first, fallback to manual
    if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
        console.log('Using Bootstrap dropdowns');
        initializeBootstrapDropdowns(modal);
    } else {
        console.log('Bootstrap not available, using manual dropdowns');
        initializeManualDropdowns(modal);
    }
    
    // Setup dropdown option handlers
    setupDropdownOptionHandlers(modal);
    
    // Setup other button handlers
    setupOtherButtonHandlers(modal);
}

function cleanupDropdowns(modal) {
    // Remove inline onclick handlers that might interfere
    const elementsWithOnclick = modal.querySelectorAll('[onclick]');
    elementsWithOnclick.forEach(el => {
        if (el.id === 'sort-dropdown' || 
            el.id === 'filter-dropdown' || 
            el.classList.contains('sort-option') || 
            el.classList.contains('filter-option')) {
            el.removeAttribute('onclick');
            console.log('Removed onclick from:', el.id || el.className);
        }
    });
}

function initializeBootstrapDropdowns(modal) {
    const dropdownToggles = modal.querySelectorAll('[data-bs-toggle="dropdown"]');
    
    dropdownToggles.forEach(toggle => {
        try {
            // Dispose any existing dropdown
            const existing = bootstrap.Dropdown.getInstance(toggle);
            if (existing) {
                existing.dispose();
            }
            
            // Create new dropdown
            const dropdown = new bootstrap.Dropdown(toggle, {
                autoClose: true,
                boundary: modal
            });
            
            console.log('Bootstrap dropdown created for:', toggle.id);
            
            // Test the dropdown
            toggle.addEventListener('click', function(e) {
                console.log('Bootstrap dropdown clicked:', toggle.id);
            });
            
        } catch (error) {
            console.error('Error creating Bootstrap dropdown for', toggle.id, error);
        }
    });
}

function initializeManualDropdowns(modal) {
    console.log('Setting up manual dropdowns');
    
    // Remove Bootstrap attributes to avoid conflicts
    const dropdownToggles = modal.querySelectorAll('[data-bs-toggle="dropdown"]');
    dropdownToggles.forEach(toggle => {
        toggle.removeAttribute('data-bs-toggle');
    });
    
    // Add click handlers for dropdown buttons
    modal.addEventListener('click', function(e) {
        if (e.target.id === 'sort-dropdown' || e.target.closest('#sort-dropdown')) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Manual sort dropdown clicked');
            toggleManualDropdown('sort-dropdown');
        }
        
        if (e.target.id === 'filter-dropdown' || e.target.closest('#filter-dropdown')) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Manual filter dropdown clicked');
            toggleManualDropdown('filter-dropdown');
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            closeAllManualDropdowns();
        }
    });
}

function toggleManualDropdown(buttonId) {
    const button = document.getElementById(buttonId);
    if (!button) return;
    
    const menu = button.nextElementSibling;
    if (!menu || !menu.classList.contains('dropdown-menu')) return;
    
    const isOpen = menu.classList.contains('show');
    
    // Close all dropdowns first
    closeAllManualDropdowns();
    
    // Open this one if it wasn't already open
    if (!isOpen) {
        menu.classList.add('show');
        button.setAttribute('aria-expanded', 'true');
        console.log('Opened manual dropdown:', buttonId);
    }
}

function closeAllManualDropdowns() {
    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
        menu.classList.remove('show');
    });
    document.querySelectorAll('[aria-expanded="true"]').forEach(button => {
        button.setAttribute('aria-expanded', 'false');
    });
}

function setupDropdownOptionHandlers(modal) {
    // Handle sort option clicks
    modal.addEventListener('click', function(e) {
        if (e.target.classList.contains('sort-option')) {
            e.preventDefault();
            const sortType = e.target.getAttribute('data-sort');
            const buttonText = e.target.textContent.trim();
            
            console.log('Sort option clicked:', sortType);
            
            // Update button text
            const sortButton = document.getElementById('sort-dropdown');
            if (sortButton) {
                sortButton.innerHTML = `<i class="bi bi-sort-down"></i> ${buttonText}`;
            }
            
            // Close dropdown
            closeAllManualDropdowns();
            
            // Show notification
            showNotification(`Sorted by: ${buttonText}`);
            
            // Execute sort function if it exists
            if (typeof sortEmails === 'function') {
                sortEmails(sortType);
            } else {
                console.log('sortEmails function not found, sort type:', sortType);
            }
        }
        
        if (e.target.classList.contains('filter-option')) {
            e.preventDefault();
            const filterType = e.target.getAttribute('data-filter');
            const buttonText = e.target.textContent.trim();
            
            console.log('Filter option clicked:', filterType);
            
            // Update button text
            const filterButton = document.getElementById('filter-dropdown');
            if (filterButton) {
                filterButton.innerHTML = `<i class="bi bi-funnel"></i> ${buttonText}`;
            }
            
            // Close dropdown
            closeAllManualDropdowns();
            
            // Show notification
            showNotification(`Filtered by: ${buttonText}`);
            
            // Execute filter function if it exists
            if (typeof filterEmails === 'function') {
                filterEmails(filterType);
            } else {
                console.log('filterEmails function not found, filter type:', filterType);
            }
        }
    });
}

function setupOtherButtonHandlers(modal) {
    // Refresh button
    const refreshBtn = modal.querySelector('#refresh-gmail');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Refresh button clicked');
            
            // Reset dropdown texts
            const sortButton = document.getElementById('sort-dropdown');
            const filterButton = document.getElementById('filter-dropdown');
            
            if (sortButton) {
                sortButton.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
            }
            if (filterButton) {
                filterButton.innerHTML = '<i class="bi bi-funnel"></i> Filter';
            }
            
            // Clear search
            const searchInput = modal.querySelector('#email-search');
            if (searchInput) {
                searchInput.value = '';
            }
            
            // Show all emails if function exists
            if (typeof showAllEmails === 'function') {
                showAllEmails();
            }
            
            showNotification('Gmail refreshed');
        });
    }
    
    // Search button
    const searchBtn = modal.querySelector('#search-btn');
    const searchInput = modal.querySelector('#email-search');
    
    if (searchBtn && searchInput) {
        searchBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const searchTerm = searchInput.value.trim();
            console.log('Search button clicked, term:', searchTerm);
            
            if (typeof searchEmails === 'function') {
                searchEmails(searchTerm);
            } else {
                console.log('searchEmails function not found');
            }
        });
        
        // Search on Enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const searchTerm = this.value.trim();
                console.log('Search Enter pressed, term:', searchTerm);
                
                if (typeof searchEmails === 'function') {
                    searchEmails(searchTerm);
                } else {
                    console.log('searchEmails function not found');
                }
            }
        });
        
        // Clear search when empty
        searchInput.addEventListener('input', function(e) {
            if (this.value === '' && typeof showAllEmails === 'function') {
                showAllEmails();
            }
        });
    }
}

function showNotification(message) {
    // Create a simple toast notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 12px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        z-index: 9999;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Fade in
    setTimeout(() => notification.style.opacity = '1', 10);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Make functions globally available for debugging
window.gmailDropdownDebug = {
    setupGmailDropdowns,
    initializeDropdownsInModal,
    toggleManualDropdown,
    closeAllManualDropdowns
};

console.log('Gmail Dropdown Fix loaded successfully');
