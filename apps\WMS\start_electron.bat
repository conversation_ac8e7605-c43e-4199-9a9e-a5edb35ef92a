@echo off
setlocal

REM Check if a mode parameter was provided
if "%1"=="" (
    set MODE=development
) else (
    set MODE=%1
)

REM Set color based on mode
if "%MODE%"=="production" (
    color 0A
    echo Starting WMS Desktop Application in PRODUCTION mode...
    npm run electron-pack
) else if "%MODE%"=="development" (
    color 0B
    echo Starting WMS Desktop Application in DEVELOPMENT mode...
    npm run electron-dev
) else (
    color 0C
    echo Invalid mode: %MODE%
    echo Valid modes are: production, development
    echo Defaulting to development mode...
    npm run electron-dev
)

pause
