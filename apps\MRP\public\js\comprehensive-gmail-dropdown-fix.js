// COMPREHENSIVE GMAIL DROPDOWN FIX
// This script fixes the Gmail modal dropdown functionality by ensuring proper Bootstrap initialization

(function() {
    'use strict';
    
    console.log('🔧 Loading Comprehensive Gmail Dropdown Fix...');
    
    // Fix function that will be called when modal is shown
    function fixGmailDropdowns() {
        console.log('🎯 Fixing Gmail dropdowns...');
        
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal) {
            console.log('❌ Gmail modal not found');
            return;
        }
        
        // Get dropdown elements
        const sortBtn = document.getElementById('sort-dropdown');
        const filterBtn = document.getElementById('filter-dropdown');
        
        if (!sortBtn || !filterBtn) {
            console.log('❌ Dropdown buttons not found');
            return;
        }
        
        console.log('✅ Found dropdown buttons');
        
        // Step 1: Clean up any existing dropdown instances
        [sortBtn, filterBtn].forEach(btn => {
            try {
                const existingInstance = bootstrap.Dropdown.getInstance(btn);
                if (existingInstance) {
                    existingInstance.dispose();
                    console.log(`✅ Disposed existing dropdown for ${btn.id}`);
                }
            } catch (e) {
                // No existing instance, that's fine
            }
            
            // Remove any conflicting onclick attributes
            btn.removeAttribute('onclick');
            
            // Ensure proper Bootstrap attributes
            btn.setAttribute('data-bs-toggle', 'dropdown');
            btn.setAttribute('aria-expanded', 'false');
        });
        
        // Step 2: Create new Bootstrap dropdown instances
        try {
            const sortDropdown = new bootstrap.Dropdown(sortBtn, {
                autoClose: true,
                boundary: modal
            });
            
            const filterDropdown = new bootstrap.Dropdown(filterBtn, {
                autoClose: true,
                boundary: modal
            });
            
            console.log('✅ Created Bootstrap dropdown instances');
            
            // Step 3: Add debug click handlers
            sortBtn.addEventListener('click', function() {
                console.log('📊 Sort dropdown clicked');
            });
            
            filterBtn.addEventListener('click', function() {
                console.log('🔍 Filter dropdown clicked');
            });
            
            // Step 4: Ensure dropdown options work
            setTimeout(() => {
                setupDropdownOptions();
            }, 100);
            
            return true;
            
        } catch (error) {
            console.log('❌ Error creating Bootstrap dropdowns:', error);
            return false;
        }
    }
    
    // Setup dropdown option event handlers
    function setupDropdownOptions() {
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal) return;
        
        console.log('🎯 Setting up dropdown option handlers...');
        
        // Sort options
        const sortOptions = modal.querySelectorAll('.sort-option');
        sortOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const sortType = this.getAttribute('data-sort');
                const buttonText = this.textContent.trim();
                
                console.log('📊 Sort option clicked:', sortType);
                
                // Update button text
                const sortButton = document.getElementById('sort-dropdown');
                if (sortButton) {
                    sortButton.innerHTML = `<i class="bi bi-sort-down"></i> ${buttonText}`;
                }
                
                // Call sort function if it exists
                if (typeof sortEmails === 'function') {
                    sortEmails(sortType);
                } else {
                    console.log('⚠️ sortEmails function not found');
                }
                
                // Show notification
                showDropdownNotification(`Sorted by: ${buttonText}`);
            });
        });
        
        // Filter options
        const filterOptions = modal.querySelectorAll('.filter-option');
        filterOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const filterType = this.getAttribute('data-filter');
                const buttonText = this.textContent.trim();
                
                console.log('🔍 Filter option clicked:', filterType);
                
                // Update button text
                const filterButton = document.getElementById('filter-dropdown');
                if (filterButton) {
                    filterButton.innerHTML = `<i class="bi bi-funnel"></i> ${buttonText}`;
                }
                
                // Call filter function if it exists
                if (typeof filterEmails === 'function') {
                    filterEmails(filterType);
                } else {
                    console.log('⚠️ filterEmails function not found');
                }
                
                // Show notification
                showDropdownNotification(`Filtered by: ${buttonText}`);
            });
        });
        
        console.log(`✅ Set up ${sortOptions.length} sort options and ${filterOptions.length} filter options`);
    }
    
    // Show notification function
    function showDropdownNotification(message) {
        // Try to use existing toast function if available
        if (typeof showToast === 'function') {
            showToast(message);
        } else if (typeof showNotification === 'function') {
            showNotification(message);
        } else {
            // Fallback notification
            console.log('📢', message);
            
            // Create a simple toast
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 9999;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }
    }
    
    // Setup modal event listeners
    function setupModalEventListeners() {
        const modal = document.getElementById('mrp-gmailModal');
        if (!modal) return;
        
        // Fix dropdowns when modal is shown
        modal.addEventListener('shown.bs.modal', function() {
            console.log('📧 Gmail modal shown, fixing dropdowns...');
            setTimeout(fixGmailDropdowns, 100);
        });
        
        console.log('✅ Modal event listeners set up');
    }
    
    // Override the existing setupGmailSortAndFilter function
    window.setupGmailSortAndFilter = function() {
        console.log('🔄 setupGmailSortAndFilter called - using comprehensive fix');
        setTimeout(fixGmailDropdowns, 100);
    };
    
    // Make fix function available globally for manual testing
    window.fixGmailDropdowns = fixGmailDropdowns;
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', setupModalEventListeners);
    } else {
        setupModalEventListeners();
    }
    
    // Also try to fix immediately if modal is already open
    setTimeout(() => {
        const modal = document.getElementById('mrp-gmailModal');
        if (modal && modal.classList.contains('show')) {
            console.log('📧 Modal already open, fixing dropdowns immediately...');
            fixGmailDropdowns();
        }
    }, 1000);
    
    console.log('✅ Comprehensive Gmail Dropdown Fix loaded successfully');
    
})();
