<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Integration Test - MRP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Gmail Integration Test</h1>
        <button class="btn btn-primary" id="mrp-open-gmail-btn">
            <i class="bi bi-envelope me-2"></i>Open Gmail
        </button>
    </div>

    <!-- Load scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../SharedFeatures/ui/gmail-implementation.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Testing Gmail integration...');
            
            // Initialize Gmail integration with MRP-specific configuration
            initializeGmail({
                appName: 'MRP',
                appPrefix: 'mrp',
                modalId: 'mrp-gmailModal',
                primaryColor: '#fd7e14', // Orange for MRP
                debug: true
            });

            console.log('Gmail integration initialized. Modal should be created.');
        });
    </script>
</body>
</html>
