<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting to WMS Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        :root {
            --app-primary-color: #2ecc71; /* Green for WMS */
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        
        .redirect-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
            color: var(--app-primary-color);
        }
        
        .redirect-text {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="redirect-text">Redirecting to WMS Dashboard...</p>
    </div>
    
    <script>
        // Redirect to dashboard.html after a short delay
        setTimeout(() => {
            window.location.href = 'dashboard.html';
        }, 1000);
    </script>
</body>
</html>
