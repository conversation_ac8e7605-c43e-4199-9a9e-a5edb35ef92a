@echo off
setlocal enabledelayedexpansion

echo ===================================
echo    ISA Suite Installer
echo ===================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo This installer requires administrator privileges.
    echo Please right-click on this file and select "Run as administrator".
    pause
    exit /b 1
)

REM Set installation directory
set "INSTALL_DIR=C:\ISASUITE"
set "DESKTOP_DIR=%USERPROFILE%\Desktop"

echo This installer will set up the ISA Suite on your computer.
echo.
echo Installation directory: %INSTALL_DIR%
echo.
echo Select installation type:
echo 1. Full Installation (Admin version with development tools)
echo 2. User Installation (User version without development tools)
echo.
set /p INSTALL_TYPE=Enter your choice (1-2):

if "%INSTALL_TYPE%" neq "1" if "%INSTALL_TYPE%" neq "2" (
    echo Invalid choice. Please enter 1 or 2.
    pause
    exit /b 1
)

REM Create installation directory if it doesn't exist
if not exist "%INSTALL_DIR%" (
    echo Creating installation directory...
    mkdir "%INSTALL_DIR%"
)

REM Copy files to installation directory
echo Copying files to installation directory...
xcopy /E /I /Y "%~dp0*" "%INSTALL_DIR%"

REM Create logs directory
if not exist "%INSTALL_DIR%\logs" (
    mkdir "%INSTALL_DIR%\logs"
)

REM Create desktop shortcuts
echo Creating desktop shortcuts...

REM Create shortcut for Service Manager
echo Creating shortcut for ISA Suite Service Manager...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_DIR%\ISA Suite Service Manager.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\service-manager.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'ISA Suite Service Manager'; $Shortcut.Save()"

REM Create shortcut for Integration Hub
echo Creating shortcut for ISA Suite Integration Hub...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_DIR%\ISA Suite Integration Hub.lnk'); $Shortcut.TargetPath = 'http://localhost:8000'; $Shortcut.Description = 'ISA Suite Integration Hub'; $Shortcut.Save()"

REM Create additional shortcuts based on installation type
if "%INSTALL_TYPE%"=="1" (
    echo Creating admin shortcuts...
    
    REM Create shortcut for Start Apps
    powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_DIR%\ISA Suite - Start Apps.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\start-apps-consolidated.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Start ISA Suite Applications'; $Shortcut.Save()"
    
    REM Create shortcut for Stop Apps
    powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_DIR%\ISA Suite - Stop Apps.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\stop-apps.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Stop ISA Suite Applications'; $Shortcut.Save()"
)

echo.
echo Installation completed successfully!
echo.
echo Desktop shortcuts have been created:
echo - ISA Suite Service Manager
echo - ISA Suite Integration Hub
if "%INSTALL_TYPE%"=="1" (
    echo - ISA Suite - Start Apps
    echo - ISA Suite - Stop Apps
)
echo.
echo To start the ISA Suite, double-click on the "ISA Suite Service Manager" shortcut on your desktop.
echo.
pause
