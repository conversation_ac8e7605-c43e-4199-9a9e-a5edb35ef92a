/**
 * CRM Gmail Fix
 * 
 * This file provides a direct fix for the Gmail attachment functionality in the CRM application.
 */

// Execute when the document is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('CRM Gmail Fix loaded');
    
    // Initialize the attachment functionality
    initGmailAttachments();
});

/**
 * Initialize Gmail attachment functionality
 */
function initGmailAttachments() {
    // Add attachment functionality to the compose form
    addAttachmentToComposeForm();
    
    // Set up event listeners for email opening
    setupEmailOpenListeners();
    
    console.log('Gmail attachment functionality initialized');
}

/**
 * Add attachment functionality to the compose form
 */
function addAttachmentToComposeForm() {
    // Find the compose form
    const composeForm = document.querySelector('#compose-content form');
    if (!composeForm) {
        console.error('Compose form not found');
        return;
    }
    
    // Find the attach button
    const attachButton = composeForm.querySelector('button:has(i.bi-paperclip)');
    if (!attachButton) {
        console.error('Attach button not found in compose form');
        return;
    }
    
    // Create a file input element
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.id = 'email-attachment';
    fileInput.className = 'd-none';
    fileInput.multiple = true;
    
    // Add the file input to the form
    composeForm.appendChild(fileInput);
    
    // Create an attachments container
    const attachmentsContainer = document.createElement('div');
    attachmentsContainer.id = 'email-attachments-container';
    attachmentsContainer.className = 'mb-3 d-none';
    attachmentsContainer.innerHTML = `
        <label class="form-label">Attachments</label>
        <div id="email-attachments-list" class="border rounded p-2 bg-light">
            <!-- Attachments will be listed here -->
        </div>
    `;
    
    // Insert the attachments container before the attach button's parent
    const attachButtonParent = attachButton.parentNode;
    attachButtonParent.parentNode.insertBefore(attachmentsContainer, attachButtonParent);
    
    // Add click event to the attach button
    attachButton.addEventListener('click', function(e) {
        e.preventDefault();
        fileInput.click();
    });
    
    // Add change event to the file input
    fileInput.addEventListener('change', function() {
        handleFileSelection(this, 'email-attachments-container', 'email-attachments-list');
    });
    
    // Add event listener to the send button
    const sendButton = composeForm.querySelector('button.btn-primary');
    if (sendButton) {
        sendButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get form values
            const to = document.getElementById('email-to').value;
            const subject = document.getElementById('email-subject').value;
            const body = document.getElementById('email-body').value;
            
            // Validate form
            if (!to || !subject || !body) {
                alert('Please fill in all required fields (To, Subject, Message)');
                return;
            }
            
            // Get attachments
            const attachmentsList = document.getElementById('email-attachments-list');
            const attachmentItems = attachmentsList ? attachmentsList.querySelectorAll('.attachment-item') : [];
            
            // Show success message
            let successMessage = 'Email sent successfully!';
            if (attachmentItems.length > 0) {
                successMessage += `\nAttachments: ${attachmentItems.length} file(s)`;
            }
            
            alert(successMessage);
            
            // Reset form
            composeForm.reset();
            
            // Clear attachments
            if (attachmentsList) {
                attachmentsList.innerHTML = '';
                document.getElementById('email-attachments-container').classList.add('d-none');
            }
            
            // Switch back to inbox tab
            document.getElementById('inbox-tab').click();
        });
    }
}

/**
 * Set up event listeners for email opening
 */
function setupEmailOpenListeners() {
    // Find all email items in the inbox
    const emailItems = document.querySelectorAll('#inbox-content .list-group-item');
    
    emailItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get email data
            const sender = this.getAttribute('data-sender');
            const subject = this.getAttribute('data-subject');
            
            // Open the email
            openEmail(sender, subject);
        });
    });
}

/**
 * Open an email and display it in the read tab
 */
function openEmail(sender, subject) {
    // Switch to read tab
    document.getElementById('read-tab').click();
    
    // Get the read content container
    const readContent = document.getElementById('read-content');
    if (!readContent) return;
    
    // Generate email content based on sender and subject
    let emailContent = '';
    let hasAttachments = false;
    
    if (sender === 'john-davis' && subject.includes('Meeting follow-up')) {
        emailContent = `
            <div class="email-header mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>${subject}</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-printer"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary ms-1">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                    </div>
                </div>
                <div class="d-flex align-items-center mt-2">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">JD</div>
                    <div class="ms-2">
                        <div class="fw-bold">John Davis</div>
                        <div class="small text-muted"><EMAIL></div>
                    </div>
                    <div class="ms-auto text-muted">10:30 AM</div>
                </div>
            </div>
            <div class="email-body mb-4">
                <p>Dear Team,</p>
                <p>Thank you for the product demonstration yesterday. Our team was impressed with the features and capabilities of your solution.</p>
                <p>We have a few questions and feedback points that we'd like to discuss:</p>
                <ol>
                    <li>Can the reporting module be customized to include our specific KPIs?</li>
                    <li>What is the timeline for implementing the integration with our existing CRM system?</li>
                    <li>The user interface is intuitive, but we'd like to see more customization options for our admin users.</li>
                </ol>
                <p>I've attached our feedback document with more detailed notes from the team. Could we schedule a follow-up call next week to discuss these points?</p>
                <p>Best regards,<br>John Davis<br>Product Manager<br>ABC Corporation</p>
            </div>
            <div class="email-attachments mb-4">
                <h6><i class="bi bi-paperclip me-2"></i>Attachments (1)</h6>
                <div class="attachment-item d-flex align-items-center p-2 border rounded mb-2">
                    <i class="bi bi-file-earmark-pdf text-danger me-2 fs-4"></i>
                    <div>
                        <div>Product_Demo_Feedback.pdf</div>
                        <small class="text-muted">245 KB</small>
                    </div>
                    <div class="ms-auto">
                        <button class="btn btn-sm btn-outline-primary view-attachment-btn" data-file-name="Product_Demo_Feedback.pdf" data-file-type="pdf">
                            <i class="bi bi-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-outline-success download-attachment-btn" data-file-name="Product_Demo_Feedback.pdf" data-file-type="pdf">
                            <i class="bi bi-download"></i> Download
                        </button>
                    </div>
                </div>
            </div>
        `;
        hasAttachments = true;
    } else if (sender === 'sarah-miller' && subject.includes('Contract renewal')) {
        emailContent = `
            <div class="email-header mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>${subject}</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-printer"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary ms-1">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                    </div>
                </div>
                <div class="d-flex align-items-center mt-2">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #ea4335; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">SM</div>
                    <div class="ms-2">
                        <div class="fw-bold">Sarah Miller</div>
                        <div class="small text-muted"><EMAIL></div>
                    </div>
                    <div class="ms-auto text-muted">Yesterday</div>
                </div>
            </div>
            <div class="email-body mb-4">
                <p>Hello,</p>
                <p>I'd like to schedule a call to discuss the upcoming contract renewal for our enterprise subscription.</p>
                <p>Our current contract expires in 45 days, and we need to review the terms and pricing for the next period. We're also interested in exploring the new features you've added since our last renewal.</p>
                <p>I've attached the current contract for reference. Could you please provide some available time slots for next week?</p>
                <p>Thank you,<br>Sarah Miller<br>Procurement Manager<br>XYZ Inc.</p>
            </div>
            <div class="email-attachments mb-4">
                <h6><i class="bi bi-paperclip me-2"></i>Attachments (2)</h6>
                <div class="attachment-item d-flex align-items-center p-2 border rounded mb-2">
                    <i class="bi bi-file-earmark-pdf text-danger me-2 fs-4"></i>
                    <div>
                        <div>Current_Contract_XYZ_Inc.pdf</div>
                        <small class="text-muted">1.2 MB</small>
                    </div>
                    <div class="ms-auto">
                        <button class="btn btn-sm btn-outline-primary view-attachment-btn" data-file-name="Current_Contract_XYZ_Inc.pdf" data-file-type="pdf">
                            <i class="bi bi-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-outline-success download-attachment-btn" data-file-name="Current_Contract_XYZ_Inc.pdf" data-file-type="pdf">
                            <i class="bi bi-download"></i> Download
                        </button>
                    </div>
                </div>
                <div class="attachment-item d-flex align-items-center p-2 border rounded mb-2">
                    <i class="bi bi-file-earmark-excel text-success me-2 fs-4"></i>
                    <div>
                        <div>Service_Usage_Report.xlsx</div>
                        <small class="text-muted">345 KB</small>
                    </div>
                    <div class="ms-auto">
                        <button class="btn btn-sm btn-outline-primary view-attachment-btn" data-file-name="Service_Usage_Report.xlsx" data-file-type="excel">
                            <i class="bi bi-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-outline-success download-attachment-btn" data-file-name="Service_Usage_Report.xlsx" data-file-type="excel">
                            <i class="bi bi-download"></i> Download
                        </button>
                    </div>
                </div>
            </div>
        `;
        hasAttachments = true;
    } else {
        emailContent = `
            <div class="email-header mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>${subject}</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-printer"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary ms-1">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                    </div>
                </div>
                <div class="d-flex align-items-center mt-2">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #34a853; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">${sender.charAt(0).toUpperCase()}</div>
                    <div class="ms-2">
                        <div class="fw-bold">${sender.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
                        <div class="small text-muted">${sender}@example.com</div>
                    </div>
                    <div class="ms-auto text-muted">May 10</div>
                </div>
            </div>
            <div class="email-body mb-4">
                <p>Hello,</p>
                <p>This is the content of the email with subject: ${subject}</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
                <p>Regards,<br>${sender.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
            </div>
        `;
    }
    
    // Add reply form
    const replyForm = `
        <div class="email-reply mt-4">
            <h6>Reply</h6>
            <form class="reply-email-form">
                <div class="mb-3">
                    <textarea class="form-control" rows="5" placeholder="Type your reply here..."></textarea>
                </div>
                <div class="mb-3">
                    <div class="d-flex align-items-center">
                        <button type="button" class="btn btn-outline-secondary me-2 crm-reply-attach-btn">
                            <i class="bi bi-paperclip"></i> Attach
                        </button>
                        <input type="file" class="d-none crm-reply-file-input" multiple>
                    </div>
                </div>
                <div class="crm-reply-attachments-container mb-3 d-none">
                    <div class="border rounded p-2 bg-light crm-reply-attachments-list">
                        <!-- Attachments will be listed here -->
                    </div>
                </div>
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-outline-secondary me-2">Discard</button>
                    <button type="button" class="btn btn-primary crm-reply-send-btn">Send</button>
                </div>
            </form>
        </div>
    `;
    
    // Set the content
    readContent.innerHTML = `
        <div class="email-content">
            ${emailContent}
            <div class="email-actions mb-4">
                <button class="btn btn-primary me-2 reply-btn">
                    <i class="bi bi-reply"></i> Reply
                </button>
                <button class="btn btn-outline-primary me-2">
                    <i class="bi bi-reply-all"></i> Reply All
                </button>
                <button class="btn btn-outline-primary me-2">
                    <i class="bi bi-forward"></i> Forward
                </button>
                <button class="btn btn-outline-danger">
                    <i class="bi bi-trash"></i> Delete
                </button>
            </div>
            ${replyForm}
        </div>
    `;
    
    // Set up attachment handlers
    if (hasAttachments) {
        setupAttachmentHandlers();
    }
    
    // Set up reply form handlers
    setupReplyFormHandlers();
}

/**
 * Set up attachment handlers
 */
function setupAttachmentHandlers() {
    // View attachment buttons
    document.querySelectorAll('.view-attachment-btn').forEach(button => {
        button.addEventListener('click', function() {
            const fileName = this.getAttribute('data-file-name');
            const fileType = this.getAttribute('data-file-type');
            alert(`Viewing ${fileName} (${fileType})`);
        });
    });
    
    // Download attachment buttons
    document.querySelectorAll('.download-attachment-btn').forEach(button => {
        button.addEventListener('click', function() {
            const fileName = this.getAttribute('data-file-name');
            const fileType = this.getAttribute('data-file-type');
            alert(`Downloading ${fileName} (${fileType})`);
        });
    });
}

/**
 * Set up reply form handlers
 */
function setupReplyFormHandlers() {
    // Attach button
    const attachButton = document.querySelector('.crm-reply-attach-btn');
    const fileInput = document.querySelector('.crm-reply-file-input');
    
    if (attachButton && fileInput) {
        attachButton.addEventListener('click', function() {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', function() {
            handleFileSelection(this, 'crm-reply-attachments-container', 'crm-reply-attachments-list');
        });
    }
    
    // Send button
    const sendButton = document.querySelector('.crm-reply-send-btn');
    if (sendButton) {
        sendButton.addEventListener('click', function() {
            // Get reply text
            const replyText = document.querySelector('.reply-email-form textarea').value;
            
            // Validate
            if (!replyText) {
                alert('Please enter a reply message');
                return;
            }
            
            // Get attachments
            const attachmentsList = document.querySelector('.crm-reply-attachments-list');
            const attachmentItems = attachmentsList ? attachmentsList.querySelectorAll('.attachment-item') : [];
            
            // Show success message
            let successMessage = 'Reply sent successfully!';
            if (attachmentItems.length > 0) {
                successMessage += `\nAttachments: ${attachmentItems.length} file(s)`;
            }
            
            alert(successMessage);
            
            // Reset form
            document.querySelector('.reply-email-form textarea').value = '';
            
            // Clear attachments
            if (attachmentsList) {
                attachmentsList.innerHTML = '';
                document.querySelector('.crm-reply-attachments-container').classList.add('d-none');
            }
            
            // Switch back to inbox tab
            document.getElementById('inbox-tab').click();
        });
    }
}

/**
 * Handle file selection
 */
function handleFileSelection(fileInput, containerID, listID) {
    if (!fileInput.files || fileInput.files.length === 0) return;
    
    // Get the container and list
    const container = document.getElementById(containerID);
    const list = document.getElementById(listID);
    
    if (!container || !list) return;
    
    // Show the container
    container.classList.remove('d-none');
    
    // Add each file to the list
    for (let i = 0; i < fileInput.files.length; i++) {
        const file = fileInput.files[i];
        
        // Create attachment item
        const attachmentItem = document.createElement('div');
        attachmentItem.className = 'attachment-item d-flex align-items-center p-2 border rounded mb-2';
        
        // Determine file icon
        let iconClass = 'bi-file-earmark';
        if (file.name.endsWith('.pdf')) {
            iconClass = 'bi-file-earmark-pdf text-danger';
        } else if (file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
            iconClass = 'bi-file-earmark-word text-primary';
        } else if (file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) {
            iconClass = 'bi-file-earmark-excel text-success';
        } else if (file.name.endsWith('.jpg') || file.name.endsWith('.png') || file.name.endsWith('.gif')) {
            iconClass = 'bi-file-earmark-image text-info';
        }
        
        // Format file size
        const fileSize = formatFileSize(file.size);
        
        // Set attachment item content
        attachmentItem.innerHTML = `
            <i class="bi ${iconClass} me-2 fs-4"></i>
            <div>
                <div>${file.name}</div>
                <small class="text-muted">${fileSize}</small>
            </div>
            <div class="ms-auto">
                <button class="btn btn-sm btn-outline-danger remove-attachment-btn">
                    <i class="bi bi-x"></i> Remove
                </button>
            </div>
        `;
        
        // Add to list
        list.appendChild(attachmentItem);
        
        // Add remove button handler
        const removeButton = attachmentItem.querySelector('.remove-attachment-btn');
        if (removeButton) {
            removeButton.addEventListener('click', function() {
                attachmentItem.remove();
                
                // If no more attachments, hide the container
                if (list.children.length === 0) {
                    container.classList.add('d-none');
                }
            });
        }
    }
    
    // Clear the file input
    fileInput.value = '';
}

/**
 * Format file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Make functions globally available
window.openEmail = openEmail;
window.handleFileSelection = handleFileSelection;
