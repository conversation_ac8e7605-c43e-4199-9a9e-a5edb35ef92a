/**
 * Debug script for Gmail dropdown functionality
 * Run this in browser console to diagnose dropdown issues
 */

function debugGmailDropdowns() {
    console.log('=== Gmail Dropdown Debug Report ===');
    
    // Check if modal exists
    const modal = document.getElementById('gmailModal');
    console.log('Gmail Modal found:', !!modal);
    
    if (modal) {
        console.log('Modal display style:', window.getComputedStyle(modal).display);
        console.log('Modal visibility:', window.getComputedStyle(modal).visibility);
    }
    
    // Check for dropdown buttons
    const sortDropdown = document.getElementById('sort-dropdown');
    const filterDropdown = document.getElementById('filter-dropdown');
    
    console.log('Sort dropdown button found:', !!sortDropdown);
    console.log('Filter dropdown button found:', !!filterDropdown);
    
    if (sortDropdown) {
        console.log('Sort dropdown attributes:', {
            'data-bs-toggle': sortDropdown.getAttribute('data-bs-toggle'),
            'aria-expanded': sortDropdown.getAttribute('aria-expanded'),
            'class': sortDropdown.className
        });
        
        const sortMenu = sortDropdown.nextElementSibling;
        console.log('Sort menu found:', !!sortMenu);
        if (sortMenu) {
            console.log('Sort menu classes:', sortMenu.className);
            console.log('Sort options count:', sortMenu.querySelectorAll('.sort-option').length);
        }
    }
    
    if (filterDropdown) {
        console.log('Filter dropdown attributes:', {
            'data-bs-toggle': filterDropdown.getAttribute('data-bs-toggle'),
            'aria-expanded': filterDropdown.getAttribute('aria-expanded'),
            'class': filterDropdown.className
        });
        
        const filterMenu = filterDropdown.nextElementSibling;
        console.log('Filter menu found:', !!filterMenu);
        if (filterMenu) {
            console.log('Filter menu classes:', filterMenu.className);
            console.log('Filter options count:', filterMenu.querySelectorAll('.filter-option').length);
        }
    }
    
    // Check Bootstrap availability
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap version:', bootstrap.Tooltip?.VERSION || 'Unknown');
    }
    
    // Check our custom scripts
    console.log('MRP Gmail Dropdowns loaded:', typeof window.MRPGmailDropdowns !== 'undefined');
    console.log('MRP Gmail Utils loaded:', typeof window.MRPGmailUtils !== 'undefined');
    
    if (window.MRPGmailDropdowns) {
        console.log('Dropdown debug object:', window.MRPGmailDropdowns.debug);
    }
    
    // Check for existing event listeners
    const events = [];
    if (sortDropdown) {
        const clickListeners = getEventListeners(sortDropdown);
        if (clickListeners && clickListeners.click) {
            events.push(`Sort dropdown has ${clickListeners.click.length} click listeners`);
        }
    }
    if (filterDropdown) {
        const clickListeners = getEventListeners(filterDropdown);
        if (clickListeners && clickListeners.click) {
            events.push(`Filter dropdown has ${clickListeners.click.length} click listeners`);
        }
    }
    
    if (events.length > 0) {
        console.log('Event listeners:', events);
    } else {
        console.log('Note: getEventListeners() not available - this is normal in production');
    }
    
    console.log('=== End Debug Report ===');
    
    return {
        modal: !!modal,
        sortDropdown: !!sortDropdown,
        filterDropdown: !!filterDropdown,
        bootstrap: typeof bootstrap !== 'undefined',
        mrpDropdowns: typeof window.MRPGmailDropdowns !== 'undefined',
        mrpUtils: typeof window.MRPGmailUtils !== 'undefined'
    };
}

function testGmailDropdownClicks() {
    console.log('=== Testing Gmail Dropdown Clicks ===');
    
    const sortDropdown = document.getElementById('sort-dropdown');
    const filterDropdown = document.getElementById('filter-dropdown');
    
    if (sortDropdown) {
        console.log('Triggering click on sort dropdown...');
        sortDropdown.click();
        
        setTimeout(() => {
            const sortMenu = sortDropdown.nextElementSibling;
            const isOpen = sortMenu.classList.contains('show');
            console.log('Sort dropdown opened:', isOpen);
        }, 100);
    }
    
    if (filterDropdown) {
        setTimeout(() => {
            console.log('Triggering click on filter dropdown...');
            filterDropdown.click();
            
            setTimeout(() => {
                const filterMenu = filterDropdown.nextElementSibling;
                const isOpen = filterMenu.classList.contains('show');
                console.log('Filter dropdown opened:', isOpen);
            }, 100);
        }, 500);
    }
}

function openGmailModal() {
    console.log('Attempting to open Gmail modal...');
    
    // Look for Gmail button or link
    const gmailButton = document.querySelector('[data-bs-target="#gmailModal"]') || 
                       document.querySelector('[onclick*="gmail"]') ||
                       document.querySelector('.gmail-btn') ||
                       document.querySelector('#gmail-btn');
    
    if (gmailButton) {
        console.log('Found Gmail button, clicking...');
        gmailButton.click();
        
        setTimeout(() => {
            const modal = document.getElementById('gmailModal');
            const isVisible = modal && window.getComputedStyle(modal).display !== 'none';
            console.log('Gmail modal opened:', isVisible);
            
            if (isVisible) {
                console.log('Modal is open, you can now test dropdowns with testGmailDropdownClicks()');
            }
        }, 500);
    } else {
        console.log('Gmail button not found. Look for it manually or check if modal is already open.');
    }
}

// Make functions available globally for console use
window.debugGmailDropdowns = debugGmailDropdowns;
window.testGmailDropdownClicks = testGmailDropdownClicks;
window.openGmailModal = openGmailModal;

console.log('Gmail dropdown debug functions loaded. Available commands:');
console.log('- debugGmailDropdowns() - Get detailed debug info');
console.log('- testGmailDropdownClicks() - Test clicking dropdowns');
console.log('- openGmailModal() - Try to open Gmail modal');
