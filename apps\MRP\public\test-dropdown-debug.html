<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Dropdown Debug Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <h1>Gmail Dropdown Debug Test</h1>
        <p>This page tests the Gmail dropdown functionality in isolation.</p>
        
        <div class="card">
            <div class="card-header">
                <h5>Gmail Modal Simulation</h5>
            </div>
            <div class="card-body">
                <!-- Simulate the Gmail modal structure -->
                <div id="mrp-gmailModal" class="show">
                    <div class="d-flex gap-2 mb-3">
                        <!-- Sort Dropdown -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sort-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-sort-down"></i> Sort
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item sort-option" href="#" data-sort="date-desc">Date (Newest First)</a></li>
                                <li><a class="dropdown-item sort-option" href="#" data-sort="date-asc">Date (Oldest First)</a></li>
                                <li><a class="dropdown-item sort-option" href="#" data-sort="sender-asc">Sender (A-Z)</a></li>
                                <li><a class="dropdown-item sort-option" href="#" data-sort="sender-desc">Sender (Z-A)</a></li>
                                <li><a class="dropdown-item sort-option" href="#" data-sort="subject-asc">Subject (A-Z)</a></li>
                                <li><a class="dropdown-item sort-option" href="#" data-sort="subject-desc">Subject (Z-A)</a></li>
                            </ul>
                        </div>

                        <!-- Filter Dropdown -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="filter-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-funnel"></i> Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item filter-option" href="#" data-filter="all">All</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="unread">Unread</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="read">Read</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="label-inventory">Inventory</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="label-production">Production</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="label-urgent">Urgent</a></li>
                            </ul>
                        </div>

                        <!-- Refresh Button -->
                        <button id="refresh-gmail" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                    </div>

                    <!-- Search -->
                    <div class="input-group mb-3">
                        <input type="text" id="email-search" class="form-control" placeholder="Search emails...">
                        <button id="search-btn" class="btn btn-outline-secondary" type="button">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>

                    <!-- Email List Section -->
                    <div id="mrp-email-list-section">
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 fw-bold">John Smith</h6>
                                    <small class="text-muted">3 days ago</small>
                                </div>
                                <p class="mb-1 fw-bold">Inventory Update Required</p>
                                <small class="text-muted">Please review the attached inventory report...</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 fw-bold">Sarah Johnson</h6>
                                    <small class="text-muted">1 week ago</small>
                                </div>
                                <p class="mb-1 fw-bold">Production Schedule Changes</p>
                                <small class="text-muted">The production schedule for next month has been updated...</small>
                            </div>
                            <div class="list-group-item unread">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 fw-bold">Mike Davis</h6>
                                    <small class="text-muted">2 hours ago</small>
                                </div>
                                <p class="mb-1 fw-bold">Urgent: Equipment Maintenance</p>
                                <small class="text-muted">The main production line requires immediate attention...</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5>Debug Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button onclick="testBootstrapDropdowns()" class="btn btn-primary mb-2 w-100">Test Bootstrap Dropdowns</button>
                        <button onclick="testManualDropdowns()" class="btn btn-secondary mb-2 w-100">Test Manual Dropdowns</button>
                        <button onclick="testSearchFunction()" class="btn btn-info mb-2 w-100">Test Search Function</button>
                    </div>
                    <div class="col-md-6">
                        <button onclick="testSortFunction()" class="btn btn-warning mb-2 w-100">Test Sort Function</button>
                        <button onclick="testFilterFunction()" class="btn btn-success mb-2 w-100">Test Filter Function</button>
                        <button onclick="showDebugInfo()" class="btn btn-dark mb-2 w-100">Show Debug Info</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="debug-output" class="card mt-4" style="display: none;">
            <div class="card-header">
                <h5>Debug Output</h5>
            </div>
            <div class="card-body">
                <pre id="debug-content"></pre>
            </div>
        </div>
    </div>

    <!-- Load Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Load our Gmail scripts -->
    <script src="/js/mrp-gmail-utils.js"></script>
    <script src="/js/mrp-gmail-dropdowns.js"></script>

    <script>
        // Debug functions
        function testBootstrapDropdowns() {
            log('Testing Bootstrap dropdowns...');
            
            if (typeof bootstrap === 'undefined') {
                log('ERROR: Bootstrap is not loaded!');
                return;
            }
            
            const sortBtn = document.getElementById('sort-dropdown');
            const filterBtn = document.getElementById('filter-dropdown');
            
            log('Sort button found:', !!sortBtn);
            log('Filter button found:', !!filterBtn);
            
            // Try to create Bootstrap dropdowns
            try {
                const sortDropdown = new bootstrap.Dropdown(sortBtn);
                log('Sort dropdown created successfully');
                sortDropdown.show();
                setTimeout(() => sortDropdown.hide(), 2000);
            } catch (error) {
                log('ERROR creating sort dropdown:', error.message);
            }
            
            try {
                const filterDropdown = new bootstrap.Dropdown(filterBtn);
                log('Filter dropdown created successfully');
            } catch (error) {
                log('ERROR creating filter dropdown:', error.message);
            }
        }
        
        function testManualDropdowns() {
            log('Testing manual dropdown toggle...');
            
            if (typeof window.gmailDropdownDebug !== 'undefined') {
                window.gmailDropdownDebug.toggleManualDropdown('sort-dropdown');
                log('Manual sort dropdown toggled');
                setTimeout(() => {
                    window.gmailDropdownDebug.toggleManualDropdown('filter-dropdown');
                    log('Manual filter dropdown toggled');
                }, 2000);
            } else {
                log('ERROR: Gmail dropdown debug functions not available');
            }
        }
        
        function testSearchFunction() {
            log('Testing search function...');
            
            if (typeof searchEmails === 'function') {
                searchEmails('John');
                log('Search executed for "John"');
                setTimeout(() => {
                    searchEmails('');
                    log('Search cleared');
                }, 3000);
            } else {
                log('ERROR: searchEmails function not found');
            }
        }
        
        function testSortFunction() {
            log('Testing sort function...');
            
            if (typeof sortEmails === 'function') {
                sortEmails('sender-asc');
                log('Sort by sender ascending executed');
            } else {
                log('ERROR: sortEmails function not found');
            }
        }
        
        function testFilterFunction() {
            log('Testing filter function...');
            
            if (typeof filterEmails === 'function') {
                filterEmails('unread');
                log('Filter by unread executed');
                setTimeout(() => {
                    filterEmails('all');
                    log('Filter reset to show all');
                }, 3000);
            } else {
                log('ERROR: filterEmails function not found');
            }
        }
        
        function showDebugInfo() {
            log('=== DEBUG INFO ===');
            log('Bootstrap available:', typeof bootstrap !== 'undefined');
            log('jQuery available:', typeof $ !== 'undefined');
            log('Gmail dropdown debug available:', typeof window.gmailDropdownDebug !== 'undefined');
            log('searchEmails function available:', typeof searchEmails === 'function');
            log('sortEmails function available:', typeof sortEmails === 'function');
            log('filterEmails function available:', typeof filterEmails === 'function');
            log('showAllEmails function available:', typeof showAllEmails === 'function');
            
            const modal = document.getElementById('mrp-gmailModal');
            log('Gmail modal found:', !!modal);
            
            const sortBtn = document.getElementById('sort-dropdown');
            const filterBtn = document.getElementById('filter-dropdown');
            log('Sort button found:', !!sortBtn);
            log('Filter button found:', !!filterBtn);
            
            if (sortBtn) {
                log('Sort button has data-bs-toggle:', sortBtn.hasAttribute('data-bs-toggle'));
                log('Sort button onclick:', sortBtn.getAttribute('onclick'));
            }
            
            if (filterBtn) {
                log('Filter button has data-bs-toggle:', filterBtn.hasAttribute('data-bs-toggle'));
                log('Filter button onclick:', filterBtn.getAttribute('onclick'));
            }
            
            const emailList = document.querySelector('#mrp-email-list-section .list-group');
            log('Email list container found:', !!emailList);
            
            if (emailList) {
                const emailItems = emailList.querySelectorAll('.list-group-item');
                log('Number of email items:', emailItems.length);
            }
        }
        
        function log(message, data = '') {
            const debugOutput = document.getElementById('debug-output');
            const debugContent = document.getElementById('debug-content');
            
            debugOutput.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message} ${data}\n`;
            
            debugContent.textContent += logEntry;
            debugContent.scrollTop = debugContent.scrollHeight;
            
            console.log(message, data);
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, waiting for scripts to initialize...');
            setTimeout(() => {
                showDebugInfo();
            }, 1000);
        });
    </script>
</body>
</html>
