/**
 * Google Integration Handler for WMS Application
 * Handles all Google Drive, Docs, Sheets, and other Google service interactions
 */

class GoogleIntegrationHandler {
    constructor() {
        this.currentItem = null;
        this.debug = true;
    }

    /**
     * Initialize the handler
     */
    init() {
        this.log('Initializing Google Integration Handler');
        this.setupActionHandlers();
    }

    /**
     * Set up handlers for Google item actions
     */
    setupActionHandlers() {
        // Delegate event handling for file actions
        document.addEventListener('click', (event) => {
            // View action
            if (event.target.closest('[onclick*="viewGoogleItem"]')) {
                const button = event.target.closest('[onclick*="viewGoogleItem"]');
                if (button) {
                    const onclickAttr = button.getAttribute('onclick');
                    const match = onclickAttr.match(/viewGoogleItem\('([^']+)',\s*'([^']+)',\s*'([^']+)'\)/);
                    if (match) {
                        const [_, app, type, itemId] = match;
                        event.preventDefault();
                        this.viewItem(app, type, itemId);
                    }
                }
            }
            
            // Download action
            if (event.target.closest('[onclick*="downloadGoogleItem"]')) {
                const button = event.target.closest('[onclick*="downloadGoogleItem"]');
                if (button) {
                    const onclickAttr = button.getAttribute('onclick');
                    const match = onclickAttr.match(/downloadGoogleItem\('([^']+)',\s*'([^']+)',\s*'([^']+)'\)/);
                    if (match) {
                        const [_, app, type, itemId] = match;
                        event.preventDefault();
                        this.downloadItem(app, type, itemId);
                    }
                }
            }
            
            // Share action
            if (event.target.closest('[onclick*="shareGoogleItem"]')) {
                const button = event.target.closest('[onclick*="shareGoogleItem"]');
                if (button) {
                    const onclickAttr = button.getAttribute('onclick');
                    const match = onclickAttr.match(/shareGoogleItem\('([^']+)',\s*'([^']+)',\s*'([^']+)'\)/);
                    if (match) {
                        const [_, app, type, itemId] = match;
                        event.preventDefault();
                        this.shareItem(app, type, itemId);
                    }
                }
            }
            
            // Delete action
            if (event.target.closest('[onclick*="deleteGoogleItem"]')) {
                const button = event.target.closest('[onclick*="deleteGoogleItem"]');
                if (button) {
                    const onclickAttr = button.getAttribute('onclick');
                    const match = onclickAttr.match(/deleteGoogleItem\('([^']+)',\s*'([^']+)',\s*'([^']+)'\)/);
                    if (match) {
                        const [_, app, type, itemId] = match;
                        event.preventDefault();
                        this.deleteItem(app, type, itemId);
                    }
                }
            }
        });
    }

    /**
     * View a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @param {string} itemId - Item identifier
     */
    viewItem(app, type, itemId) {
        this.log(`Viewing ${app} ${type}: ${itemId}`);
        
        // Set current item for other operations
        this.currentItem = {
            app: app,
            type: type,
            id: itemId
        };
        
        // Hide any open modals
        const modals = ['driveModal', 'docsModal', 'sheetsModal', 'calendarModal', 'gmailModal', 'mapsModal'];
        modals.forEach(modalId => {
            const modalEl = document.getElementById(modalId);
            if (modalEl) {
                const instance = bootstrap.Modal.getInstance(modalEl);
                if (instance) {
                    instance.hide();
                }
            }
        });
        
        // Clean up modal artifacts
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });
        
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
        
        // Get or create the file viewer modal
        let fileViewerModal = document.getElementById('fileViewerModal');
        
        if (!fileViewerModal) {
            // Create file viewer modal if it doesn't exist
            this.createFileViewerModal();
            fileViewerModal = document.getElementById('fileViewerModal');
        }
        
        // Update modal content
        const fileTitle = document.getElementById('fileViewerTitle');
        const fileContent = document.getElementById('fileViewerContent');
        
        if (fileTitle && fileContent) {
            const iconClass = this.getIconClass(app, type);
            const readableName = itemId.replace(/_/g, ' ');
            fileTitle.innerHTML = `<i class="bi ${iconClass}"></i> ${readableName}`;
            fileContent.innerHTML = this.getItemPreviewContent(app, type, itemId);
            
            // Store current item for future actions
            window.currentGoogleItem = {
                app: app,
                type: type,
                id: itemId
            };
            
            try {
                let modal = bootstrap.Modal.getInstance(fileViewerModal);
                if (!modal) {
                    modal = new bootstrap.Modal(fileViewerModal);
                }
                
                // Show the modal
                modal.show();
            } catch (err) {
                this.log('Error showing file viewer modal:', err);
                alert('Could not open file viewer. Please try again.');
            }
        } else {
            this.log('File viewer elements not found');
            alert('File viewer is not available. Please try again later.');
        }
    }

    /**
     * Download a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @param {string} itemId - Item identifier
     */
    downloadItem(app, type, itemId) {
        this.log(`Downloading ${app} ${type}: ${itemId}`);
        
        // In a real app, this would trigger an API call to download the item
        // For demo purposes, we just show an alert
        const readableName = itemId.replace(/_/g, ' ');
        alert(`Download started for ${readableName}`);
    }

    /**
     * Download current item
     */
    downloadCurrentItem() {
        if (this.currentItem) {
            this.downloadItem(this.currentItem.app, this.currentItem.type, this.currentItem.id);
        }
    }

    /**
     * Share a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @param {string} itemId - Item identifier
     */
    shareItem(app, type, itemId) {
        this.log(`Sharing ${app} ${type}: ${itemId}`);
        
        const readableName = itemId.replace(/_/g, ' ');
        const email = prompt(`Enter email address to share "${readableName}" with:`);
        
        if (email) {
            // In a real app, this would trigger an API call to share the item
            // For demo purposes, we just show an alert
            alert(`${readableName} has been shared with ${email}`);
        }
    }

    /**
     * Share current item
     */
    shareCurrentItem() {
        if (this.currentItem) {
            this.shareItem(this.currentItem.app, this.currentItem.type, this.currentItem.id);
        }
    }

    /**
     * Delete a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @param {string} itemId - Item identifier
     */
    deleteItem(app, type, itemId) {
        this.log(`Deleting ${app} ${type}: ${itemId}`);
        
        const readableName = itemId.replace(/_/g, ' ');
        if (confirm(`Are you sure you want to delete "${readableName}"?`)) {
            // In a real app, this would trigger an API call to delete the item
            // For demo purposes, we just show an alert
            alert(`${readableName} has been deleted`);
            
            // Close the file viewer if viewing this item
            if (this.currentItem && this.currentItem.id === itemId) {
                const fileViewerModal = document.getElementById('fileViewerModal');
                if (fileViewerModal) {
                    const modalInstance = bootstrap.Modal.getInstance(fileViewerModal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
            }
            
            // Remove the element from the DOM if possible
            const listItems = document.querySelectorAll(`.list-group-item [onclick*="${itemId}"]`);
            listItems.forEach(item => {
                const listItem = item.closest('.list-group-item');
                if (listItem) {
                    listItem.remove();
                }
            });
        }
    }

    /**
     * Delete current item
     */
    deleteCurrentItem() {
        if (this.currentItem) {
            this.deleteItem(this.currentItem.app, this.currentItem.type, this.currentItem.id);
        }
    }

    /**
     * Get icon class for a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @returns {string} Bootstrap icon class
     */
    getIconClass(app, type) {
        if (app === 'drive') {
            switch(type) {
                case 'folder': return 'bi-folder-fill text-primary';
                case 'pdf': return 'bi-file-earmark-pdf text-danger';
                default: return 'bi-file-earmark text-secondary';
            }
        } else if (app === 'docs') {
            return 'bi-file-earmark-text text-primary';
        } else if (app === 'sheets') {
            return 'bi-file-earmark-spreadsheet text-success';
        } else if (app === 'gmail') {
            return 'bi-envelope text-primary';
        } else if (app === 'calendar') {
            return 'bi-calendar-event text-success';
        } else {
            return 'bi-file-earmark text-secondary';
        }
    }

    /**
     * Get preview content for a Google item
     * @param {string} app - The Google app (drive, docs, sheets, etc.)
     * @param {string} type - Type of item (document, spreadsheet, folder, etc.)
     * @param {string} itemId - Item identifier
     * @returns {string} HTML content for preview
     */
    getItemPreviewContent(app, type, itemId) {
        const readableName = itemId.replace(/_/g, ' ');
        
        if (app === 'drive') {
            if (type === 'folder') {
                return `<div class="text-center">
                    <i class="bi bi-folder-fill text-primary" style="font-size: 64px;"></i>
                    <h4 class="mt-3">${readableName}</h4>
                    <div class="list-group mt-4">
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-pdf text-danger me-3"></i>
                                    <div>
                                        <h6 class="mb-1">Document 1.pdf</h6>
                                        <small>1.2 MB - Last updated: Yesterday</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="googleIntegrationHandler.viewItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="googleIntegrationHandler.downloadItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="googleIntegrationHandler.shareItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="googleIntegrationHandler.deleteItem('drive', 'pdf', 'Document_1.pdf')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark-text text-primary me-3"></i>
                                    <div>
                                        <h6 class="mb-1">Document 2.docx</h6>
                                        <small>0.8 MB - Last updated: 2 days ago</small>
                                    </div>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="googleIntegrationHandler.viewItem('drive', 'document', 'Document_2.docx')"><i class="bi bi-eye"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="googleIntegrationHandler.downloadItem('drive', 'document', 'Document_2.docx')"><i class="bi bi-download"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="googleIntegrationHandler.shareItem('drive', 'document', 'Document_2.docx')"><i class="bi bi-share"></i></button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="googleIntegrationHandler.deleteItem('drive', 'document', 'Document_2.docx')"><i class="bi bi-trash"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;
            } else if (type === 'pdf') {
                return `<div class="text-center p-4">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 64px;"></i>
                    <h4 class="mt-3">${readableName}</h4>
                    <div class="border p-3 mt-4 text-start bg-light">
                        <h5>PDF Preview</h5>
                        <p>PDF preview would be displayed here in a production environment.</p>
                        <p>For this demo, we're just showing this placeholder.</p>
                    </div>
                </div>`;
            }
        } else if (app === 'docs') {
            return `<div class="border p-4 bg-light h-100">
                <h4 class="mb-4">${readableName}</h4>
                <hr>
                <p>Document content would be displayed here in a production environment.</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed vitae nisi eget nunc ultricies 
                aliquet. Sed vitae nisi eget nunc ultricies aliquet.</p>
                <p>Proin ac magna eu quam faucibus tempus id at purus. Sed vitae nisi eget nunc ultricies 
                aliquet. Sed vitae nisi eget nunc ultricies aliquet.</p>
            </div>`;
        } else if (app === 'sheets') {
            return `<div class="border p-3 bg-light h-100">
                <h4 class="mb-3">${readableName}</h4>
                <hr>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Item ID</th>
                                <th>Product Name</th>
                                <th>Quantity</th>
                                <th>Location</th>
                                <th>Last Updated</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>WH-1001</td>
                                <td>Widget A</td>
                                <td>250</td>
                                <td>A-12-03</td>
                                <td>2024-05-10</td>
                            </tr>
                            <tr>
                                <td>WH-1002</td>
                                <td>Widget B</td>
                                <td>175</td>
                                <td>B-05-11</td>
                                <td>2024-05-09</td>
                            </tr>
                            <tr>
                                <td>WH-1003</td>
                                <td>Widget C</td>
                                <td>340</td>
                                <td>A-14-07</td>
                                <td>2024-05-08</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>`;
        } else if (app === 'calendar' && type === 'event') {
            // Handle calendar events
            let eventDetails = this.getCalendarEventDetails(itemId);
            
            return `
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4><i class="bi bi-calendar-event me-2"></i>${eventDetails.title}</h4>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Date & Time</h5>
                                <p class="mb-1"><i class="bi bi-clock me-2"></i>${eventDetails.dateTime}</p>
                                <p><i class="bi bi-geo-alt me-2"></i>${eventDetails.location}</p>
                            </div>
                            <div class="col-md-6">
                                <h5>Organizer</h5>
                                <p><i class="bi bi-person me-2"></i>${eventDetails.organizer}</p>
                            </div>
                        </div>
                        
                        <h5>Description</h5>
                        <p>${eventDetails.description}</p>
                        
                        <h5 class="mt-4">Attendees</h5>
                        <div class="list-group">
                            ${eventDetails.attendees.map(attendee => `
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-person-circle me-2"></i>
                                        ${attendee.name} 
                                        <span class="text-muted">${attendee.email}</span>
                                    </div>
                                    <span class="badge bg-${attendee.status === 'Accepted' ? 'success' : 
                                                           attendee.status === 'Declined' ? 'danger' : 
                                                           attendee.status === 'Tentative' ? 'warning' : 'secondary'} rounded-pill">
                                        ${attendee.status}
                                    </span>
                                </div>
                            `).join('')}
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="button" class="btn btn-outline-primary" onclick="googleIntegrationHandler.respondToEvent('${itemId}', 'accept')">
                                <i class="bi bi-check-circle me-2"></i>Accept
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="googleIntegrationHandler.respondToEvent('${itemId}', 'tentative')">
                                <i class="bi bi-question-circle me-2"></i>Maybe
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="googleIntegrationHandler.respondToEvent('${itemId}', 'decline')">
                                <i class="bi bi-x-circle me-2"></i>Decline
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
        
        return `<div class="alert alert-info p-5">
            <h4 class="alert-heading">${readableName}</h4>
            <p>Preview not available for this file type.</p>
            <hr>
            <p class="mb-0">Please download the file to view its contents.</p>
        </div>`;
    }

    /**
     * Get calendar event details based on the event ID
     * @param {string} eventId - The ID of the calendar event
     * @returns {Object} Event details object
     */
    getCalendarEventDetails(eventId) {
        // In a real application, this would fetch data from Google Calendar API
        // For demo purposes, we'll return mock data based on the event ID
        
        const events = {
            'Inventory_Audit': {
                title: 'Inventory Audit',
                dateTime: 'Today, 2:00 PM - 4:00 PM',
                location: 'Main Warehouse',
                organizer: 'Warehouse Manager (<EMAIL>)',
                description: 'Quarterly inventory audit with the warehouse team. Please prepare all necessary reports and ensure all items are correctly labeled before the audit begins.',
                attendees: [
                    { name: 'John Smith', email: '<EMAIL>', status: 'Accepted' },
                    { name: 'Sarah Johnson', email: '<EMAIL>', status: 'Accepted' },
                    { name: 'Michael Brown', email: '<EMAIL>', status: 'Tentative' },
                    { name: 'Emily Davis', email: '<EMAIL>', status: 'Declined' }
                ]
            },
            'Shipping_Coordination_Meeting': {
                title: 'Shipping Coordination Meeting',
                dateTime: 'Tomorrow, 10:00 AM - 11:00 AM',
                location: 'Conference Room B',
                organizer: 'Logistics Manager (<EMAIL>)',
                description: 'Weekly shipping coordination meeting with logistics team. We will discuss upcoming shipments, priorities, and any issues from last week\'s deliveries.',
                attendees: [
                    { name: 'Robert Wilson', email: '<EMAIL>', status: 'Accepted' },
                    { name: 'Lisa Miller', email: '<EMAIL>', status: 'Tentative' },
                    { name: 'David Thomas', email: '<EMAIL>', status: 'Accepted' },
                    { name: 'Jennifer Garcia', email: '<EMAIL>', status: 'Declined' }
                ]
            },
            'Warehouse_Staff_Training': {
                title: 'Warehouse Staff Training',
                dateTime: 'May 15, 9:00 AM - 12:00 PM',
                location: 'Training Room A',
                organizer: 'HR Department (<EMAIL>)',
                description: 'Training session for new warehouse management software. All staff should attend this important session to learn about the new inventory tracking features and updated procedures.',
                attendees: [
                    { name: 'All Warehouse Staff', email: '<EMAIL>', status: 'Accepted' },
                    { name: 'IT Support', email: '<EMAIL>', status: 'Accepted' },
                    { name: 'Management Team', email: '<EMAIL>', status: 'Accepted' }
                ]
            }
        };
        
        return events[eventId] || {
            title: eventId.replace(/_/g, ' '),
            dateTime: 'Date not specified',
            location: 'Location not specified',
            organizer: 'Organizer not specified',
            description: 'No description available.',
            attendees: []
        };
    }

    /**
     * Respond to a calendar event invitation
     * @param {string} eventId - The ID of the calendar event
     * @param {string} response - The response: 'accept', 'tentative', or 'decline'
     */
    respondToEvent(eventId, response) {
        this.log(`Responding to event ${eventId} with ${response}`);
        
        // In a real app, this would send the response to the Google Calendar API
        // For demo purposes, we just show an alert
        const responseText = 
            response === 'accept' ? 'accepted' : 
            response === 'tentative' ? 'tentatively accepted' : 
            'declined';
        
        const eventTitle = eventId.replace(/_/g, ' ');
        alert(`You have ${responseText} the "${eventTitle}" event.`);
    }

    /**
     * Create the file viewer modal if it doesn't exist
     */
    createFileViewerModal() {
        if (!document.getElementById('fileViewerModal')) {
            const modalHtml = `
                <div class="modal fade" id="fileViewerModal" tabindex="-1" aria-labelledby="fileViewerModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="fileViewerTitle"></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="fileViewerContent" style="min-height: 400px; overflow: auto;"></div>
                            </div>
                            <div class="modal-footer">
                                <div class="d-flex justify-content-start gap-2 me-auto">
                                    <button type="button" class="btn btn-outline-success" onclick="googleIntegrationHandler.downloadCurrentItem()"><i class="bi bi-download me-1"></i> Download</button>
                                    <button type="button" class="btn btn-outline-info" onclick="googleIntegrationHandler.shareCurrentItem()"><i class="bi bi-share me-1"></i> Share</button>
                                    <button type="button" class="btn btn-outline-danger" onclick="googleIntegrationHandler.deleteCurrentItem()"><i class="bi bi-trash me-1"></i> Delete</button>
                                </div>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = modalHtml;
            document.body.appendChild(tempDiv.firstElementChild);
        }
    }

    /**
     * Log a message to console if debug mode is enabled
     */
    log(...args) {
        if (this.debug) {
            console.log('GoogleIntegrationHandler:', ...args);
        }
    }
}

// Create and initialize the handler
const googleIntegrationHandler = new GoogleIntegrationHandler();
document.addEventListener('DOMContentLoaded', () => {
    googleIntegrationHandler.init();
    
    // Replace inline onclick handlers with proper event listeners
    window.viewGoogleItem = function(app, type, itemId) {
        googleIntegrationHandler.viewItem(app, type, itemId);
    };
    
    window.downloadGoogleItem = function(app, type, itemId) {
        googleIntegrationHandler.downloadItem(app, type, itemId);
    };
    
    window.shareGoogleItem = function(app, type, itemId) {
        googleIntegrationHandler.shareItem(app, type, itemId);
    };
    
    window.deleteGoogleItem = function(app, type, itemId) {
        googleIntegrationHandler.deleteItem(app, type, itemId);
    };
});
