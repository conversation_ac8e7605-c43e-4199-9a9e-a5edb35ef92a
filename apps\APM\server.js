const http = require('http');

let assets = [
  {
    id: 1,
    name: 'Production Line A',
    status: 'Operational',
    uptime: 98.5,
    lastMaintenance: '2024-04-01',
    nextMaintenance: '2024-06-01',
    alerts: 2,
  },
  {
    id: 2,
    name: 'Assembly Robot B',
    status: 'Maintenance',
    uptime: 95.2,
    lastMaintenance: '2024-05-01',
    nextMaintenance: '2024-05-20',
    alerts: 5,
  },
  {
    id: 3,
    name: 'Quality Control C',
    status: 'Operational',
    uptime: 99.1,
    lastMaintenance: '2024-03-15',
    nextMaintenance: '2024-06-15',
    alerts: 0,
  },
];

let maintenanceTasks = [
  {
    id: 1,
    asset: 'Assembly Robot B',
    type: 'Preventive',
    scheduled: '2024-05-20',
    status: 'Scheduled',
    priority: 'High',
  },
  {
    id: 2,
    asset: 'Production Line A',
    type: 'Routine',
    scheduled: '2024-06-01',
    status: 'Planned',
    priority: 'Medium',
  },
  {
    id: 3,
    asset: 'Quality Control C',
    type: 'Routine',
    scheduled: '2024-06-15',
    status: 'Planned',
    priority: 'Low',
  },
];

const server = http.createServer((req, res) => {
  if (req.url === '/') {
    const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>APM Dashboard</title>
                <style>
                    body { font-family: Arial; margin: 20px; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .dashboard-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }
                    .card {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        padding: 20px;
                        margin-bottom: 20px;
                    }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
                    th { background: #f4f4f4; }
                    .actions { margin: 20px 0; }
                    button { padding: 10px; margin: 5px; cursor: pointer; }
                    .status { 
                        padding: 5px 10px; 
                        border-radius: 3px; 
                        color: white;
                        font-weight: bold;
                    }
                    .operational { background: #28a745; }
                    .maintenance { background: #ffc107; }
                    .metrics {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 20px;
                        margin-bottom: 20px;
                    }
                    .metric-card {
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 8px;
                        text-align: center;
                    }
                    .metric-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: #007bff;
                    }
                    .uptime { 
                        font-weight: bold;
                        color: #28a745;
                    }
                    .alert-count {
                        display: inline-block;
                        background: #dc3545;
                        color: white;
                        padding: 2px 6px;
                        border-radius: 10px;
                        font-size: 0.8em;
                        margin-left: 5px;
                    }
                    .priority {
                        padding: 3px 8px;
                        border-radius: 3px;
                        font-size: 0.9em;
                    }
                    .high { background: #dc3545; color: white; }
                    .medium { background: #ffc107; color: black; }
                    .low { background: #28a745; color: white; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Asset Performance Management</h1>
                    <div class="actions">
                        <button onclick="window.location.href='http://localhost:3000'">Back to Hub</button>
                    </div>
                    
                    <div class="metrics">
                        <div class="metric-card">
                            <div class="metric-value">97.6%</div>
                            <div>Average Uptime</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">7</div>
                            <div>Active Alerts</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">3</div>
                            <div>Maintenance Tasks</div>
                        </div>
                    </div>

                    <div class="dashboard-grid">
                        <div class="card">
                            <h2>Asset Status</h2>
                            <table>
                                <tr>
                                    <th>Asset</th>
                                    <th>Status</th>
                                    <th>Uptime</th>
                                    <th>Last Maintenance</th>
                                    <th>Next Maintenance</th>
                                    <th>Alerts</th>
                                </tr>
                                ${assets
                                  .map(
                                    (asset) => `
                                    <tr>
                                        <td>${asset.name}</td>
                                        <td>
                                            <span class="status ${asset.status.toLowerCase()}">
                                                ${asset.status}
                                            </span>
                                        </td>
                                        <td class="uptime">${asset.uptime}%</td>
                                        <td>${asset.lastMaintenance}</td>
                                        <td>${asset.nextMaintenance}</td>
                                        <td>
                                            ${asset.alerts > 0 ? `<span class="alert-count">${asset.alerts}</span>` : 'None'}
                                        </td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>

                        <div class="card">
                            <h2>Maintenance Schedule</h2>
                            <table>
                                <tr>
                                    <th>Asset</th>
                                    <th>Type</th>
                                    <th>Scheduled</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                </tr>
                                ${maintenanceTasks
                                  .map(
                                    (task) => `
                                    <tr>
                                        <td>${task.asset}</td>
                                        <td>${task.type}</td>
                                        <td>${task.scheduled}</td>
                                        <td>
                                            <span class="status ${task.status.toLowerCase()}">
                                                ${task.status}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="priority ${task.priority.toLowerCase()}">
                                                ${task.priority}
                                            </span>
                                        </td>
                                    </tr>
                                `,
                                  )
                                  .join('')}
                            </table>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `;
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  } else {
    res.writeHead(404);
    res.end('Not found');
  }
});

server.listen(3005, () => {
  console.log('APM running on http://localhost:3005');
});
