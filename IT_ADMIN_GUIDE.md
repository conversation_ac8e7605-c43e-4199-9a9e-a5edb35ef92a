# ISA APPS IT Administrator Guide

This guide provides instructions for IT administrators to deploy the Ice Systems Australasia Applications Suite to users.

## Deployment Options

### Option 1: Local Installation on Each User's Computer

1. Copy the entire ISA APPS directory to each user's computer
2. Run `setup-for-users.bat` on each computer to:
   - Install dependencies
   - Create application icons
   - Create desktop shortcuts
3. Users can then launch applications directly from their desktop

### Option 2: Network Share Installation

1. Place the ISA APPS directory on a network share
2. Create a script to map the network drive on user computers
3. Run `setup-for-users.bat` from the mapped drive
4. Users can then launch applications from their desktop or the network share

### Option 3: Portable Installation (USB Drive)

1. Copy the entire ISA APPS directory to a USB drive
2. Users can run applications directly from the USB drive using `launcher-local.bat`
3. Optionally, run `create-desktop-shortcuts.bat` on each computer to create shortcuts

## System Requirements

- Windows 10 or later
- 4GB RAM minimum (8GB recommended)
- 2GB free disk space
- Network connectivity between user computers (for multi-user scenarios)

## Port Configuration

The applications use the following ports:

- Integration Hub: 8000
- BMS: 3001
- MRP: 3002
- CRM: 3003
- WMS: 3004
- APS: 3005
- APM: 3006

Ensure these ports are not blocked by firewalls and are not used by other applications.

## Updating the Applications

To update the applications:

1. Replace the application files with the new versions
2. Run `install-dependencies.bat` to update dependencies
3. No need to recreate shortcuts unless the application paths have changed

## Troubleshooting Common Issues

### Node.js Not Found

The applications use a local Node.js installation in the `Node` directory. If this directory is missing or corrupted:

1. Download Node.js v22.15.0 for Windows (x64)
2. Extract it to the `Node\node-v22.15.0-win-x64` directory

### Application Won't Start

If an application fails to start:

1. Check if the Integration Hub is running
2. Verify that the required port is not in use
3. Check the application logs in `Apps\[AppName]\logs`

### Desktop Shortcuts Not Working

If desktop shortcuts don't work:

1. Run `create-desktop-shortcuts.bat` again
2. Check if the target paths in the shortcuts are correct
3. Verify that the application files exist at the specified locations

## Support

For additional support, please contact the ISA APPS support <NAME_EMAIL>
