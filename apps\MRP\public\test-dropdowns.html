<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .console-output-style {
            height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Dropdown Test</h1>
        
        <div class="d-flex gap-3">
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="test-sort-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-sort-down"></i> Sort
                </button>
                <ul class="dropdown-menu" aria-labelledby="test-sort-dropdown">
                    <li><a class="dropdown-item" href="#" onclick="console.log('Sort: Date Desc')">Newest first</a></li>
                    <li><a class="dropdown-item" href="#" onclick="console.log('Sort: Date Asc')">Oldest first</a></li>
                    <li><a class="dropdown-item" href="#" onclick="console.log('Sort: Sender A-Z')">Sender A-Z</a></li>
                </ul>
            </div>
            
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="test-filter-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-funnel"></i> Filter
                </button>                <ul class="dropdown-menu" aria-labelledby="test-filter-dropdown">
                    <li><a class="dropdown-item" href="#" onclick="console.log('Filter: All')">All emails</a></li>
                    <li><a class="dropdown-item" href="#" onclick="console.log('Filter: Unread')">Unread</a></li>
                    <li><a class="dropdown-item" href="#" onclick="console.log('Filter: Read')">Read</a></li>
                </ul>
            </div>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-primary" onclick="testManualDropdown()">Test Manual Dropdown</button>
            <button class="btn btn-success" onclick="testBootstrapDropdown()">Test Bootstrap Dropdown</button>
        </div>
        
        <div class="mt-4">
            <h3>Console Output:</h3>
            <div id="console-output" class="border p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>        function testManualDropdown() {
            const dropdown = document.getElementById('test-sort-dropdown');
            const menu = dropdown.nextElementSibling;
            
            console.log('Manual test - Dropdown found:', !!dropdown);
            console.log('Manual test - Menu found:', !!menu);
            console.log('Manual test - Bootstrap available:', typeof bootstrap !== 'undefined');
            
            if (menu.classList.contains('show')) {
                menu.classList.remove('show');
                console.log('Manual test - Menu closed');
            } else {
                menu.classList.add('show');
                console.log('Manual test - Menu opened');
            }
        }
        
        function testBootstrapDropdown() {
            const dropdownButton = document.getElementById('test-sort-dropdown');
            console.log('Bootstrap test - Dropdown button found:', !!dropdownButton);
            
            try {
                const dropdownInstance = new bootstrap.Dropdown(dropdownButton);
                console.log('Bootstrap test - Dropdown instance created');
                dropdownInstance.toggle();
                console.log('Bootstrap test - Dropdown toggled');
            } catch (error) {
                console.log('Bootstrap test - Error:', error.message);
            }
        }
        
        // Override console.log to show in page
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const output = document.getElementById('console-output');
            const div = document.createElement('div');
            div.textContent = args.join(' ');
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        };
        
        console.log('Test page loaded. Bootstrap version:', bootstrap?.Tooltip?.VERSION || 'Not available');
    </script>
</body>
</html>
