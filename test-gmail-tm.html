<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test TM Gmail Integration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>TM Gmail Integration Test</h1>
        <button id="gmail-link" class="btn btn-primary">
            <i class="bi bi-envelope"></i> Open Gmail
        </button>
        <div id="test-results" class="mt-4"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="./SharedFeatures/ui/gmail-integration.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('test-results');
            
            try {
                // Test if the Gmail integration can be initialized
                initializeGmail({
                    appName: 'TM',
                    appPrefix: 'tm',
                    appColor: '#6c5ce7',
                    modalId: 'tm-gmailModal',
                    triggerId: 'gmail-link'
                });
                
                testResults.innerHTML = '<div class="alert alert-success">✅ Gmail integration initialized successfully!</div>';
                console.log('Enhanced Gmail integration initialized for TM application');
                
                // Test clicking the Gmail button
                setTimeout(() => {
                    const gmailButton = document.getElementById('gmail-link');
                    if (gmailButton) {
                        gmailButton.addEventListener('click', function() {
                            testResults.innerHTML += '<div class="alert alert-info">📧 Gmail button clicked - Modal should open</div>';
                        });
                    }
                }, 1000);
                
            } catch (error) {
                testResults.innerHTML = '<div class="alert alert-danger">❌ Error: ' + error.message + '</div>';
                console.error('Gmail integration error:', error);
            }
        });
    </script>
</body>
</html>
