# BMS API Documentation

This documentation provides details about the BMS API endpoints, authentication, and usage.

## Authentication

The BMS API uses JWT (JSON Web Tokens) for authentication. To authenticate, send a POST request to the `/api/auth/login` endpoint with your credentials. The response will include a token that should be included in the `Authorization` header of subsequent requests.

Example:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## API Endpoints

### Dashboard

#### GET /api/dashboard

Returns dashboard data including financial metrics and recent transactions.

**Response:**

```json
{
  "status": "success",
  "data": {
    "metrics": {
      "revenue": 125000,
      "expenses": 78000,
      "profit": 47000
    },
    "recentTransactions": [
      { "id": 1, "date": "2025-04-25", "amount": 1200, "type": "income" },
      { "id": 2, "date": "2025-04-24", "amount": 800, "type": "expense" }
    ]
  }
}
```

### Calendar

#### GET /api/calendar/events

Returns calendar events for the specified date range.

**Query Parameters:**

- `startDate` (optional): Start date in ISO format (default: current date)
- `endDate` (optional): End date in ISO format (default: 30 days from start date)

**Response:**

```json
{
  "status": "success",
  "data": [
    {
      "id": "1",
      "title": "Meeting with Client",
      "start": "2025-04-25T10:00:00Z",
      "end": "2025-04-25T11:00:00Z",
      "location": "Conference Room A"
    }
  ]
}
```

### Invoices

#### GET /api/accounting/invoices

Returns a list of invoices.

**Query Parameters:**

- `status` (optional): Filter by status (draft, sent, paid, overdue)
- `limit` (optional): Maximum number of invoices to return (default: 10)
- `offset` (optional): Offset for pagination (default: 0)

**Response:**

```json
{
  "status": "success",
  "data": [
    {
      "id": "INV-001",
      "customer": "Acme Inc.",
      "amount": 1200,
      "date": "2025-04-25",
      "dueDate": "2025-05-25",
      "status": "sent"
    }
  ],
  "pagination": {
    "total": 100,
    "limit": 10,
    "offset": 0
  }
}
```

## Error Handling

The API returns appropriate HTTP status codes and error messages in case of errors.

Example error response:

```json
{
  "status": "error",
  "message": "Invalid authentication token"
}
```

Common error codes:

- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required or invalid authentication
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

## Rate Limiting

The API implements rate limiting to prevent abuse. The current limits are:

- 100 requests per minute per IP address
- 1000 requests per hour per user

If you exceed these limits, you will receive a `429 Too Many Requests` response.
