/**
 * Enhanced Gmail Implementation for CRM Application
 * Implements the BMS-style search, sort, and filter functionality
 */

// Configuration
const CRM_GMAIL_CONFIG = {
    // CRM color scheme
    colors: {
        primary: '#ff6b6b',       // Primary color (coral/red)
        secondary: '#6c757d',     // Secondary color (gray)
        success: '#28a745',       // Success color (green)
        danger: '#dc3545',        // Danger color (red)
        warning: '#ffc107',       // Warning color (yellow)
        info: '#17a2b8',          // Info color (teal/blue)
        light: '#f8f9fa',         // Light color (off-white)
        dark: '#343a40'           // Dark color (dark gray)
    },
    // Email categories/labels
    labels: [
        { id: 'important', name: 'Important', icon: 'bookmark', count: 2, color: '#dc3545' },
        { id: 'work', name: 'Work', icon: 'briefcase', count: 5, color: '#28a745' },
        { id: 'personal', name: 'Personal', icon: 'person', count: 3, color: '#17a2b8' },
        { id: 'client', name: 'Client', icon: 'people', count: 4, color: '#ff6b6b' }
    ]
};

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('CRM Enhanced Gmail Implementation loaded');

    // Find the Gmail modal
    const gmailModal = document.getElementById('gmailModal');
    if (gmailModal) {
        console.log('Found Gmail modal, enhancing it');
        enhanceGmailModal(gmailModal);
    }

    // Find the Open Gmail button
    const openGmailBtn = document.getElementById('crm-open-gmail-btn');
    if (openGmailBtn) {
        console.log('Found Open Gmail button, adding enhanced event listener');

        // Add our direct click handler
        openGmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Open Gmail button clicked - using enhanced implementation');

            // If the modal is already enhanced, just open it
            const enhancedModal = document.getElementById('gmailModal');
            if (enhancedModal) {
                const modal = new bootstrap.Modal(enhancedModal);
                modal.show();
            }
        });
    }
});

/**
 * Enhance the existing Gmail modal with BMS-style search, sort, and filter functionality
 */
function enhanceGmailModal(modal) {
    console.log('Enhancing Gmail modal with BMS-style functionality');

    // Find the inbox tab content
    const inboxContent = modal.querySelector('.tab-pane[id*="inbox"]');
    if (!inboxContent) {
        console.error('Inbox tab content not found');
        return;
    }

    // Add search, sort, and filter controls
    const controlsHtml = `
    <div class="d-flex justify-content-between mb-3">
        <div class="input-group" style="max-width: 300px;">
            <input type="text" class="form-control" id="crm-email-search" placeholder="Search emails" aria-label="Search emails">
            <button class="btn btn-outline-secondary" id="crm-email-search-btn" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
        <div>
            <div class="dropdown d-inline-block me-2">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="crmEmailSortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-sort-down"></i> Sort
                </button>
                <ul class="dropdown-menu" aria-labelledby="crmEmailSortDropdown">
                    <li><a class="dropdown-item crm-sort-option" href="#" data-sort="date-desc">Newest first</a></li>
                    <li><a class="dropdown-item crm-sort-option" href="#" data-sort="date-asc">Oldest first</a></li>
                    <li><a class="dropdown-item crm-sort-option" href="#" data-sort="sender-asc">Sender A-Z</a></li>
                    <li><a class="dropdown-item crm-sort-option" href="#" data-sort="sender-desc">Sender Z-A</a></li>
                    <li><a class="dropdown-item crm-sort-option" href="#" data-sort="subject-asc">Subject A-Z</a></li>
                    <li><a class="dropdown-item crm-sort-option" href="#" data-sort="subject-desc">Subject Z-A</a></li>
                </ul>
            </div>
            <div class="dropdown d-inline-block me-2">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="crmEmailFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-funnel"></i> Filter
                </button>
                <ul class="dropdown-menu" aria-labelledby="crmEmailFilterDropdown">
                    <li><a class="dropdown-item crm-filter-option" href="#" data-filter="all">All emails</a></li>
                    <li><a class="dropdown-item crm-filter-option" href="#" data-filter="unread">Unread</a></li>
                    <li><a class="dropdown-item crm-filter-option" href="#" data-filter="read">Read</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header">By Label</h6></li>
                    ${CRM_GMAIL_CONFIG.labels.map(label => `
                        <li><a class="dropdown-item crm-filter-option" href="#" data-filter="label-${label.id}">
                            <span class="badge" style="background-color: ${label.color};">${label.name}</span>
                        </a></li>
                    `).join('')}
                </ul>
            </div>
            <button class="btn btn-outline-secondary" id="crm-gmail-refresh-btn">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
    </div>
    `;

    // Insert the controls at the top of the inbox content
    inboxContent.insertAdjacentHTML('afterbegin', controlsHtml);

    // Add data attributes to email items for sorting and filtering
    const emailItems = inboxContent.querySelectorAll('.list-group-item');
    emailItems.forEach((item, index) => {
        // Add data attributes if they don't exist
        if (!item.hasAttribute('data-sender')) {
            const sender = item.querySelector('h6, .fw-bold')?.textContent.trim() || `Sender ${index + 1}`;
            item.setAttribute('data-sender', sender.toLowerCase().replace(/\s+/g, '-'));
        }

        if (!item.hasAttribute('data-subject')) {
            const subject = item.querySelector('p, .mb-1')?.textContent.trim() || `Subject ${index + 1}`;
            item.setAttribute('data-subject', subject);
        }

        if (!item.hasAttribute('data-date')) {
            const date = new Date();
            date.setDate(date.getDate() - index); // Simulate different dates
            item.setAttribute('data-date', date.toISOString());
        }

        if (!item.hasAttribute('data-label')) {
            // Assign random labels
            const labels = CRM_GMAIL_CONFIG.labels;
            const randomLabel = labels[Math.floor(Math.random() * labels.length)].id;
            item.setAttribute('data-label', randomLabel);

            // Add label badge if it doesn't exist
            if (!item.querySelector('.badge')) {
                const labelObj = CRM_GMAIL_CONFIG.labels.find(l => l.id === randomLabel);
                const labelBadge = document.createElement('span');
                labelBadge.className = 'badge ms-2';
                labelBadge.style.backgroundColor = labelObj.color;
                labelBadge.textContent = labelObj.name;

                const titleElement = item.querySelector('h6, .fw-bold');
                if (titleElement) {
                    titleElement.appendChild(labelBadge);
                }
            }
        }

        // Add unread class to some emails
        if (index % 3 === 0) {
            item.classList.add('unread');
            item.style.backgroundColor = 'rgba(255, 107, 107, 0.05)';
        }
    });

    // Set up event listeners
    setupCrmGmailEventListeners();
}

/**
 * Set up event listeners for the enhanced Gmail modal
 */
function setupCrmGmailEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('crm-email-search');
    const searchBtn = document.getElementById('crm-email-search-btn');

    if (searchInput && searchBtn) {
        // Search on button click
        searchBtn.addEventListener('click', function() {
            searchCrmEmails(searchInput.value);
        });

        // Search on Enter key
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                searchCrmEmails(this.value);
            }
        });
    }

    // Sort functionality
    document.querySelectorAll('.crm-sort-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const sortType = this.getAttribute('data-sort');
            sortCrmEmails(sortType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('crmEmailSortDropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-sort-down"></i> ${this.textContent}`;
            }
        });
    });

    // Filter functionality
    document.querySelectorAll('.crm-filter-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const filterType = this.getAttribute('data-filter');
            filterCrmEmails(filterType);

            // Update dropdown button text
            const dropdownButton = document.getElementById('crmEmailFilterDropdown');
            if (dropdownButton) {
                dropdownButton.innerHTML = `<i class="bi bi-funnel"></i> ${this.textContent.trim()}`;
            }
        });
    });

    // Refresh button
    const refreshBtn = document.getElementById('crm-gmail-refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // Reset search input
            if (searchInput) {
                searchInput.value = '';
            }

            // Reset sort dropdown
            const sortDropdown = document.getElementById('crmEmailSortDropdown');
            if (sortDropdown) {
                sortDropdown.innerHTML = '<i class="bi bi-sort-down"></i> Sort';
            }

            // Reset filter dropdown
            const filterDropdown = document.getElementById('crmEmailFilterDropdown');
            if (filterDropdown) {
                filterDropdown.innerHTML = '<i class="bi bi-funnel"></i> Filter';
            }

            // Show all emails
            showAllCrmEmails();

            // Show success message
            showToast('Emails refreshed successfully');
        });
    }
}

/**
 * Search emails by query
 */
function searchCrmEmails(query) {
    if (!query) {
        showAllCrmEmails();
        return;
    }

    const inboxContent = document.querySelector('#gmailModal .tab-pane[id*="inbox"]');
    if (!inboxContent) return;

    const emailItems = inboxContent.querySelectorAll('.list-group-item');
    if (!emailItems.length) return;

    // Normalize query
    const normalizedQuery = query.toLowerCase().trim();

    // Show/hide based on search query
    emailItems.forEach(item => {
        const sender = item.getAttribute('data-sender') || '';
        const subject = item.getAttribute('data-subject') || '';
        const content = item.querySelector('small, .text-muted')?.textContent.toLowerCase() || '';

        if (sender.includes(normalizedQuery) || subject.toLowerCase().includes(normalizedQuery) || content.includes(normalizedQuery)) {
            item.style.display = '';

            // Highlight matching text
            highlightCrmText(item, normalizedQuery);
        } else {
            item.style.display = 'none';
        }
    });

    // Show toast with results count
    const visibleCount = Array.from(emailItems).filter(item => item.style.display !== 'none').length;
    showToast(`Found ${visibleCount} email${visibleCount !== 1 ? 's' : ''} matching "${query}"`);
}

/**
 * Highlight matching text in email items
 */
function highlightCrmText(item, query) {
    // Remove existing highlights
    item.querySelectorAll('.highlight').forEach(el => {
        const parent = el.parentNode;
        parent.replaceChild(document.createTextNode(el.textContent), el);
        parent.normalize();
    });

    // Highlight text in subject
    const subjectEl = item.querySelector('p, .mb-1');
    if (subjectEl) {
        const subject = subjectEl.textContent;
        const normalizedSubject = subject.toLowerCase();
        const index = normalizedSubject.indexOf(query);

        if (index >= 0) {
            const before = subject.substring(0, index);
            const match = subject.substring(index, index + query.length);
            const after = subject.substring(index + query.length);

            subjectEl.innerHTML = before + '<span class="highlight" style="background-color: yellow;">' + match + '</span>' + after;
        }
    }

    // Highlight text in content
    const contentEl = item.querySelector('small, .text-muted');
    if (contentEl) {
        const content = contentEl.textContent;
        const normalizedContent = content.toLowerCase();
        const index = normalizedContent.indexOf(query);

        if (index >= 0) {
            const before = content.substring(0, index);
            const match = content.substring(index, index + query.length);
            const after = content.substring(index + query.length);

            contentEl.innerHTML = before + '<span class="highlight" style="background-color: yellow;">' + match + '</span>' + after;
        }
    }
}

/**
 * Sort emails by specified criteria
 */
function sortCrmEmails(sortType) {
    const inboxContent = document.querySelector('#gmailModal .tab-pane[id*="inbox"]');
    if (!inboxContent) return;

    const emailList = inboxContent.querySelector('.list-group');
    if (!emailList) return;

    // Get all email items
    const emailItems = Array.from(emailList.querySelectorAll('.list-group-item'));
    if (!emailItems.length) return;

    // Sort based on criteria
    emailItems.sort((a, b) => {
        switch (sortType) {
            case 'date-desc':
                // Newest first (default)
                const dateA = a.getAttribute('data-date') || '';
                const dateB = b.getAttribute('data-date') || '';
                return dateB.localeCompare(dateA);
            case 'date-asc':
                // Oldest first
                const dateC = a.getAttribute('data-date') || '';
                const dateD = b.getAttribute('data-date') || '';
                return dateC.localeCompare(dateD);
            case 'sender-asc':
                // Sender A-Z
                const senderA = a.getAttribute('data-sender') || '';
                const senderB = b.getAttribute('data-sender') || '';
                return senderA.localeCompare(senderB);
            case 'sender-desc':
                // Sender Z-A
                const senderC = a.getAttribute('data-sender') || '';
                const senderD = b.getAttribute('data-sender') || '';
                return senderD.localeCompare(senderC);
            case 'subject-asc':
                // Subject A-Z
                const subjectA = a.getAttribute('data-subject') || '';
                const subjectB = b.getAttribute('data-subject') || '';
                return subjectA.toLowerCase().localeCompare(subjectB.toLowerCase());
            case 'subject-desc':
                // Subject Z-A
                const subjectC = a.getAttribute('data-subject') || '';
                const subjectD = b.getAttribute('data-subject') || '';
                return subjectD.toLowerCase().localeCompare(subjectC.toLowerCase());
            default:
                return 0;
        }
    });

    // Reorder the DOM elements
    emailItems.forEach(item => {
        emailList.appendChild(item);
    });

    // Show toast
    showToast(`Emails sorted by ${sortType.replace('-', ' ')}`);
}

/**
 * Filter emails by specified criteria
 */
function filterCrmEmails(filterType) {
    const inboxContent = document.querySelector('#gmailModal .tab-pane[id*="inbox"]');
    if (!inboxContent) return;

    // Get all email items
    const emailItems = inboxContent.querySelectorAll('.list-group-item');
    if (!emailItems.length) return;

    // Show/hide based on filter
    emailItems.forEach(item => {
        if (filterType === 'all') {
            // Show all emails
            item.style.display = '';
        } else if (filterType === 'unread') {
            // Show only unread emails
            item.style.display = item.classList.contains('unread') ? '' : 'none';
        } else if (filterType === 'read') {
            // Show only read emails
            item.style.display = !item.classList.contains('unread') ? '' : 'none';
        } else if (filterType.startsWith('label-')) {
            // Show emails with specific label
            const label = filterType.replace('label-', '');
            const itemLabel = item.getAttribute('data-label');
            item.style.display = itemLabel === label ? '' : 'none';
        }
    });

    // Show toast
    const visibleCount = Array.from(emailItems).filter(item => item.style.display !== 'none').length;
    showToast(`Showing ${visibleCount} email${visibleCount !== 1 ? 's' : ''} with filter: ${filterType}`);
}

/**
 * Show all emails
 */
function showAllCrmEmails() {
    const inboxContent = document.querySelector('#gmailModal .tab-pane[id*="inbox"]');
    if (!inboxContent) return;

    // Get all email items
    const emailItems = inboxContent.querySelectorAll('.list-group-item');
    if (!emailItems.length) return;

    // Show all emails
    emailItems.forEach(item => {
        item.style.display = '';

        // Remove highlights
        item.querySelectorAll('.highlight').forEach(el => {
            const parent = el.parentNode;
            parent.replaceChild(document.createTextNode(el.textContent), el);
            parent.normalize();
        });
    });
}

/**
 * Show a toast notification
 */
function showToast(message, type = 'success') {
    // Use the ISADataUtils.showToast function if available
    if (window.ISADataUtils && typeof window.ISADataUtils.showToast === 'function') {
        window.ISADataUtils.showToast(message, type);
        return;
    }

    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Create a unique ID for this toast
    const toastId = 'toast-' + Date.now();

    // Determine icon and title based on type
    let icon, title;
    switch (type) {
        case 'success':
            icon = 'check-circle-fill text-success';
            title = 'Success';
            break;
        case 'danger':
        case 'error':
            icon = 'exclamation-circle-fill text-danger';
            title = 'Error';
            break;
        case 'warning':
            icon = 'exclamation-triangle-fill text-warning';
            title = 'Warning';
            break;
        case 'info':
            icon = 'info-circle-fill text-info';
            title = 'Information';
            break;
        default:
            icon = 'bell-fill text-primary';
            title = 'Notification';
    }

    // Create toast element
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi bi-${icon} me-2"></i>
                <strong class="me-auto">${title}</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // Add toast to container
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialize and show the toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
    toast.show();

    // Remove toast from DOM after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function () {
        toastElement.remove();
    });
}