/**
 * Real-time Service for MRP
 *
 * This service handles real-time communication with the Integration Hub
 * using Socket.IO for data synchronization between applications.
 */

class RealTimeService {
  constructor() {
    this.socket = null;
    this.connected = false;
    this.listeners = new Map();
    this.pendingUpdates = [];
    this.isOnline = navigator.onLine;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000; // 5 seconds
    this.reconnectTimer = null;

    // Listen for online/offline events
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
  }

  /**
   * Initialize the real-time service
   * @param {Object} options - Configuration options
   * @param {string} options.url - Integration Hub URL
   * @param {string} options.token - Authentication token
   * @param {Array<string>} options.systems - Systems to subscribe to
   */
  initialize(options = {}) {
    this.options = {
      url: 'http://localhost:8000',
      token: null,
      systems: ['mrp', 'all'],
      ...options,
    };

    this.connect();

    // Load any pending updates from storage
    this.loadPendingUpdates();

    console.log('Real-time service initialized');
  }

  /**
   * Connect to the Integration Hub
   */
  connect() {
    if (this.socket) {
      // Clean up existing socket
      this.socket.disconnect();
    }

    // Create new socket connection
    this.socket = io(this.options.url, {
      auth: { token: this.options.token },
    });

    // Set up event handlers
    this.socket.on('connect', this.handleConnect.bind(this));
    this.socket.on('disconnect', this.handleDisconnect.bind(this));
    this.socket.on('connect_error', this.handleConnectError.bind(this));
    this.socket.on('data-updated', this.handleDataUpdate.bind(this));
    this.socket.on('update-confirmed', this.handleUpdateConfirmed.bind(this));
    this.socket.on('system-status', this.handleSystemStatus.bind(this));
    this.socket.on('error', this.handleError.bind(this));
  }

  /**
   * Handle socket connection
   */
  handleConnect() {
    console.log('Connected to Integration Hub');
    this.connected = true;
    this.reconnectAttempts = 0;

    // Subscribe to relevant systems
    this.subscribeToSystems();

    // Process any pending updates
    this.processPendingUpdates();

    // Notify listeners
    this.notifyListeners('connection', { status: 'connected' });
  }

  /**
   * Handle socket disconnection
   */
  handleDisconnect(reason) {
    console.log(`Disconnected from Integration Hub: ${reason}`);
    this.connected = false;

    // Notify listeners
    this.notifyListeners('connection', { status: 'disconnected', reason });

    // Attempt to reconnect if not closing or disconnecting manually
    if (reason !== 'io client disconnect' && reason !== 'io server disconnect') {
      this.attemptReconnect();
    }
  }

  /**
   * Handle connection error
   */
  handleConnectError(error) {
    console.error('Connection error:', error);
    this.connected = false;

    // Notify listeners
    this.notifyListeners('connection', { status: 'error', error: error.message });

    // Attempt to reconnect
    this.attemptReconnect();
  }

  /**
   * Attempt to reconnect to the Integration Hub
   */
  attemptReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`,
      );

      this.reconnectTimer = setTimeout(() => {
        if (this.isOnline) {
          this.connect();
        }
      }, this.reconnectInterval);
    } else {
      console.error('Max reconnect attempts reached. Please refresh the page.');
      this.notifyListeners('connection', {
        status: 'max_attempts_reached',
        attempts: this.reconnectAttempts,
      });
    }
  }

  /**
   * Subscribe to relevant systems
   */
  subscribeToSystems() {
    if (!this.connected) return;

    this.options.systems.forEach((system) => {
      this.socket.emit('subscribe', system);
      console.log(`Subscribed to ${system}`);
    });
  }

  /**
   * Handle data update from the Integration Hub
   */
  handleDataUpdate(data) {
    console.log('Received data update:', data);

    // Notify listeners for this data type
    this.notifyListeners(data.type, data);

    // Also notify generic update listeners
    this.notifyListeners('update', data);
  }

  /**
   * Handle update confirmation from the Integration Hub
   */
  handleUpdateConfirmed(confirmation) {
    console.log('Update confirmed:', confirmation);

    // Remove from pending updates if it was stored
    this.removePendingUpdate(confirmation.id);

    // Notify listeners
    this.notifyListeners('update-confirmed', confirmation);
  }

  /**
   * Handle system status update from the Integration Hub
   */
  handleSystemStatus(status) {
    console.log(`System status update for ${status.system}: ${status.status}`);

    // Notify listeners
    this.notifyListeners('system-status', status);
  }

  /**
   * Handle error from the Integration Hub
   */
  handleError(error) {
    console.error('Error from Integration Hub:', error);

    // Notify listeners
    this.notifyListeners('error', error);
  }

  /**
   * Handle online event
   */
  handleOnline() {
    console.log('Back online');
    this.isOnline = true;

    // Reconnect to the socket
    this.connect();
  }

  /**
   * Handle offline event
   */
  handleOffline() {
    console.log('Offline');
    this.isOnline = false;

    // Notify listeners
    this.notifyListeners('connection', { status: 'offline' });
  }

  /**
   * Send data update to the Integration Hub
   * @param {Object} data - Data to send
   * @returns {boolean} - Whether the update was sent successfully
   */
  sendDataUpdate(data) {
    // Add system if not provided
    if (!data.system) {
      data.system = 'mrp';
    }

    // Add timestamp if not provided
    if (!data.timestamp) {
      data.timestamp = new Date().toISOString();
    }

    // Add unique ID if not provided
    if (!data.id) {
      data.id = this.generateUpdateId();
    }

    if (!this.isOnline || !this.connected) {
      // Store update for later
      this.storePendingUpdate(data);
      console.log('Stored update for later processing:', data);
      return false;
    }

    // Send data update to Integration Hub
    this.socket.emit('data-update', data);
    console.log('Sent data update:', data);
    return true;
  }

  /**
   * Generate a unique update ID
   * @returns {string} - Unique ID
   */
  generateUpdateId() {
    return `mrp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Store pending update for later processing
   * @param {Object} data - Data to store
   */
  storePendingUpdate(data) {
    this.pendingUpdates.push(data);

    // Store in localStorage
    try {
      const pendingUpdates = JSON.parse(localStorage.getItem('mrp_pendingUpdates')) || [];
      pendingUpdates.push(data);
      localStorage.setItem('mrp_pendingUpdates', JSON.stringify(pendingUpdates));
    } catch (error) {
      console.error('Error storing pending update:', error);
    }
  }

  /**
   * Remove pending update
   * @param {string} id - Update ID to remove
   */
  removePendingUpdate(id) {
    this.pendingUpdates = this.pendingUpdates.filter((update) => update.id !== id);

    // Update localStorage
    try {
      const pendingUpdates = JSON.parse(localStorage.getItem('mrp_pendingUpdates')) || [];
      const filteredUpdates = pendingUpdates.filter((update) => update.id !== id);
      localStorage.setItem('mrp_pendingUpdates', JSON.stringify(filteredUpdates));
    } catch (error) {
      console.error('Error removing pending update:', error);
    }
  }

  /**
   * Load pending updates from localStorage
   */
  loadPendingUpdates() {
    try {
      const pendingUpdates = JSON.parse(localStorage.getItem('mrp_pendingUpdates')) || [];
      this.pendingUpdates = pendingUpdates;
      console.log(`Loaded ${pendingUpdates.length} pending updates`);
    } catch (error) {
      console.error('Error loading pending updates:', error);
    }
  }

  /**
   * Process pending updates
   */
  processPendingUpdates() {
    if (this.pendingUpdates.length > 0 && this.connected) {
      console.log(`Processing ${this.pendingUpdates.length} pending updates`);

      // Process each pending update
      [...this.pendingUpdates].forEach((update) => {
        this.socket.emit('data-update', update);
      });
    }
  }

  /**
   * Add event listener
   * @param {string} event - Event to listen for
   * @param {Function} callback - Callback function
   */
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }

    this.listeners.get(event).push(callback);
    return this;
  }

  /**
   * Remove event listener
   * @param {string} event - Event to remove listener for
   * @param {Function} callback - Callback function to remove
   */
  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);

      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    }
    return this;
  }

  /**
   * Notify listeners of an event
   * @param {string} event - Event to notify listeners of
   * @param {Object} data - Data to pass to listeners
   */
  notifyListeners(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }

  /**
   * Disconnect from the Integration Hub
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }

    this.connected = false;
    console.log('Disconnected from Integration Hub');
  }
}

// Create a singleton instance
const realTimeService = new RealTimeService();

// Add to window for global access
window.realTimeService = realTimeService;
