# ISA Suite Hub

The central hub for monitoring and managing ISA Suite applications.

## Prerequisites

- Node.js 18+
- pnpm
- MongoDB
- Redis

## Setup

1. Install dependencies:

```bash
pnpm install
```

2. Create and configure environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
MONGODB_URI=mongodb://localhost:27017/isahub
REDIS_URL=redis://localhost:6379
BI_URL=http://localhost:3001
BMS_URL=http://localhost:3002
CRM_URL=http://localhost:3003
PMS_URL=http://localhost:3004
TMS_URL=http://localhost:3005
SCM_URL=http://localhost:3006
PDM_URL=http://localhost:3007
```

## Running the Hub

### Option 1: Run Hub and Monitor Together

```bash
pnpm start:all
```

This will start both the Hub server and monitoring service in parallel.

### Option 2: Run Separately

In one terminal:

```bash
pnpm dev
```

In another terminal:

```bash
pnpm monitor
```

## Features

- Real-time application status monitoring
- System metrics dashboard
- Health check endpoints
- Redis caching for performance
- MongoDB for persistent storage

## API Endpoints

- `GET /api/apps` - List all applications and their status
- `GET /api/metrics` - Get system metrics
- `GET /api/health` - Check system health

## Monitoring

The monitoring service:

- Checks application status every minute
- Updates MongoDB with latest status
- Caches results in Redis
- Logs all activities
