/**
 * CRM Gmail Attachments
 * 
 * This file provides attachment functionality for the Gmail modal in the CRM application.
 * It allows users to attach files when composing or replying to emails.
 */

// Execute when the document is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('CRM Gmail Attachments loaded');
    
    // Initialize Gmail attachment functionality
    CRMGmailAttachments.init();
});

/**
 * CRMGmailAttachments - Namespace for Gmail attachment functionality
 */
const CRMGmailAttachments = {
    /**
     * Initialize Gmail attachment functionality
     */
    init: function() {
        // Add attachment UI to compose form
        this.addAttachmentUIToComposeForm();
        
        // Add attachment UI to reply form
        this.addAttachmentUIToReplyForm();
        
        // Initialize event listeners
        this.initEventListeners();
        
        console.log('CRM Gmail Attachments initialized');
    },
    
    /**
     * Add attachment UI to compose form
     */
    addAttachmentUIToComposeForm: function() {
        // Find the compose form
        const composeForm = document.querySelector('#gmailModal .compose-email-form');
        if (!composeForm) {
            console.error('Compose form not found');
            return;
        }
        
        // Find the form group for the message body
        const messageFormGroup = composeForm.querySelector('.form-group:last-of-type');
        if (!messageFormGroup) {
            console.error('Message form group not found');
            return;
        }
        
        // Create attachment UI
        const attachmentUI = document.createElement('div');
        attachmentUI.className = 'form-group mt-3';
        attachmentUI.innerHTML = `
            <div class="d-flex align-items-center mb-2">
                <label class="form-label mb-0 me-2">Attachments:</label>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-secondary crm-attach-file-btn">
                        <i class="bi bi-paperclip me-1"></i> Attach
                    </button>
                    <input type="file" class="crm-file-input d-none" multiple>
                </div>
            </div>
            <div class="crm-attachment-list border rounded p-2 bg-light" style="min-height: 60px; max-height: 150px; overflow-y: auto;">
                <p class="text-muted text-center mb-0 py-2 crm-no-attachments-msg">No files attached</p>
                <div class="crm-attached-files d-none">
                    <!-- Attached files will be listed here -->
                </div>
            </div>
        `;
        
        // Insert attachment UI after message form group
        messageFormGroup.insertAdjacentElement('afterend', attachmentUI);
    },
    
    /**
     * Add attachment UI to reply form
     */
    addAttachmentUIToReplyForm: function() {
        // Find the reply form
        const replyForm = document.querySelector('#gmailModal .reply-email-form');
        if (!replyForm) {
            console.error('Reply form not found');
            return;
        }
        
        // Find the form group for the message body
        const messageFormGroup = replyForm.querySelector('.form-group:last-of-type');
        if (!messageFormGroup) {
            console.error('Message form group not found in reply form');
            return;
        }
        
        // Create attachment UI
        const attachmentUI = document.createElement('div');
        attachmentUI.className = 'form-group mt-3';
        attachmentUI.innerHTML = `
            <div class="d-flex align-items-center mb-2">
                <label class="form-label mb-0 me-2">Attachments:</label>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-secondary crm-attach-file-btn">
                        <i class="bi bi-paperclip me-1"></i> Attach
                    </button>
                    <input type="file" class="crm-file-input d-none" multiple>
                </div>
            </div>
            <div class="crm-attachment-list border rounded p-2 bg-light" style="min-height: 60px; max-height: 150px; overflow-y: auto;">
                <p class="text-muted text-center mb-0 py-2 crm-no-attachments-msg">No files attached</p>
                <div class="crm-attached-files d-none">
                    <!-- Attached files will be listed here -->
                </div>
            </div>
        `;
        
        // Insert attachment UI after message form group
        messageFormGroup.insertAdjacentElement('afterend', attachmentUI);
    },
    
    /**
     * Initialize event listeners
     */
    initEventListeners: function() {
        // Attach file buttons
        document.querySelectorAll('.crm-attach-file-btn').forEach(button => {
            button.addEventListener('click', function() {
                // Find the file input in the same form group
                const fileInput = this.closest('.form-group').querySelector('.crm-file-input');
                if (fileInput) {
                    fileInput.click();
                }
            });
        });
        
        // File inputs
        document.querySelectorAll('.crm-file-input').forEach(input => {
            input.addEventListener('change', function() {
                CRMGmailAttachments.handleFileSelection(this);
            });
        });
        
        // Add event listeners to send buttons
        document.querySelectorAll('#gmailModal .send-email-btn').forEach(button => {
            // Clone the button to remove any existing event listeners
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // Add new event listener
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get the form
                const form = this.closest('form');
                if (!form) return;
                
                // Get form data
                const to = form.querySelector('input[name="to"]').value;
                const subject = form.querySelector('input[name="subject"]').value;
                const message = form.querySelector('textarea[name="message"]').value;
                
                // Validate form
                if (!to || !subject || !message) {
                    alert('Please fill in all fields');
                    return;
                }
                
                // Get attachments
                const attachmentItems = form.querySelectorAll('.crm-attached-file-item');
                let attachmentMsg = '';
                if (attachmentItems.length > 0) {
                    attachmentMsg = `\nAttachments: ${attachmentItems.length} file(s)`;
                }
                
                // Show success message
                alert(`Email sent successfully!${attachmentMsg}`);
                
                // Clear form
                form.reset();
                
                // Clear attachments
                CRMGmailAttachments.clearAttachments(form);
                
                // Close compose/reply view
                if (form.classList.contains('compose-email-form')) {
                    // Switch back to inbox view
                    document.querySelector('#gmailModal .nav-link[href="#inbox"]').click();
                } else if (form.classList.contains('reply-email-form')) {
                    // Hide reply form
                    form.classList.add('d-none');
                    
                    // Show email content
                    document.querySelector('#gmailModal .email-content').classList.remove('d-none');
                }
            });
        });
        
        // Add event listeners to reply buttons
        document.querySelectorAll('#gmailModal .reply-btn').forEach(button => {
            // Clone the button to remove any existing event listeners
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // Add new event listener
            newButton.addEventListener('click', function() {
                // Clear any existing attachments in the reply form
                const replyForm = document.querySelector('#gmailModal .reply-email-form');
                if (replyForm) {
                    CRMGmailAttachments.clearAttachments(replyForm);
                }
            });
        });
        
        // Add event listeners to compose button
        const composeButton = document.querySelector('#gmailModal .compose-btn');
        if (composeButton) {
            // Clone the button to remove any existing event listeners
            const newButton = composeButton.cloneNode(true);
            composeButton.parentNode.replaceChild(newButton, composeButton);
            
            // Add new event listener
            newButton.addEventListener('click', function() {
                // Clear any existing attachments in the compose form
                const composeForm = document.querySelector('#gmailModal .compose-email-form');
                if (composeForm) {
                    CRMGmailAttachments.clearAttachments(composeForm);
                }
            });
        }
    },
    
    /**
     * Handle file selection
     * @param {HTMLInputElement} fileInput - The file input element
     */
    handleFileSelection: function(fileInput) {
        if (!fileInput.files || fileInput.files.length === 0) return;
        
        // Find the attachment container
        const form = fileInput.closest('form') || fileInput.closest('.form-group').parentNode;
        const attachedFilesContainer = form.querySelector('.crm-attached-files');
        const noAttachmentsMsg = form.querySelector('.crm-no-attachments-msg');
        
        if (!attachedFilesContainer || !noAttachmentsMsg) return;
        
        // Hide the "No files attached" message
        noAttachmentsMsg.classList.add('d-none');
        
        // Show the attached files container
        attachedFilesContainer.classList.remove('d-none');
        
        // Add each file to the list
        for (let i = 0; i < fileInput.files.length; i++) {
            const file = fileInput.files[i];
            const fileSize = this.formatFileSize(file.size);
            const fileType = this.getFileTypeIcon(file.name);
            
            const fileItem = document.createElement('div');
            fileItem.className = 'crm-attached-file-item d-flex align-items-center p-2 border-bottom';
            fileItem.innerHTML = `
                <i class="${fileType.icon} ${fileType.color} me-2"></i>
                <div class="flex-grow-1">
                    <div class="text-truncate" style="max-width: 200px;">${file.name}</div>
                    <small class="text-muted">${fileSize}</small>
                </div>
                <button type="button" class="btn btn-sm btn-link text-danger p-0 ms-2 remove-attachment-btn">
                    <i class="bi bi-x-circle"></i>
                </button>
            `;
            
            attachedFilesContainer.appendChild(fileItem);
            
            // Add event listener to remove button
            const removeBtn = fileItem.querySelector('.remove-attachment-btn');
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    fileItem.remove();
                    
                    // If no more files, show the "No files attached" message
                    if (attachedFilesContainer.children.length === 0) {
                        attachedFilesContainer.classList.add('d-none');
                        noAttachmentsMsg.classList.remove('d-none');
                    }
                });
            }
        }
        
        // Clear the file input so the same files can be selected again if needed
        fileInput.value = '';
    },
    
    /**
     * Format file size in human-readable format
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted file size
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * Get file type icon based on file extension
     * @param {string} fileName - File name
     * @returns {Object} Icon class and color
     */
    getFileTypeIcon: function(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        
        switch (extension) {
            case 'pdf':
                return { icon: 'bi bi-file-earmark-pdf', color: 'text-danger' };
            case 'doc':
            case 'docx':
                return { icon: 'bi bi-file-earmark-word', color: 'text-primary' };
            case 'xls':
            case 'xlsx':
                return { icon: 'bi bi-file-earmark-excel', color: 'text-success' };
            case 'ppt':
            case 'pptx':
                return { icon: 'bi bi-file-earmark-ppt', color: 'text-warning' };
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
                return { icon: 'bi bi-file-earmark-image', color: 'text-info' };
            case 'zip':
            case 'rar':
            case '7z':
                return { icon: 'bi bi-file-earmark-zip', color: 'text-secondary' };
            default:
                return { icon: 'bi bi-file-earmark', color: 'text-secondary' };
        }
    },
    
    /**
     * Clear attachments from a form
     * @param {HTMLElement} form - The form element
     */
    clearAttachments: function(form) {
        const attachedFilesContainer = form.querySelector('.crm-attached-files');
        const noAttachmentsMsg = form.querySelector('.crm-no-attachments-msg');
        
        if (attachedFilesContainer && noAttachmentsMsg) {
            // Clear attached files
            attachedFilesContainer.innerHTML = '';
            attachedFilesContainer.classList.add('d-none');
            
            // Show "No files attached" message
            noAttachmentsMsg.classList.remove('d-none');
        }
    }
};

// Make the functionality available globally
window.CRMGmailAttachments = CRMGmailAttachments;
