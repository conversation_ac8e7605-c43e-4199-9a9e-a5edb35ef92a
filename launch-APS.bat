@echo off
echo Starting Advanced Planning and Scheduling (APS)...
echo.

REM Set up environment variables
set PATH=C:\ISASUITE\PortableNodeJS;%PATH%

REM Change to the APS directory
cd /d C:\ISASUITE\apps\APS\

REM Check if dependencies need to be installed
if not exist node_modules\. (
    echo Installing dependencies...
    call pnpm install
)

REM Run the application with production mode by default
echo Starting APS in production mode...
echo To access APS, go to: http://localhost:3005
start "" http://localhost:3005

REM Start using pnpm instead of directly invoking Node
pnpm start

echo.
echo APS application has been closed.
pause

