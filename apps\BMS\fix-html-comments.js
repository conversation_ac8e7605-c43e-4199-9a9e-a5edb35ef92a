// <PERSON>ript to fix HTML comments in BMS index.html
const fs = require('fs');
const path = require('path');

// Read the current BMS index.html file
const indexPath = path.join(__dirname, 'public', 'index.html');
let content = fs.readFileSync(indexPath, 'utf8');

// Create a backup of the original file
fs.writeFileSync(indexPath + '.bak5', content, 'utf8');

// Fix HTML comments
content = content.replace(/!-- /g, '<!-- ');

// Write the fixed content back to the file
fs.writeFileSync(indexPath, content, 'utf8');

console.log('HTML comments fixed successfully!');
