/**
 * Enhanced Google Drive Actions for CRM
 * Professional search, sort, filter, and modal system
 */

/**
 * Initialize search, sort, and filter functionality for all Google modals
 */
function initializeSearchSortFilter() {
    // Google Drive - Search functionality
    initializeDriveSearch();

    // Google Drive Shared - Search functionality
    initializeSharedSearch();

    // Google Sheets - Search functionality
    initializeSheetsSearch();

    // Google Docs - Search functionality
    initializeDocsSearch();

    // Gmail - Search functionality
    initializeGmailSearch();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Also try to initialize immediately if modal exists
    initializeSearchSortFilter();
    
    // Initialize Gmail search when Gmail modal is shown
    const gmailModal = document.getElementById('gmailModal');
    if (gmailModal) {
        gmailModal.addEventListener('shown.bs.modal', function() {
            console.log('Gmail modal shown, initializing Gmail search');
            setTimeout(() => {
                initializeGmailSearch();
            }, 100);
        });
    }
});

/**
 * Enhanced action button handler using event delegation
 */
document.addEventListener('click', function(e) {
    const button = e.target.closest('button');
    if (!button) return;
    
    // Check if this is an action button in a Google modal
    if (button.querySelector('i.bi-eye, i.bi-download, i.bi-share, i.bi-trash')) {
        // Make sure we're in the drive modal, sheets modal, docs modal, or gmail modal
        const driveModal = button.closest('#driveModal');
        const sheetsModal = button.closest('#sheetsModal');
        const docsModal = button.closest('#docsModal');
        const gmailModal = button.closest('#gmailModal');
        if (!driveModal && !sheetsModal && !docsModal && !gmailModal) return;
        
        // Get the file information
        const row = button.closest('tr');
        const attachmentItem = button.closest('.attachment-item');
        
        // Handle Gmail attachments differently
        if (gmailModal && attachmentItem) {
            handleGmailAttachment(button, attachmentItem);
            return;
        }
        
        if (!row) return;
        
        // Get file details from the row
        const nameCell = row.querySelector('td:first-child');
        const fileName = nameCell ? nameCell.textContent.trim() : 'Unknown File';
        
        // Determine file type from icon
        const iconElement = nameCell ? nameCell.querySelector('i') : null;
        let fileType = 'file';
        if (iconElement) {
            if (iconElement.classList.contains('bi-folder-fill')) {
                fileType = 'folder';
            } else if (iconElement.classList.contains('bi-file-earmark-pdf')) {
                fileType = 'pdf';
            } else if (iconElement.classList.contains('bi-file-earmark-spreadsheet')) {
                fileType = 'spreadsheet';
            } else if (iconElement.classList.contains('bi-file-earmark-text')) {
                fileType = 'document';
            } else if (iconElement.classList.contains('bi-file-earmark-image')) {
                fileType = 'image';
            }
        }
        
        // Determine action based on button type
        const buttonIcon = button.querySelector('i');
        if (!buttonIcon) return;
        
        if (buttonIcon.classList.contains('bi-eye')) {
            // View action
            showFilePreviewModal(fileName, fileType);
        } else if (buttonIcon.classList.contains('bi-download')) {
            // Download action
            showDownloadModal(fileName, fileType);
        } else if (buttonIcon.classList.contains('bi-share')) {
            // Share action
            showShareModal(fileName, fileType);
        } else if (buttonIcon.classList.contains('bi-trash')) {
            // Delete action
            showDeleteConfirmationModal(fileName, fileType);
        }
        
        e.preventDefault();
        e.stopPropagation();
    }
});

/**
 * Initialize Google Drive search functionality
 */
function initializeDriveSearch() {
    const searchInput = document.getElementById('file-search-drive');
    const sortSelect = document.getElementById('file-sort');
    const filterSelect = document.getElementById('file-filter');

    if (!searchInput) {
        console.warn('Drive search input not found');
        return;
    }

    // Search functionality for Google Drive
    const performDriveSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const sortValue = sortSelect ? sortSelect.value : 'name-asc';
        const filterValue = filterSelect ? filterSelect.value : 'all';
        filterAndSortDriveFiles(searchTerm, sortValue, filterValue);
    };

    // Search on input change (real-time search)
    searchInput.addEventListener('input', performDriveSearch);

    // Search on Enter key
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performDriveSearch();
        }
    });

    // Sort functionality
    if (sortSelect) {
        sortSelect.addEventListener('change', performDriveSearch);
    }

    // Filter functionality
    if (filterSelect) {
        filterSelect.addEventListener('change', performDriveSearch);
    }

    // Search button functionality
    const searchButton = document.getElementById('search-files-btn');
    if (searchButton) {
        searchButton.addEventListener('click', performDriveSearch);
    }
}

/**
 * Filter and sort Google Drive files based on search term and sort option
 */
function filterAndSortDriveFiles(searchTerm, sortValue, filterValue) {
    const filesList = document.querySelector('#drive-files-list');
    if (!filesList) {
        console.warn('Drive files list not found');
        return;
    }
    
    // Store original data if not already stored
    if (!filesList.dataset.originalFiles) {
        const originalFiles = Array.from(filesList.querySelectorAll('tr'));
        filesList.dataset.originalFiles = JSON.stringify(originalFiles.map(file => file.outerHTML));
    }
    
    // If we need to reset, restore from original HTML and re-add to DOM
    if (filesList.querySelectorAll('tr').length === 0 || 
        (searchTerm === '' && sortValue === 'name-asc' && filterValue === 'all')) {
        
        // Restore original content
        const originalFilesData = JSON.parse(filesList.dataset.originalFiles);
        filesList.innerHTML = originalFilesData.join('');
    }
    
    // Now work with the current DOM files
    const files = Array.from(filesList.querySelectorAll('tr'));
    
    // Filter files
    const filteredFiles = files.filter(file => {
        const nameCell = file.querySelector('td:first-child');
        const fileName = nameCell ? nameCell.textContent.toLowerCase() : '';
        
        // Apply search filter
        const matchesSearch = !searchTerm || fileName.includes(searchTerm);
        
        // Apply type filter (for future use)
        let matchesFilter = true;
        switch (filterValue) {
            case 'folders':
                matchesFilter = nameCell && nameCell.querySelector('.bi-folder-fill');
                break;
            case 'documents':
                matchesFilter = nameCell && (
                    nameCell.querySelector('.bi-file-earmark-text') ||
                    nameCell.querySelector('.bi-file-earmark-pdf')
                );
                break;
            case 'spreadsheets':
                matchesFilter = nameCell && nameCell.querySelector('.bi-file-earmark-spreadsheet');
                break;
            case 'images':
                matchesFilter = nameCell && nameCell.querySelector('.bi-file-earmark-image');
                break;
            default:
                matchesFilter = true;
        }
        
        return matchesSearch && matchesFilter;
    });
    
    // Sort files
    filteredFiles.sort((a, b) => {
        const aNameCell = a.querySelector('td:first-child');
        const bNameCell = b.querySelector('td:first-child');
        const aName = aNameCell ? aNameCell.textContent.trim() : '';
        const bName = bNameCell ? bNameCell.textContent.trim() : '';
        
        const aDateCell = a.querySelector('td:nth-child(2)');
        const bDateCell = b.querySelector('td:nth-child(2)');
        const aDate = aDateCell ? new Date(aDateCell.textContent) : new Date(0);
        const bDate = bDateCell ? new Date(bDateCell.textContent) : new Date(0);
        
        switch (sortValue) {
            case 'name':
            case 'name-asc':
                return aName.localeCompare(bName);
            case 'name-desc':
                return bName.localeCompare(aName);
            case 'date':
            case 'date-desc':
                return bDate - aDate;
            case 'date-asc':
                return aDate - bDate;
            case 'size':
                // For demo purposes, sort by name if size not available
                return aName.localeCompare(bName);
            case 'type':
                // Sort by file extension
                const aExt = aName.split('.').pop() || '';
                const bExt = bName.split('.').pop() || '';
                return aExt.localeCompare(bExt);
            default:
                return 0;
        }
    });
    
    // Clear files list and add filtered/sorted files
    filesList.innerHTML = '';
    
    if (filteredFiles.length === 0) {
        // Show no results message
        const noResultsRow = document.createElement('tr');
        noResultsRow.innerHTML = `
            <td colspan="3" class="text-center text-muted py-4">
                <i class="bi bi-search display-4 mb-2"></i>
                <p>No files found matching your criteria</p>
                <small>Try adjusting your search terms</small>
            </td>
        `;
        filesList.appendChild(noResultsRow);
    } else {
        filteredFiles.forEach(file => filesList.appendChild(file));
    }
}

/**
 * Show enhanced file preview modal
 */
function showFilePreviewModal(fileName, fileType) {
    // Create modal HTML
    const modalHtml = `
        <div class="modal fade" id="filePreviewModal" tabindex="-1" aria-labelledby="filePreviewModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="filePreviewModalLabel">
                            <i class="bi bi-eye me-2"></i>File Preview
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center p-4">
                            <div class="mb-3">
                                ${getFileIcon(fileType, 'display-1')}
                            </div>
                            <h4>${fileName}</h4>
                            <p class="text-muted">File Type: ${fileType.charAt(0).toUpperCase() + fileType.slice(1)}</p>
                            <div class="mt-4">
                                <p>Preview functionality would be implemented here.</p>
                                <p class="small text-muted">In a real application, this would show the file content.</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="showDownloadModal('${fileName}', '${fileType}')">
                            <i class="bi bi-download me-2"></i>Download
                        </button>
                        <button type="button" class="btn btn-success" onclick="showShareModal('${fileName}', '${fileType}')">
                            <i class="bi bi-share me-2"></i>Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('filePreviewModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Get the modal element and set very high z-index to appear above Google Drive modal
    const modalElement = document.getElementById('filePreviewModal');
    modalElement.style.zIndex = '9999'; // Much higher than Google Drive modal to ensure it appears on top

    // Show modal without backdrop to avoid conflicts with parent modal
    const modal = new bootstrap.Modal(modalElement, {
        backdrop: false, // No backdrop to avoid conflicts with Google Drive modal
        keyboard: true,
        focus: true
    });
    modal.show();

    // Clean up when modal is hidden
    modalElement.addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * Show enhanced download modal with format options
 */
function showDownloadModal(fileName, fileType) {
    const formats = getDownloadFormats(fileType);
    const formatOptions = formats.map(format =>
        `<option value="${format.value}">${format.label}</option>`
    ).join('');

    const modalHtml = `
        <div class="modal fade" id="downloadModal" tabindex="-1" aria-labelledby="downloadModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="downloadModalLabel">
                            <i class="bi bi-download me-2"></i>Download File
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-3">
                            ${getFileIcon(fileType, 'fs-1')}
                            <h6 class="mt-2">${fileName}</h6>
                        </div>
                        <div class="mb-3">
                            <label for="downloadFormat" class="form-label">Download Format</label>
                            <select class="form-select" id="downloadFormat">
                                ${formatOptions}
                            </select>
                        </div>
                        <div class="progress" id="downloadProgress" style="display: none;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="startDownload('${fileName}', '${fileType}')">
                            <i class="bi bi-download me-2"></i>Download
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('downloadModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Get the modal element and set very high z-index
    const modalElement = document.getElementById('downloadModal');
    modalElement.style.zIndex = '10000'; // Higher than file preview modal (9999)

    // Show modal without backdrop to avoid conflicts with parent modal
    const modal = new bootstrap.Modal(modalElement, {
        backdrop: false, // No backdrop to avoid conflicts
        keyboard: true,
        focus: true
    });
    modal.show();

    // Clean up when modal is hidden
    modalElement.addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * Show enhanced share modal
 */
function showShareModal(fileName, fileType) {
    const shareLink = `https://drive.google.com/file/d/example-${fileName.replace(/\s+/g, '-').toLowerCase()}/view`;

    const modalHtml = `
        <div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="shareModalLabel">
                            <i class="bi bi-share me-2"></i>Share File
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-3">
                            ${getFileIcon(fileType, 'fs-1')}
                            <h6 class="mt-2">${fileName}</h6>
                        </div>

                        <div class="mb-3">
                            <label for="shareLink" class="form-label">Share Link</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="shareLink" value="${shareLink}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyShareLink()">
                                    <i class="bi bi-clipboard"></i> Copy
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Share via Email</label>
                            <div class="input-group mb-2">
                                <input type="email" class="form-control" id="shareEmail" placeholder="Enter email address">
                                <select class="form-select" id="sharePermission" style="max-width: 150px;">
                                    <option value="view">Can view</option>
                                    <option value="comment">Can comment</option>
                                    <option value="edit">Can edit</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-sm btn-primary" onclick="shareViaEmail('${fileName}')">
                                <i class="bi bi-envelope me-1"></i>Send Invitation
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="copyShareLink()">
                            <i class="bi bi-clipboard me-2"></i>Copy Link
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('shareModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Get the modal element and set very high z-index
    const modalElement = document.getElementById('shareModal');
    modalElement.style.zIndex = '10000'; // Higher than file preview modal (9999)

    // Show modal without backdrop to avoid conflicts with parent modal
    const modal = new bootstrap.Modal(modalElement, {
        backdrop: false, // No backdrop to avoid conflicts
        keyboard: true,
        focus: true
    });
    modal.show();

    // Clean up when modal is hidden
    modalElement.addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * Show delete confirmation modal
 */
function showDeleteConfirmationModal(fileName, fileType) {
    const modalHtml = `
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel">
                            <i class="bi bi-trash me-2"></i>Delete File
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-3">
                            ${getFileIcon(fileType, 'fs-1 text-danger')}
                            <h6 class="mt-2">${fileName}</h6>
                        </div>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Are you sure you want to delete this file? This action cannot be undone.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" onclick="confirmDelete('${fileName}')">
                            <i class="bi bi-trash me-2"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('deleteModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Get the modal element and set very high z-index
    const modalElement = document.getElementById('deleteModal');
    modalElement.style.zIndex = '10000'; // Higher than file preview modal (9999)

    // Show modal without backdrop to avoid conflicts with parent modal
    const modal = new bootstrap.Modal(modalElement, {
        backdrop: false, // No backdrop to avoid conflicts
        keyboard: true,
        focus: true
    });
    modal.show();

    // Clean up when modal is hidden
    modalElement.addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * Utility Functions
 */

/**
 * Get file icon based on file type
 */
function getFileIcon(fileType, className = '') {
    const icons = {
        'folder': `<i class="bi bi-folder-fill text-primary ${className}"></i>`,
        'pdf': `<i class="bi bi-file-earmark-pdf text-danger ${className}"></i>`,
        'spreadsheet': `<i class="bi bi-file-earmark-spreadsheet text-success ${className}"></i>`,
        'document': `<i class="bi bi-file-earmark-text text-primary ${className}"></i>`,
        'image': `<i class="bi bi-file-earmark-image text-info ${className}"></i>`,
        'file': `<i class="bi bi-file-earmark text-secondary ${className}"></i>`
    };
    return icons[fileType] || icons['file'];
}

/**
 * Get download formats based on file type
 */
function getDownloadFormats(fileType) {
    const formats = {
        'pdf': [
            { value: 'original', label: 'Original PDF' },
            { value: 'docx', label: 'Word Document (.docx)' }
        ],
        'spreadsheet': [
            { value: 'original', label: 'Original Excel (.xlsx)' },
            { value: 'pdf', label: 'PDF Document' },
            { value: 'csv', label: 'CSV File' }
        ],
        'document': [
            { value: 'original', label: 'Original Document (.docx)' },
            { value: 'pdf', label: 'PDF Document' },
            { value: 'txt', label: 'Plain Text (.txt)' }
        ],
        'image': [
            { value: 'original', label: 'Original Image' },
            { value: 'png', label: 'PNG Image' },
            { value: 'jpg', label: 'JPEG Image' }
        ]
    };
    return formats[fileType] || [{ value: 'original', label: 'Original File' }];
}

/**
 * Start download process
 */
function startDownload(fileName, fileType) {
    const format = document.getElementById('downloadFormat')?.value || 'original';
    const progressBar = document.querySelector('#downloadProgress .progress-bar');
    const progressContainer = document.getElementById('downloadProgress');

    if (progressContainer) {
        progressContainer.style.display = 'block';
    }

    // Simulate download progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);

            // Show completion
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.textContent = 'Download Complete!';
            }

            // Hide modal after a short delay
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('downloadModal'));
                if (modal) {
                    modal.hide();
                }
            }, 1000);
        }

        if (progressBar) {
            progressBar.style.width = progress + '%';
            progressBar.textContent = Math.round(progress) + '%';
        }
    }, 200);
}

/**
 * Copy share link to clipboard
 */
function copyShareLink() {
    const shareLink = document.getElementById('shareLink');
    if (shareLink) {
        shareLink.select();
        shareLink.setSelectionRange(0, 99999); // For mobile devices

        try {
            document.execCommand('copy');

            // Show feedback
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check"></i> Copied!';
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-success');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);
        } catch (err) {
            console.error('Failed to copy link:', err);
        }
    }
}

/**
 * Share via email
 */
function shareViaEmail(fileName) {
    const email = document.getElementById('shareEmail')?.value;
    const permission = document.getElementById('sharePermission')?.value || 'view';

    if (!email) {
        alert('Please enter an email address');
        return;
    }

    // Simulate sending email
    alert(`Sharing invitation sent to ${email} with ${permission} permissions for ${fileName}`);

    // Clear the email input
    const emailInput = document.getElementById('shareEmail');
    if (emailInput) {
        emailInput.value = '';
    }
}

/**
 * Confirm file deletion
 */
function confirmDelete(fileName) {
    // Simulate deletion
    alert(`${fileName} has been deleted successfully`);

    // Hide modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
    if (modal) {
        modal.hide();
    }

    // In a real application, you would remove the file from the list
    // and refresh the view
}

/**
 * Initialize shared files search functionality
 */
function initializeSharedSearch() {
    const searchInput = document.getElementById('shared-search');
    const sortSelect = document.getElementById('shared-sort');
    const filterSelect = document.getElementById('shared-filter');

    if (!searchInput) {
        console.warn('Shared search input not found');
        return;
    }

    // Search functionality for shared files
    const performSharedSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const sortValue = sortSelect ? sortSelect.value : 'name-asc';
        const filterValue = filterSelect ? filterSelect.value : 'all';
        filterAndSortSharedFiles(searchTerm, sortValue, filterValue);
    };

    // Search on input change (real-time search)
    searchInput.addEventListener('input', performSharedSearch);

    // Search on Enter key
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSharedSearch();
        }
    });

    // Sort functionality
    if (sortSelect) {
        sortSelect.addEventListener('change', performSharedSearch);
    }

    // Filter functionality
    if (filterSelect) {
        filterSelect.addEventListener('change', performSharedSearch);
    }

    // Search button functionality
    const searchButton = document.getElementById('search-shared-btn');
    if (searchButton) {
        searchButton.addEventListener('click', performSharedSearch);
    }
}

/**
 * Filter and sort shared files
 */
function filterAndSortSharedFiles(searchTerm, sortValue, filterValue) {
    const filesList = document.querySelector('#shared-files-list');
    if (!filesList) {
        console.warn('Shared files list not found');
        return;
    }

    // Store original data if not already stored
    if (!filesList.dataset.originalFiles) {
        const originalFiles = Array.from(filesList.querySelectorAll('tr'));
        filesList.dataset.originalFiles = JSON.stringify(originalFiles.map(file => file.outerHTML));
    }

    // If we need to reset, restore from original HTML and re-add to DOM
    if (filesList.querySelectorAll('tr').length === 0 ||
        (searchTerm === '' && sortValue === 'name-asc' && filterValue === 'all')) {

        // Restore original content
        const originalFilesData = JSON.parse(filesList.dataset.originalFiles);
        filesList.innerHTML = originalFilesData.join('');
    }

    // Now work with the current DOM files
    const files = Array.from(filesList.querySelectorAll('tr'));

    // Filter files (same logic as drive files)
    const filteredFiles = files.filter(file => {
        const nameCell = file.querySelector('td:first-child');
        const fileName = nameCell ? nameCell.textContent.toLowerCase() : '';

        // Apply search filter
        const matchesSearch = !searchTerm || fileName.includes(searchTerm);

        // Apply type filter
        let matchesFilter = true;
        switch (filterValue) {
            case 'documents':
                matchesFilter = nameCell && (
                    nameCell.querySelector('.bi-file-earmark-text') ||
                    nameCell.querySelector('.bi-file-earmark-pdf')
                );
                break;
            case 'spreadsheets':
                matchesFilter = nameCell && nameCell.querySelector('.bi-file-earmark-spreadsheet');
                break;
            case 'images':
                matchesFilter = nameCell && nameCell.querySelector('.bi-file-earmark-image');
                break;
            case 'pdfs':
                matchesFilter = nameCell && nameCell.querySelector('.bi-file-earmark-pdf');
                break;
            default:
                matchesFilter = true;
        }

        return matchesSearch && matchesFilter;
    });

    // Sort files (same logic as drive files)
    filteredFiles.sort((a, b) => {
        const aNameCell = a.querySelector('td:first-child');
        const bNameCell = b.querySelector('td:first-child');
        const aName = aNameCell ? aNameCell.textContent.trim() : '';
        const bName = bNameCell ? bNameCell.textContent.trim() : '';

        switch (sortValue) {
            case 'name':
            case 'name-asc':
                return aName.localeCompare(bName);
            case 'name-desc':
                return bName.localeCompare(aName);
            case 'shared-desc':
            case 'shared-asc':
                // For demo purposes, sort by name if shared date not available
                return sortValue === 'shared-desc' ? bName.localeCompare(aName) : aName.localeCompare(bName);
            default:
                return 0;
        }
    });

    // Clear files list and add filtered/sorted files
    filesList.innerHTML = '';

    if (filteredFiles.length === 0) {
        // Show no results message
        const noResultsRow = document.createElement('tr');
        noResultsRow.innerHTML = `
            <td colspan="3" class="text-center text-muted py-4">
                <i class="bi bi-search display-4 mb-2"></i>
                <p>No files found matching your criteria</p>
                <small>Try adjusting your search terms</small>
            </td>
        `;
        filesList.appendChild(noResultsRow);
    } else {
        filteredFiles.forEach(file => filesList.appendChild(file));
    }
}

/**
 * Placeholder functions for other Google services (to be implemented)
 */

/**
 * Initialize Google Sheets search functionality
 */
function initializeSheetsSearch() {
    console.log('Google Sheets search initialization - to be implemented');
}

/**
 * Initialize Google Docs search functionality
 */
function initializeDocsSearch() {
    console.log('Google Docs search initialization - to be implemented');
}

/**
 * Initialize Gmail search functionality
 */
function initializeGmailSearch() {
    console.log('Gmail search initialization - to be implemented');
}

/**
 * Handle Gmail attachment actions
 */
function handleGmailAttachment(button, attachmentItem) {
    console.log('Gmail attachment handling - to be implemented');
}
