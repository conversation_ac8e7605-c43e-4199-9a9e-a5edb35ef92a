/**
 * Gmail Performance Verification Script
 * This script verifies that all major performance bottlenecks have been eliminated
 */

console.log('🔍 Gmail Performance Verification Starting...');
console.log('=' * 50);

// Track performance metrics
const performanceResults = {
    setTimeoutCalls: 0,
    clickResponseTimes: [],
    modalOpenTimes: [],
    issues: []
};

// Monitor setTimeout calls
const originalSetTimeout = window.setTimeout;
window.setTimeout = function(...args) {
    performanceResults.setTimeoutCalls++;
    
    // Check for problematic delays
    const delay = args[1];
    if (delay > 300) {
        performanceResults.issues.push(`Long setTimeout delay detected: ${delay}ms`);
    }
    
    return originalSetTimeout.apply(this, args);
};

// Test Gmail click responsiveness
function testGmailClickResponsiveness() {
    console.log('📊 Testing Gmail click responsiveness...');
    
    const gmailLinks = document.querySelectorAll('[data-bs-target="#gmailModal"], .gmail-open-btn, a[href*="gmail"]');
    
    if (gmailLinks.length === 0) {
        performanceResults.issues.push('No Gmail links found for testing');
        return;
    }
    
    gmailLinks.forEach((link, index) => {
        const originalClick = link.onclick;
        
        link.onclick = function(e) {
            const startTime = performance.now();
            
            // Call original handler if exists
            if (originalClick) {
                originalClick.call(this, e);
            }
            
            // Measure response time
            setTimeout(() => {
                const responseTime = performance.now() - startTime;
                performanceResults.clickResponseTimes.push(responseTime);
                
                if (responseTime > 100) {
                    performanceResults.issues.push(`Slow click response on Gmail link ${index}: ${responseTime.toFixed(2)}ms`);
                }
            }, 0);
        };
    });
    
    console.log(`✅ Monitored ${gmailLinks.length} Gmail links for click responsiveness`);
}

// Test modal opening performance
function testModalPerformance() {
    console.log('📊 Testing Gmail modal performance...');
    
    const gmailModal = document.getElementById('gmailModal');
    if (!gmailModal) {
        performanceResults.issues.push('Gmail modal not found');
        return;
    }
    
    // Monitor modal show events
    gmailModal.addEventListener('show.bs.modal', function() {
        const startTime = performance.now();
        
        this.addEventListener('shown.bs.modal', function() {
            const openTime = performance.now() - startTime;
            performanceResults.modalOpenTimes.push(openTime);
            
            if (openTime > 200) {
                performanceResults.issues.push(`Slow modal opening: ${openTime.toFixed(2)}ms`);
            }
        }, { once: true });
    });
    
    console.log('✅ Modal performance monitoring active');
}

// Check for aggressive polling/monitoring
function checkForAggressivePolling() {
    console.log('📊 Checking for aggressive polling patterns...');
    
    // Check for setInterval usage
    const originalSetInterval = window.setInterval;
    let intervalCount = 0;
    
    window.setInterval = function(fn, delay) {
        intervalCount++;
        
        if (delay < 2000) {
            performanceResults.issues.push(`Aggressive setInterval detected: ${delay}ms interval`);
        }
        
        return originalSetInterval.apply(this, arguments);
    };
    
    // Check for MutationObserver overuse
    const originalMutationObserver = window.MutationObserver;
    let observerCount = 0;
    
    window.MutationObserver = function(callback) {
        observerCount++;
        return new originalMutationObserver(callback);
    };
    
    setTimeout(() => {
        console.log(`📈 Created ${observerCount} MutationObservers and ${intervalCount} intervals`);
        
        if (observerCount > 10) {
            performanceResults.issues.push(`Excessive MutationObservers: ${observerCount}`);
        }
        
        if (intervalCount > 5) {
            performanceResults.issues.push(`Excessive setIntervals: ${intervalCount}`);
        }
    }, 2000);
}

// Generate performance report
function generatePerformanceReport() {
    console.log('📋 Gmail Performance Verification Report');
    console.log('=' * 50);
    
    // Click performance
    if (performanceResults.clickResponseTimes.length > 0) {
        const avgClickTime = performanceResults.clickResponseTimes.reduce((a, b) => a + b, 0) / performanceResults.clickResponseTimes.length;
        console.log(`🖱️  Average Click Response: ${avgClickTime.toFixed(2)}ms`);
        
        if (avgClickTime < 50) {
            console.log('✅ Excellent click responsiveness');
        } else if (avgClickTime < 100) {
            console.log('⚠️ Good click responsiveness');
        } else {
            console.log('❌ Poor click responsiveness');
        }
    }
    
    // Modal performance
    if (performanceResults.modalOpenTimes.length > 0) {
        const avgModalTime = performanceResults.modalOpenTimes.reduce((a, b) => a + b, 0) / performanceResults.modalOpenTimes.length;
        console.log(`🪟 Average Modal Open Time: ${avgModalTime.toFixed(2)}ms`);
        
        if (avgModalTime < 100) {
            console.log('✅ Excellent modal performance');
        } else if (avgModalTime < 200) {
            console.log('⚠️ Good modal performance');
        } else {
            console.log('❌ Poor modal performance');
        }
    }
    
    // setTimeout usage
    console.log(`⏰ setTimeout Calls: ${performanceResults.setTimeoutCalls}`);
    if (performanceResults.setTimeoutCalls < 5) {
        console.log('✅ Minimal setTimeout usage');
    } else if (performanceResults.setTimeoutCalls < 15) {
        console.log('⚠️ Moderate setTimeout usage');
    } else {
        console.log('❌ Excessive setTimeout usage');
    }
    
    // Issues found
    if (performanceResults.issues.length === 0) {
        console.log('🎉 No performance issues detected!');
    } else {
        console.log(`❌ ${performanceResults.issues.length} performance issues found:`);
        performanceResults.issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue}`);
        });
    }
    
    console.log('=' * 50);
    console.log('Gmail Performance Verification Complete');
}

// Initialize performance verification
function initializePerformanceVerification() {
    console.log('🚀 Initializing Gmail performance verification...');
    
    testGmailClickResponsiveness();
    testModalPerformance();
    checkForAggressivePolling();
    
    // Generate report after 5 seconds
    setTimeout(generatePerformanceReport, 5000);
    
    console.log('✅ Performance verification active - report will generate in 5 seconds');
    console.log('💡 Try clicking Gmail links and opening modals to test performance');
}

// Auto-run when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePerformanceVerification);
} else {
    initializePerformanceVerification();
}

// Export for manual use
window.gmailPerformanceVerification = {
    results: performanceResults,
    generateReport: generatePerformanceReport,
    test: initializePerformanceVerification
};

console.log('📋 Gmail Performance Verification Script Loaded');
console.log('💡 Access results via: window.gmailPerformanceVerification.results');
