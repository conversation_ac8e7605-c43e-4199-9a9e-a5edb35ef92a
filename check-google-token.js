// Script to check Google token status
const fs = require('fs');
const path = require('path');

const TOKEN_PATH = path.join(__dirname, 'SharedFeatures', 'integrations', 'google-token.json');
const CREDENTIALS_PATH = path.join(__dirname, 'SharedFeatures', 'integrations', 'google-credentials.json');

function checkToken() {
  console.log('Checking Google token status...');
  
  // Check if credentials file exists
  if (!fs.existsSync(CREDENTIALS_PATH)) {
    console.error('❌ Google credentials file not found at:', CREDENTIALS_PATH);
    return;
  }
  console.log('✅ Google credentials file found');
  
  // Check if token file exists
  if (!fs.existsSync(TOKEN_PATH)) {
    console.error('❌ Google token file not found at:', TOKEN_PATH);
    return;
  }
  console.log('✅ Google token file found');
  
  // Read and validate token
  try {
    const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH, 'utf8'));
    console.log('✅ Token file is valid JSON');
    
    // Check if token has expired
    if (tokenData.expiry_date) {
      const expiryDate = new Date(tokenData.expiry_date);
      const now = new Date();
      
      console.log(`Token expiry date: ${expiryDate.toLocaleString()}`);
      console.log(`Current date: ${now.toLocaleString()}`);
      
      if (expiryDate > now) {
        console.log(`✅ Token is valid for another ${Math.round((expiryDate - now) / 1000 / 60)} minutes`);
      } else {
        console.log(`❌ Token has expired ${Math.round((now - expiryDate) / 1000 / 60)} minutes ago`);
        console.log('You need to regenerate the token by running:');
        console.log('node SharedFeatures/integrations/google.js');
      }
    } else {
      console.log('⚠️ Token does not have an expiry date');
    }
    
    // Check required token fields
    const requiredFields = ['access_token', 'refresh_token', 'scope', 'token_type'];
    const missingFields = requiredFields.filter(field => !tokenData[field]);
    
    if (missingFields.length > 0) {
      console.log(`❌ Token is missing required fields: ${missingFields.join(', ')}`);
    } else {
      console.log('✅ Token has all required fields');
    }
    
  } catch (error) {
    console.error('❌ Error reading or parsing token file:', error.message);
  }
}

checkToken();
