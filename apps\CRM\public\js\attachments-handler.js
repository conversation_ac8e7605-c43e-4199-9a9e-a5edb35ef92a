
/**
 * Attachments Handler for CRM Application
 * Manages all attachment-related operations
 */

class AttachmentsHandler {
    constructor() {
        this.selectedFiles = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalItems = 0;
        this.sortBy = 'name';
        this.sortDirection = 'asc';
        this.debug = true;
    }

    /**
     * Initialize the attachments handler
     */
    init() {
        this.log("Initializing Attachments Handler");
        this.bindEvents();
        this.loadFiles();
    }

    /**
     * Bind all event handlers
     */
    bindEvents() {
        // Bind tab change events
        const tabs = document.querySelectorAll('#attachmentsTab button[data-bs-toggle="tab"]');
        tabs.forEach(tab => {
            tab.addEventListener('shown.bs.tab', event => {
                this.onTabChange(event.target.id);
            });
        });

        // Bind search
        const searchInput = document.getElementById('attachment-search');
        if (searchInput) {
            searchInput.addEventListener('input', e => {
                this.searchFiles(e.target.value);
            });
        }

        // Bind sort
        const sortDropdown = document.querySelectorAll('#sortDropdown + .dropdown-menu .dropdown-item');
        if (sortDropdown) {
            sortDropdown.forEach(item => {
                item.addEventListener('click', e => {
                    e.preventDefault();
                    const sortBy = e.target.getAttribute('data-sort');
                    this.sortFiles(sortBy);
                });
            });
        }

        // Bind upload button
        const uploadBtn = document.getElementById('upload-attachment-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', e => {
                this.uploadFiles();
            });
        }

        // Bind pagination
        const paginationLinks = document.querySelectorAll('.pagination .page-link');
        if (paginationLinks) {
            paginationLinks.forEach(link => {
                link.addEventListener('click', e => {
                    e.preventDefault();
                    const page = e.target.getAttribute('data-page');
                    if (page) {
                        this.goToPage(parseInt(page));
                    }
                });
            });
        }
    }

    /**
     * Load files into the attachments list
     */
    loadFiles() {
        this.log("Loading files");
        
        // Normally, we'd fetch files from an API here
        // For demo purposes, we'll use the existing files in the DOM
        
        // Make sure all file rows have proper event handlers
        document.querySelectorAll('#attachments-list tr').forEach(row => {
            // Format the cells with proper content if needed
            const actionCell = row.querySelector('td:last-child');
            if (actionCell && !actionCell.querySelector('.btn-group')) {
                const filename = row.getAttribute('data-filename');
                if (filename) {
                    const fileType = this.getFileType(filename);
                    actionCell.innerHTML = this.getActionButtonsHTML(filename, fileType);
                }
            }
        });

        // Update pagination
        this.updatePagination();
    }

    /**
     * Handle tab change
     * @param {string} tabId - ID of the selected tab
     */
    onTabChange(tabId) {
        this.log(`Tab changed to: ${tabId}`);
        
        switch(tabId) {
            case 'files-tab':
                this.loadFiles();
                break;
            case 'upload-tab':
                this.resetUploadForm();
                break;
            case 'shared-tab':
                this.loadSharedFiles();
                break;
            case 'recent-tab':
                this.loadRecentFiles();
                break;
        }
    }

    /**
     * Reset the upload form
     */
    resetUploadForm() {
        const uploadForm = document.getElementById('upload-attachment-form');
        if (uploadForm) {
            uploadForm.reset();
        }
    }

    /**
     * Load shared files
     */
    loadSharedFiles() {
        this.log("Loading shared files");
        // We'd fetch shared files from an API here
        // For demo, we're using the existing HTML
    }

    /**
     * Load recent files
     */
    loadRecentFiles() {
        this.log("Loading recent files");
        // We'd fetch recent files from an API here
    }

    /**
     * Search files by name
     * @param {string} query - Search query
     */
    searchFiles(query) {
        this.log(`Searching for: ${query}`);
        
        if (!query) {
            // Show all files if query is empty
            document.querySelectorAll('#attachments-list tr').forEach(row => {
                row.style.display = '';
            });
            return;
        }
        
        // Filter files based on name
        const lowerQuery = query.toLowerCase();
        document.querySelectorAll('#attachments-list tr').forEach(row => {
            const filename = row.getAttribute('data-filename');
            if (filename && filename.toLowerCase().includes(lowerQuery)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    /**
     * Sort files by column
     * @param {string} sortBy - Column to sort by
     */
    sortFiles(sortBy) {
        this.log(`Sorting by: ${sortBy}`);
        
        if (this.sortBy === sortBy) {
            // Reverse sort direction if clicking the same column
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            // New column, default to ascending
            this.sortBy = sortBy;
            this.sortDirection = 'asc';
        }
        
        const table = document.getElementById('attachments-list');
        if (!table) return;
        
        const rows = Array.from(table.querySelectorAll('tr'));
        
        rows.sort((rowA, rowB) => {
            let valA, valB;
            
            switch (sortBy) {
                case 'name':
                    valA = rowA.getAttribute('data-filename') || '';
                    valB = rowB.getAttribute('data-filename') || '';
                    break;
                case 'type':
                    valA = this.getFileType(rowA.getAttribute('data-filename') || '');
                    valB = this.getFileType(rowB.getAttribute('data-filename') || '');
                    break;
                case 'size':
                    valA = parseFloat(rowA.querySelector('td:nth-child(3)').textContent) || 0;
                    valB = parseFloat(rowB.querySelector('td:nth-child(3)').textContent) || 0;
                    break;
                case 'modified':
                    valA = rowA.querySelector('td:nth-child(4)').textContent || '';
                    valB = rowB.querySelector('td:nth-child(4)').textContent || '';
                    break;
                default:
                    valA = rowA.getAttribute('data-filename') || '';
                    valB = rowB.getAttribute('data-filename') || '';
            }
            
            // Compare based on direction
            if (this.sortDirection === 'asc') {
                return valA > valB ? 1 : -1;
            } else {
                return valA < valB ? 1 : -1;
            }
        });
        
        // Remove all rows
        rows.forEach(row => row.remove());
        
        // Add sorted rows back
        rows.forEach(row => table.appendChild(row));
    }

    /**
     * Upload files
     */
    uploadFiles() {
        this.log('Uploading files');
        
        const fileInput = document.getElementById('attachment-file');
        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
            alert('Please select a file to upload');
            return;
        }
        
        const file = fileInput.files[0];
        const nameInput = document.getElementById('attachment-name');
        const fileName = nameInput && nameInput.value ? nameInput.value : file.name;
        
        // Show progress (simulated)
        const progressBar = document.querySelector('.progress');
        const progressBarInner = progressBar.querySelector('.progress-bar');
        progressBar.style.display = 'flex';
        
        // Simulate upload progress
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 10;
            progressBarInner.style.width = `${progress}%`;
            progressBarInner.setAttribute('aria-valuenow', progress);
            
            if (progress >= 100) {
                clearInterval(progressInterval);
                
                // Hide progress bar and show success
                setTimeout(() => {
                    progressBar.style.display = 'none';
                    this.showToast(`File "${fileName}" uploaded successfully`);
                    
                    // Reset form
                    document.getElementById('upload-attachment-form').reset();
                    
                    // Switch to files tab
                    const filesTab = document.getElementById('files-tab');
                    if (filesTab) {
                        bootstrap.Tab.getOrCreateInstance(filesTab).show();
                    }
                }, 500);
            }
        }, 300);
    }

    /**
     * Get file type based on extension
     * @param {string} filename - Filename to check
     * @returns {string} File type
     */
    getFileType(filename) {
        if (!filename) return 'unknown';
        
        const extension = filename.split('.').pop().toLowerCase();
        
        switch(extension) {
            case 'pdf':
                return 'pdf';
            case 'doc':
            case 'docx':
            case 'txt':
            case 'rtf':
                return 'document';
            case 'xls':
            case 'xlsx':
            case 'csv':
                return 'spreadsheet';
            case 'ppt':
            case 'pptx':
                return 'presentation';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'svg':
                return 'image';
            case 'zip':
            case 'rar':
            case '7z':
            case 'tar':
            case 'gz':
                return 'archive';
            default:
                return 'unknown';
        }
    }

    /**
     * Get file icon class
     * @param {string} type - File type
     * @returns {string} Icon class
     */
    getFileIcon(type) {
        switch(type) {
            case 'pdf':
                return 'bi-file-earmark-pdf text-danger';
            case 'document':
                return 'bi-file-earmark-word text-primary';
            case 'spreadsheet':
                return 'bi-file-earmark-excel text-success';
            case 'presentation':
                return 'bi-file-earmark-slides text-warning';
            case 'image':
                return 'bi-file-earmark-image text-info';
            case 'archive':
                return 'bi-file-earmark-zip text-secondary';
            default:
                return 'bi-file-earmark text-secondary';
        }
    }

    /**
     * Get action buttons HTML
     * @param {string} filename - Filename
     * @param {string} fileType - File type
     * @returns {string} HTML for action buttons
     */
    getActionButtonsHTML(filename, fileType) {
        return `
            <div class="d-flex">
                <button type="button" class="btn btn-sm btn-link" onclick="viewAttachment('${fileType}', '${filename}')"><i class="bi bi-eye"></i></button>
                <button type="button" class="btn btn-sm btn-link" onclick="downloadFile('${filename}')"><i class="bi bi-download"></i></button>
                <button type="button" class="btn btn-sm btn-link" onclick="shareFile('${filename}')"><i class="bi bi-share"></i></button>
                <button type="button" class="btn btn-sm btn-link text-danger" onclick="deleteAttachment('${filename}')"><i class="bi bi-trash"></i></button>
            </div>
        `;
    }

    /**
     * Update pagination display
     */
    updatePagination() {
        // In a real app, we'd calculate pages based on total items
        // For demo, we just use the existing HTML
    }

    /**
     * Go to specific page
     * @param {number} page - Page number
     */
    goToPage(page) {
        this.currentPage = page;
        this.loadFiles();
    }

    /**
     * Show toast notification
     * @param {string} message - Message to show
     * @param {string} type - Toast type (success, error, warning, info)
     */
    showToast(message, type = 'success') {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            // Create toast container if it doesn't exist
            const newContainer = document.createElement('div');
            newContainer.id = 'toast-container';
            newContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(newContainer);
        }
        
        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type}" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        document.getElementById('toast-container').insertAdjacentHTML('beforeend', toastHTML);
        const toastEl = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastEl, { autohide: true, delay: 5000 });
        toast.show();
    }

    /**
     * Logger function
     * @param {string} message - Message to log
     */
    log(message) {
        if (this.debug) {
            console.log(`AttachmentsHandler: ${message}`);
        }
    }
}

// Create and initialize the handler
const attachmentsHandler = new AttachmentsHandler();
document.addEventListener('DOMContentLoaded', () => {
    attachmentsHandler.init();
});

// Global functions for backward compatibility
/**
 * View an attachment
 */
function viewAttachment(type, fileName) {
    console.log(`Viewing attachment: ${fileName} of type ${type}`);
    
    try {
        // Hide the attachments modal first
        const attachmentsModal = document.getElementById('attachmentsModal');
        if (attachmentsModal) {
            const modalInstance = bootstrap.Modal.getInstance(attachmentsModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
        
        // Show file viewer modal after a short delay to ensure attachments modal is hidden
        setTimeout(() => {
            // Get the file viewer modal
            const fileViewerModal = document.getElementById('fileViewerModal');
            if (!fileViewerModal) {
                console.error('File viewer modal not found in the DOM');
                alert('File viewer is not available. Please try again later.');
                return;
            }
            
            // Update modal content
            const fileTitle = fileViewerModal.querySelector('#fileViewerTitle');
            const fileContent = fileViewerModal.querySelector('#fileViewerContent');
            
            if (fileTitle && fileContent) {
                const iconClass = attachmentsHandler.getFileIcon(type);
                fileTitle.innerHTML = `<i class="bi ${iconClass}"></i> ${fileName}`;
                fileContent.innerHTML = getPreviewContent(type, fileName);
                
                // Store current file info for download/share/delete operations
                window.currentFile = {
                    name: fileName,
                    type: type
                };
                
                // Show the modal using ISA UI Manager if available
                if (typeof ISAUIManager !== 'undefined') {
                    ISAUIManager.openModal('fileViewerModal');
                } else {
                    const modal = new bootstrap.Modal(fileViewerModal);
                    modal.show();
                }
            }
        }, 300);
        
    } catch (error) {
        console.error(`Error viewing attachment: ${error.message}`);
        alert(`Could not open ${fileName}. Error: ${error.message}`);
    }
}

// Debounce mechanism to prevent multiple rapid calls
let downloadDebounce = {};

function downloadFile(fileName) {
    try {
        // Prevent multiple rapid calls for the same file
        const now = Date.now();
        if (downloadDebounce[fileName] && (now - downloadDebounce[fileName]) < 1000) {
            return; // Ignore if called within 1 second
        }
        downloadDebounce[fileName] = now;
        
        console.log(`Enhanced download for: ${fileName}`);
        showDownloadModal(fileName);
    } catch (error) {
        console.error('Error in downloadFile:', error);
    }
}

// Debounce mechanism for share function
let shareDebounce = {};

function shareFile(fileName) {
    try {
        // Prevent multiple rapid calls for the same file
        const now = Date.now();
        if (shareDebounce[fileName] && (now - shareDebounce[fileName]) < 1000) {
            return; // Ignore if called within 1 second
        }
        shareDebounce[fileName] = now;

        console.log(`Enhanced share for: ${fileName}`);

        // Show share modal
        showShareModal(fileName);

    } catch (error) {
        console.error('Error in shareFile:', error);
    }
}

/**
 * Create a modal element (BMS-style)
 */
function createModal(id, title, body, buttons = []) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = id;
    modal.tabIndex = -1;

    const buttonsHtml = buttons.map(btn =>
        `<button type="button" class="btn ${btn.class}" ${btn.dismiss ? 'data-bs-dismiss="modal"' : ''}>
            ${btn.text}
        </button>`
    ).join('');

    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${body}
                </div>
                <div class="modal-footer">
                    ${buttonsHtml}
                </div>
            </div>
        </div>
    `;

    // Add click handlers for action buttons
    buttons.forEach((btn, index) => {
        if (btn.action) {
            const buttonElement = modal.querySelectorAll('.modal-footer .btn')[index];
            buttonElement.addEventListener('click', (event) => {
                event.preventDefault();
                event.stopPropagation();
                const result = btn.action();
                if (result !== false) {
                    const bootstrapModal = bootstrap.Modal.getInstance(modal);
                    if (bootstrapModal) {
                        bootstrapModal.hide();
                    }
                }
            });
        }
    });

    return modal;
}

/**
 * Show a modal (BMS-style)
 */
function showModal(modal) {
    // Check if a modal with the same ID is already open
    const existing = document.getElementById(modal.id);
    if (existing) {
        // If modal is already visible, don't create another one
        const existingBootstrapModal = bootstrap.Modal.getInstance(existing);
        if (existingBootstrapModal && existing.classList.contains('show')) {
            return; // Modal is already open, don't create another
        }
        existing.remove();
    }
    
    // Set proper z-index and show modal
    modal.style.zIndex = '9999';
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal, {
        backdrop: true,
        keyboard: true,
        focus: true
    });
    bootstrapModal.show();
}

function showDownloadModal(fileName) {
    const modal = createModal('downloadModal', 'Download File', `
        <div class="text-center">
            <div class="mb-4">
                <i class="bi bi-download display-1 text-success"></i>
            </div>
            <div class="file-info mb-4">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="bi bi-file-earmark me-2 fs-3"></i>
                    <h5 class="mb-0">${fileName}</h5>
                </div>
            </div>
            <div class="download-options">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio" name="downloadFormat" id="original" value="original" checked>
                    <label class="form-check-label" for="original">
                        Original format
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio" name="downloadFormat" id="pdf" value="pdf">
                    <label class="form-check-label" for="pdf">
                        PDF format
                    </label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="radio" name="downloadFormat" id="excel" value="excel">
                    <label class="form-check-label" for="excel">
                        Excel format (.xlsx)
                    </label>
                </div>
            </div>
            <p class="text-muted small">The file will be downloaded to your default download folder.</p>
        </div>
    `, [
        { text: 'Cancel', class: 'btn-secondary', dismiss: true },
        { text: 'Download', class: 'btn-success', action: () => {
            const selectedFormat = document.querySelector('input[name="downloadFormat"]:checked')?.value || 'original';
            performDownload(fileName, selectedFormat);
        }}
    ]);

    showModal(modal);
}

function showShareModal(fileName) {
    const modal = createModal('shareModal', 'Share File', `
        <div class="mb-4">
            <div class="d-flex align-items-center mb-3">
                <i class="bi bi-file-earmark text-primary me-3 fs-3"></i>
                <div>
                    <h5 class="mb-0">${fileName}</h5>
                    <small class="text-muted">File</small>
                </div>
            </div>
        </div>

        <!-- Share Link Section -->
        <div class="mb-4">
            <label class="form-label">Share Link</label>
            <div class="input-group">
                <input type="text" class="form-control" id="shareLink" value="https://drive.google.com/share/${fileName.replace(/\s+/g, '-').toLowerCase()}" readonly>
                <button class="btn btn-outline-secondary" type="button" onclick="copyShareLink()">
                    <i class="bi bi-clipboard"></i> Copy
                </button>
            </div>
            <div class="form-text">Anyone with this link can view the file</div>
        </div>

        <!-- Share with Specific People -->
        <form id="shareForm">
            <h6 class="mb-3">Share with specific people</h6>
            <div class="mb-3">
                <label for="shareEmail" class="form-label">Email Address</label>
                <input type="email" class="form-control" id="shareEmail" placeholder="Enter email address">
            </div>
            <div class="mb-3">
                <label for="sharePermission" class="form-label">Permission Level</label>
                <select class="form-select" id="sharePermission">
                    <option value="view">Can view</option>
                    <option value="comment">Can comment</option>
                    <option value="edit">Can edit</option>
                </select>
            </div>
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="notifyPeople" checked>
                    <label class="form-check-label" for="notifyPeople">
                        Notify people via email
                    </label>
                </div>
            </div>
            <div class="mb-3">
                <label for="shareMessage" class="form-label">Message (optional)</label>
                <textarea class="form-control" id="shareMessage" rows="3" placeholder="Add a message..."></textarea>
            </div>
        </form>
    `, [
        { text: 'Done', class: 'btn-secondary', dismiss: true },
        { text: 'Share', class: 'btn-info', action: () => {
            const email = document.getElementById('shareEmail').value;
            const permission = document.getElementById('sharePermission').value;
            const message = document.getElementById('shareMessage').value;
            const notify = document.getElementById('notifyPeople').checked;

            if (email) {
                performShare(fileName, email, permission, message, notify);
            } else {
                // If no email, just copy the link
                copyShareLink();
                return true;
            }
        }}
    ]);

    showModal(modal);
}

function performDownload(fileName, format) {
    console.log(`Downloading ${fileName} in ${format} format`);
    // Simulate download - in real app this would trigger actual download
    alert(`Downloading ${fileName} in ${format} format...`);
}

function performShare(fileName, email, permission, message, notify) {
    console.log(`Sharing ${fileName} with ${email} (${permission} permission)`);
    // Simulate sharing - in real app this would call API
    alert(`${fileName} has been shared with ${email}`);
}

function copyShareLink() {
    const shareLink = document.getElementById('shareLink');
    if (shareLink) {
        shareLink.select();
        document.execCommand('copy');
        alert('Share link copied to clipboard!');
    }
}

function deleteAttachment(fileName) {
    try {
        const modal = createModal('deleteModal', 'Delete File', `
            <div class="text-center">
                <div class="mb-4">
                    <i class="bi bi-trash display-1 text-danger"></i>
                </div>
                <h5>Delete ${fileName}?</h5>
                <p class="text-muted">This action cannot be undone. The file will be moved to trash.</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Are you sure you want to delete this file?
                </div>
            </div>
        `, [
            { text: 'Cancel', class: 'btn-secondary', dismiss: true },
            { text: 'Delete', class: 'btn-danger', action: () => {
                console.log(`Deleting file: ${fileName}`);
                alert(`${fileName} has been deleted`);
                // In a real app, we would remove the file from the list
            }}
        ]);

        showModal(modal);
    } catch (error) {
        console.error('Error in deleteAttachment:', error);
    }
}

/**
 * Generate preview content for file viewer
 */
function getPreviewContent(type, fileName) {
    switch (type) {
        case 'pdf':
            return `<div class="text-center">
                <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                <h4 class="mt-3">${fileName}</h4>
                <div class="border p-3 mt-3 text-start bg-light">
                    <h5>PDF Preview</h5>
                    <p>PDF preview would be shown here in a real application.</p>
                </div>
            </div>`;
        
        case 'document':
            return `<div class="border p-3 bg-light">
                <h4>${fileName}</h4>
                <hr>
                <p>Document content would be displayed here in a real application.</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, 
                nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
            </div>`;
            
        case 'spreadsheet':
            return `<div class="border p-3 bg-light">
                <h4>${fileName}</h4>
                <hr>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Item A</td>
                                <td>Category 1</td>
                                <td>$100.00</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>Item B</td>
                                <td>Category 2</td>
                                <td>$75.50</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>Item C</td>
                                <td>Category 1</td>
                                <td>$125.25</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>`;
            
        case 'image':
            return `<div class="text-center">
                <h4 class="mb-3">${fileName}</h4>
                <img src="https://via.placeholder.com/800x600.png?text=${fileName.replace(/\.[^/.]+$/, "")}" 
                    class="img-fluid border" alt="${fileName}">
            </div>`;
            
        default:
            return `<div class="alert alert-info">
                <h4 class="alert-heading">${fileName}</h4>
                <p>Preview not available for this file type.</p>
                <hr>
                <p class="mb-0">Please download the file to view its contents.</p>
            </div>`;
    }
}
