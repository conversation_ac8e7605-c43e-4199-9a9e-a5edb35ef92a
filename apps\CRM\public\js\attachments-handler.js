/**
 * Attachments Handler for CRM Application
 * Manages all attachment-related operations
 */

class AttachmentsHandler {
    constructor() {
        this.selectedFiles = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalItems = 0;
        this.sortBy = 'name';
        this.sortDirection = 'asc';
        this.debug = true;
    }

    /**
     * Initialize the attachments handler
     */
    init() {
        this.log("Initializing Attachments Handler");
        this.bindEvents();
        this.loadFiles();
    }

    /**
     * Bind all event handlers
     */
    bindEvents() {
        // Bind tab change events
        const tabs = document.querySelectorAll('#attachmentsTab button[data-bs-toggle="tab"]');
        tabs.forEach(tab => {
            tab.addEventListener('shown.bs.tab', event => {
                this.onTabChange(event.target.id);
            });
        });

        // Bind search
        const searchInput = document.getElementById('attachment-search');
        if (searchInput) {
            searchInput.addEventListener('input', e => {
                this.searchFiles(e.target.value);
            });
        }

        // Bind sort
        const sortDropdown = document.querySelectorAll('#sortDropdown + .dropdown-menu .dropdown-item');
        if (sortDropdown) {
            sortDropdown.forEach(item => {
                item.addEventListener('click', e => {
                    e.preventDefault();
                    const sortBy = e.target.getAttribute('data-sort');
                    this.sortFiles(sortBy);
                });
            });
        }

        // Bind upload button
        const uploadBtn = document.getElementById('upload-attachment-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', e => {
                this.uploadFiles();
            });
        }

        // Bind pagination
        const paginationLinks = document.querySelectorAll('.pagination .page-link');
        if (paginationLinks) {
            paginationLinks.forEach(link => {
                link.addEventListener('click', e => {
                    e.preventDefault();
                    const page = e.target.getAttribute('data-page');
                    if (page) {
                        this.goToPage(parseInt(page));
                    }
                });
            });
        }
    }

    /**
     * Load files into the attachments list
     */
    loadFiles() {
        this.log("Loading files");
        
        // Normally, we'd fetch files from an API here
        // For demo purposes, we'll use the existing files in the DOM
        
        // Make sure all file rows have proper event handlers
        document.querySelectorAll('#attachments-list tr').forEach(row => {
            // Format the cells with proper content if needed
            const actionCell = row.querySelector('td:last-child');
            if (actionCell && !actionCell.querySelector('.btn-group')) {
                const filename = row.getAttribute('data-filename');
                if (filename) {
                    const fileType = this.getFileType(filename);
                    actionCell.innerHTML = this.getActionButtonsHTML(filename, fileType);
                }
            }
        });

        // Update pagination
        this.updatePagination();
    }

    /**
     * Handle tab change
     * @param {string} tabId - ID of the selected tab
     */
    onTabChange(tabId) {
        this.log(`Tab changed to: ${tabId}`);
        
        switch(tabId) {
            case 'files-tab':
                this.loadFiles();
                break;
            case 'upload-tab':
                this.resetUploadForm();
                break;
            case 'shared-tab':
                this.loadSharedFiles();
                break;
            case 'recent-tab':
                this.loadRecentFiles();
                break;
        }
    }

    /**
     * Reset the upload form
     */
    resetUploadForm() {
        const uploadForm = document.getElementById('upload-attachment-form');
        if (uploadForm) {
            uploadForm.reset();
        }
    }

    /**
     * Load shared files
     */
    loadSharedFiles() {
        this.log("Loading shared files");
        // We'd fetch shared files from an API here
        // For demo, we're using the existing HTML
    }

    /**
     * Load recent files
     */
    loadRecentFiles() {
        this.log("Loading recent files");
        // We'd fetch recent files from an API here
    }

    /**
     * Search files by name
     * @param {string} query - Search query
     */
    searchFiles(query) {
        this.log(`Searching for: ${query}`);
        
        if (!query) {
            // Show all files if query is empty
            document.querySelectorAll('#attachments-list tr').forEach(row => {
                row.style.display = '';
            });
            return;
        }
        
        // Filter files based on name
        const lowerQuery = query.toLowerCase();
        document.querySelectorAll('#attachments-list tr').forEach(row => {
            const filename = row.getAttribute('data-filename');
            if (filename && filename.toLowerCase().includes(lowerQuery)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    /**
     * Sort files by column
     * @param {string} sortBy - Column to sort by
     */
    sortFiles(sortBy) {
        this.log(`Sorting by: ${sortBy}`);
        
        if (this.sortBy === sortBy) {
            // Reverse sort direction if clicking the same column
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            // New column, default to ascending
            this.sortBy = sortBy;
            this.sortDirection = 'asc';
        }
        
        const table = document.getElementById('attachments-list');
        if (!table) return;
        
        const rows = Array.from(table.querySelectorAll('tr'));
        
        rows.sort((rowA, rowB) => {
            let valA, valB;
            
            switch (sortBy) {
                case 'name':
                    valA = rowA.getAttribute('data-filename') || '';
                    valB = rowB.getAttribute('data-filename') || '';
                    break;
                case 'type':
                    valA = this.getFileType(rowA.getAttribute('data-filename') || '');
                    valB = this.getFileType(rowB.getAttribute('data-filename') || '');
                    break;
                case 'size':
                    valA = parseFloat(rowA.querySelector('td:nth-child(3)').textContent) || 0;
                    valB = parseFloat(rowB.querySelector('td:nth-child(3)').textContent) || 0;
                    break;
                case 'modified':
                    valA = rowA.querySelector('td:nth-child(4)').textContent || '';
                    valB = rowB.querySelector('td:nth-child(4)').textContent || '';
                    break;
                default:
                    valA = rowA.getAttribute('data-filename') || '';
                    valB = rowB.getAttribute('data-filename') || '';
            }
            
            // Compare based on direction
            if (this.sortDirection === 'asc') {
                return valA > valB ? 1 : -1;
            } else {
                return valA < valB ? 1 : -1;
            }
        });
        
        // Remove all rows
        rows.forEach(row => row.remove());
        
        // Add sorted rows back
        rows.forEach(row => table.appendChild(row));
    }

    /**
     * Upload files
     */
    uploadFiles() {
        this.log('Uploading files');
        
        const fileInput = document.getElementById('attachment-file');
        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
            alert('Please select a file to upload');
            return;
        }
        
        const file = fileInput.files[0];
        const nameInput = document.getElementById('attachment-name');
        const fileName = nameInput && nameInput.value ? nameInput.value : file.name;
        
        // Show progress (simulated)
        const progressBar = document.querySelector('.progress');
        const progressBarInner = progressBar.querySelector('.progress-bar');
        progressBar.style.display = 'flex';
        
        // Simulate upload progress
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 10;
            progressBarInner.style.width = `${progress}%`;
            progressBarInner.setAttribute('aria-valuenow', progress);
            
            if (progress >= 100) {
                clearInterval(progressInterval);
                
                // Hide progress bar and show success
                setTimeout(() => {
                    progressBar.style.display = 'none';
                    this.showToast(`File "${fileName}" uploaded successfully`);
                    
                    // Reset form
                    document.getElementById('upload-attachment-form').reset();
                    
                    // Switch to files tab
                    const filesTab = document.getElementById('files-tab');
                    if (filesTab) {
                        bootstrap.Tab.getOrCreateInstance(filesTab).show();
                    }
                }, 500);
            }
        }, 300);
    }

    /**
     * Get file type based on extension
     * @param {string} filename - Filename to check
     * @returns {string} File type
     */
    getFileType(filename) {
        if (!filename) return 'unknown';
        
        const extension = filename.split('.').pop().toLowerCase();
        
        switch(extension) {
            case 'pdf':
                return 'pdf';
            case 'doc':
            case 'docx':
            case 'txt':
            case 'rtf':
                return 'document';
            case 'xls':
            case 'xlsx':
            case 'csv':
                return 'spreadsheet';
            case 'ppt':
            case 'pptx':
                return 'presentation';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'svg':
                return 'image';
            case 'zip':
            case 'rar':
            case '7z':
            case 'tar':
            case 'gz':
                return 'archive';
            default:
                return 'unknown';
        }
    }

    /**
     * Get file icon class
     * @param {string} type - File type
     * @returns {string} Icon class
     */
    getFileIcon(type) {
        switch(type) {
            case 'pdf':
                return 'bi-file-earmark-pdf text-danger';
            case 'document':
                return 'bi-file-earmark-word text-primary';
            case 'spreadsheet':
                return 'bi-file-earmark-excel text-success';
            case 'presentation':
                return 'bi-file-earmark-slides text-warning';
            case 'image':
                return 'bi-file-earmark-image text-info';
            case 'archive':
                return 'bi-file-earmark-zip text-secondary';
            default:
                return 'bi-file-earmark text-secondary';
        }
    }

    /**
     * Get action buttons HTML
     * @param {string} filename - Filename
     * @param {string} fileType - File type
     * @returns {string} HTML for action buttons
     */
    getActionButtonsHTML(filename, fileType) {
        return `
            <div class="d-flex">
                <button type="button" class="btn btn-sm btn-link" onclick="viewAttachment('${fileType}', '${filename}')"><i class="bi bi-eye"></i></button>
                <button type="button" class="btn btn-sm btn-link" onclick="downloadFile('${filename}')"><i class="bi bi-download"></i></button>
                <button type="button" class="btn btn-sm btn-link" onclick="shareFile('${filename}')"><i class="bi bi-share"></i></button>
                <button type="button" class="btn btn-sm btn-link text-danger" onclick="deleteAttachment('${filename}')"><i class="bi bi-trash"></i></button>
            </div>
        `;
    }

    /**
     * Update pagination display
     */
    updatePagination() {
        // In a real app, we'd calculate pages based on total items
        // For demo, we just use the existing HTML
    }

    /**
     * Go to specific page
     * @param {number} page - Page number
     */
    goToPage(page) {
        this.currentPage = page;
        this.loadFiles();
    }

    /**
     * Show toast notification
     * @param {string} message - Message to show
     * @param {string} type - Toast type (success, error, warning, info)
     */
    showToast(message, type = 'success') {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            // Create toast container if it doesn't exist
            const newContainer = document.createElement('div');
            newContainer.id = 'toast-container';
            newContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(newContainer);
        }
        
        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type}" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        document.getElementById('toast-container').insertAdjacentHTML('beforeend', toastHTML);
        const toastEl = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastEl, { autohide: true, delay: 5000 });
        toast.show();
    }

    /**
     * Logger function
     * @param {string} message - Message to log
     */
    log(message) {
        if (this.debug) {
            console.log(`AttachmentsHandler: ${message}`);
        }
    }
}

// Create and initialize the handler
const attachmentsHandler = new AttachmentsHandler();
document.addEventListener('DOMContentLoaded', () => {
    attachmentsHandler.init();
});

// Global functions for backward compatibility
/**
 * View an attachment
 */
function viewAttachment(type, fileName) {
    console.log(`Viewing attachment: ${fileName} of type ${type}`);
    
    try {
        // Hide the attachments modal first
        const attachmentsModal = document.getElementById('attachmentsModal');
        if (attachmentsModal) {
            const modalInstance = bootstrap.Modal.getInstance(attachmentsModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
        
        // Show file viewer modal after a short delay to ensure attachments modal is hidden
        setTimeout(() => {
            // Get the file viewer modal
            const fileViewerModal = document.getElementById('fileViewerModal');
            if (!fileViewerModal) {
                console.error('File viewer modal not found in the DOM');
                alert('File viewer is not available. Please try again later.');
                return;
            }
            
            // Update modal content
            const fileTitle = fileViewerModal.querySelector('#fileViewerTitle');
            const fileContent = fileViewerModal.querySelector('#fileViewerContent');
            
            if (fileTitle && fileContent) {
                const iconClass = attachmentsHandler.getFileIcon(type);
                fileTitle.innerHTML = `<i class="bi ${iconClass}"></i> ${fileName}`;
                fileContent.innerHTML = getPreviewContent(type, fileName);
                
                // Store current file info for download/share/delete operations
                window.currentFile = {
                    name: fileName,
                    type: type
                };
                
                // Show the modal using ISA UI Manager if available
                if (typeof ISAUIManager !== 'undefined') {
                    ISAUIManager.openModal('fileViewerModal');
                } else {
                    const modal = new bootstrap.Modal(fileViewerModal);
                    modal.show();
                }
            }
        }, 300);
        
    } catch (error) {
        console.error(`Error viewing attachment: ${error.message}`);
        alert(`Could not open ${fileName}. Error: ${error.message}`);
    }
}

function downloadFile(fileName) {
    try {
        console.log(`Downloading file: ${fileName}`);

        // Show download modal with format selection
        showDownloadModal(fileName);

    } catch (error) {
        console.error('Error in downloadFile:', error);
    }
}

function shareFile(fileName) {
    try {
        console.log(`Sharing file: ${fileName}`);

        // Show share modal
        showShareModal(fileName);

    } catch (error) {
        console.error('Error in shareFile:', error);
    }
}

function showDownloadModal(fileName) {
    // Create modal HTML with high z-index
    const modalHTML = `
        <div class="modal fade" id="downloadModal" tabindex="-1" aria-labelledby="downloadModalLabel" aria-hidden="true" style="z-index: 9999;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="downloadModalLabel">
                            <i class="bi bi-download me-2"></i>Download File
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p><strong>File:</strong> ${fileName}</p>
                        <div class="mb-3">
                            <label for="downloadFormat" class="form-label">Select Format:</label>
                            <select class="form-select" id="downloadFormat">
                                <option value="original">Original Format</option>
                                <option value="pdf">PDF</option>
                                <option value="docx">Word Document (.docx)</option>
                                <option value="xlsx">Excel Spreadsheet (.xlsx)</option>
                            </select>
                        </div>
                        <div class="progress" style="display: none;" id="downloadProgress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="startDownload('${fileName}'); event.stopPropagation();">
                            <i class="bi bi-download me-2"></i>Download
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('downloadModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal with proper backdrop
    const modal = new bootstrap.Modal(document.getElementById('downloadModal'), {
        backdrop: 'static',
        keyboard: false
    });
    modal.show();

    // Ensure modal backdrop has correct z-index
    setTimeout(() => {
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.style.zIndex = '9998';
        }
    }, 100);
}

function showShareModal(fileName) {
    // Create modal HTML with high z-index
    const modalHTML = `
        <div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true" style="z-index: 9999;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="shareModalLabel">
                            <i class="bi bi-share me-2"></i>Share File
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p><strong>File:</strong> ${fileName}</p>
                        <div class="mb-3">
                            <label for="shareEmail" class="form-label">Email Address:</label>
                            <input type="email" class="form-control" id="shareEmail" placeholder="Enter email address">
                        </div>
                        <div class="mb-3">
                            <label for="sharePermission" class="form-label">Permission Level:</label>
                            <select class="form-select" id="sharePermission">
                                <option value="view">Can View</option>
                                <option value="comment">Can Comment</option>
                                <option value="edit">Can Edit</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="shareMessage" class="form-label">Message (Optional):</label>
                            <textarea class="form-control" id="shareMessage" rows="3" placeholder="Add a message..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="sendShare('${fileName}'); event.stopPropagation();">
                            <i class="bi bi-send me-2"></i>Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('shareModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal with proper backdrop
    const modal = new bootstrap.Modal(document.getElementById('shareModal'), {
        backdrop: 'static',
        keyboard: false
    });
    modal.show();

    // Ensure modal backdrop has correct z-index
    setTimeout(() => {
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.style.zIndex = '9998';
        }
    }, 100);
}

function startDownload(fileName) {
    const format = document.getElementById('downloadFormat').value;
    const progressContainer = document.getElementById('downloadProgress');
    const progressBar = progressContainer.querySelector('.progress-bar');

    // Show progress bar
    progressContainer.style.display = 'block';

    // Simulate download progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);

            // Hide modal after completion
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('downloadModal'));
                modal.hide();
            }, 500);
        }
        progressBar.style.width = progress + '%';
    }, 200);

    console.log(`Downloading ${fileName} in ${format} format`);
}

function sendShare(fileName) {
    const email = document.getElementById('shareEmail').value;
    const permission = document.getElementById('sharePermission').value;
    const message = document.getElementById('shareMessage').value;

    if (!email) {
        alert('Please enter an email address');
        return;
    }

    console.log(`Sharing ${fileName} with ${email} (${permission} permission)`);

    // Hide modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('shareModal'));
    modal.hide();

    // Show success message
    setTimeout(() => {
        alert(`${fileName} has been shared with ${email}`);
    }, 300);
}

function deleteAttachment(fileName) {
    try {
        if (confirm(`Are you sure you want to delete ${fileName}?`)) {
            console.log(`Deleting file: ${fileName}`);
            alert(`${fileName} has been deleted`);
            // In a real app, we would remove the file from the list
        }
    } catch (error) {
        console.error('Error in deleteAttachment:', error);
    }
}

function deleteAttachment(fileName) {
    console.log(`Deleting file: ${fileName}`);
    if (confirm(`Are you sure you want to delete "${fileName}"?`)) {
        attachmentsHandler.showToast(`${fileName} deleted successfully`, 'success');
        // In a real app, we would remove the file from the server and UI
    }
}

/**
 * Generate preview content for file viewer
 */
function getPreviewContent(type, fileName) {
    switch (type) {
        case 'pdf':
            return `<div class="text-center">
                <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                <h4 class="mt-3">${fileName}</h4>
                <div class="border p-3 mt-3 text-start bg-light">
                    <h5>PDF Preview</h5>
                    <p>PDF preview would be shown here in a real application.</p>
                </div>
            </div>`;
        
        case 'document':
            return `<div class="border p-3 bg-light">
                <h4>${fileName}</h4>
                <hr>
                <p>Document content would be displayed here in a real application.</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, 
                nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
            </div>`;
            
        case 'spreadsheet':
            return `<div class="border p-3 bg-light">
                <h4>${fileName}</h4>
                <hr>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Item A</td>
                                <td>Category 1</td>
                                <td>$100.00</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>Item B</td>
                                <td>Category 2</td>
                                <td>$75.50</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>Item C</td>
                                <td>Category 1</td>
                                <td>$125.25</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>`;
            
        case 'image':
            return `<div class="text-center">
                <h4 class="mb-3">${fileName}</h4>
                <img src="https://via.placeholder.com/800x600.png?text=${fileName.replace(/\.[^/.]+$/, "")}" 
                    class="img-fluid border" alt="${fileName}">
            </div>`;
            
        default:
            return `<div class="alert alert-info">
                <h4 class="alert-heading">${fileName}</h4>
                <p>Preview not available for this file type.</p>
                <hr>
                <p class="mb-0">Please download the file to view its contents.</p>
            </div>`;
    }
}
