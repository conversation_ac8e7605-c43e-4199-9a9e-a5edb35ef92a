// Business Management System - Main Entry Point

// Load environment variables
require('dotenv').config({ path: `.env.${process.env.NODE_ENV || 'development'}` });

const express = require('express');
const cors = require('cors');
const app = express();
const port = process.env.PORT || 3001;

// Get application mode from command line arguments
const appMode = process.argv[2] || 'production';
console.log(`Starting BMS in ${appMode} mode`);

// Import shared features
const sharedFeatures = require('../../SharedFeatures');
const auth = sharedFeatures.auth;
const logger = sharedFeatures.logger.createLogger('BMS');
const google = require('../../SharedFeatures/integrations/google');
const xero = sharedFeatures.xero;
const slack = sharedFeatures.slack;
const appModes = sharedFeatures.appModes;
const gamification = sharedFeatures.gamification;
const helpSystem = sharedFeatures.helpSystem;
const aiHelpSystem = sharedFeatures.aiHelpSystem;
const internetResources = sharedFeatures.internetResources;

// Initialize application mode
const modeConfig = appModes.initializeMode(appMode, app);

// Import performance utilities
const performance = require('./utils/performance');

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));
app.use('/shared', express.static('../../SharedFeatures/ui-components'));
app.use(appModes.createModeMiddleware(appMode));

// Configure performance optimizations
performance.configurePerformance(app);
performance.configureClientPerformance(app);
app.use(performance.memoryLeakDetection());

// Initialize integrations
google.initGoogleAPI().catch((err) => {
  logger && logger.error ? logger.error('Failed to initialize Google API', { error: err.message }) : console.error('Failed to initialize Google API', err);
});

// Define routes
app.get('/', (req, res) => {
  // Check if a mode parameter was provided in the URL
  const requestedMode = req.query.mode;

  if (
    requestedMode &&
    ['production', 'sandbox', 'demo'].includes(requestedMode) &&
    requestedMode !== appMode
  ) {
    // Redirect to the start script with the requested mode
    console.log(`Switching to ${requestedMode} mode`);
    res.redirect(`/mode-switch.html?mode=${requestedMode}`);
    return;
  }

  res.sendFile('public/index.html', { root: __dirname });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API endpoint for dashboard data
app.get('/api/dashboard', (req, res) => {
  // Simulate dashboard data
  const dashboardData = {
    kpis: {
      revenue: {
        value: 125000,
        trend: 12,
        trendDirection: 'up'
      },
      expenses: {
        value: 78000,
        trend: -5,
        trendDirection: 'down'
      },
      profit: {
        value: 47000,
        trend: 18,
        trendDirection: 'up'
      },
      employees: {
        value: 42,
        trend: 2,
        trendDirection: 'up'
      }
    },
    financialTrends: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      revenue: [95000, 110000, 105000, 115000, 120000, 125000],
      expenses: [70000, 75000, 72000, 80000, 82000, 78000]
    },
    budgetAllocation: {
      labels: ['Operations', 'Marketing', 'R&D', 'HR', 'IT', 'Admin'],
      data: [35, 20, 15, 10, 12, 8]
    },
    recentTransactions: [
      {
        id: 1001,
        date: '2025-04-28',
        description: 'Client Payment - ABC Corp',
        category: 'Income',
        amount: 12500.00,
        status: 'Completed'
      },
      {
        id: 1002,
        date: '2025-04-27',
        description: 'Office Supplies',
        category: 'Expense',
        amount: -850.75,
        status: 'Completed'
      },
      {
        id: 1003,
        date: '2025-04-26',
        description: 'Monthly Rent',
        category: 'Expense',
        amount: -3500.00,
        status: 'Completed'
      },
      {
        id: 1004,
        date: '2025-04-25',
        description: 'Client Payment - XYZ Ltd',
        category: 'Income',
        amount: 8750.00,
        status: 'Completed'
      },
      {
        id: 1005,
        date: '2025-04-25',
        description: 'Employee Salaries',
        category: 'Expense',
        amount: -32450.00,
        status: 'Pending'
      }
    ],
    notifications: [
      {
        id: 1,
        title: 'Invoice Payment Overdue',
        message: 'Invoice #INV-2025-042 for client DEF Industries is 15 days overdue.',
        timestamp: '2025-04-28 09:15:22',
        priority: 'high'
      },
      {
        id: 2,
        title: 'Expense Approval Required',
        message: 'New expense report submitted by John Smith requires your approval.',
        timestamp: '2025-04-27 14:30:45',
        priority: 'medium'
      },
      {
        id: 3,
        title: 'New Client Added',
        message: 'GHI Corporation has been added as a new client by Sarah Johnson.',
        timestamp: '2025-04-26 11:20:33',
        priority: 'low'
      }
    ],
    upcomingEvents: [
      {
        id: 1,
        title: 'Board Meeting',
        date: '2025-04-30',
        time: '10:00 AM',
        description: 'Quarterly board meeting to discuss financial performance.'
      },
      {
        id: 2,
        title: 'Client Presentation',
        date: '2025-05-02',
        time: '2:00 PM',
        description: 'Presentation of new product features to ABC Corp.'
      },
      {
        id: 3,
        title: 'Tax Filing Deadline',
        date: '2025-05-15',
        time: '',
        description: 'Deadline for quarterly tax filing.'
      }
    ]
  };

  res.json({ status: 'success', data: dashboardData });
});

// API endpoint for application mode
app.get('/api/app-mode', (req, res) => {
  res.json({ status: 'success', data: { mode: appMode } });
});

// API endpoint for business data (used by Integration Hub)
app.get('/api/business-data', (req, res) => {
  // Simulate business data
  const businessData = {
    financials: {
      revenue: 125000,
      expenses: 78000,
      profit: 47000,
      cashFlow: 35000
    },
    departments: [
      { name: 'Operations', budget: 35000, spent: 28000 },
      { name: 'Marketing', budget: 20000, spent: 15000 },
      { name: 'R&D', budget: 15000, spent: 12000 },
      { name: 'HR', budget: 10000, spent: 8000 },
      { name: 'IT', budget: 12000, spent: 10000 },
      { name: 'Admin', budget: 8000, spent: 5000 }
    ],
    employees: {
      total: 42,
      departments: {
        Operations: 15,
        Marketing: 8,
        'R&D': 6,
        HR: 4,
        IT: 5,
        Admin: 4
      }
    }
  };

  res.json({ status: 'success', data: businessData });
});



// Get current application mode
app.get('/api/app-mode', (req, res) => {
  res.json({
    status: 'success',
    data: {
      mode: appMode,
      config: modeConfig,
    },
  });
});

app.get('/api/dashboard', (req, res) => {
  res.json({
    status: 'success',
    data: {
      metrics: {
        revenue: 125000,
        expenses: 78000,
        profit: 47000,
      },
      recentTransactions: [
        { id: 1, date: '2025-04-25', amount: 1200, type: 'income' },
        { id: 2, date: '2025-04-24', amount: 800, type: 'expense' },
      ],
    },
  });
});

// Google Calendar Integration
app.get('/api/calendar/events', auth.authenticate, async (req, res) => {
  try {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 30);

    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger.error('Failed to fetch calendar events', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration
app.get('/api/drive/files', auth.authenticate, async (req, res) => {
  try {
    const files = await google.Drive.listFiles();
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger.error('Failed to fetch drive files', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Sample Google Sheets endpoint
app.get('/api/google-sheets/:spreadsheetId', async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Sheets data', { error: error.message }) : console.error('Failed to fetch Google Sheets data', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Xero Integration
app.get('/api/accounting/invoices', auth.authenticate, async (req, res) => {
  try {
    const invoices = await xero.Invoice.list();
    res.json({ status: 'success', data: invoices });
  } catch (error) {
    logger.error('Failed to fetch invoices', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Slack Integration
app.post('/api/notifications/send', auth.authenticate, async (req, res) => {
  try {
    const { channel, message } = req.body;
    const result = await slack.sendMessage(channel, message);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger.error('Failed to send notification', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Help System Routes

// Get tutorials
app.get('/api/help/tutorials', (req, res) => {
  try {
    const tutorials = helpSystem.getTutorials('bms');
    res.json({ status: 'success', data: tutorials });
  } catch (error) {
    logger.error('Failed to fetch tutorials', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Get specific tutorial
app.get('/api/help/tutorials/:id', (req, res) => {
  try {
    const tutorial = helpSystem.getTutorialById(req.params.id);

    if (!tutorial) {
      return res.status(404).json({ status: 'error', message: 'Tutorial not found' });
    }

    res.json({ status: 'success', data: tutorial });
  } catch (error) {
    logger.error('Failed to fetch tutorial', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Get user manual
app.get('/api/help/manual', (req, res) => {
  try {
    const manual = helpSystem.getUserManual('bms');
    res.json({ status: 'success', data: manual });
  } catch (error) {
    logger.error('Failed to fetch user manual', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Get contextual help
app.get('/api/help/contextual/:context', auth.authenticate, async (req, res) => {
  try {
    const { context } = req.params;
    const userData = req.user; // Assuming user data is attached to request by auth middleware

    const contextualHelp = await aiHelpSystem.generateContextualHelp('bms', context, userData);

    if (!contextualHelp) {
      return res.status(404).json({ status: 'error', message: 'Contextual help not found' });
    }

    res.json({ status: 'success', data: contextualHelp });
  } catch (error) {
    logger.error('Failed to fetch contextual help', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Get diagnosis tools
app.get('/api/help/diagnosis', (req, res) => {
  try {
    const tools = helpSystem.getDiagnosisTools('bms');
    res.json({ status: 'success', data: tools });
  } catch (error) {
    logger.error('Failed to fetch diagnosis tools', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Get specific diagnosis tool
app.get('/api/help/diagnosis/:id', (req, res) => {
  try {
    const tool = helpSystem.getDiagnosisToolById(req.params.id);

    if (!tool) {
      return res.status(404).json({ status: 'error', message: 'Diagnosis tool not found' });
    }

    res.json({ status: 'success', data: tool });
  } catch (error) {
    logger.error('Failed to fetch diagnosis tool', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// AI Help System Routes

// Process natural language query
app.post('/api/help/ai/query', async (req, res) => {
  try {
    const { query, appCode = 'bms' } = req.body;

    if (!query) {
      return res.status(400).json({ status: 'error', message: 'Query is required' });
    }

    // In a real implementation, this would use the aiHelpSystem to process the query
    // For demo purposes, we'll return mock responses

    // Simulate processing delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    let response;

    if (query.toLowerCase().includes('invoice')) {
      response = {
        query,
        response:
          'To create a new invoice, navigate to the Financial Management module and click on "Invoices" in the sidebar. Then click the "New Invoice" button in the top-right corner. Fill in the customer details, add line items, set the payment terms, and click "Save" to create the invoice.',
        externalResources: [
          {
            title: 'Invoice Management Best Practices',
            url: 'https://www.xero.com/blog/invoice-management-best-practices/',
            source: 'Xero Blog',
            type: 'link',
          },
          {
            title: 'How to Create Professional Invoices',
            url: 'https://www.youtube.com/watch?v=example1',
            source: 'YouTube',
            type: 'video',
          },
        ],
        source: 'internal-ai',
      };
    } else if (query.toLowerCase().includes('report')) {
      response = {
        query,
        response:
          'To generate reports, navigate to the Reports section in the relevant module. Select the type of report you want to generate, set the date range and any other filters, and click "Generate Report". You can export reports in various formats including PDF, Excel, and CSV.',
        externalResources: [
          {
            title: 'Financial Reporting Best Practices',
            url: 'https://www.accountingweb.com/practice/practice-excellence/best-practices-for-financial-reporting',
            source: 'AccountingWeb',
            type: 'link',
          },
        ],
        source: 'internal-ai',
      };
    } else {
      // Try to use the aiHelpSystem if available
      try {
        response = await aiHelpSystem.processQuery(query, appCode);
      } catch (error) {
        // Fallback response
        response = {
          query,
          response:
            "I'm not sure I understand your question. Could you please provide more details or rephrase your question?",
          externalResources: [],
          source: 'fallback',
        };
      }
    }

    res.json({ status: 'success', data: response });
  } catch (error) {
    logger.error('Failed to process query', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Search internet resources
app.post('/api/help/internet/search', async (req, res) => {
  try {
    const { query, providers, type } = req.body;

    if (!query) {
      return res.status(400).json({ status: 'error', message: 'Query is required' });
    }

    let results;

    if (providers && Array.isArray(providers)) {
      // Search specific providers
      results = await internetResources.searchSpecificProviders(query, providers);
    } else if (type) {
      // Search by type
      results = await internetResources.searchByType(query, type);
    } else {
      // Search all providers
      results = await internetResources.searchAllProviders(query);
    }

    res.json({ status: 'success', data: results });
  } catch (error) {
    logger.error('Failed to search internet resources', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Get available internet resource providers
app.get('/api/help/internet/providers', (req, res) => {
  try {
    const providers = internetResources.getProviders();
    res.json({ status: 'success', data: providers });
  } catch (error) {
    logger.error('Failed to fetch internet resource providers', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Gamification Routes (for sandbox/training mode)

// Record tutorial completion
app.post('/api/gamification/tutorial/complete', auth.authenticate, (req, res) => {
  try {
    // Only available in sandbox mode
    if (appMode !== appModes.APP_MODES.SANDBOX) {
      return res.status(403).json({
        status: 'error',
        message: 'Gamification features are only available in sandbox/training mode',
      });
    }

    const { tutorialId } = req.body;
    const userId = req.user.id;

    if (!tutorialId) {
      return res.status(400).json({ status: 'error', message: 'Tutorial ID is required' });
    }

    const result = gamification.recordTutorialCompletion(userId, tutorialId);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger.error('Failed to record tutorial completion', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Record transaction
app.post('/api/gamification/transaction/record', auth.authenticate, (req, res) => {
  try {
    // Only available in sandbox mode
    if (appMode !== appModes.APP_MODES.SANDBOX) {
      return res.status(403).json({
        status: 'error',
        message: 'Gamification features are only available in sandbox/training mode',
      });
    }

    const userId = req.user.id;
    const result = gamification.recordTransaction(userId);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger.error('Failed to record transaction', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Get user progress
app.get('/api/gamification/progress', auth.authenticate, (req, res) => {
  try {
    // Only available in sandbox mode
    if (appMode !== appModes.APP_MODES.SANDBOX) {
      return res.status(403).json({
        status: 'error',
        message: 'Gamification features are only available in sandbox/training mode',
      });
    }

    const userId = req.user.id;
    const progress = gamification.getUserProgress(userId);
    res.json({ status: 'success', data: progress });
  } catch (error) {
    logger.error('Failed to get user progress', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Get leaderboard
app.get('/api/gamification/leaderboard', auth.authenticate, (req, res) => {
  try {
    // Only available in sandbox mode
    if (appMode !== appModes.APP_MODES.SANDBOX) {
      return res.status(403).json({
        status: 'error',
        message: 'Gamification features are only available in sandbox/training mode',
      });
    }

    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const leaderboard = gamification.getLeaderboard(limit);
    res.json({ status: 'success', data: leaderboard });
  } catch (error) {
    logger.error('Failed to get leaderboard', { error: error.message });
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Restart the server in a different mode
app.post('/api/restart', (req, res) => {
  const requestedMode = req.query.mode;

  if (!requestedMode || !['production', 'sandbox', 'demo'].includes(requestedMode)) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid mode specified',
    });
  }

  // In a real implementation, this would execute the start.bat script with the mode parameter
  // For this demo, we'll just return success
  res.json({
    status: 'success',
    message: `Server restarting in ${requestedMode} mode`,
  });

  // Simulate restarting the server by changing the app mode
  // In a real implementation, this would actually restart the server
  console.log(`Restarting server in ${requestedMode} mode`);

  // In a real implementation, we would use child_process.exec to run the start.bat script
  // For example:
  // const { exec } = require('child_process');
  // exec(`start.bat ${requestedMode}`, (error, stdout, stderr) => {
  //   if (error) {
  //     console.error(`Error restarting server: ${error}`);
  //     return;
  //   }
  //   console.log(`Server restarted in ${requestedMode} mode`);
  // });
});

// Google Drive Integration
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Drive files', { error: error.message }) : console.error('Failed to fetch Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration
app.get('/api/google-calendar/events', async (req, res) => {
  try {
    const startDate = new Date(req.query.startDate || Date.now());
    const endDate = new Date(req.query.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Calendar events', { error: error.message }) : console.error('Failed to fetch Google Calendar events', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/google-gmail/send', async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to send Gmail email', { error: error.message }) : console.error('Failed to send Gmail email', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration
app.post('/api/google-docs/create', async (req, res) => {
  try {
    const { title } = req.body;
    const doc = await google.Docs.createDocument(title);
    res.json({ status: 'success', data: doc });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to create Google Doc', { error: error.message }) : console.error('Failed to create Google Doc', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google-contacts', async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Contacts', { error: error.message }) : console.error('Failed to fetch Google Contacts', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive: List uploaded files by current user
app.get('/api/google-drive/uploads', auth.authenticate, async (req, res) => {
  try {
    // You may need to adjust this depending on your google.Drive.listFiles implementation
    const files = await google.Drive.listFiles({ ownedByMe: true });
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch uploaded Google Drive files', { error: error.message }) : console.error('Failed to fetch uploaded Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive: List files shared with current user
app.get('/api/google-drive/shared', auth.authenticate, async (req, res) => {
  try {
    // You may need to adjust this depending on your google.Drive.listFiles implementation
    const files = await google.Drive.listFiles({ sharedWithMe: true });
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch shared Google Drive files', { error: error.message }) : console.error('Failed to fetch shared Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Start server
app.listen(port, () => {
  console.log(`BMS running at http://localhost:${port} in ${appMode} mode`);
  console.log('Connected to IntegrationHub');
  console.log('Server is ready to accept connections');
});
