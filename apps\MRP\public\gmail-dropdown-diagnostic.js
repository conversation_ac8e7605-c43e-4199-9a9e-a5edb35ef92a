// GMAIL DROPDOWN DIAGNOSTIC - Copy/Paste into Browser Console
// Run this at http://localhost:3002 after opening Gmail modal

console.clear();
console.log('🔍 GMAIL DROPDOWN DIAGNOSTIC STARTING...');
console.log('================================================');

function runGmailDropdownDiagnostic() {
    // Step 1: Check if Gmail modal exists and is open
    console.log('\n1️⃣ CHECKING GMAIL MODAL STATUS');
    const modal = document.getElementById('mrp-gmailModal');
    console.log('Gmail modal found:', !!modal);
    
    if (!modal) {
        console.log('❌ Gmail modal not found. Please ensure the page is loaded.');
        return;
    }
    
    const isModalOpen = modal.classList.contains('show');
    console.log('Gmail modal is open:', isModalOpen);
    
    if (!isModalOpen) {
        console.log('⚠️ Gmail modal is not open. Opening it now...');
        try {
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
            console.log('✅ Modal opened. Please run this script again in 2 seconds.');
            return;
        } catch (error) {
            console.log('❌ Failed to open modal:', error);
            return;
        }
    }
    
    // Step 2: Check dropdown elements
    console.log('\n2️⃣ CHECKING DROPDOWN ELEMENTS');
    const sortBtn = document.getElementById('sort-dropdown');
    const filterBtn = document.getElementById('filter-dropdown');
    
    console.log('Sort button found:', !!sortBtn);
    console.log('Filter button found:', !!filterBtn);
    
    if (sortBtn) {
        console.log('Sort button attributes:');
        console.log('  - data-bs-toggle:', sortBtn.getAttribute('data-bs-toggle'));
        console.log('  - aria-expanded:', sortBtn.getAttribute('aria-expanded'));
        console.log('  - class:', sortBtn.className);
        
        const sortMenu = sortBtn.nextElementSibling;
        console.log('Sort menu found:', !!sortMenu);
        if (sortMenu) {
            console.log('Sort menu classes:', sortMenu.className);
        }
    }
    
    if (filterBtn) {
        console.log('Filter button attributes:');
        console.log('  - data-bs-toggle:', filterBtn.getAttribute('data-bs-toggle'));
        console.log('  - aria-expanded:', filterBtn.getAttribute('aria-expanded'));
        console.log('  - class:', filterBtn.className);
        
        const filterMenu = filterBtn.nextElementSibling;
        console.log('Filter menu found:', !!filterMenu);
        if (filterMenu) {
            console.log('Filter menu classes:', filterMenu.className);
        }
    }
    
    // Step 3: Check Bootstrap availability
    console.log('\n3️⃣ CHECKING BOOTSTRAP AVAILABILITY');
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap Dropdown available:', typeof bootstrap.Dropdown !== 'undefined');
        
        // Try to get existing dropdown instances
        if (sortBtn) {
            const sortInstance = bootstrap.Dropdown.getInstance(sortBtn);
            console.log('Sort dropdown instance exists:', !!sortInstance);
        }
        if (filterBtn) {
            const filterInstance = bootstrap.Dropdown.getInstance(filterBtn);
            console.log('Filter dropdown instance exists:', !!filterInstance);
        }
    }
    
    // Step 4: Test manual dropdown creation
    console.log('\n4️⃣ TESTING DROPDOWN CREATION');
    if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
        try {
            if (sortBtn) {
                // Dispose existing instance if any
                const existingSortInstance = bootstrap.Dropdown.getInstance(sortBtn);
                if (existingSortInstance) {
                    existingSortInstance.dispose();
                    console.log('✅ Disposed existing sort dropdown');
                }
                
                // Create new instance
                const newSortInstance = new bootstrap.Dropdown(sortBtn);
                console.log('✅ Created new sort dropdown instance');
            }
            
            if (filterBtn) {
                // Dispose existing instance if any
                const existingFilterInstance = bootstrap.Dropdown.getInstance(filterBtn);
                if (existingFilterInstance) {
                    existingFilterInstance.dispose();
                    console.log('✅ Disposed existing filter dropdown');
                }
                
                // Create new instance
                const newFilterInstance = new bootstrap.Dropdown(filterBtn);
                console.log('✅ Created new filter dropdown instance');
            }
        } catch (error) {
            console.log('❌ Error creating dropdown instances:', error);
        }
    }
    
    // Step 5: Test clicking
    console.log('\n5️⃣ TESTING DROPDOWN CLICKS');
    
    window.testSortDropdown = function() {
        console.log('Testing sort dropdown click...');
        if (sortBtn) {
            sortBtn.click();
            setTimeout(() => {
                const sortMenu = sortBtn.nextElementSibling;
                const isOpen = sortMenu && sortMenu.classList.contains('show');
                console.log('Sort dropdown is now:', isOpen ? 'OPEN ✅' : 'CLOSED ❌');
            }, 100);
        }
    };
    
    window.testFilterDropdown = function() {
        console.log('Testing filter dropdown click...');
        if (filterBtn) {
            filterBtn.click();
            setTimeout(() => {
                const filterMenu = filterBtn.nextElementSibling;
                const isOpen = filterMenu && filterMenu.classList.contains('show');
                console.log('Filter dropdown is now:', isOpen ? 'OPEN ✅' : 'CLOSED ❌');
            }, 100);
        }
    };
    
    // Step 6: Check for conflicts
    console.log('\n6️⃣ CHECKING FOR CONFLICTS');
    const scriptsWithGmail = Array.from(document.scripts).filter(script => 
        script.src.includes('gmail') || script.textContent.includes('gmail')
    );
    console.log('Scripts with Gmail functionality:', scriptsWithGmail.length);
    
    // Check for multiple dropdown implementations
    const gmailDropdownScripts = Array.from(document.scripts).filter(script => 
        script.src.includes('dropdown') || script.src.includes('gmail')
    );
    console.log('Gmail/dropdown related scripts:', gmailDropdownScripts.map(s => s.src.split('/').pop()));
    
    console.log('\n🎯 QUICK TESTS AVAILABLE:');
    console.log('Type: testSortDropdown() - to test sort dropdown');
    console.log('Type: testFilterDropdown() - to test filter dropdown');
    
    console.log('\n📋 DIAGNOSIS COMPLETE!');
}

// Auto-run the diagnostic
runGmailDropdownDiagnostic();
