/**
 * Quick Error Check and Gmail Modal Test
 * Paste this into the browser console after page loads
 */

(function() {
    'use strict';
    
    console.clear();
    console.log('🔍 Quick Error Check and Gmail Modal Test');
    console.log('========================================');
    
    // Test 1: Check if critical resources loaded
    function checkResourceLoading() {
        console.log('📦 Checking Resource Loading...');
        
        if (typeof ISADataUtils !== 'undefined') {
            console.log('✅ ISADataUtils loaded successfully');
        } else {
            console.log('❌ ISADataUtils NOT loaded');
        }
        
        if (typeof bootstrap !== 'undefined') {
            console.log(`✅ Bootstrap ${bootstrap.Tooltip?.VERSION || 'unknown'} loaded`);
        } else {
            console.log('❌ Bootstrap NOT loaded');
        }
        
        if (typeof window.MRPGmailDropdowns !== 'undefined') {
            console.log('✅ MRP Gmail Dropdowns script loaded');
        } else {
            console.log('❌ MRP Gmail Dropdowns script NOT loaded');
        }
        
        if (typeof window.MRPGmailUtils !== 'undefined') {
            console.log('✅ MRP Gmail Utils script loaded');
        } else {
            console.log('❌ MRP Gmail Utils script NOT loaded');
        }
    }
    
    // Test 2: Check if Gmail modal elements exist
    function checkGmailModalElements() {
        console.log('\n🎯 Checking Gmail Modal Elements...');
        
        const modal = document.getElementById('mrp-gmailModal');
        if (modal) {
            console.log('✅ Gmail modal found');
            
            const sortDropdown = document.getElementById('sort-dropdown');
            const filterDropdown = document.getElementById('filter-dropdown');
            
            if (sortDropdown) {
                console.log('✅ Sort dropdown button found');
            } else {
                console.log('❌ Sort dropdown button NOT found');
            }
            
            if (filterDropdown) {
                console.log('✅ Filter dropdown button found');
            } else {
                console.log('❌ Filter dropdown button NOT found');
            }
        } else {
            console.log('❌ Gmail modal NOT found');
        }
    }
    
    // Test 3: Try to open Gmail modal
    function testGmailModalOpening() {
        console.log('\n🚀 Testing Gmail Modal Opening...');
        
        const gmailButton = document.querySelector('[data-bs-target="#mrp-gmailModal"]') ||
                           document.querySelector('#mrp-open-gmail-btn') ||
                           document.querySelector('#compose-email');
        
        if (gmailButton) {
            console.log('✅ Gmail button found, attempting to open modal...');
            gmailButton.click();
            
            setTimeout(() => {
                const modal = document.getElementById('mrp-gmailModal');
                const isVisible = modal && window.getComputedStyle(modal).display !== 'none';
                
                if (isVisible) {
                    console.log('✅ Gmail modal opened successfully!');
                    console.log('🎯 Now testing dropdowns...');
                    testDropdownFunctionality();
                } else {
                    console.log('❌ Gmail modal failed to open');
                }
            }, 1000);
        } else {
            console.log('❌ Gmail button not found');
        }
    }
    
    // Test 4: Test dropdown functionality
    function testDropdownFunctionality() {
        const sortDropdown = document.getElementById('sort-dropdown');
        const filterDropdown = document.getElementById('filter-dropdown');
        
        if (sortDropdown) {
            console.log('🔧 Testing sort dropdown...');
            sortDropdown.click();
            
            setTimeout(() => {
                const sortMenu = sortDropdown.nextElementSibling;
                const isOpen = sortMenu && sortMenu.classList.contains('show');
                
                if (isOpen) {
                    console.log('✅ Sort dropdown opens correctly!');
                    // Close it
                    sortDropdown.click();
                } else {
                    console.log('❌ Sort dropdown does not open');
                }
            }, 300);
        }
        
        if (filterDropdown) {
            setTimeout(() => {
                console.log('🔧 Testing filter dropdown...');
                filterDropdown.click();
                
                setTimeout(() => {
                    const filterMenu = filterDropdown.nextElementSibling;
                    const isOpen = filterMenu && filterMenu.classList.contains('show');
                    
                    if (isOpen) {
                        console.log('✅ Filter dropdown opens correctly!');
                        // Close it
                        filterDropdown.click();
                        
                        setTimeout(() => {
                            console.log('\n🎉 Test Complete! Both dropdowns working correctly!');
                        }, 300);
                    } else {
                        console.log('❌ Filter dropdown does not open');
                    }
                }, 300);
            }, 600);
        }
    }
    
    // Run all tests
    checkResourceLoading();
    checkGmailModalElements();
    testGmailModalOpening();
    
    // Export functions for manual use
    window.checkResourceLoading = checkResourceLoading;
    window.testGmailModalOpening = testGmailModalOpening;
    window.testDropdownFunctionality = testDropdownFunctionality;
    
    console.log('\n📋 Manual Commands Available:');
    console.log('- checkResourceLoading() - Check if all scripts loaded');
    console.log('- testGmailModalOpening() - Open Gmail modal and test');
    console.log('- testDropdownFunctionality() - Test dropdowns directly');
    
})();
