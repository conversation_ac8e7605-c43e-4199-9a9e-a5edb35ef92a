/**
 * Enhanced Gmail Implementation for MRP Application
 */

// Define the MRP application colors
const MRP_COLORS = {
    primary: '#fd7e14',    // Orange
    secondary: '#6c757d',  // Gray
    success: '#28a745',    // Green
    danger: '#dc3545',     // Red
    warning: '#ffc107',    // Yellow
    info: '#17a2b8',       // Teal
    light: '#f8f9fa',      // Light gray
    dark: '#343a40'        // Dark gray
};

// Initialize the enhanced Gmail functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Enhanced Gmail for MRP');

    // Load the shared Gmail scripts
    loadScript('/shared/enhanced-gmail.js', function() {
        loadScript('/shared/enhanced-gmail-functions.js', function() {
            loadScript('/shared/enhanced-gmail-utils.js', function() {
                // Initialize the enhanced Gmail with MRP colors
                initEnhancedGmail('MRP', MRP_COLORS);

                // Make the openEmail function globally available
                window.openEmail = openEmail;

                // Make the openEnhancedGmailModal function globally available
                window.openEnhancedGmailModal = openEnhancedGmailModal;

                // Add direct click handler to the Open Gmail button
                const openGmailBtn = document.getElementById('mrp-open-gmail-btn');
                if (openGmailBtn) {
                    console.log('Found Open Gmail button by ID, adding direct event listener');

                    openGmailBtn.addEventListener('click', function() {
                        console.log('Open Gmail button clicked directly');
                        openEnhancedGmailModal();
                    });
                } else {
                    console.error('Could not find Open Gmail button by ID');
                }
            });
        });
    });
});

/**
 * Load a script dynamically
 */
function loadScript(url, callback) {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.onload = callback;
    document.head.appendChild(script);
}
