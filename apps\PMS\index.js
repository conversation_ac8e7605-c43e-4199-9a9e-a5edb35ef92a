// Project Management System - Main Entry Point

const express = require('express');
const cors = require('cors');
const app = express();
const port = 3007;

// Import shared features
const sharedFeatures = require('../../SharedFeatures');
const auth = sharedFeatures.auth;
const logger = sharedFeatures.logger.createLogger('PMS');
const google = require('../../SharedFeatures/integrations/google');

// Configure middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve shared resources
app.use('/shared', express.static('../../shared'));
app.use('/SharedFeatures', express.static('../../SharedFeatures'));

// Initialize Google API
google.initGoogleAPI().catch((err) => {
  logger && logger.error ? logger.error('Failed to initialize Google API', { error: err.message }) : console.error('Failed to initialize Google API', err);
});

// Define routes
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: __dirname + '/public' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API endpoints
app.get('/api/projects', (req, res) => {
  res.json({
    status: 'success',
    data: {
      projects: [
        {
          id: 1,
          name: 'Website Redesign',
          status: 'in-progress',
          startDate: '2025-03-01',
          endDate: '2025-06-30',
          progress: 45,
          manager: 'John Smith',
          budget: 25000,
          spent: 12000
        },
        {
          id: 2,
          name: 'Mobile App Development',
          status: 'planning',
          startDate: '2025-05-15',
          endDate: '2025-09-30',
          progress: 10,
          manager: 'Sarah Johnson',
          budget: 50000,
          spent: 5000
        },
        {
          id: 3,
          name: 'ERP Implementation',
          status: 'completed',
          startDate: '2025-01-01',
          endDate: '2025-04-15',
          progress: 100,
          manager: 'Mike Brown',
          budget: 100000,
          spent: 95000
        }
      ]
    }
  });
});

app.get('/api/tasks', (req, res) => {
  res.json({
    status: 'success',
    data: {
      tasks: [
        {
          id: 1,
          projectId: 1,
          name: 'Design Homepage',
          status: 'completed',
          assignee: 'Jane Doe',
          dueDate: '2025-04-15',
          priority: 'high'
        },
        {
          id: 2,
          projectId: 1,
          name: 'Implement Frontend',
          status: 'in-progress',
          assignee: 'Bob Johnson',
          dueDate: '2025-05-30',
          priority: 'medium'
        },
        {
          id: 3,
          projectId: 1,
          name: 'Backend Integration',
          status: 'not-started',
          assignee: 'Alice Smith',
          dueDate: '2025-06-15',
          priority: 'high'
        },
        {
          id: 4,
          projectId: 2,
          name: 'Requirements Gathering',
          status: 'in-progress',
          assignee: 'John Smith',
          dueDate: '2025-05-30',
          priority: 'high'
        },
        {
          id: 5,
          projectId: 2,
          name: 'UI/UX Design',
          status: 'not-started',
          assignee: 'Jane Doe',
          dueDate: '2025-06-30',
          priority: 'medium'
        }
      ]
    }
  });
});

// Sample Google Sheets endpoint
app.get('/api/google-sheets/:spreadsheetId', async (req, res) => {
  try {
    const { spreadsheetId } = req.params;
    const range = req.query.range || 'Sheet1!A1:Z100';
    const data = await google.Sheets.getValues(spreadsheetId, range);
    res.json({ status: 'success', data });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Sheets data', { error: error.message }) : console.error('Failed to fetch Google Sheets data', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Drive Integration
app.get('/api/google-drive/files', async (req, res) => {
  try {
    const folderId = req.query.folderId || null;
    const files = await google.Drive.listFiles(folderId);
    res.json({ status: 'success', data: files });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Drive files', { error: error.message }) : console.error('Failed to fetch Google Drive files', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Calendar Integration
app.get('/api/google-calendar/events', async (req, res) => {
  try {
    const startDate = new Date(req.query.startDate || Date.now());
    const endDate = new Date(req.query.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const events = await google.Calendar.listEvents(startDate, endDate);
    res.json({ status: 'success', data: events });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Calendar events', { error: error.message }) : console.error('Failed to fetch Google Calendar events', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Gmail Integration
app.post('/api/google-gmail/send', async (req, res) => {
  try {
    const { to, subject, body } = req.body;
    const email = { to, subject, body };
    const result = await google.Gmail.sendEmail(email);
    res.json({ status: 'success', data: result });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to send Gmail email', { error: error.message }) : console.error('Failed to send Gmail email', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Docs Integration
app.post('/api/google-docs/create', async (req, res) => {
  try {
    const { title } = req.body;
    const doc = await google.Docs.createDocument(title);
    res.json({ status: 'success', data: doc });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to create Google Doc', { error: error.message }) : console.error('Failed to create Google Doc', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Google Contacts Integration
app.get('/api/google-contacts', async (req, res) => {
  try {
    const contacts = await google.Contacts.listContacts();
    res.json({ status: 'success', data: contacts });
  } catch (error) {
    logger && logger.error ? logger.error('Failed to fetch Google Contacts', { error: error.message }) : console.error('Failed to fetch Google Contacts', error);
    res.status(500).json({ status: 'error', message: error.message });
  }
});

// Start server
app.listen(port, () => {
  console.log(`PMS running at http://localhost:${port}`);
  console.log('Connected to IntegrationHub');
});
