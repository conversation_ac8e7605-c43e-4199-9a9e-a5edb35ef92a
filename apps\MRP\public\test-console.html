<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Dropdown Live Test Console</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1e1e1e;
            color: #d4d4d4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #569cd6;
            border-bottom: 2px solid #569cd6;
            padding-bottom: 10px;
        }
        .test-section {
            background-color: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            background-color: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #1177bb;
        }
        .test-button.danger {
            background-color: #d73a49;
        }
        .test-button.danger:hover {
            background-color: #e94c5c;
        }
        .test-button.success {
            background-color: #28a745;
        }
        .test-button.success:hover {
            background-color: #34ce57;
        }
        #console {
            background-color: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 1px solid #3e3e42;
        }
        .instruction {
            background-color: #264f78;
            border-left: 4px solid #569cd6;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #735c0f;
            border-left: 4px solid #ffd700;
            padding: 15px;
            margin: 15px 0;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: #569cd6;
        }
        .input-group input {
            background-color: #3c3c3c;
            border: 1px solid #555;
            color: #d4d4d4;
            padding: 8px;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gmail Dropdown Live Test Console</h1>
        
        <div class="instruction">
            <h3>Instructions:</h3>
            <p>1. Make sure the MRP application is running at <strong>http://localhost:3002</strong></p>
            <p>2. Open the main application in another tab</p>
            <p>3. Use the buttons below to test various aspects of the Gmail modal</p>
            <p>4. Watch the console output below for detailed results</p>
        </div>

        <div class="test-section">
            <h3>Application Connection</h3>
            <div class="input-group">
                <label for="appUrl">Application URL:</label>
                <input type="text" id="appUrl" value="http://localhost:3002" />
            </div>
            <button class="test-button" onclick="openMainApp()">Open Main App</button>
            <button class="test-button" onclick="checkAppStatus()">Check App Status</button>
        </div>

        <div class="test-section">
            <h3>Quick Tests</h3>
            <button class="test-button" onclick="runQuickInspection()">Quick Inspection</button>
            <button class="test-button" onclick="testScriptLoading()">Test Script Loading</button>
            <button class="test-button" onclick="testBootstrap()">Test Bootstrap</button>
            <button class="test-button success" onclick="runFullTest()">Run Full Test</button>
        </div>

        <div class="test-section">
            <h3>Manual Dropdown Tests</h3>
            <button class="test-button" onclick="testSortDropdown()">Test Sort Dropdown</button>
            <button class="test-button" onclick="testFilterDropdown()">Test Filter Dropdown</button>
            <button class="test-button" onclick="testManualToggles()">Test Manual Toggles</button>
        </div>

        <div class="test-section">
            <h3>Modal Tests</h3>
            <button class="test-button" onclick="findGmailModal()">Find Gmail Modal</button>
            <button class="test-button" onclick="openGmailModal()">Open Gmail Modal</button>
            <button class="test-button" onclick="testModalDropdowns()">Test Modal Dropdowns</button>
        </div>

        <div class="warning">
            <h3>Note:</h3>
            <p>These tests work by communicating with the main application window. Make sure the main MRP application is open in another tab for the tests to work properly.</p>
        </div>

        <div class="test-section">
            <h3>Console Output</h3>
            <button class="test-button danger" onclick="clearConsole()">Clear Console</button>
            <button class="test-button" onclick="saveResults()">Save Results</button>
            <div id="console"></div>
        </div>
    </div>

    <script>
        let mainAppWindow = null;
        const consoleElement = document.getElementById('console');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            consoleElement.textContent += logMessage;
            consoleElement.scrollTop = consoleElement.scrollHeight;
        }

        function clearConsole() {
            consoleElement.textContent = '';
        }

        function openMainApp() {
            const url = document.getElementById('appUrl').value;
            log(`Opening main application at ${url}`);
            mainAppWindow = window.open(url, 'mrp-app');
            if (mainAppWindow) {
                log('Main application window opened successfully', 'success');
            } else {
                log('Failed to open main application window', 'error');
            }
        }

        function checkAppStatus() {
            if (!mainAppWindow) {
                log('Main application window not opened yet', 'warning');
                return;
            }

            try {
                if (mainAppWindow.closed) {
                    log('Main application window is closed', 'warning');
                    mainAppWindow = null;
                } else {
                    log('Main application window is open and accessible', 'success');
                    // Try to access the document
                    try {
                        const title = mainAppWindow.document.title;
                        log(`Main app title: ${title}`);
                    } catch (e) {
                        log('Cannot access main app document (likely CORS)', 'warning');
                    }
                }
            } catch (e) {
                log(`Error checking main app status: ${e.message}`, 'error');
            }
        }

        function executeInMainApp(code, description) {
            if (!mainAppWindow || mainAppWindow.closed) {
                log('Main application window not available. Please open it first.', 'error');
                return;
            }

            try {
                log(`Executing: ${description}`);
                const result = mainAppWindow.eval(code);
                log(`Result: ${JSON.stringify(result)}`, 'success');
                return result;
            } catch (e) {
                log(`Error executing code: ${e.message}`, 'error');
                return null;
            }
        }

        function runQuickInspection() {
            const inspectionCode = `
                (function() {
                    const results = {
                        hasBootstrap: typeof bootstrap !== 'undefined',
                        hasJQuery: typeof $ !== 'undefined',
                        gmailModal: !!document.querySelector('#mrp-gmail-modal'),
                        sortButton: !!document.querySelector('#sortButton'),
                        filterButton: !!document.querySelector('#filterButton'),
                        sortDropdown: !!document.querySelector('#sortDropdown'),
                        filterDropdown: !!document.querySelector('#filterDropdown'),
                        scriptsLoaded: {
                            dropdowns: !!document.querySelector('script[src*="mrp-gmail-dropdowns"]'),
                            utils: !!document.querySelector('script[src*="mrp-gmail-utils"]')
                        }
                    };
                    return results;
                })()
            `;
            
            const result = executeInMainApp(inspectionCode, 'Quick inspection of page elements');
            if (result) {
                log('Inspection results:');
                for (const [key, value] of Object.entries(result)) {
                    if (typeof value === 'object') {
                        log(`  ${key}:`);
                        for (const [subKey, subValue] of Object.entries(value)) {
                            log(`    ${subKey}: ${subValue ? '✅' : '❌'}`);
                        }
                    } else {
                        log(`  ${key}: ${value ? '✅' : '❌'}`);
                    }
                }
            }
        }

        function testScriptLoading() {
            const testCode = `
                (function() {
                    const scripts = Array.from(document.querySelectorAll('script')).map(s => s.src);
                    const gmailScripts = scripts.filter(src => src.includes('mrp-gmail'));
                    return {
                        allScripts: scripts.length,
                        gmailScripts: gmailScripts,
                        dropdownsLoaded: gmailScripts.some(s => s.includes('dropdowns')),
                        utilsLoaded: gmailScripts.some(s => s.includes('utils'))
                    };
                })()
            `;
            
            executeInMainApp(testCode, 'Testing script loading');
        }

        function testBootstrap() {
            const testCode = `
                (function() {
                    if (typeof bootstrap === 'undefined') {
                        return { error: 'Bootstrap not loaded' };
                    }
                    
                    return {
                        version: bootstrap.Tooltip?.VERSION || 'unknown',
                        dropdown: typeof bootstrap.Dropdown,
                        modal: typeof bootstrap.Modal
                    };
                })()
            `;
            
            executeInMainApp(testCode, 'Testing Bootstrap availability');
        }

        function findGmailModal() {
            const testCode = `
                (function() {
                    const modal = document.querySelector('#mrp-gmail-modal');
                    if (!modal) {
                        return { error: 'Gmail modal not found' };
                    }
                    
                    return {
                        exists: true,
                        isVisible: modal.classList.contains('show'),
                        style: modal.style.display,
                        children: modal.children.length,
                        hasDropdowns: {
                            sort: !!modal.querySelector('#sortButton'),
                            filter: !!modal.querySelector('#filterButton')
                        }
                    };
                })()
            `;
            
            executeInMainApp(testCode, 'Finding Gmail modal');
        }

        function testSortDropdown() {
            const testCode = `
                (function() {
                    const button = document.querySelector('#sortButton');
                    const dropdown = document.querySelector('#sortDropdown');
                    
                    if (!button || !dropdown) {
                        return { error: 'Sort dropdown elements not found' };
                    }
                    
                    const originalDisplay = dropdown.style.display;
                    button.click();
                    
                    setTimeout(() => {
                        const newDisplay = dropdown.style.display;
                        const isVisible = dropdown.classList.contains('show');
                        console.log('Sort dropdown test:', { originalDisplay, newDisplay, isVisible });
                    }, 100);
                    
                    return { clicked: true, button: button.textContent, items: dropdown.children.length };
                })()
            `;
            
            executeInMainApp(testCode, 'Testing sort dropdown');
        }

        function testFilterDropdown() {
            const testCode = `
                (function() {
                    const button = document.querySelector('#filterButton');
                    const dropdown = document.querySelector('#filterDropdown');
                    
                    if (!button || !dropdown) {
                        return { error: 'Filter dropdown elements not found' };
                    }
                    
                    const originalDisplay = dropdown.style.display;
                    button.click();
                    
                    setTimeout(() => {
                        const newDisplay = dropdown.style.display;
                        const isVisible = dropdown.classList.contains('show');
                        console.log('Filter dropdown test:', { originalDisplay, newDisplay, isVisible });
                    }, 100);
                    
                    return { clicked: true, button: button.textContent, items: dropdown.children.length };
                })()
            `;
            
            executeInMainApp(testCode, 'Testing filter dropdown');
        }

        function testManualToggles() {
            const testCode = `
                (function() {
                    const results = {};
                    
                    if (typeof toggleSortDropdown === 'function') {
                        try {
                            toggleSortDropdown();
                            results.sortToggle = 'success';
                        } catch (e) {
                            results.sortToggle = 'error: ' + e.message;
                        }
                    } else {
                        results.sortToggle = 'function not found';
                    }
                    
                    if (typeof toggleFilterDropdown === 'function') {
                        try {
                            toggleFilterDropdown();
                            results.filterToggle = 'success';
                        } catch (e) {
                            results.filterToggle = 'error: ' + e.message;
                        }
                    } else {
                        results.filterToggle = 'function not found';
                    }
                    
                    return results;
                })()
            `;
            
            executeInMainApp(testCode, 'Testing manual toggle functions');
        }

        function openGmailModal() {
            const testCode = `
                (function() {
                    // Try different ways to open Gmail modal
                    const methods = [
                        () => document.querySelector('[onclick*="openGmail"]')?.click(),
                        () => document.querySelector('[href*="gmail"]')?.click(),
                        () => document.querySelector('.gmail-btn')?.click(),
                        () => document.querySelector('#gmail-btn')?.click(),
                        () => typeof openGmail === 'function' ? openGmail() : null
                    ];
                    
                    for (let i = 0; i < methods.length; i++) {
                        try {
                            const result = methods[i]();
                            if (result) {
                                return { method: i, success: true };
                            }
                        } catch (e) {
                            console.log('Method', i, 'failed:', e.message);
                        }
                    }
                    
                    return { error: 'Could not find way to open Gmail modal' };
                })()
            `;
            
            executeInMainApp(testCode, 'Attempting to open Gmail modal');
        }

        function testModalDropdowns() {
            const testCode = `
                (function() {
                    const modal = document.querySelector('#mrp-gmail-modal');
                    if (!modal || !modal.classList.contains('show')) {
                        return { error: 'Gmail modal is not open' };
                    }
                    
                    const sortBtn = modal.querySelector('#sortButton');
                    const filterBtn = modal.querySelector('#filterButton');
                    
                    const results = { tests: [] };
                    
                    if (sortBtn) {
                        sortBtn.click();
                        setTimeout(() => {
                            const dropdown = modal.querySelector('#sortDropdown');
                            const isVisible = dropdown?.classList.contains('show') || dropdown?.style.display === 'block';
                            results.tests.push({ name: 'Sort', clicked: true, opened: isVisible });
                        }, 100);
                    } else {
                        results.tests.push({ name: 'Sort', error: 'Button not found' });
                    }
                    
                    setTimeout(() => {
                        if (filterBtn) {
                            filterBtn.click();
                            setTimeout(() => {
                                const dropdown = modal.querySelector('#filterDropdown');
                                const isVisible = dropdown?.classList.contains('show') || dropdown?.style.display === 'block';
                                results.tests.push({ name: 'Filter', clicked: true, opened: isVisible });
                                console.log('Modal dropdown test results:', results);
                            }, 100);
                        } else {
                            results.tests.push({ name: 'Filter', error: 'Button not found' });
                            console.log('Modal dropdown test results:', results);
                        }
                    }, 300);
                    
                    return { started: true };
                })()
            `;
            
            executeInMainApp(testCode, 'Testing dropdowns within Gmail modal');
        }

        function runFullTest() {
            log('Starting full test sequence...', 'info');
            
            setTimeout(() => {
                log('Step 1: Quick inspection');
                runQuickInspection();
            }, 500);
            
            setTimeout(() => {
                log('Step 2: Script loading test');
                testScriptLoading();
            }, 1500);
            
            setTimeout(() => {
                log('Step 3: Bootstrap test');
                testBootstrap();
            }, 2500);
            
            setTimeout(() => {
                log('Step 4: Find Gmail modal');
                findGmailModal();
            }, 3500);
            
            setTimeout(() => {
                log('Step 5: Test manual toggles');
                testManualToggles();
            }, 4500);
            
            setTimeout(() => {
                log('Step 6: Test dropdowns');
                testSortDropdown();
            }, 5500);
            
            setTimeout(() => {
                testFilterDropdown();
            }, 6500);
            
            setTimeout(() => {
                log('Full test sequence completed', 'success');
            }, 7500);
        }

        function saveResults() {
            const results = consoleElement.textContent;
            const blob = new Blob([results], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `gmail-dropdown-test-results-${new Date().toISOString().replace(/[:.]/g, '-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            log('Results saved to file', 'success');
        }

        // Initialize
        log('Gmail Dropdown Live Test Console initialized');
        log('Click "Open Main App" to start testing');
    </script>
</body>
</html>
