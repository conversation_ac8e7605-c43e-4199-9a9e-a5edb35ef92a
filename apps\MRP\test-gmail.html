<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Function Test</title>
    <style>
        .iframe-bordered {
            border: 1px solid #ccc;
        }
    </style>
    <script>
        function testOpenEmail() {
            console.log('Testing openEmail function...');
            console.log('Available functions:', typeof openEmail, typeof replyEmail, typeof showToast);
            
            if (typeof openEmail === 'function') {
                console.log('openEmail function found');
                openEmail('sarah-chen', 'Inventory update: New shipment arrived');
            } else {
                console.error('openEmail function not found');
            }
        }
        
        // Wait for page to load
        window.onload = function() {
            console.log('Page loaded, testing functions...');
            testOpenEmail();
        };
    </script>
</head>
<body>
    <p>Check console for test results</p>
    <button onclick="testOpenEmail()">Test openEmail</button>
    
    <!-- Include the main index.html in an iframe for function access -->
    <iframe src="http://localhost:3002" width="100%" height="600" class="iframe-bordered" title="Main application interface"></iframe>
</body>
</html>
</content>
</invoke>
