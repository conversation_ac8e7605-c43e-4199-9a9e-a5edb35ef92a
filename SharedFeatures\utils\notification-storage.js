/**
 * Notification Storage Module
 * 
 * This module provides persistent storage for notifications using SQLite.
 * It allows applications to store and retrieve notifications across system restarts.
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Ensure the data directory exists
const dataDir = path.join(__dirname, '..', '..', 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Create or open the SQLite database
const dbPath = path.join(dataDir, 'notifications.db');
const db = new sqlite3.Database(dbPath);

// Initialize the database
function initializeDatabase() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      db.run(`
        CREATE TABLE IF NOT EXISTS notifications (
          id TEXT PRIMARY KEY,
          source TEXT NOT NULL,
          type TEXT NOT NULL,
          message TEXT NOT NULL,
          timestamp TEXT NOT NULL,
          link TEXT,
          linkText TEXT,
          data TEXT,
          read INTEGER DEFAULT 0,
          deleted INTEGER DEFAULT 0
        )
      `, (err) => {
        if (err) {
          console.error('Error creating notifications table:', err);
          reject(err);
        } else {
          console.log('Notifications database initialized');
          resolve();
        }
      });
    });
  });
}

// Initialize the database when the module is loaded
initializeDatabase().catch(err => {
  console.error('Failed to initialize notifications database:', err);
});

/**
 * Save a notification to the database
 * 
 * @param {Object} notification - The notification object
 * @returns {Promise<Object>} - The saved notification
 */
function saveNotification(notification) {
  return new Promise((resolve, reject) => {
    // Ensure notification has an ID
    if (!notification.id) {
      notification.id = `notif-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    }
    
    // Ensure notification has a timestamp
    if (!notification.timestamp) {
      notification.timestamp = new Date().toISOString();
    }
    
    // Convert data object to JSON string if it exists
    const dataString = notification.data ? JSON.stringify(notification.data) : null;
    
    const stmt = db.prepare(`
      INSERT INTO notifications (id, source, type, message, timestamp, link, linkText, data, read, deleted)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      notification.id,
      notification.source,
      notification.type,
      notification.message,
      notification.timestamp,
      notification.link || null,
      notification.linkText || null,
      dataString,
      notification.read ? 1 : 0,
      notification.deleted ? 1 : 0,
      function(err) {
        stmt.finalize();
        
        if (err) {
          console.error('Error saving notification:', err);
          reject(err);
        } else {
          console.log(`Notification saved with ID: ${notification.id}`);
          resolve(notification);
        }
      }
    );
  });
}

/**
 * Get notifications from the database
 * 
 * @param {Object} options - Query options
 * @param {number} [options.limit=50] - Maximum number of notifications to return
 * @param {number} [options.offset=0] - Number of notifications to skip
 * @param {string} [options.source] - Filter by source
 * @param {string} [options.type] - Filter by type
 * @param {boolean} [options.includeRead=false] - Whether to include read notifications
 * @param {boolean} [options.includeDeleted=false] - Whether to include deleted notifications
 * @returns {Promise<Array>} - Array of notifications
 */
function getNotifications(options = {}) {
  return new Promise((resolve, reject) => {
    const limit = options.limit || 50;
    const offset = options.offset || 0;
    const includeRead = options.includeRead || false;
    const includeDeleted = options.includeDeleted || false;
    
    let query = 'SELECT * FROM notifications WHERE 1=1';
    const params = [];
    
    if (!includeRead) {
      query += ' AND read = 0';
    }
    
    if (!includeDeleted) {
      query += ' AND deleted = 0';
    }
    
    if (options.source) {
      query += ' AND source = ?';
      params.push(options.source);
    }
    
    if (options.type) {
      query += ' AND type = ?';
      params.push(options.type);
    }
    
    query += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);
    
    db.all(query, params, (err, rows) => {
      if (err) {
        console.error('Error retrieving notifications:', err);
        reject(err);
      } else {
        // Convert data string back to object
        const notifications = rows.map(row => {
          const notification = { ...row };
          
          if (notification.data) {
            try {
              notification.data = JSON.parse(notification.data);
            } catch (e) {
              console.error('Error parsing notification data:', e);
            }
          }
          
          // Convert numeric values to boolean
          notification.read = !!notification.read;
          notification.deleted = !!notification.deleted;
          
          return notification;
        });
        
        resolve(notifications);
      }
    });
  });
}

/**
 * Mark a notification as read
 * 
 * @param {string} id - The notification ID
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
function markAsRead(id) {
  return new Promise((resolve, reject) => {
    db.run('UPDATE notifications SET read = 1 WHERE id = ?', [id], function(err) {
      if (err) {
        console.error('Error marking notification as read:', err);
        reject(err);
      } else {
        resolve(this.changes > 0);
      }
    });
  });
}

/**
 * Mark a notification as deleted
 * 
 * @param {string} id - The notification ID
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
function markAsDeleted(id) {
  return new Promise((resolve, reject) => {
    db.run('UPDATE notifications SET deleted = 1 WHERE id = ?', [id], function(err) {
      if (err) {
        console.error('Error marking notification as deleted:', err);
        reject(err);
      } else {
        resolve(this.changes > 0);
      }
    });
  });
}

/**
 * Get notification count
 * 
 * @param {Object} options - Query options
 * @param {string} [options.source] - Filter by source
 * @param {string} [options.type] - Filter by type
 * @param {boolean} [options.includeRead=false] - Whether to include read notifications
 * @param {boolean} [options.includeDeleted=false] - Whether to include deleted notifications
 * @returns {Promise<number>} - Number of notifications
 */
function getNotificationCount(options = {}) {
  return new Promise((resolve, reject) => {
    const includeRead = options.includeRead || false;
    const includeDeleted = options.includeDeleted || false;
    
    let query = 'SELECT COUNT(*) as count FROM notifications WHERE 1=1';
    const params = [];
    
    if (!includeRead) {
      query += ' AND read = 0';
    }
    
    if (!includeDeleted) {
      query += ' AND deleted = 0';
    }
    
    if (options.source) {
      query += ' AND source = ?';
      params.push(options.source);
    }
    
    if (options.type) {
      query += ' AND type = ?';
      params.push(options.type);
    }
    
    db.get(query, params, (err, row) => {
      if (err) {
        console.error('Error getting notification count:', err);
        reject(err);
      } else {
        resolve(row.count);
      }
    });
  });
}

/**
 * Close the database connection
 * 
 * @returns {Promise<void>}
 */
function close() {
  return new Promise((resolve, reject) => {
    db.close(err => {
      if (err) {
        console.error('Error closing notifications database:', err);
        reject(err);
      } else {
        console.log('Notifications database closed');
        resolve();
      }
    });
  });
}

// Export the module functions
module.exports = {
  saveNotification,
  getNotifications,
  markAsRead,
  markAsDeleted,
  getNotificationCount,
  close
};
