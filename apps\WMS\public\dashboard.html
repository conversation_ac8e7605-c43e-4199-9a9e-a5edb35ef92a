<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Warehouse Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        :root {
            --app-primary-color: #2ecc71; /* Green for WMS */
            --app-primary-dark: #27ae60;
            --app-primary-light: rgba(46, 204, 113, 0.1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }

        h1 {
            margin-bottom: 20px;
            color: #333;
        }

        .back-button {
            display: inline-block;
            padding: 6px 12px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            margin-bottom: 20px;
        }

        .back-button:hover {
            background-color: #e9ecef;
        }

        .kpi-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .kpi-box {
            flex: 1;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 0 10px;
        }

        .kpi-box:first-child {
            margin-left: 0;
        }

        .kpi-box:last-child {
            margin-right: 0;
        }

        .kpi-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--app-primary-color);
            margin-bottom: 5px;
        }

        .kpi-label {
            color: #6c757d;
            font-size: 14px;
        }

        .section {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 18px;
            color: #333;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            text-align: left;
            padding: 12px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            color: #495057;
            font-weight: 600;
        }

        td {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
            color: #212529;
        }

        tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .status-in-stock {
            background-color: var(--app-primary-color);
        }

        .status-low-stock {
            background-color: #f1c40f;
        }

        .status-processing {
            background-color: #3498db;
        }

        .status-picking {
            background-color: #f1c40f;
        }

        .status-shipped {
            background-color: var(--app-primary-color);
        }
    </style>
</head>
<body>
    <h1>Warehouse Management System</h1>

    <a href="http://localhost:8000" class="back-button">Back to Hub</a>

    <div class="kpi-container">
        <div class="kpi-box">
            <div class="kpi-value">1,750</div>
            <div class="kpi-label">Total Items</div>
        </div>
        <div class="kpi-box">
            <div class="kpi-value">3</div>
            <div class="kpi-label">Active Orders</div>
        </div>
        <div class="kpi-box">
            <div class="kpi-value">98%</div>
            <div class="kpi-label">Accuracy Rate</div>
        </div>
    </div>

    <div class="section">
        <h2>Inventory Status</h2>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Item</th>
                    <th>Location</th>
                    <th>Quantity</th>
                    <th>Status</th>
                    <th>Last Updated</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>Widget A</td>
                    <td>A-01-02</td>
                    <td>500</td>
                    <td><span class="status-badge status-in-stock">In Stock</span></td>
                    <td>2024-05-15</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Widget B</td>
                    <td>B-03-01</td>
                    <td>250</td>
                    <td><span class="status-badge status-low-stock">Low Stock</span></td>
                    <td>2024-05-14</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Widget C</td>
                    <td>C-02-03</td>
                    <td>1000</td>
                    <td><span class="status-badge status-in-stock">In Stock</span></td>
                    <td>2024-05-15</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>Active Orders</h2>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Customer</th>
                    <th>Items</th>
                    <th>Status</th>
                    <th>Due Date</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>Acme Corp</td>
                    <td>Widget A x 100</td>
                    <td><span class="status-badge status-processing">Processing</span></td>
                    <td>2024-05-16</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Globex Inc</td>
                    <td>Widget B x 50</td>
                    <td><span class="status-badge status-picking">Picking</span></td>
                    <td>2024-05-17</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Initech</td>
                    <td>Widget C x 200</td>
                    <td><span class="status-badge status-shipped">Shipped</span></td>
                    <td>2024-05-15</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        // Create a simple health endpoint for status checks
        if (window.fetch) {
            // Create a fake health endpoint for the hub to check
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                if (url.includes('/health')) {
                    return Promise.resolve({
                        ok: true,
                        status: 200,
                        json: () => Promise.resolve({ status: 'ok' })
                    });
                }
                return originalFetch(url, options);
            };
        }
    </script>
</body>
</html>
