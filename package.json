{"name": "app-suite", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "SharedFeatures/*"], "scripts": {"start": "pnpm --filter hub start", "dev": "pnpm --filter hub dev", "build": "pnpm -r build", "test": "pnpm -r test", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "prettier --write .", "clean": "pnpm -r clean"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^3.0.0"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "dependencies": {"googleapis": "^148.0.0"}}