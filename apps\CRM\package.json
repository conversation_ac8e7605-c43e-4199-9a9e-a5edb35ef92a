{"name": "crm", "version": "1.0.0", "description": "Customer Relationship Management System", "main": "electron.js", "scripts": {"start": "node index.js production", "dev": "nodemon index.js development", "test": "echo \"Error: no test specified\" && exit 1", "electron-dev": "concurrently \"cross-env BROWSER=none npm run dev\" \"wait-on http://localhost:3003 && electron .\"", "electron-pack": "npm run build && electron-builder build --win --publish never", "electron-dist": "electron-builder --win --publish never", "build": "webpack --mode production", "build:dev": "webpack --mode development"}, "keywords": ["customer", "relationship", "management"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "react": "^18.2.0", "react-dom": "^18.2.0", "cors": "^2.8.5", "axios": "^1.6.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^30.0.0", "electron-builder": "^24.13.3", "nodemon": "^3.0.1", "wait-on": "^7.2.0", "webpack": "^5.99.7", "webpack-cli": "^6.0.1"}, "build": {"appId": "com.isa.crm", "productName": "ISA Customer Relationship Management", "files": ["build/**/*", "electron.js", "preload.js", "package.json", "manifest.json"], "directories": {"buildResources": "public"}, "win": {"target": ["nsis"], "icon": "public/icons/icon-512x512.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}